import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { NextRequest } from 'next/server';
import { prisma } from './prisma';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

export interface JWTPayload {
  userId: string;
  email: string;
  roles: string[];
}

export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

export function verifyToken(token: string): JWTPayload {
  return jwt.verify(token, JWT_SECRET) as JWTPayload;
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

export async function getUserRoles(userId: string): Promise<string[]> {
  const userRoles = await prisma.userRole.findMany({
    where: { userId },
    include: {
      role: true,
    },
  });

  return userRoles.map(ur => ur.role.name);
}

export async function getAuthUser(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const payload = verifyToken(token);

    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        isActive: true,
        createdAt: true,
      },
    });

    if (!user || !user.isActive) {
      return null;
    }

    // Get user roles
    const roles = await getUserRoles(user.id);

    return {
      ...user,
      roles,
    };
  } catch (error) {
    return null;
  }
}

export function requireAuth(handler: Function) {
  return async (request: NextRequest, context: any) => {
    const user = await getAuthUser(request);
    if (!user) {
      return Response.json(
        { success: false, error: 'Unauthorized', code: 'UNAUTHORIZED' },
        { status: 401 }
      );
    }

    return handler(request, context, user);
  };
}

export function requireRole(roles: string[]) {
  return (handler: Function) => {
    return async (request: NextRequest, context: any) => {
      const user = await getAuthUser(request);
      if (!user) {
        return Response.json(
          { success: false, error: 'Unauthorized', code: 'UNAUTHORIZED' },
          { status: 401 }
        );
      }

      const hasRole = roles.some(role => user.roles.includes(role));
      if (!hasRole) {
        return Response.json(
          { success: false, error: 'Insufficient permissions', code: 'FORBIDDEN' },
          { status: 403 }
        );
      }

      return handler(request, context, user);
    };
  };
}

export async function hasPermission(userId: string, resource: string, action: string): Promise<boolean> {
  const userRoles = await prisma.userRole.findMany({
    where: { userId },
    include: {
      role: {
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
        },
      },
    },
  });

  for (const userRole of userRoles) {
    for (const rolePermission of userRole.role.rolePermissions) {
      const permission = rolePermission.permission;
      if (permission.resource === resource && permission.action === action) {
        return true;
      }
    }
  }

  return false;
}

export function requirePermission(resource: string, action: string) {
  return (handler: Function) => {
    return async (request: NextRequest, context: any) => {
      const user = await getAuthUser(request);
      if (!user) {
        return Response.json(
          { success: false, error: 'Unauthorized', code: 'UNAUTHORIZED' },
          { status: 401 }
        );
      }

      const hasAccess = await hasPermission(user.id, resource, action);
      if (!hasAccess) {
        return Response.json(
          { success: false, error: 'Insufficient permissions', code: 'FORBIDDEN' },
          { status: 403 }
        );
      }

      return handler(request, context, user);
    };
  };
}
