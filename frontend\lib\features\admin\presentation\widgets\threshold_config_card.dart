import 'package:flutter/material.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../screens/threshold_config_screen.dart';

class ThresholdConfigCard extends StatefulWidget {
  final String title;
  final IconData icon;
  final Color color;
  final String description;
  final List<ThresholdItem> thresholds;
  final Function(Map<String, double>) onSave;

  const ThresholdConfigCard({
    super.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.description,
    required this.thresholds,
    required this.onSave,
  });

  @override
  State<ThresholdConfigCard> createState() => _ThresholdConfigCardState();
}

class _ThresholdConfigCardState extends State<ThresholdConfigCard> {
  late FormGroup form;
  bool isExpanded = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final controls = <String, FormControl<double>>{};

    for (final threshold in widget.thresholds) {
      controls[threshold.key] = FormControl<double>(
        value: threshold.value,
        validators: [
          Validators.required,
          Validators.min(threshold.min),
          Validators.max(threshold.max),
        ],
      );
    }

    form = FormGroup(controls);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: widget.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      widget.icon,
                      color: widget.color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.description,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),

          // Expandable Content
          if (isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: ReactiveForm(
                formGroup: form,
                child: Column(
                  children: [
                    // Threshold Items
                    ...widget.thresholds.map((threshold) => _buildThresholdItem(threshold)),

                    const SizedBox(height: AppConstants.defaultPadding),

                    // Action Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: isLoading ? null : _resetForm,
                          child: const Text('Reset'),
                        ),
                        const SizedBox(width: AppConstants.smallPadding),
                        ElevatedButton(
                          onPressed: isLoading ? null : _saveThresholds,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: widget.color,
                            foregroundColor: Colors.white,
                          ),
                          child: isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('Save'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildThresholdItem(ThresholdItem threshold) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label and Description
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      threshold.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      threshold.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),

          // Input Field with Slider
          Column(
            children: [
              // Text Input Row
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: ReactiveTextField<double>(
                      formControlName: threshold.key,
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        suffixText: threshold.unit,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      validationMessages: {
                        ValidationMessage.required: (_) => 'Required',
                        ValidationMessage.min: (_) => 'Min: ${threshold.min}',
                        ValidationMessage.max: (_) => 'Max: ${threshold.max}',
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    flex: 1,
                    child: Text(
                      'Range: ${threshold.min}-${threshold.max}${threshold.unit}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.smallPadding),

              // Slider Row
              ReactiveSlider(
                formControlName: threshold.key,
                min: threshold.min,
                max: threshold.max,
                divisions: ((threshold.max - threshold.min) * 2).round(),
                activeColor: widget.color,
                inactiveColor: widget.color.withValues(alpha: 0.3),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _resetForm() {
    for (final threshold in widget.thresholds) {
      form.control(threshold.key).value = threshold.value;
    }
  }

  Future<void> _saveThresholds() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final values = <String, double>{};
      for (final threshold in widget.thresholds) {
        values[threshold.key] = form.control(threshold.key).value as double;
      }

      await widget.onSave(values);
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
