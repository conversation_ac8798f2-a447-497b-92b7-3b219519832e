"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"

export type MaintenanceIssue = {
  id?: string
  property_id: string
  issue_type: string
  category: string
  issue_date: string
  status: string
  resolution_date?: string
  remarks?: string
  reported_by: string
  assigned_to?: string
  priority: string
  is_recurring?: boolean
  recurrence_period?: string
  next_due_date?: string
}

export async function createMaintenanceIssue(formData: FormData) {
  const supabase = createServerClient()

  const issue: MaintenanceIssue = {
    property_id: formData.get("property_id") as string,
    issue_type: formData.get("issue_title") as string,
    category: formData.get("department") as string,
    issue_date: formData.get("start_date") as string,
    resolution_date: (formData.get("end_date") as string) || null,
    status: (formData.get("status") as string) || "Open",
    remarks: formData.get("remarks") as string,
    reported_by: formData.get("reported_by") as string,
    priority: formData.get("priority") as string,
    is_recurring: formData.get("is_recurring") === "true",
    recurrence_period: (formData.get("recurrence_period") as string) || null,
    next_due_date: (formData.get("next_due_date") as string) || null,
  }

  try {
    // First, insert the issue without waiting for escalation setup
    const { data, error } = await supabase.from("maintenance_issues").insert(issue).select()

    if (error) {
      console.error("Error creating maintenance issue:", error)
      return { success: false, error: error.message }
    }

    // If the insert was successful, try to set up escalation
    // But don't let escalation errors prevent the issue from being saved
    try {
      if (data && data.length > 0) {
        await setupEscalation(data[0].id, issue.priority)
      }
    } catch (escalationError) {
      console.error("Error in escalation setup, but issue was saved:", escalationError)
      // Don't return an error here, as the issue was already saved
    }

    revalidatePath(`/dashboard/home/<USER>/maintenance`)
    return { success: true, data }
  } catch (err) {
    console.error("Unexpected error in createMaintenanceIssue:", err)
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function updateMaintenanceIssue(formData: FormData) {
  const supabase = createServerClient()

  const id = formData.get("id") as string
  const issue: Partial<MaintenanceIssue> = {
    issue_type: formData.get("issue_title") as string,
    category: formData.get("department") as string,
    issue_date: formData.get("start_date") as string,
    resolution_date: (formData.get("end_date") as string) || null,
    status: formData.get("status") as string,
    remarks: formData.get("remarks") as string,
    reported_by: formData.get("reported_by") as string,
    priority: formData.get("priority") as string,
    is_recurring: formData.get("is_recurring") === "true",
    recurrence_period: (formData.get("recurrence_period") as string) || null,
    next_due_date: (formData.get("next_due_date") as string) || null,
  }

  try {
    const { data, error } = await supabase.from("maintenance_issues").update(issue).eq("id", id).select()

    if (error) {
      return { success: false, error: error.message }
    }

    const propertyId = formData.get("property_id") as string
    revalidatePath(`/dashboard/home/<USER>/maintenance`)
    return { success: true, data }
  } catch (err) {
    console.error("Unexpected error in updateMaintenanceIssue:", err)
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function updateIssueStatus(id: string, status: string, propertyId: string) {
  const supabase = createServerClient()

  try {
    // First, get the current issue to check if it's recurring
    const { data: currentIssue, error: fetchError } = await supabase
      .from("maintenance_issues")
      .select("*")
      .eq("id", id)
      .single()

    if (fetchError) {
      return { success: false, error: fetchError.message }
    }

    // Update the status
    const { data, error } = await supabase.from("maintenance_issues").update({ status }).eq("id", id).select()

    if (error) {
      return { success: false, error: error.message }
    }

    // If the issue is being closed and it's recurring, create a new issue for the next period
    if (status === "Closed" && currentIssue.is_recurring && currentIssue.recurrence_period) {
      await createNextRecurringIssue(currentIssue)
    }

    revalidatePath(`/dashboard/home/<USER>/maintenance`)
    return { success: true, data }
  } catch (err) {
    console.error("Unexpected error in updateIssueStatus:", err)
    return { success: false, error: "An unexpected error occurred" }
  }
}

// Function to create the next recurring issue
async function createNextRecurringIssue(currentIssue: MaintenanceIssue) {
  const supabase = createServerClient()

  // Calculate the next due date based on recurrence period
  let nextDueDate = new Date()

  if (currentIssue.next_due_date) {
    nextDueDate = new Date(currentIssue.next_due_date)
  } else {
    // If no next_due_date is set, calculate from resolution_date or current date
    nextDueDate = currentIssue.resolution_date ? new Date(currentIssue.resolution_date) : new Date()
  }

  // Add the appropriate time based on recurrence period
  switch (currentIssue.recurrence_period) {
    case "weekly":
      nextDueDate.setDate(nextDueDate.getDate() + 7)
      break
    case "monthly":
      nextDueDate.setMonth(nextDueDate.getMonth() + 1)
      break
    case "quarterly":
      nextDueDate.setMonth(nextDueDate.getMonth() + 3)
      break
    case "yearly":
      nextDueDate.setFullYear(nextDueDate.getFullYear() + 1)
      break
    default:
      // Default to monthly if not specified
      nextDueDate.setMonth(nextDueDate.getMonth() + 1)
  }

  // Create a new issue with the same details but updated dates
  const newIssue = {
    ...currentIssue,
    id: undefined, // Remove the ID so a new one is generated
    status: "Open", // Reset status to Open
    issue_date: new Date().toISOString().split("T")[0], // Today
    resolution_date: null, // Reset resolution date
    next_due_date: nextDueDate.toISOString().split("T")[0], // Set next due date
    remarks: `Recurring issue from previous issue ID: ${currentIssue.id}. ${currentIssue.remarks || ""}`,
  }

  try {
    const { error } = await supabase.from("maintenance_issues").insert(newIssue)

    if (error) {
      console.error("Error creating next recurring issue:", error)
    }
  } catch (err) {
    console.error("Unexpected error creating next recurring issue:", err)
  }
}

export async function deleteMaintenanceIssue(id: string, propertyId: string) {
  const supabase = createServerClient()

  try {
    const { error } = await supabase.from("maintenance_issues").delete().eq("id", id)

    if (error) {
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/home/<USER>/maintenance`)
    return { success: true }
  } catch (err) {
    console.error("Unexpected error in deleteMaintenanceIssue:", err)
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function getMaintenanceIssues(propertyId: string) {
  const supabase = createServerClient()

  // Add delay to prevent rate limiting
  await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000 + 500))

  let retryCount = 0
  const maxRetries = 3

  while (retryCount < maxRetries) {
    try {
      // Wrap the entire Supabase call to catch JSON parsing errors
      let data, error

      try {
        const result = await supabase
          .from("maintenance_issues")
          .select("*")
          .eq("property_id", propertyId)
          .order("created_at", { ascending: false })

        data = result.data
        error = result.error
      } catch (supabaseError) {
        // Handle JSON parsing errors from Supabase client
        const errorMessage = supabaseError instanceof Error ? supabaseError.message : String(supabaseError)

        if (
          errorMessage.includes("Unexpected token") ||
          errorMessage.includes("Too Many R") ||
          errorMessage.includes("SyntaxError") ||
          errorMessage.includes("JSON")
        ) {
          console.warn(
            `JSON parsing error for maintenance issues (attempt ${retryCount + 1}/${maxRetries}):`,
            errorMessage,
          )

          if (retryCount < maxRetries - 1) {
            const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000
            console.log(`Retrying maintenance issues fetch in ${delay}ms due to JSON error...`)
            await new Promise((resolve) => setTimeout(resolve, delay))
            retryCount++
            continue
          } else {
            console.error("Max retries reached for maintenance issues due to JSON parsing error, returning empty array")
            return []
          }
        }

        // Re-throw other Supabase errors to be handled below
        throw supabaseError
      }

      if (error) {
        // Check if it's a rate limiting error
        if (
          error.message?.includes("Too Many Requests") ||
          error.message?.includes("rate limit") ||
          error.message?.includes("429")
        ) {
          console.warn(
            `Rate limit hit for maintenance issues (attempt ${retryCount + 1}/${maxRetries}):`,
            error.message,
          )

          if (retryCount < maxRetries - 1) {
            // Exponential backoff: wait longer each retry
            const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000
            console.log(`Retrying maintenance issues fetch in ${delay}ms...`)
            await new Promise((resolve) => setTimeout(resolve, delay))
            retryCount++
            continue
          } else {
            console.error("Max retries reached for maintenance issues, returning empty array")
            return []
          }
        }

        // For other errors, log and return empty array
        console.error("Error fetching maintenance issues:", error)
        return []
      }

      // Success - log and return data
      console.log(`Successfully fetched ${data?.length || 0} maintenance issues for property ${propertyId}`)

      if (data && data.length > 0) {
        console.log(
          "Sample maintenance issue statuses:",
          data.slice(0, 3).map((issue) => ({
            id: issue.id?.substring(0, 8),
            status: issue.status,
            priority: issue.priority,
          })),
        )
      }

      return data || []
    } catch (err) {
      // Handle network errors and other unexpected errors
      const errorMessage = err instanceof Error ? err.message : String(err)

      if (
        errorMessage.includes("Too Many Requests") ||
        errorMessage.includes("rate limit") ||
        errorMessage.includes("429") ||
        errorMessage.includes("Unexpected token") ||
        errorMessage.includes("SyntaxError") ||
        errorMessage.includes("JSON")
      ) {
        console.warn(
          `Rate limiting/JSON error detected for maintenance issues (attempt ${retryCount + 1}/${maxRetries}):`,
          errorMessage,
        )

        if (retryCount < maxRetries - 1) {
          // Exponential backoff
          const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000
          console.log(`Retrying maintenance issues fetch in ${delay}ms...`)
          await new Promise((resolve) => setTimeout(resolve, delay))
          retryCount++
          continue
        } else {
          console.error(
            "Max retries reached for maintenance issues due to rate limiting/JSON error, returning empty array",
          )
          return []
        }
      }

      // For other unexpected errors
      console.error("Unexpected error in getMaintenanceIssues:", err)
      return []
    }
  }

  // Fallback (should not reach here)
  console.error("Unexpected exit from retry loop in getMaintenanceIssues")
  return []
}

export async function getMaintenanceIssue(id: string) {
  const supabase = createServerClient()

  try {
    const { data, error } = await supabase.from("maintenance_issues").select("*").eq("id", id).single()

    if (error) {
      console.error("Error fetching maintenance issue:", error)
      return null
    }

    return data
  } catch (err) {
    console.error("Unexpected error in getMaintenanceIssue:", err)
    return null
  }
}

// Function to set up escalation for an issue
async function setupEscalation(issueId: string, priority: string) {
  const supabase = createServerClient()

  try {
    // Get the escalation config for this priority and level 1
    const { data: configData, error: configError } = await supabase
      .from("escalation_config")
      .select("*")
      .eq("priority", priority)
      .eq("escalation_level", 1)

    if (configError) {
      console.error("Error fetching escalation config:", configError)
      return
    }

    // If no config exists, just log and return without error
    if (!configData || configData.length === 0) {
      console.log(`No escalation config found for priority ${priority} and level 1`)
      return
    }

    // Use the first config if multiple exist
    const config = configData[0]

    // Create an escalation entry
    const { error: escalationError } = await supabase.from("escalation_matrix").insert({
      issue_id: issueId,
      escalation_level: 1,
      escalated_to: config.escalate_to,
      resolved: false,
    })

    if (escalationError) {
      console.error("Error creating escalation entry:", escalationError)
    }
  } catch (err) {
    console.error("Unexpected error in setupEscalation:", err)
    // Don't throw the error, just log it
  }
}

// Function to check for issues that need escalation
export async function checkEscalations() {
  const supabase = createServerClient()

  try {
    // Get all open issues
    const { data: issues, error: issuesError } = await supabase
      .from("maintenance_issues")
      .select("*")
      .eq("status", "Open")

    if (issuesError || !issues) {
      console.error("Error fetching open issues:", issuesError)
      return
    }

    // For each issue, check if it needs escalation
    for (const issue of issues) {
      const issueDate = new Date(issue.issue_date)
      const today = new Date()
      const daysDifference = Math.floor((today.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24))

      try {
        // Get the escalation config for this priority
        const { data: configData, error: configError } = await supabase
          .from("escalation_config")
          .select("*")
          .eq("priority", issue.priority)
          .order("escalation_level", { ascending: true })

        if (configError) {
          console.error("Error fetching escalation config:", configError)
          continue
        }

        // If no config exists, skip this issue
        if (!configData || configData.length === 0) {
          console.log(`No escalation config found for priority ${issue.priority}`)
          continue
        }

        // Get current escalation level for this issue
        const { data: currentEscalation, error: escalationError } = await supabase
          .from("escalation_matrix")
          .select("*")
          .eq("issue_id", issue.id)
          .order("escalation_level", { ascending: false })
          .limit(1)

        if (escalationError) {
          console.error("Error fetching current escalation:", escalationError)
          continue
        }

        const currentLevel =
          currentEscalation && currentEscalation.length > 0 ? currentEscalation[0].escalation_level : 0

        // Find the next escalation level if days have passed
        for (const config of configData) {
          if (config.escalation_level > currentLevel && daysDifference >= config.days_to_escalate) {
            // Create a new escalation
            await supabase.from("escalation_matrix").insert({
              issue_id: issue.id,
              escalation_level: config.escalation_level,
              escalated_to: config.escalate_to,
              resolved: false,
            })

            break
          }
        }
      } catch (err) {
        console.error(`Error processing escalation for issue ${issue.id}:`, err)
        // Continue with the next issue
      }
    }
  } catch (err) {
    console.error("Unexpected error in checkEscalations:", err)
  }
}
