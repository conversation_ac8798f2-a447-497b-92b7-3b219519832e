<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="29" failures="23" errors="0" time="3.668">
  <testsuite name="Admin Role Management API Tests" errors="0" failures="23" skipped="0" timestamp="2025-06-01T11:21:51" time="3.573" tests="29">
    <testcase classname="Admin Role Management API Tests Role Creation should create custom role with valid data" name="Admin Role Management API Tests Role Creation should create custom role with valid data" time="0.035">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:52:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create facilities manager role" name="Admin Role Management API Tests Role Creation should create facilities manager role" time="0.026">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:72:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create security supervisor role" name="Admin Role Management API Tests Role Creation should create security supervisor role" time="0.023">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:90:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create escalation manager role" name="Admin Role Management API Tests Role Creation should create escalation manager role" time="0.028">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:108:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should reject role with invalid name" name="Admin Role Management API Tests Role Creation should reject role with invalid name" time="0.038">
      <failure>Error: expect(received).toMatch(expected)

Expected pattern: /name/i
Received string:  &quot;Validation failed&quot;
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:127:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should reject role with missing required fields" name="Admin Role Management API Tests Role Creation should reject role with missing required fields" time="0.036">
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should reject duplicate role name" name="Admin Role Management API Tests Role Creation should reject duplicate role name" time="0.034">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:159:36)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create role with minimal permissions" name="Admin Role Management API Tests Role Creation should create role with minimal permissions" time="0.037">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:185:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Creation should create role with maximum permissions" name="Admin Role Management API Tests Role Creation should create role with maximum permissions" time="0.037">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:202:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should get all roles" name="Admin Role Management API Tests Role Retrieval should get all roles" time="0.036">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:235:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should get role by ID" name="Admin Role Management API Tests Role Retrieval should get role by ID" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:248:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should return 404 for non-existent role" name="Admin Role Management API Tests Role Retrieval should return 404 for non-existent role" time="0.442">
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should filter roles by type" name="Admin Role Management API Tests Role Retrieval should filter roles by type" time="0.035">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:279:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Retrieval should search roles by name" name="Admin Role Management API Tests Role Retrieval should search roles by name" time="0">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;name&apos;)
    at Object.name (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:291:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should update role details" name="Admin Role Management API Tests Role Updates should update role details" time="0.031">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;permissions&apos;)
    at Object.permissions (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:320:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should add permissions to role" name="Admin Role Management API Tests Role Updates should add permissions to role" time="0.028">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;permissions&apos;)
    at Object.permissions (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:339:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should remove permissions from role" name="Admin Role Management API Tests Role Updates should remove permissions from role" time="0.023">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;permissions&apos;)
    at Object.permissions (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:355:47)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should reject update with invalid data" name="Admin Role Management API Tests Role Updates should reject update with invalid data" time="0.02">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:385:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Updates should prevent updating system roles" name="Admin Role Management API Tests Role Updates should prevent updating system roles" time="0.042">
      <failure>TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:406:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Management should get all available permissions" name="Admin Role Management API Tests Permission Management should get all available permissions" time="0.194">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:432:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Management should get permissions by category" name="Admin Role Management API Tests Permission Management should get permissions by category" time="0.028">
      <failure>Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:451:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Management should validate permission assignments" name="Admin Role Management API Tests Permission Management should validate permission assignments" time="0.028">
      <failure>Error: expect(received).toMatch(expected)

Expected pattern: /invalid permission|permission not found/i
Received string:  &quot;Validation failed&quot;
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:472:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Enforcement should enforce role creation permissions" name="Admin Role Management API Tests Permission Enforcement should enforce role creation permissions" time="0.629">
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Enforcement should enforce role update permissions" name="Admin Role Management API Tests Permission Enforcement should enforce role update permissions" time="0.042">
    </testcase>
    <testcase classname="Admin Role Management API Tests Permission Enforcement should enforce role deletion permissions" name="Admin Role Management API Tests Permission Enforcement should enforce role deletion permissions" time="0.022">
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should delete custom role" name="Admin Role Management API Tests Role Deletion should delete custom role" time="0.021">
      <failure>TypeError: Cannot read properties of undefined (reading &apos;id&apos;)
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:533:47)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should prevent deletion of system roles" name="Admin Role Management API Tests Role Deletion should prevent deletion of system roles" time="0.022">
      <failure>TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:563:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should prevent deletion of role assigned to users" name="Admin Role Management API Tests Role Deletion should prevent deletion of role assigned to users" time="0.02">
      <failure>TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:586:59)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="Admin Role Management API Tests Role Deletion should return 404 when deleting non-existent role" name="Admin Role Management API Tests Role Deletion should return 404 when deleting non-existent role" time="0.028">
    </testcase>
    <testcase classname=" Test execution failure: could be caused by test hooks like &apos;afterAll&apos;." name=" Test execution failure: could be caused by test hooks like &apos;afterAll&apos;." time="0">
      <failure>{&quot;message&quot;:&quot;&quot;,&quot;stack&quot;:&quot;TypeError: Cannot read properties of undefined (reading &apos;name&apos;)\n    at Object.name (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\tests\\admin\\role_management.test.js:33:56)\n    at Promise.then.completed (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (&lt;anonymous&gt;)\n    at callAsyncCircusFn (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusHook (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTestsForDescribeBlock (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:154:7)\n    at _runTestsForDescribeBlock (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\api_tests\\node_modules\\jest-runner\\build\\runTest.js:444:34)&quot;}</failure>
    </testcase>
  </testsuite>
</testsuites>