import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Climate tab - HVAC, temperature, and environmental controls
class ClimateTab extends StatefulWidget {
  const ClimateTab({super.key});

  @override
  State<ClimateTab> createState() => _ClimateTabState();
}

class _ClimateTabState extends State<ClimateTab> {
  double _targetTemperature = 22.0;
  bool _hvacEnabled = true;
  String _mode = 'auto';

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildThermostat(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildModeSelector(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildClimateZones(context),
          ),
        ],
      ),
    );
  }

  Widget _buildThermostat(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Text(
              'Current Temperature',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              '${_targetTemperature.toStringAsFixed(1)}°C',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            Slider(
              value: _targetTemperature,
              min: 16.0,
              max: 30.0,
              divisions: 28,
              label: '${_targetTemperature.toStringAsFixed(1)}°C',
              onChanged: (value) => setState(() => _targetTemperature = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeSelector(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildModeCard('Auto', 'auto', Icons.auto_mode)),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(child: _buildModeCard('Cool', 'cool', Icons.ac_unit)),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(child: _buildModeCard('Heat', 'heat', Icons.whatshot)),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(child: _buildModeCard('Fan', 'fan', Icons.air)),
      ],
    );
  }

  Widget _buildModeCard(String title, String mode, IconData icon) {
    final isSelected = _mode == mode;
    return Card(
      color: isSelected ? Colors.orange.shade100 : null,
      child: InkWell(
        onTap: () => setState(() => _mode = mode),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.orange : Colors.grey,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? Colors.orange : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClimateZones(BuildContext context) {
    return ListView(
      children: [
        _buildZoneCard('Building A - Main Floor', 23.5, 45, true),
        _buildZoneCard('Building A - Server Room', 18.2, 35, true),
        _buildZoneCard('Building B - Office Area', 24.1, 50, false),
        _buildZoneCard('Building B - Storage', 20.0, 60, false),
      ],
    );
  }

  Widget _buildZoneCard(String name, double temp, int humidity, bool isActive) {
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.thermostat,
          color: isActive ? Colors.orange : Colors.grey,
          size: 32,
        ),
        title: Text(name),
        subtitle: Text('Humidity: $humidity%'),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${temp.toStringAsFixed(1)}°C',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Chip(
              label: Text(isActive ? 'ACTIVE' : 'OFF'),
              backgroundColor: isActive ? Colors.green.shade100 : Colors.grey.shade100,
            ),
          ],
        ),
        onTap: () => _showZoneDetails(name),
      ),
    );
  }

  void _showZoneDetails(String zoneName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(zoneName),
        content: const Text('Climate zone details coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
