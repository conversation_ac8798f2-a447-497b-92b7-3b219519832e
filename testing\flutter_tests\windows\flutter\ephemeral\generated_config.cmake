# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\flutter_tests" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\flutter"
  "PROJECT_DIR=D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\flutter_tests"
  "FLUTTER_ROOT=C:\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\flutter_tests\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\flutter_tests"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\AppData\\Local\\Temp\\flutter_tools.ddfd4df5\\flutter_test_listener.d3988d69/listener.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuMQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YjI1MzA1YTg4Mw==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MTQyNWU1ZTllYw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE=,SU5URUdSQVRJT05fVEVTVF9TSE9VTERfUkVQT1JUX1JFU1VMVFNfVE9fTkFUSVZFPWZhbHNl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\workspaces\\nsl\\back\\SrsrMan\\testing\\flutter_tests\\.dart_tool\\package_config.json"
)
