import '../../../shared/models/user.dart';

abstract class AuthRepository {
  Future<AuthResult> login(String email, String password);
  Future<AuthResult> loginWithIdentifier({
    required String identifier,
    required String password,
    required String loginType,
  });
  Future<AuthResult> register(String email, String password, String fullName, String? phone);
  Future<User> getCurrentUser();
  Future<void> logout();
  bool get isLoggedIn;
}

class AuthResult {
  final bool success;
  final String? token;
  final User? user;
  final String? error;

  const AuthResult({
    required this.success,
    this.token,
    this.user,
    this.error,
  });

  factory AuthResult.success(String token, User user) {
    return AuthResult(
      success: true,
      token: token,
      user: user,
    );
  }

  factory AuthResult.failure(String error) {
    return AuthResult(
      success: false,
      error: error,
    );
  }

  @override
  String toString() {
    return 'AuthResult(success: $success, token: $token, user: $user, error: $error)';
  }
}
