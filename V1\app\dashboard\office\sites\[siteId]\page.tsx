import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { notFound } from "next/navigation"
import { SiteAttendanceForm } from "@/components/site-attendance-form"
import { SiteAttendanceReports } from "@/components/site-attendance-reports"
import { SiteMembersManagement } from "@/components/site-members-management"
import { getSiteMembers } from "@/app/actions/site-management"

// This is a server component that gets site data
export default async function SitePage({ params }: { params: { siteId: string } }) {
  const { siteId } = params

  // Map of site IDs to names
  const siteNames: Record<string, string> = {
    "gandipet-1": "Gandipet 1",
    "gandipet-2": "Gandipet 2",
    "gandipet-3-4": "Gandipet 3 & 4",
    bachupally: "Bachupally",
  }

  const siteName = siteNames[siteId]

  if (!siteName) {
    notFound()
  }

  // Fetch site members
  const members = await getSiteMembers(siteId).catch(() => [])

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office">Office</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office/sites">Sites</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/dashboard/office/sites/${siteId}`}>{siteName}</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <h1 className="mb-8 text-3xl font-bold">{siteName}</h1>

        <Tabs defaultValue="attendance">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="attendance">Submit Attendance</TabsTrigger>
            <TabsTrigger value="reports">Download Reports</TabsTrigger>
            <TabsTrigger value="members">Add/Remove Members</TabsTrigger>
          </TabsList>

          <TabsContent value="attendance" className="p-4 border rounded-md mt-4">
            <h2 className="text-xl font-semibold mb-4">Submit Attendance</h2>
            <SiteAttendanceForm siteId={siteId} siteName={siteName} members={members} />
          </TabsContent>

          <TabsContent value="reports" className="p-4 border rounded-md mt-4">
            <h2 className="text-xl font-semibold mb-4">Attendance Reports</h2>
            <SiteAttendanceReports siteId={siteId} siteName={siteName} />
          </TabsContent>

          <TabsContent value="members" className="p-4 border rounded-md mt-4">
            <h2 className="text-xl font-semibold mb-4">Manage Site Members</h2>
            <SiteMembersManagement siteId={siteId} siteName={siteName} initialMembers={members} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
