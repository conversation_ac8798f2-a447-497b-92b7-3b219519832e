import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';

/// Location filter dropdown widget
class LocationFilterDropdown extends ConsumerWidget {
  final String? selectedLocation;
  final List<String> locations;
  final ValueChanged<String?> onChanged;
  final String hint;

  const LocationFilterDropdown({
    super.key,
    this.selectedLocation,
    required this.locations,
    required this.onChanged,
    this.hint = 'Select Location',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedLocation,
          hint: Text(hint),
          isExpanded: true,
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('All Locations'),
            ),
            ...locations.map((location) => DropdownMenuItem<String>(
              value: location,
              child: Text(location),
            )),
          ],
          onChanged: onChanged,
        ),
      ),
    );
  }
}

/// Property filter dropdown widget
class PropertyFilterDropdown extends ConsumerWidget {
  final String? selectedProperty;
  final List<PropertyOption> properties;
  final ValueChanged<String?> onChanged;
  final String hint;

  const PropertyFilterDropdown({
    super.key,
    this.selectedProperty,
    required this.properties,
    required this.onChanged,
    this.hint = 'Select Property',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedProperty,
          hint: Text(hint),
          isExpanded: true,
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('All Properties'),
            ),
            ...properties.map((property) => DropdownMenuItem<String>(
              value: property.id,
              child: Text(property.name),
            )),
          ],
          onChanged: onChanged,
        ),
      ),
    );
  }
}

/// Category filter dropdown widget
class CategoryFilterDropdown extends ConsumerWidget {
  final String? selectedCategory;
  final List<String> categories;
  final ValueChanged<String?> onChanged;
  final String hint;

  const CategoryFilterDropdown({
    super.key,
    this.selectedCategory,
    required this.categories,
    required this.onChanged,
    this.hint = 'Select Category',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedCategory,
          hint: Text(hint),
          isExpanded: true,
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('All Categories'),
            ),
            ...categories.map((category) => DropdownMenuItem<String>(
              value: category,
              child: Text(category),
            )),
          ],
          onChanged: onChanged,
        ),
      ),
    );
  }
}

/// Export options widget
class ExportOptionsWidget extends StatelessWidget {
  final VoidCallback? onExportCSV;
  final VoidCallback? onExportPDF;
  final bool isLoading;

  const ExportOptionsWidget({
    super.key,
    this.onExportCSV,
    this.onExportPDF,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (onExportCSV != null)
          OutlinedButton.icon(
            onPressed: isLoading ? null : onExportCSV,
            icon: isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.file_download, size: 16),
            label: const Text('CSV'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        
        if (onExportCSV != null && onExportPDF != null)
          const SizedBox(width: 8),
        
        if (onExportPDF != null)
          OutlinedButton.icon(
            onPressed: isLoading ? null : onExportPDF,
            icon: isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.picture_as_pdf, size: 16),
            label: const Text('PDF'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
      ],
    );
  }
}

/// Enhanced filter bar widget
class EnhancedFilterBar extends StatelessWidget {
  final List<Widget> filters;
  final Widget? exportWidget;
  final VoidCallback? onClearFilters;

  const EnhancedFilterBar({
    super.key,
    required this.filters,
    this.exportWidget,
    this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, size: 20),
              const SizedBox(width: 8),
              Text(
                'Filters',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (exportWidget != null) ...[
                exportWidget!,
                const SizedBox(width: 8),
              ],
              if (onClearFilters != null)
                TextButton.icon(
                  onPressed: onClearFilters,
                  icon: const Icon(Icons.clear, size: 16),
                  label: const Text('Clear'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
            ],
          ),
          
          if (filters.isNotEmpty) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            Wrap(
              spacing: AppConstants.defaultPadding,
              runSpacing: AppConstants.smallPadding,
              children: filters,
            ),
          ],
        ],
      ),
    );
  }
}

/// Property option model
class PropertyOption {
  final String id;
  final String name;
  final String? type;

  const PropertyOption({
    required this.id,
    required this.name,
    this.type,
  });
}

/// Common maintenance categories
class MaintenanceCategories {
  static const List<String> categories = [
    'Electrical',
    'Plumbing',
    'HVAC',
    'Security',
    'Cleaning',
    'Landscaping',
    'Equipment',
    'Infrastructure',
    'Safety',
    'Other',
  ];
}

/// Common attendance locations
class AttendanceLocations {
  static const List<String> locations = [
    'Main Office',
    'Construction Site A',
    'Construction Site B',
    'Warehouse',
    'Remote Work',
    'Client Site',
    'Other',
  ];
}
