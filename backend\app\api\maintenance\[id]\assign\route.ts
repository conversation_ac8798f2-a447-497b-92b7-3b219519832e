import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const assignMaintenanceIssueSchema = Joi.object({
  assigned_to: Joi.string().uuid().required(),
  notes: Joi.string().optional(),
});

async function assignMaintenanceIssueHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(assignMaintenanceIssueSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { assigned_to, notes } = validation.data;

    // Verify issue exists
    const existingIssue = await prisma.maintenanceIssue.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!existingIssue) {
      return Response.json(
        createApiResponse(null, 'Maintenance issue not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Verify assignee exists
    const assignee = await prisma.user.findUnique({
      where: { id: assigned_to },
      select: {
        id: true,
        fullName: true,
        email: true,
      },
    });

    if (!assignee) {
      return Response.json(
        createApiResponse(null, 'Assignee not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Update issue with assignment
    const updatedIssue = await prisma.maintenanceIssue.update({
      where: { id },
      data: {
        assignedTo: assigned_to,
        status: 'IN_PROGRESS', // Auto-update status when assigned
        updatedAt: new Date(),
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        assignee: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    // Create assignment log entry
    await prisma.maintenanceAssignmentLog.create({
      data: {
        maintenanceIssueId: id,
        assignedTo: assigned_to,
        assignedBy: currentUser.id,
        notes: notes || `Assigned to ${assignee.fullName}`,
        assignedAt: new Date(),
      },
    });

    const transformedIssue = {
      id: updatedIssue.id,
      property: {
        id: updatedIssue.property.id,
        name: updatedIssue.property.name,
        type: updatedIssue.property.type.toLowerCase(),
      },
      title: updatedIssue.title,
      description: updatedIssue.description,
      priority: updatedIssue.priority.toLowerCase(),
      status: updatedIssue.status.toLowerCase(),
      service_type: updatedIssue.serviceType,
      department: updatedIssue.department,
      assignee: updatedIssue.assignee,
      due_date: updatedIssue.dueDate,
      created_at: updatedIssue.createdAt,
      updated_at: updatedIssue.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'Maintenance issue assigned successfully',
        issue: transformedIssue,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to assign maintenance issue');
  }
}

export const POST = requireAuth(assignMaintenanceIssueHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
