#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// <PERSON>ript to run role-based and permission tests
/// This script will:
/// 1. Check if backend server is running
/// 2. Start backend server if needed
/// 3. Run role-based screen tests
/// 4. Run dynamic permission tests
/// 5. Generate test report

Future<void> main(List<String> args) async {
  print('🧪 Starting SRSR Role-Based and Permission Tests');
  print('=' * 60);

  final bool autoStartBackend = args.contains('--start-backend');
  final bool keepBackendRunning = args.contains('--keep-backend');
  final bool runRoleTests = !args.contains('--skip-role-tests');
  final bool runPermissionTests = !args.contains('--skip-permission-tests');
  
  Process? backendProcess;
  final List<TestResult> testResults = [];
  
  try {
    // Check if backend is already running
    final isBackendRunning = await checkBackendHealth();
    
    if (!isBackendRunning) {
      if (autoStartBackend) {
        print('📡 Backend not running. Starting backend server...');
        backendProcess = await startBackendServer();
        
        // Wait for backend to be ready
        await waitForBackendReady();
      } else {
        print('❌ Backend server is not running!');
        print('');
        print('Please start the backend server first:');
        print('  cd backend');
        print('  npm run dev');
        print('');
        print('Or run this script with --start-backend flag:');
        print('  dart test/run_role_permission_tests.dart --start-backend');
        exit(1);
      }
    } else {
      print('✅ Backend server is already running');
    }

    // Run role-based screen tests
    if (runRoleTests) {
      print('\n🎭 Running Role-Based Screen Tests...');
      print('-' * 40);
      
      final roleTestResult = await runTestFile('test/role_based_screen_tests.dart');
      testResults.add(roleTestResult);
      
      if (roleTestResult.success) {
        print('✅ Role-based screen tests completed successfully');
      } else {
        print('❌ Role-based screen tests failed');
      }
    }

    // Run dynamic permission tests
    if (runPermissionTests) {
      print('\n🔐 Running Dynamic Permission Tests...');
      print('-' * 40);
      
      final permissionTestResult = await runTestFile('test/dynamic_permission_tests.dart');
      testResults.add(permissionTestResult);
      
      if (permissionTestResult.success) {
        print('✅ Dynamic permission tests completed successfully');
      } else {
        print('❌ Dynamic permission tests failed');
      }
    }

    // Generate test report
    await generateTestReport(testResults);

    // Determine overall result
    final allTestsPassed = testResults.every((result) => result.success);
    
    if (allTestsPassed) {
      print('\n🎉 All role and permission tests passed!');
      exit(0);
    } else {
      print('\n❌ Some tests failed. Check the report above.');
      exit(1);
    }

  } catch (e) {
    print('❌ Error running tests: $e');
    exit(1);
  } finally {
    // Clean up backend process if we started it
    if (backendProcess != null && !keepBackendRunning) {
      print('\n🛑 Stopping backend server...');
      backendProcess.kill();
      await backendProcess.exitCode;
      print('✅ Backend server stopped');
    }
  }
}

/// Check if backend server is healthy
Future<bool> checkBackendHealth() async {
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://192.168.1.3:3000/api'));
    request.headers.set('Content-Type', 'application/json');
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    client.close();
    
    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      return data['status'] == 'operational';
    }
    return false;
  } catch (e) {
    return false;
  }
}

/// Start the backend server
Future<Process> startBackendServer() async {
  final backendDir = Directory('../backend');
  if (!await backendDir.exists()) {
    throw Exception('Backend directory not found. Please ensure you are running this from the frontend directory.');
  }

  // Start the backend server
  final process = await Process.start(
    'npm',
    ['run', 'dev'],
    workingDirectory: backendDir.path,
    mode: ProcessStartMode.normal,
  );

  // Listen to output for debugging
  process.stdout.transform(utf8.decoder).listen((data) {
    print('[BACKEND] $data');
  });
  
  process.stderr.transform(utf8.decoder).listen((data) {
    print('[BACKEND ERROR] $data');
  });

  return process;
}

/// Wait for backend to be ready
Future<void> waitForBackendReady() async {
  print('⏳ Waiting for backend server to be ready...');
  
  for (int i = 0; i < 30; i++) {
    await Future.delayed(Duration(seconds: 2));
    
    if (await checkBackendHealth()) {
      print('✅ Backend server is ready!');
      return;
    }
    
    print('   Attempt ${i + 1}/30...');
  }
  
  throw Exception('Backend server did not become ready within 60 seconds');
}

/// Run a specific test file
Future<TestResult> runTestFile(String testFile) async {
  final stopwatch = Stopwatch()..start();
  
  try {
    final result = await Process.run(
      'flutter',
      ['test', testFile, '--reporter=expanded'],
      workingDirectory: '.',
    );

    stopwatch.stop();

    print(result.stdout);
    if (result.stderr.isNotEmpty) {
      print('STDERR: ${result.stderr}');
    }

    return TestResult(
      testFile: testFile,
      success: result.exitCode == 0,
      duration: stopwatch.elapsed,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
    );
  } catch (e) {
    stopwatch.stop();
    return TestResult(
      testFile: testFile,
      success: false,
      duration: stopwatch.elapsed,
      output: '',
      error: e.toString(),
    );
  }
}

/// Generate test report
Future<void> generateTestReport(List<TestResult> results) async {
  print('\n📊 Test Report');
  print('=' * 60);
  
  for (final result in results) {
    final status = result.success ? '✅ PASSED' : '❌ FAILED';
    final duration = '${result.duration.inMilliseconds}ms';
    
    print('${result.testFile}: $status ($duration)');
    
    if (!result.success && result.error.isNotEmpty) {
      print('   Error: ${result.error}');
    }
  }
  
  final totalTests = results.length;
  final passedTests = results.where((r) => r.success).length;
  final failedTests = totalTests - passedTests;
  final totalDuration = results.fold<Duration>(
    Duration.zero,
    (sum, result) => sum + result.duration,
  );
  
  print('\n📈 Summary:');
  print('   Total Tests: $totalTests');
  print('   Passed: $passedTests');
  print('   Failed: $failedTests');
  print('   Total Duration: ${totalDuration.inSeconds}s');
  
  // Save report to file
  final reportFile = File('test_report_${DateTime.now().millisecondsSinceEpoch}.txt');
  final reportContent = results.map((r) => 
    '${r.testFile}: ${r.success ? "PASSED" : "FAILED"} (${r.duration.inMilliseconds}ms)'
  ).join('\n');
  
  await reportFile.writeAsString(reportContent);
  print('   Report saved to: ${reportFile.path}');
}

class TestResult {
  final String testFile;
  final bool success;
  final Duration duration;
  final String output;
  final String error;

  TestResult({
    required this.testFile,
    required this.success,
    required this.duration,
    required this.output,
    required this.error,
  });
}
