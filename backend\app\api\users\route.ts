import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole, hashPassword } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders, getQueryParams, createPaginationResponse } from '@/lib/utils';
import Joi from 'joi';

const createUserSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  full_name: Joi.string().min(2).required(),
  phone: Joi.string().optional(),
  roles: Joi.array().items(Joi.string()).optional(),
});

const updateUserSchema = Joi.object({
  email: Joi.string().email().optional(),
  full_name: Joi.string().min(2).optional(),
  phone: Joi.string().optional(),
  is_active: Joi.boolean().optional(),
  roles: Joi.array().items(Joi.string()).optional(),
});

async function getUsersHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const queryParams = getQueryParams(request);
    const page = queryParams.page || 1;
    const limit = Math.min(queryParams.limit || 10, 100);
    const offset = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (queryParams.is_active !== undefined) {
      where.isActive = queryParams.is_active;
    }
    
    if (queryParams.search) {
      where.OR = [
        { fullName: { contains: queryParams.search, mode: 'insensitive' } },
        { email: { contains: queryParams.search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await prisma.user.count({ where });

    // Get users with roles
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: offset,
      take: limit,
    });

    // Transform users data
    const transformedUsers = users.map(user => ({
      id: user.id,
      email: user.email,
      full_name: user.fullName,
      phone: user.phone,
      is_active: user.isActive,
      roles: user.userRoles.map(ur => ({
        id: ur.role.id,
        name: ur.role.name,
        description: ur.role.description,
      })),
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    }));

    return Response.json(
      createPaginationResponse(transformedUsers, page, limit, total),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch users');
  }
}

async function createUserHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createUserSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { email, password, full_name, phone, roles } = validation.data;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return Response.json(
        createApiResponse(null, 'User with this email already exists', 'DUPLICATE_ENTRY'),
        { status: 409 }
      );
    }

    // Hash password
    const passwordHash = await hashPassword(password);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        fullName: full_name,
        phone,
        isActive: true,
      },
    });

    // Assign roles if provided
    if (roles && roles.length > 0) {
      // Verify roles exist
      const existingRoles = await prisma.role.findMany({
        where: {
          name: { in: roles },
        },
      });

      if (existingRoles.length !== roles.length) {
        // Some roles don't exist, but continue with valid ones
        console.warn('Some roles not found:', roles.filter(r => !existingRoles.find(er => er.name === r)));
      }

      // Create user role assignments
      const userRoleData = existingRoles.map(role => ({
        userId: user.id,
        roleId: role.id,
        assignedBy: currentUser.id,
      }));

      await prisma.userRole.createMany({
        data: userRoleData,
      });
    }

    // Get user with roles for response
    const userWithRoles = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'User created successfully',
        user: {
          id: userWithRoles!.id,
          email: userWithRoles!.email,
          full_name: userWithRoles!.fullName,
          phone: userWithRoles!.phone,
          is_active: userWithRoles!.isActive,
          roles: userWithRoles!.userRoles.map(ur => ur.role.name),
          created_at: userWithRoles!.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create user');
  }
}

export const GET = requireAuth(getUsersHandler);
export const POST = requireRole(['admin'])(createUserHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
