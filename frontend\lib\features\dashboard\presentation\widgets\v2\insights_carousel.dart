import 'package:flutter/material.dart';
import '../../../../../core/constants/app_constants.dart';

/// Insights carousel that displays rotating business insights and recommendations
class InsightsCarousel extends StatefulWidget {
  final List<String> insights;
  final Duration autoScrollDuration;
  final bool enableAutoScroll;

  const InsightsCarousel({
    super.key,
    required this.insights,
    this.autoScrollDuration = const Duration(seconds: 5),
    this.enableAutoScroll = true,
  });

  @override
  State<InsightsCarousel> createState() => _InsightsCarouselState();
}

class _InsightsCarouselState extends State<InsightsCarousel> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    if (widget.enableAutoScroll && widget.insights.isNotEmpty) {
      _startAutoScroll();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    Future.delayed(widget.autoScrollDuration, () {
      if (mounted && widget.insights.isNotEmpty) {
        final nextIndex = (_currentIndex + 1) % widget.insights.length;
        _pageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _startAutoScroll();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.insights.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: AppConstants.smallPadding),
        _buildCarousel(context),
        if (widget.insights.length > 1) ...[
          const SizedBox(height: AppConstants.smallPadding),
          _buildPageIndicator(context),
        ],
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.lightbulb,
            color: Colors.blue.shade600,
            size: 20,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: Text(
            'Insights & Recommendations',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          icon: Icon(
            Icons.refresh,
            color: Colors.blue.shade600,
          ),
          onPressed: _refreshInsights,
          tooltip: 'Refresh insights',
        ),
      ],
    );
  }

  Widget _buildCarousel(BuildContext context) {
    return SizedBox(
      height: 120,
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: widget.insights.length,
        itemBuilder: (context, index) {
          return _buildInsightCard(context, widget.insights[index], index);
        },
      ),
    );
  }

  Widget _buildInsightCard(BuildContext context, String insight, int index) {
    final insightType = _getInsightType(insight);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            insightType.color.withOpacity(0.1),
            insightType.color.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: insightType.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                insightType.icon,
                color: insightType.color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  insightType.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: insightType.color,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: insightType.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${index + 1}/${widget.insights.length}',
                  style: TextStyle(
                    fontSize: 10,
                    color: insightType.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Expanded(
            child: Text(
              insight,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade700,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'AI Generated',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade500,
                  fontStyle: FontStyle.italic,
                ),
              ),
              GestureDetector(
                onTap: () => _showInsightDetails(context, insight),
                child: Text(
                  'Learn More',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: insightType.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicator(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.insights.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: index == _currentIndex
                ? Colors.blue.shade600
                : Colors.grey.shade300,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.insights,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'No Insights Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          Text(
            'Insights will appear here as data is analyzed',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  InsightType _getInsightType(String insight) {
    final lowerInsight = insight.toLowerCase();
    
    if (lowerInsight.contains('cost') || lowerInsight.contains('save') || lowerInsight.contains('budget')) {
      return InsightType(
        title: 'Cost Optimization',
        icon: Icons.savings,
        color: Colors.green,
      );
    } else if (lowerInsight.contains('maintenance') || lowerInsight.contains('repair')) {
      return InsightType(
        title: 'Maintenance Alert',
        icon: Icons.build,
        color: Colors.orange,
      );
    } else if (lowerInsight.contains('efficiency') || lowerInsight.contains('performance')) {
      return InsightType(
        title: 'Performance Insight',
        icon: Icons.trending_up,
        color: Colors.blue,
      );
    } else if (lowerInsight.contains('security') || lowerInsight.contains('alert')) {
      return InsightType(
        title: 'Security Notice',
        icon: Icons.security,
        color: Colors.red,
      );
    } else {
      return InsightType(
        title: 'General Insight',
        icon: Icons.info,
        color: Colors.purple,
      );
    }
  }

  void _refreshInsights() {
    // Trigger insights refresh
    setState(() {
      _currentIndex = 0;
    });
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _showInsightDetails(BuildContext context, String insight) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insight Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(insight),
              const SizedBox(height: 16),
              const Text(
                'Recommendations:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• Review the affected systems'),
              const Text('• Consider implementing suggested changes'),
              const Text('• Monitor progress over the next week'),
              const Text('• Consult with relevant team members'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement action based on insight
            },
            child: const Text('Take Action'),
          ),
        ],
      ),
    );
  }
}

/// Insight type data model
class InsightType {
  final String title;
  final IconData icon;
  final Color color;

  const InsightType({
    required this.title,
    required this.icon,
    required this.color,
  });
}
