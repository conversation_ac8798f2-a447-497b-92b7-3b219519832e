import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Analytics tab - Device performance, energy usage, and insights
class AnalyticsTab extends StatefulWidget {
  const AnalyticsTab({super.key});

  @override
  State<AnalyticsTab> createState() => _AnalyticsTabState();
}

class _AnalyticsTabState extends State<AnalyticsTab> {
  String _selectedPeriod = '24h';
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildPeriodSelector(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildMetricsOverview(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildAnalyticsCharts(context),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector(BuildContext context) {
    final periods = ['24h', '7d', '30d', '1y'];
    
    return Row(
      children: [
        Text(
          'Analytics Period:',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        ...periods.map((period) => Padding(
          padding: const EdgeInsets.only(right: AppConstants.smallPadding),
          child: ChoiceChip(
            label: Text(period),
            selected: _selectedPeriod == period,
            onSelected: (selected) {
              if (selected) {
                setState(() => _selectedPeriod = period);
              }
            },
            selectedColor: Colors.teal.shade100,
          ),
        )),
      ],
    );
  }

  Widget _buildMetricsOverview(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            'Energy Usage',
            '2.4 kWh',
            '+12%',
            Icons.electrical_services,
            Colors.blue,
            true,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildMetricCard(
            'Efficiency',
            '87%',
            '+5%',
            Icons.trending_up,
            Colors.green,
            true,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildMetricCard(
            'Downtime',
            '0.2h',
            '-15%',
            Icons.timer_off,
            Colors.orange,
            false,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildMetricCard(
            'Cost',
            '\$45.20',
            '-8%',
            Icons.attach_money,
            Colors.purple,
            false,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String change,
    IconData icon,
    Color color,
    bool isPositive,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isPositive ? Icons.trending_up : Icons.trending_down,
                  size: 12,
                  color: isPositive ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 2),
                Text(
                  change,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: isPositive ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsCharts(BuildContext context) {
    return ListView(
      children: [
        _buildChartCard(
          'Energy Consumption',
          'Power usage over time',
          Icons.show_chart,
          Colors.blue,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        _buildChartCard(
          'Device Performance',
          'Efficiency and uptime metrics',
          Icons.speed,
          Colors.green,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        _buildChartCard(
          'Cost Analysis',
          'Operating costs breakdown',
          Icons.pie_chart,
          Colors.purple,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        _buildChartCard(
          'Predictive Maintenance',
          'Maintenance recommendations',
          Icons.build,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildChartCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.fullscreen),
                  onPressed: () => _showFullChart(title),
                  tooltip: 'View Full Chart',
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: color.withOpacity(0.3)),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.insert_chart,
                      size: 48,
                      color: color.withOpacity(0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Chart Placeholder',
                      style: TextStyle(
                        color: color.withOpacity(0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      'Real charts coming soon...',
                      style: TextStyle(
                        fontSize: 12,
                        color: color.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _exportData(title),
                    icon: const Icon(Icons.download, size: 16),
                    label: const Text('Export'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _shareChart(title),
                    icon: const Icon(Icons.share, size: 16),
                    label: const Text('Share'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _configureChart(title),
                    icon: const Icon(Icons.settings, size: 16),
                    label: const Text('Configure'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showFullChart(String chartTitle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(chartTitle),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.insert_chart,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'Full Chart View',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Interactive charts coming soon...',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportData(String chartTitle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Exporting $chartTitle data...')),
    );
  }

  void _shareChart(String chartTitle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Sharing $chartTitle...')),
    );
  }

  void _configureChart(String chartTitle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Configure $chartTitle'),
        content: const Text('Chart configuration options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
