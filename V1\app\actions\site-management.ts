"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns"
import { getSiteUuid } from "@/lib/site-mappings"

// Types
export type Site = {
  id: string
  name: string
  location: string
}

export type SiteMember = {
  id: string
  name: string
  mobile_number: string
  site_id: string
  role?: string
  duty_time?: string
}

export type AttendanceRecord = {
  id: string
  site_id: string
  worker_id: string
  worker_name: string
  worker_role: string
  date: string
  status: string
  hours_worked: number
  notes?: string
}

// Get all sites
export async function getSites() {
  const supabase = createClient()

  try {
    const { data, error } = await supabase.from("sites").select("*").order("name")

    if (error) {
      console.error("Error fetching sites:", error)
      return []
    }

    return data as Site[]
  } catch (error) {
    console.error("Error fetching sites:", error)
    return []
  }
}

// Get site by ID
export async function getSiteById(siteId: string) {
  const supabase = createClient()
  const uuid = getSiteUuid(siteId)

  try {
    const { data, error } = await supabase.from("sites").select("*").eq("id", uuid).single()

    if (error) {
      console.error("Error fetching site:", error)
      return null
    }

    return data as Site
  } catch (error) {
    console.error("Error fetching site:", error)
    return null
  }
}

// Get site members
export async function getSiteMembers(siteId: string) {
  const supabase = createClient()
  const uuid = getSiteUuid(siteId)

  try {
    const { data, error } = await supabase.from("site_members").select("*").eq("site_id", uuid).order("name")

    if (error) {
      console.error("Error fetching site members:", error)
      return []
    }

    return data as SiteMember[]
  } catch (error) {
    console.error("Error fetching site members:", error)
    return []
  }
}

// Add site member
export async function addSiteMember(member: Omit<SiteMember, "id">) {
  const supabase = createClient()

  try {
    // Convert the route parameter to a UUID
    const siteUuid = getSiteUuid(member.site_id)

    const memberToAdd = {
      ...member,
      site_id: siteUuid,
    }

    const { data, error } = await supabase.from("site_members").insert([memberToAdd]).select()

    if (error) {
      console.error("Error adding site member:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/sites/${member.site_id}`)
    return { success: true, data: data[0] }
  } catch (error) {
    console.error("Error adding site member:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Update site member
export async function updateSiteMember(member: SiteMember) {
  const supabase = createClient()

  try {
    // Convert the route parameter to a UUID if needed
    const siteUuid = getSiteUuid(member.site_id)

    const { data, error } = await supabase
      .from("site_members")
      .update({
        name: member.name,
        mobile_number: member.mobile_number,
        role: member.role,
        duty_time: member.duty_time,
        updated_at: new Date().toISOString(),
      })
      .eq("id", member.id)
      .select()

    if (error) {
      console.error("Error updating site member:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/sites/${member.site_id}`)
    return { success: true, data: data[0] }
  } catch (error) {
    console.error("Error updating site member:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Delete site member
export async function deleteSiteMember(memberId: string, siteId: string) {
  const supabase = createClient()

  try {
    const { error } = await supabase.from("site_members").delete().eq("id", memberId)

    if (error) {
      console.error("Error deleting site member:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/sites/${siteId}`)
    return { success: true }
  } catch (error) {
    console.error("Error deleting site member:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Submit attendance
export async function submitAttendance(records: Omit<AttendanceRecord, "id">[]) {
  const supabase = createClient()

  try {
    if (!records || records.length === 0) {
      return { success: false, error: "No attendance records provided" }
    }

    // Convert the route parameter to a UUID
    const recordsWithUuid = records.map((record) => ({
      ...record,
      site_id: getSiteUuid(record.site_id),
    }))

    const { data, error } = await supabase
      .from("site_attendance")
      .upsert(recordsWithUuid, { onConflict: "site_id,worker_id,date" })
      .select()

    if (error) {
      console.error("Error submitting attendance:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/sites/${records[0].site_id}`)
    return { success: true, data }
  } catch (error) {
    console.error("Error submitting attendance:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Check if attendance exists for a specific date
export async function checkAttendanceExists(siteId: string, date: string) {
  const supabase = createClient()
  const uuid = getSiteUuid(siteId)

  try {
    const { data, error } = await supabase
      .from("site_attendance")
      .select("id")
      .eq("site_id", uuid)
      .eq("date", date)
      .limit(1)

    if (error) {
      console.error("Error checking attendance:", error)
      return false
    }

    return data.length > 0
  } catch (error) {
    console.error("Error checking attendance:", error)
    return false
  }
}

// Get attendance records for a specific date
export async function getDailyAttendance(siteId: string, date: string) {
  const supabase = createClient()
  const uuid = getSiteUuid(siteId)

  try {
    const { data, error } = await supabase
      .from("site_attendance")
      .select("*")
      .eq("site_id", uuid)
      .eq("date", date)
      .order("worker_name")

    if (error) {
      console.error("Error fetching daily attendance:", error)
      return []
    }

    return data as AttendanceRecord[]
  } catch (error) {
    console.error("Error fetching daily attendance:", error)
    return []
  }
}

// Get attendance records for a specific week
export async function getWeeklyAttendance(siteId: string, date: Date) {
  const supabase = createClient()
  const uuid = getSiteUuid(siteId)

  try {
    const weekStart = format(startOfWeek(date), "yyyy-MM-dd")
    const weekEnd = format(endOfWeek(date), "yyyy-MM-dd")

    const { data, error } = await supabase
      .from("site_attendance")
      .select("*")
      .eq("site_id", uuid)
      .gte("date", weekStart)
      .lte("date", weekEnd)
      .order("date")

    if (error) {
      console.error("Error fetching weekly attendance:", error)
      return []
    }

    return data as AttendanceRecord[]
  } catch (error) {
    console.error("Error fetching weekly attendance:", error)
    return []
  }
}

// Get attendance records for a specific month
export async function getMonthlyAttendance(siteId: string, date: Date) {
  const supabase = createClient()
  const uuid = getSiteUuid(siteId)

  try {
    const monthStart = format(startOfMonth(date), "yyyy-MM-dd")
    const monthEnd = format(endOfMonth(date), "yyyy-MM-dd")

    const { data, error } = await supabase
      .from("site_attendance")
      .select("*")
      .eq("site_id", uuid)
      .gte("date", monthStart)
      .lte("date", monthEnd)
      .order("date")

    if (error) {
      console.error("Error fetching monthly attendance:", error)
      return []
    }

    return data as AttendanceRecord[]
  } catch (error) {
    console.error("Error fetching monthly attendance:", error)
    return []
  }
}
