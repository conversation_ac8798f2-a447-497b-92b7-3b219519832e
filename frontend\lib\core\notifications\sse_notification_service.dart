import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../constants/api_constants.dart';
import '../storage/storage_service.dart';
import 'notification_service.dart';

class SSENotificationService {
  static final SSENotificationService _instance = SSENotificationService._internal();
  factory SSENotificationService() => _instance;
  SSENotificationService._internal();

  StreamController<ServerNotification>? _notificationController;
  http.Client? _client;
  bool _isConnected = false;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 5);

  Stream<ServerNotification> get notificationStream {
    _notificationController ??= StreamController<ServerNotification>.broadcast();
    return _notificationController!.stream;
  }

  bool get isConnected => _isConnected;

  /// Initialize SSE connection
  Future<void> initialize() async {
    if (_isConnected) {
      debugPrint('SSE already connected');
      return;
    }

    final token = await StorageService.getToken();
    if (token == null) {
      debugPrint('No auth token available for SSE connection');
      return;
    }

    await _connect(token);
  }

  /// Connect to SSE endpoint
  Future<void> _connect(String token) async {
    try {
      _client = http.Client();

      final request = http.Request(
        'GET',
        Uri.parse('${ApiConstants.baseUrl}/api/notifications/sse'),
      );

      request.headers.addAll({
        'Authorization': 'Bearer $token',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
      });

      debugPrint('Connecting to SSE endpoint...');
      final response = await _client!.send(request);

      if (response.statusCode == 200) {
        _isConnected = true;
        _reconnectAttempts = 0;
        debugPrint('SSE connection established');

        // Listen to the stream
        response.stream
            .transform(utf8.decoder)
            .transform(const LineSplitter())
            .listen(
              _handleSSEData,
              onError: _handleSSEError,
              onDone: _handleSSEDone,
            );
      } else {
        debugPrint('SSE connection failed with status: ${response.statusCode}');
        _handleConnectionFailure();
      }
    } catch (e) {
      debugPrint('SSE connection error: $e');
      _handleConnectionFailure();
    }
  }

  /// Handle incoming SSE data
  void _handleSSEData(String data) {
    if (data.isEmpty) return;

    try {
      if (data.startsWith('data: ')) {
        final jsonData = data.substring(6); // Remove 'data: ' prefix
        final Map<String, dynamic> eventData = json.decode(jsonData);

        final notification = ServerNotification.fromJson(eventData);

        // Add to notification controller
        _notificationController?.add(notification);

        // Show local notification if it's not a heartbeat
        if (notification.type != 'heartbeat' && notification.type != 'connection') {
          _showLocalNotification(notification);
        }

        debugPrint('SSE notification received: ${notification.type}');
      }
    } catch (e) {
      debugPrint('Error parsing SSE data: $e');
    }
  }

  /// Handle SSE errors
  void _handleSSEError(dynamic error) {
    debugPrint('SSE stream error: $error');
    _isConnected = false;
    _scheduleReconnect();
  }

  /// Handle SSE connection done
  void _handleSSEDone() {
    debugPrint('SSE stream closed');
    _isConnected = false;
    _scheduleReconnect();
  }

  /// Handle connection failure
  void _handleConnectionFailure() {
    _isConnected = false;
    _client?.close();
    _client = null;
    _scheduleReconnect();
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= maxReconnectAttempts) {
      debugPrint('Max reconnection attempts reached. Stopping SSE reconnection.');
      return;
    }

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () async {
      _reconnectAttempts++;
      debugPrint('SSE reconnection attempt $_reconnectAttempts/$maxReconnectAttempts');

      final token = await StorageService.getToken();
      if (token != null) {
        await _connect(token);
      }
    });
  }

  /// Show local notification
  Future<void> _showLocalNotification(ServerNotification notification) async {
    try {
      final localNotification = AppNotification(
        id: notification.id,
        title: notification.title,
        body: notification.message,
        type: _mapServerNotificationType(notification.type),
        priority: _mapServerNotificationPriority(notification.priority),
        timestamp: notification.timestamp,
        data: notification.data,
      );

      await NotificationService().showNotification(localNotification);
    } catch (e) {
      debugPrint('Error showing local notification: $e');
    }
  }

  /// Map server notification type to local notification type
  NotificationType _mapServerNotificationType(String serverType) {
    switch (serverType) {
      case 'threshold_alert':
        return NotificationType.thresholdAlert;
      case 'alert_resolution':
        return NotificationType.alertResolution;
      case 'system_update':
        return NotificationType.systemUpdate;
      case 'maintenance_reminder':
        return NotificationType.maintenance;
      default:
        return NotificationType.systemUpdate;
    }
  }

  /// Map server notification priority to local notification priority
  NotificationPriority _mapServerNotificationPriority(String serverPriority) {
    switch (serverPriority) {
      case 'critical':
        return NotificationPriority.critical;
      case 'high':
        return NotificationPriority.high;
      case 'normal':
        return NotificationPriority.normal;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.normal;
    }
  }

  /// Disconnect from SSE
  Future<void> disconnect() async {
    debugPrint('Disconnecting SSE...');
    _isConnected = false;
    _reconnectTimer?.cancel();
    _client?.close();
    _client = null;
    _notificationController?.close();
    _notificationController = null;
  }

  /// Reconnect SSE (useful after login)
  Future<void> reconnect() async {
    await disconnect();
    await initialize();
  }
}

/// Server notification model
class ServerNotification {
  final String id;
  final String type;
  final String title;
  final String message;
  final Map<String, dynamic>? data;
  final String priority;
  final List<String>? targetUsers;
  final List<String>? targetRoles;
  final String? propertyId;
  final DateTime timestamp;

  const ServerNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    this.data,
    required this.priority,
    this.targetUsers,
    this.targetRoles,
    this.propertyId,
    required this.timestamp,
  });

  factory ServerNotification.fromJson(Map<String, dynamic> json) {
    return ServerNotification(
      id: json['id'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
      priority: json['priority'] as String,
      targetUsers: (json['target_users'] as List<dynamic>?)?.cast<String>(),
      targetRoles: (json['target_roles'] as List<dynamic>?)?.cast<String>(),
      propertyId: json['property_id'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'message': message,
      'data': data,
      'priority': priority,
      'target_users': targetUsers,
      'target_roles': targetRoles,
      'property_id': propertyId,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
