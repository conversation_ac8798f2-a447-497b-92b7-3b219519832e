import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../shared/models/api_response.dart';
import '../../../shared/models/attendance.dart';

part 'attendance_api_service.g.dart';

@RestApi()
abstract class AttendanceApiService {
  factory AttendanceApiService(Dio dio) = _AttendanceApiService;

  // Property Attendance (replaces Site Attendance)
  @GET('/api/attendance')
  Future<ApiResponse<List<AttendanceRecord>>> getPropertyAttendance({
    @Query('property_id') String? propertyId,
    @Query('date') String? date,
    @Query('start_date') String? startDate,
    @Query('end_date') String? endDate,
    @Query('user_id') String? userId,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @POST('/api/attendance/sites')
  Future<ApiResponse<List<AttendanceRecord>>> submitSiteAttendance(@Body() SubmitSiteAttendanceRequest request);

  @GET('/api/attendance/sites/{id}')
  Future<ApiResponse<AttendanceRecord>> getSiteAttendanceById(@Path('id') String id);

  @PUT('/api/attendance/sites/{id}')
  Future<ApiResponse<AttendanceRecord>> updateSiteAttendance(
    @Path('id') String id,
    @Body() UpdateSiteAttendanceRequest request,
  );

  @DELETE('/api/attendance/sites/{id}')
  Future<ApiResponse<VoidResponse>> deleteSiteAttendance(@Path('id') String id);

  // Property Attendance (unified for all property types including offices)
  @GET('/api/attendance')
  Future<ApiResponse<List<AttendanceRecord>>> getAttendanceByProperty({
    @Query('property_id') String? propertyId,
    @Query('date') String? date,
    @Query('start_date') String? startDate,
    @Query('end_date') String? endDate,
    @Query('user_id') String? userId,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @POST('/api/attendance')
  Future<ApiResponse<List<AttendanceRecord>>> submitAttendance(@Body() SubmitPropertyAttendanceRequest request);

  @GET('/api/attendance/{id}')
  Future<ApiResponse<AttendanceRecord>> getAttendanceById(@Path('id') String id);

  @PUT('/api/attendance/{id}')
  Future<ApiResponse<AttendanceRecord>> updateAttendance(
    @Path('id') String id,
    @Body() UpdatePropertyAttendanceRequest request,
  );

  @DELETE('/api/attendance/{id}')
  Future<ApiResponse<VoidResponse>> deleteAttendance(@Path('id') String id);

  // Attendance Reports
  @GET('/api/attendance/reports/summary')
  Future<ApiResponse<AttendanceSummary>> getAttendanceSummary({
    @Query('start_date') String? startDate,
    @Query('end_date') String? endDate,
    @Query('property_id') String? propertyId,
  });

  @GET('/api/attendance/reports/user/{userId}')
  Future<ApiResponse<UserAttendanceReport>> getUserAttendanceReport(
    @Path('userId') String userId,
    @Query('start_date') String? startDate,
    @Query('end_date') String? endDate,
  );
}

// Unified Property Attendance Request Models
@JsonSerializable()
class SubmitPropertyAttendanceRequest {
  @JsonKey(name: 'property_id')
  final String propertyId;
  final String date;
  final List<AttendanceEntry> attendance;

  const SubmitPropertyAttendanceRequest({
    required this.propertyId,
    required this.date,
    required this.attendance,
  });

  factory SubmitPropertyAttendanceRequest.fromJson(Map<String, dynamic> json) => _$SubmitPropertyAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() {
    final json = _$SubmitPropertyAttendanceRequestToJson(this);
    print('📋 ATTENDANCE API: SubmitPropertyAttendanceRequest.toJson() = $json');
    return json;
  }
}

@JsonSerializable()
class UpdatePropertyAttendanceRequest {
  final String? status;
  @JsonKey(name: 'check_in_time')
  final String? checkInTime;
  @JsonKey(name: 'check_out_time')
  final String? checkOutTime;
  final String? notes;

  const UpdatePropertyAttendanceRequest({
    this.status,
    this.checkInTime,
    this.checkOutTime,
    this.notes,
  });

  factory UpdatePropertyAttendanceRequest.fromJson(Map<String, dynamic> json) => _$UpdatePropertyAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdatePropertyAttendanceRequestToJson(this);
}

// Legacy Site/Office Request Models (for backward compatibility)
@JsonSerializable()
class SubmitSiteAttendanceRequest {
  @JsonKey(name: 'property_id')
  final String propertyId;
  final String date;
  final List<AttendanceEntry> attendance;

  const SubmitSiteAttendanceRequest({
    required this.propertyId,
    required this.date,
    required this.attendance,
  });

  factory SubmitSiteAttendanceRequest.fromJson(Map<String, dynamic> json) => _$SubmitSiteAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$SubmitSiteAttendanceRequestToJson(this);
}

@JsonSerializable()
class SubmitOfficeAttendanceRequest {
  @JsonKey(name: 'office_id')
  final String officeId;
  final String date;
  final List<AttendanceEntry> attendance;

  const SubmitOfficeAttendanceRequest({
    required this.officeId,
    required this.date,
    required this.attendance,
  });

  factory SubmitOfficeAttendanceRequest.fromJson(Map<String, dynamic> json) => _$SubmitOfficeAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$SubmitOfficeAttendanceRequestToJson(this);
}

@JsonSerializable()
class UpdateSiteAttendanceRequest {
  final String? status;
  @JsonKey(name: 'check_in_time')
  final String? checkInTime;
  @JsonKey(name: 'check_out_time')
  final String? checkOutTime;
  final String? notes;

  const UpdateSiteAttendanceRequest({
    this.status,
    this.checkInTime,
    this.checkOutTime,
    this.notes,
  });

  factory UpdateSiteAttendanceRequest.fromJson(Map<String, dynamic> json) => _$UpdateSiteAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateSiteAttendanceRequestToJson(this);
}

@JsonSerializable()
class UpdateOfficeAttendanceRequest {
  final String? status;
  @JsonKey(name: 'check_in_time')
  final String? checkInTime;
  @JsonKey(name: 'check_out_time')
  final String? checkOutTime;
  final String? notes;

  const UpdateOfficeAttendanceRequest({
    this.status,
    this.checkInTime,
    this.checkOutTime,
    this.notes,
  });

  factory UpdateOfficeAttendanceRequest.fromJson(Map<String, dynamic> json) => _$UpdateOfficeAttendanceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateOfficeAttendanceRequestToJson(this);
}

@JsonSerializable()
class AttendanceEntry {
  @JsonKey(name: 'user_id')
  final String userId;
  final String status;
  @JsonKey(name: 'check_in_time')
  final String? checkInTime;
  @JsonKey(name: 'check_out_time')
  final String? checkOutTime;
  final String? notes;

  const AttendanceEntry({
    required this.userId,
    required this.status,
    this.checkInTime,
    this.checkOutTime,
    this.notes,
  });

  factory AttendanceEntry.fromJson(Map<String, dynamic> json) => _$AttendanceEntryFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceEntryToJson(this);
}

@JsonSerializable()
class AttendanceSummary {
  @JsonKey(name: 'total_employees')
  final int totalEmployees;
  @JsonKey(name: 'present_count')
  final int presentCount;
  @JsonKey(name: 'absent_count')
  final int absentCount;
  @JsonKey(name: 'late_count')
  final int lateCount;
  @JsonKey(name: 'attendance_percentage')
  final double attendancePercentage;
  @JsonKey(name: 'start_date')
  final String startDate;
  @JsonKey(name: 'end_date')
  final String endDate;

  const AttendanceSummary({
    required this.totalEmployees,
    required this.presentCount,
    required this.absentCount,
    required this.lateCount,
    required this.attendancePercentage,
    required this.startDate,
    required this.endDate,
  });

  factory AttendanceSummary.fromJson(Map<String, dynamic> json) => _$AttendanceSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceSummaryToJson(this);
}

@JsonSerializable()
class UserAttendanceReport {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'user_name')
  final String userName;
  @JsonKey(name: 'total_days')
  final int totalDays;
  @JsonKey(name: 'present_days')
  final int presentDays;
  @JsonKey(name: 'absent_days')
  final int absentDays;
  @JsonKey(name: 'late_days')
  final int lateDays;
  @JsonKey(name: 'attendance_percentage')
  final double attendancePercentage;
  final List<AttendanceRecord> records;

  const UserAttendanceReport({
    required this.userId,
    required this.userName,
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.attendancePercentage,
    required this.records,
  });

  factory UserAttendanceReport.fromJson(Map<String, dynamic> json) => _$UserAttendanceReportFromJson(json);
  Map<String, dynamic> toJson() => _$UserAttendanceReportToJson(this);
}

@JsonSerializable()
class VoidResponse {
  final String message;

  const VoidResponse({required this.message});

  factory VoidResponse.fromJson(Map<String, dynamic> json) => _$VoidResponseFromJson(json);
  Map<String, dynamic> toJson() => _$VoidResponseToJson(this);
}
