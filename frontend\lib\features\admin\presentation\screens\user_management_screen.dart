import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/user.dart';
import '../providers/user_management_providers.dart';
import '../widgets/user_card.dart';
import '../widgets/user_approval_dialog.dart';

class UserManagementScreen extends ConsumerStatefulWidget {
  const UserManagementScreen({super.key});

  @override
  ConsumerState<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends ConsumerState<UserManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoleBasedAppBar(
        title: 'User Management',
        showRoleIndicator: true,
      ),
      body: PermissionWidget(
        permission: 'users.read',
        fallback: _buildNoPermissionView(),
        child: Column(
          children: [
            // Search Bar
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search users...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() {
                              _searchQuery = '';
                            });
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),

            // Tab Bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'All Users'),
                Tab(text: 'Pending Approval'),
                Tab(text: 'Active'),
                Tab(text: 'Inactive'),
              ],
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAllUsersTab(),
                  _buildPendingUsersTab(),
                  _buildActiveUsersTab(),
                  _buildInactiveUsersTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to manage users.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAllUsersTab() {
    final usersAsync = ref.watch(allUsersProvider);
    return _buildUsersList(usersAsync);
  }

  Widget _buildPendingUsersTab() {
    final usersAsync = ref.watch(pendingUsersProvider);
    return _buildUsersList(usersAsync, showApprovalActions: true);
  }

  Widget _buildActiveUsersTab() {
    final usersAsync = ref.watch(activeUsersProvider);
    return _buildUsersList(usersAsync);
  }

  Widget _buildInactiveUsersTab() {
    final usersAsync = ref.watch(inactiveUsersProvider);
    return _buildUsersList(usersAsync);
  }

  Widget _buildUsersList(AsyncValue<List<User>> usersAsync, {bool showApprovalActions = false}) {
    return usersAsync.when(
      data: (users) {
        final filteredUsers = _filterUsers(users);

        if (filteredUsers.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(allUsersProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: filteredUsers.length + 1, // Add 1 for bottom padding
            itemBuilder: (context, index) {
              // Add bottom padding as last item
              if (index == filteredUsers.length) {
                return const SizedBox(height: 80);
              }

              final user = filteredUsers[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: UserCard(
                  user: user,
                  onApprove: () => _approveUser(user),
                  onReject: () => _rejectUser(user),
                  onEdit: () => _editUser(user),
                  onDeactivate: () => _deactivateUser(user),
                  onActivate: () => _activateUser(user),
                  onDelete: () => _deleteUser(user),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _searchQuery.isNotEmpty ? 'No users found' : 'No users yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try adjusting your search'
                : 'Users will appear here when they register',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load users',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(allUsersProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  List<User> _filterUsers(List<User> users) {
    if (_searchQuery.isEmpty) return users;

    final lowercaseQuery = _searchQuery.toLowerCase();
    return users.where((user) {
      return user.fullName.toLowerCase().contains(lowercaseQuery) ||
             user.email.toLowerCase().contains(lowercaseQuery) ||
             (user.username?.toLowerCase().contains(lowercaseQuery) ?? false) ||
             (user.primaryRole?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  void _approveUser(User user) {
    showDialog(
      context: context,
      builder: (context) => UserApprovalDialog(
        user: user,
        onApprove: (role, comments) async {
          final currentContext = context;
          try {
            await ref.read(userManagementProvider.notifier).approveUser(user);
            if (mounted) {
              AppUtils.showSuccessSnackBar(currentContext, 'User approved successfully');
            }
          } catch (e) {
            if (mounted) {
              AppUtils.showErrorSnackBar(currentContext, 'Failed to approve user: $e');
            }
          }
        },
      ),
    );
  }

  void _rejectUser(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject User'),
        content: Text('Are you sure you want to reject ${user.fullName}\'s registration?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final currentContext = context;
              try {
                await ref.read(userManagementProvider.notifier).deleteUser(user.id);
                if (mounted) {
                  AppUtils.showSuccessSnackBar(currentContext, 'User registration rejected');
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showErrorSnackBar(currentContext, 'Failed to reject user: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _editUser(User user) {
    // TODO: Implement edit user dialog
    AppUtils.showInfoSnackBar(context, 'Edit user feature coming soon');
  }

  void _deactivateUser(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deactivate User'),
        content: Text('Are you sure you want to deactivate ${user.fullName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final currentContext = context;
              try {
                await ref.read(userManagementProvider.notifier).deactivateUser(user.id);
                if (mounted) {
                  AppUtils.showSuccessSnackBar(currentContext, 'User deactivated');
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showErrorSnackBar(currentContext, 'Failed to deactivate user: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.orange),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );
  }

  void _activateUser(User user) {
    ref.read(userManagementProvider.notifier).activateUser(user.id);
    AppUtils.showSuccessSnackBar(context, 'User activated');
  }

  void _deleteUser(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to permanently delete ${user.fullName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final currentContext = context;
              try {
                await ref.read(userManagementProvider.notifier).deleteUser(user.id);
                if (mounted) {
                  AppUtils.showSuccessSnackBar(currentContext, 'User deleted');
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showErrorSnackBar(currentContext, 'Failed to delete user: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
