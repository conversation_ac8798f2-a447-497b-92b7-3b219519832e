import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"

export default function SitesPage() {
  // Hardcoded sites as requested
  const sites = [
    { id: "gandipet-1", name: "Gandipet 1" },
    { id: "gandipet-2", name: "Gandipet 2" },
    { id: "gandipet-3-4", name: "Gandipet 3 & 4" },
    { id: "bachupally", name: "<PERSON><PERSON><PERSON>" },
  ]

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office">Office</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office/sites">Sites</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <h1 className="mb-8 text-3xl font-bold">Construction Sites</h1>

        <div className="grid gap-6 md:grid-cols-2">
          {sites.map((site) => (
            <Card key={site.id} className="h-full transition-all hover:shadow-md">
              <CardHeader>
                <CardTitle>{site.name}</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-end gap-2">
                <Button variant="outline" asChild>
                  <Link href={`/dashboard/office/sites/${site.id}`}>Manage</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href={`/dashboard/office/sites/${site.id}?tab=attendance`}>Attendance</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
