import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { notFound } from "next/navigation"
import { officeNames } from "@/lib/site-mappings"
import { getOfficeMembers } from "@/app/actions/office-management"
import { OfficeMembersManagement } from "@/components/office-members-management"
import { OfficeAttendanceForm } from "@/components/office-attendance-form"
import { OfficeAttendanceReports } from "@/components/office-attendance-reports"

// This is a server component that gets office data
export default async function OfficeDetailsPage({ params }: { params: { office: string } }) {
  const { office } = params

  // Get office name from mappings
  const officeName = officeNames[office]

  if (!officeName) {
    notFound()
  }

  // Get office members
  const members = await getOfficeMembers(office).catch(() => [])

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office">Office</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office/office">Office Locations</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/dashboard/office/office/${office}`}>{officeName}</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <h1 className="mb-8 text-3xl font-bold">{officeName}</h1>

        <Tabs defaultValue="submit-attendance">
          <TabsList className="mb-6 grid w-full grid-cols-3">
            <TabsTrigger value="submit-attendance">Submit Attendance</TabsTrigger>
            <TabsTrigger value="add-remove-members">Add/Remove Members</TabsTrigger>
            <TabsTrigger value="download-reports">Download Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="submit-attendance">
            <Card>
              <CardHeader>
                <CardTitle>Submit Attendance</CardTitle>
                <CardDescription>Record daily attendance for {officeName}</CardDescription>
              </CardHeader>
              <CardContent>
                <OfficeAttendanceForm officeId={office} officeName={officeName} members={members} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="add-remove-members">
            <Card>
              <CardHeader>
                <CardTitle>Manage Team Members</CardTitle>
                <CardDescription>Add or remove team members for {officeName}</CardDescription>
              </CardHeader>
              <CardContent>
                <OfficeMembersManagement officeId={office} officeName={officeName} initialMembers={members} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="download-reports">
            <Card>
              <CardHeader>
                <CardTitle>Download Reports</CardTitle>
                <CardDescription>Generate and download various reports for {officeName}</CardDescription>
              </CardHeader>
              <CardContent>
                <OfficeAttendanceReports officeId={office} officeName={officeName} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
