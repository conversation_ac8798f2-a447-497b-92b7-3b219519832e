import 'package:flutter/material.dart';

class MaintenanceFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onSelected;
  final Color? color;
  final IconData? icon;

  const MaintenanceFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onSelected,
    this.color,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.primaryColor;

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : effectiveColor,
            ),
            const SizedBox(width: 4),
          ],
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.white : effectiveColor,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: (_) => onSelected(),
      backgroundColor: Colors.transparent,
      selectedColor: effectiveColor,
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: effectiveColor,
        width: 1.5,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }
}

class MaintenancePriorityChip extends StatelessWidget {
  final String priority;
  final bool isCompact;

  const MaintenancePriorityChip({
    super.key,
    required this.priority,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getPriorityConfig(priority);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? 6 : 8,
        vertical: isCompact ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: config.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: config.color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            config.icon,
            size: isCompact ? 12 : 16,
            color: config.color,
          ),
          const SizedBox(width: 4),
          Text(
            config.label,
            style: TextStyle(
              color: config.color,
              fontSize: isCompact ? 10 : 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  _PriorityConfig _getPriorityConfig(String priority) {
    switch (priority.toLowerCase()) {
      case 'critical':
        return _PriorityConfig(
          label: 'Critical',
          color: Colors.red,
          icon: Icons.priority_high,
        );
      case 'high':
        return _PriorityConfig(
          label: 'High',
          color: Colors.orange,
          icon: Icons.keyboard_arrow_up,
        );
      case 'medium':
        return _PriorityConfig(
          label: 'Medium',
          color: Colors.yellow.shade700,
          icon: Icons.remove,
        );
      case 'low':
        return _PriorityConfig(
          label: 'Low',
          color: Colors.green,
          icon: Icons.keyboard_arrow_down,
        );
      default:
        return _PriorityConfig(
          label: 'Unknown',
          color: Colors.grey,
          icon: Icons.help_outline,
        );
    }
  }
}

class MaintenanceStatusChip extends StatelessWidget {
  final String status;
  final bool isCompact;
  final VoidCallback? onTap;

  const MaintenanceStatusChip({
    super.key,
    required this.status,
    this.isCompact = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getStatusConfig(status);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isCompact ? 6 : 8,
          vertical: isCompact ? 2 : 4,
        ),
        decoration: BoxDecoration(
          color: config.color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: config.color, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: isCompact ? 6 : 8,
              height: isCompact ? 6 : 8,
              decoration: BoxDecoration(
                color: config.color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              config.label,
              style: TextStyle(
                color: config.color,
                fontSize: isCompact ? 10 : 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (onTap != null) ...[
              const SizedBox(width: 4),
              Icon(
                Icons.keyboard_arrow_down,
                size: isCompact ? 12 : 16,
                color: config.color,
              ),
            ],
          ],
        ),
      ),
    );
  }

  _StatusConfig _getStatusConfig(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return _StatusConfig(
          label: 'Open',
          color: Colors.orange,
        );
      case 'in_progress':
        return _StatusConfig(
          label: 'In Progress',
          color: Colors.blue,
        );
      case 'resolved':
        return _StatusConfig(
          label: 'Resolved',
          color: Colors.green,
        );
      case 'closed':
        return _StatusConfig(
          label: 'Closed',
          color: Colors.grey,
        );
      case 'cancelled':
        return _StatusConfig(
          label: 'Cancelled',
          color: Colors.red.shade300,
        );
      default:
        return _StatusConfig(
          label: 'Unknown',
          color: Colors.grey,
        );
    }
  }
}

class MaintenanceCategoryChip extends StatelessWidget {
  final String category;
  final bool isCompact;

  const MaintenanceCategoryChip({
    super.key,
    required this.category,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getCategoryConfig(category);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isCompact ? 6 : 8,
        vertical: isCompact ? 2 : 4,
      ),
      decoration: BoxDecoration(
        color: config.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: config.color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            config.icon,
            size: isCompact ? 12 : 16,
            color: config.color,
          ),
          const SizedBox(width: 4),
          Text(
            config.label,
            style: TextStyle(
              color: config.color,
              fontSize: isCompact ? 10 : 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  _CategoryConfig _getCategoryConfig(String category) {
    switch (category.toLowerCase()) {
      case 'electrical':
        return _CategoryConfig(
          label: 'Electrical',
          color: Colors.amber,
          icon: Icons.electrical_services,
        );
      case 'plumbing':
        return _CategoryConfig(
          label: 'Plumbing',
          color: Colors.blue,
          icon: Icons.plumbing,
        );
      case 'hvac':
        return _CategoryConfig(
          label: 'HVAC',
          color: Colors.cyan,
          icon: Icons.air,
        );
      case 'security':
        return _CategoryConfig(
          label: 'Security',
          color: Colors.red,
          icon: Icons.security,
        );
      case 'cleaning':
        return _CategoryConfig(
          label: 'Cleaning',
          color: Colors.green,
          icon: Icons.cleaning_services,
        );
      case 'general':
        return _CategoryConfig(
          label: 'General',
          color: Colors.grey,
          icon: Icons.build,
        );
      default:
        return _CategoryConfig(
          label: category,
          color: Colors.grey,
          icon: Icons.category,
        );
    }
  }
}

class _PriorityConfig {
  final String label;
  final Color color;
  final IconData icon;

  const _PriorityConfig({
    required this.label,
    required this.color,
    required this.icon,
  });
}

class _StatusConfig {
  final String label;
  final Color color;

  const _StatusConfig({
    required this.label,
    required this.color,
  });
}

class _CategoryConfig {
  final String label;
  final Color color;
  final IconData icon;

  const _CategoryConfig({
    required this.label,
    required this.color,
    required this.icon,
  });
}
