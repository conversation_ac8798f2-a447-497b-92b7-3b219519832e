import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';
import '../../shared/models/maintenance_issue.dart' as models;

enum EscalationLevel { level1, level2, level3, level4 }

class EscalationConfig {
  final String id;
  final String priority;
  final EscalationLevel level;
  final int daysToEscalate;
  final String escalateTo;
  final String description;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EscalationConfig({
    required this.id,
    required this.priority,
    required this.level,
    required this.daysToEscalate,
    required this.escalateTo,
    required this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EscalationConfig.fromJson(Map<String, dynamic> json) {
    return EscalationConfig(
      id: json['id'] as String,
      priority: json['priority'] as String,
      level: EscalationLevel.values[json['level'] as int],
      daysToEscalate: json['days_to_escalate'] as int,
      escalateTo: json['escalate_to'] as String,
      description: json['description'] as String,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'priority': priority,
      'level': level.index,
      'days_to_escalate': daysToEscalate,
      'escalate_to': escalateTo,
      'description': description,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class EscalationEntry {
  final String id;
  final String issueId;
  final EscalationLevel level;
  final String escalatedTo;
  final DateTime escalatedAt;
  final bool isResolved;
  final DateTime? resolvedAt;
  final String? resolutionNotes;

  const EscalationEntry({
    required this.id,
    required this.issueId,
    required this.level,
    required this.escalatedTo,
    required this.escalatedAt,
    required this.isResolved,
    this.resolvedAt,
    this.resolutionNotes,
  });

  factory EscalationEntry.fromJson(Map<String, dynamic> json) {
    return EscalationEntry(
      id: json['id'] as String,
      issueId: json['issue_id'] as String,
      level: EscalationLevel.values[json['level'] as int],
      escalatedTo: json['escalated_to'] as String,
      escalatedAt: DateTime.parse(json['escalated_at'] as String),
      isResolved: json['is_resolved'] as bool,
      resolvedAt: json['resolved_at'] != null ? DateTime.parse(json['resolved_at'] as String) : null,
      resolutionNotes: json['resolution_notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'issue_id': issueId,
      'level': level.index,
      'escalated_to': escalatedTo,
      'escalated_at': escalatedAt.toIso8601String(),
      'is_resolved': isResolved,
      'resolved_at': resolvedAt?.toIso8601String(),
      'resolution_notes': resolutionNotes,
    };
  }
}

class EscalationManager {
  static const String _configStorageKey = 'escalation_configs';
  static const String _entriesStorageKey = 'escalation_entries';
  static EscalationManager? _instance;

  EscalationManager._();

  static EscalationManager get instance {
    _instance ??= EscalationManager._();
    return _instance!;
  }

  List<EscalationConfig> _configs = [];
  List<EscalationEntry> _entries = [];
  bool _isInitialized = false;

  /// Initialize with default configurations
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadConfigs();
    await _loadEntries();

    if (_configs.isEmpty) {
      _configs = _getDefaultConfigs();
      await _saveConfigs();
    }

    _isInitialized = true;
  }

  /// Get escalation configurations for a priority
  List<EscalationConfig> getConfigsForPriority(String priority) {
    return _configs.where((config) =>
      config.priority.toLowerCase() == priority.toLowerCase() && config.isActive
    ).toList()..sort((a, b) => a.level.index.compareTo(b.level.index));
  }

  /// Setup escalation for a new issue
  Future<void> setupEscalation(String issueId, String priority) async {
    try {
      final configs = getConfigsForPriority(priority);

      if (configs.isEmpty) {
        debugPrint('No escalation config found for priority: $priority');
        return;
      }

      // Create initial escalation entry (Level 1)
      final firstConfig = configs.first;
      final entry = EscalationEntry(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        issueId: issueId,
        level: firstConfig.level,
        escalatedTo: firstConfig.escalateTo,
        escalatedAt: DateTime.now(),
        isResolved: false,
      );

      _entries.add(entry);
      await _saveEntries();

      debugPrint('Escalation setup for issue $issueId to ${firstConfig.escalateTo}');
    } catch (e) {
      debugPrint('Error setting up escalation: $e');
    }
  }

  /// Check for issues that need escalation
  Future<List<EscalationEntry>> checkEscalations(List<models.MaintenanceIssue> openIssues) async {
    final newEscalations = <EscalationEntry>[];

    for (final issue in openIssues) {
      try {
        final configs = getConfigsForPriority(issue.priority);
        if (configs.isEmpty) continue;

        final existingEscalations = _entries.where((e) =>
          e.issueId == issue.id && !e.isResolved
        ).toList();

        final currentLevel = existingEscalations.isNotEmpty
          ? existingEscalations.map((e) => e.level.index).reduce((a, b) => a > b ? a : b)
          : -1;

        final daysSinceCreated = DateTime.now().difference(issue.createdAt).inDays;

        // Check if we need to escalate to the next level
        for (final config in configs) {
          if (config.level.index > currentLevel && daysSinceCreated >= config.daysToEscalate) {
            final entry = EscalationEntry(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              issueId: issue.id,
              level: config.level,
              escalatedTo: config.escalateTo,
              escalatedAt: DateTime.now(),
              isResolved: false,
            );

            _entries.add(entry);
            newEscalations.add(entry);

            debugPrint('Escalated issue ${issue.id} to level ${config.level.index + 1}');
            break; // Only escalate one level at a time
          }
        }
      } catch (e) {
        debugPrint('Error checking escalation for issue ${issue.id}: $e');
      }
    }

    if (newEscalations.isNotEmpty) {
      await _saveEntries();
    }

    return newEscalations;
  }

  /// Resolve escalation when issue is resolved
  Future<void> resolveEscalation(String issueId, String resolutionNotes) async {
    final escalations = _entries.where((e) => e.issueId == issueId && !e.isResolved).toList();

    for (final escalation in escalations) {
      final index = _entries.indexOf(escalation);
      if (index >= 0) {
        _entries[index] = EscalationEntry(
          id: escalation.id,
          issueId: escalation.issueId,
          level: escalation.level,
          escalatedTo: escalation.escalatedTo,
          escalatedAt: escalation.escalatedAt,
          isResolved: true,
          resolvedAt: DateTime.now(),
          resolutionNotes: resolutionNotes,
        );
      }
    }

    await _saveEntries();
  }

  /// Get escalation history for an issue
  List<EscalationEntry> getEscalationHistory(String issueId) {
    return _entries.where((e) => e.issueId == issueId).toList()
      ..sort((a, b) => a.escalatedAt.compareTo(b.escalatedAt));
  }

  /// Load configurations from storage
  Future<void> _loadConfigs() async {
    try {
      final data = StorageService.getString(_configStorageKey);
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _configs = jsonList.map((json) => EscalationConfig.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error loading escalation configs: $e');
      _configs = [];
    }
  }

  /// Save configurations to storage
  Future<void> _saveConfigs() async {
    try {
      final jsonList = _configs.map((c) => c.toJson()).toList();
      await StorageService.setString(_configStorageKey, jsonEncode(jsonList));
    } catch (e) {
      debugPrint('Error saving escalation configs: $e');
    }
  }

  /// Load entries from storage
  Future<void> _loadEntries() async {
    try {
      final data = StorageService.getString(_entriesStorageKey);
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _entries = jsonList.map((json) => EscalationEntry.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error loading escalation entries: $e');
      _entries = [];
    }
  }

  /// Save entries to storage
  Future<void> _saveEntries() async {
    try {
      final jsonList = _entries.map((e) => e.toJson()).toList();
      await StorageService.setString(_entriesStorageKey, jsonEncode(jsonList));
    } catch (e) {
      debugPrint('Error saving escalation entries: $e');
    }
  }

  /// Get default escalation configurations
  List<EscalationConfig> _getDefaultConfigs() {
    final now = DateTime.now();

    return [
      // Critical priority escalations
      EscalationConfig(
        id: 'critical_level1',
        priority: 'critical',
        level: EscalationLevel.level1,
        daysToEscalate: 0, // Immediate
        escalateTo: 'manager',
        description: 'Immediate escalation to manager for critical issues',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      EscalationConfig(
        id: 'critical_level2',
        priority: 'critical',
        level: EscalationLevel.level2,
        daysToEscalate: 1,
        escalateTo: 'admin',
        description: 'Escalate to admin if not resolved in 1 day',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      // High priority escalations
      EscalationConfig(
        id: 'high_level1',
        priority: 'high',
        level: EscalationLevel.level1,
        daysToEscalate: 1,
        escalateTo: 'manager',
        description: 'Escalate to manager after 1 day',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      EscalationConfig(
        id: 'high_level2',
        priority: 'high',
        level: EscalationLevel.level2,
        daysToEscalate: 3,
        escalateTo: 'admin',
        description: 'Escalate to admin after 3 days',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      // Medium priority escalations
      EscalationConfig(
        id: 'medium_level1',
        priority: 'medium',
        level: EscalationLevel.level1,
        daysToEscalate: 3,
        escalateTo: 'manager',
        description: 'Escalate to manager after 3 days',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      EscalationConfig(
        id: 'medium_level2',
        priority: 'medium',
        level: EscalationLevel.level2,
        daysToEscalate: 7,
        escalateTo: 'admin',
        description: 'Escalate to admin after 7 days',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}

// Supporting data class
class EscalationIssueData {
  final String id;
  final String priority;
  final DateTime createdAt;

  const EscalationIssueData({
    required this.id,
    required this.priority,
    required this.createdAt,
  });
}
