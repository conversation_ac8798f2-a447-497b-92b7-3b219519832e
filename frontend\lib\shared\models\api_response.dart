import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final String? code;

  const ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.code,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  // Alias for error for compatibility
  String? get message => error;

  @override
  String toString() {
    return 'ApiResponse(success: $success, data: $data, error: $error, code: $code)';
  }
}

@JsonSerializable()
class Pagination {
  final int page;
  final int limit;
  final int total;
  final int pages;
  @JsonKey(name: 'has_next')
  final bool hasNext;
  @JsonKey(name: 'has_prev')
  final bool hasPrev;

  const Pagination({
    required this.page,
    required this.limit,
    required this.total,
    required this.pages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) => _$PaginationFromJson(json);
  Map<String, dynamic> toJson() => _$PaginationToJson(this);

  @override
  String toString() {
    return 'Pagination(page: $page, limit: $limit, total: $total, pages: $pages, hasNext: $hasNext, hasPrev: $hasPrev)';
  }
}

@JsonSerializable()
class VoidResponse {
  final String? message;

  const VoidResponse({this.message});

  factory VoidResponse.fromJson(Map<String, dynamic> json) => _$VoidResponseFromJson(json);
  Map<String, dynamic> toJson() => _$VoidResponseToJson(this);

  @override
  String toString() {
    return 'VoidResponse(message: $message)';
  }
}

@JsonSerializable(genericArgumentFactories: true)
class PaginationResponse<T> {
  final bool success;
  final List<T> data;
  final Pagination pagination;

  const PaginationResponse({
    required this.success,
    required this.data,
    required this.pagination,
  });

  factory PaginationResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginationResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginationResponseToJson(this, toJsonT);

  @override
  String toString() {
    return 'PaginationResponse(success: $success, data: $data, pagination: $pagination)';
  }
}
