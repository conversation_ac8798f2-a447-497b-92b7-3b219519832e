"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import type { PropertyStatus } from "@/app/actions/dashboard-status"
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Droplet,
  Gauge,
  Lightbulb,
  Tv,
  Wifi,
  Shield,
  PenToolIcon as Tool,
  Users,
} from "lucide-react"
import Link from "next/link"
import { formatMetricValue } from "@/lib/metric-utils"

interface PropertyStatusListProps {
  statuses: PropertyStatus[]
  type: "all" | "home" | "office"
}

export function PropertyStatusList({ statuses, type }: PropertyStatusListProps) {
  const [filter, setFilter] = useState<"all" | "warning" | "critical" | "healthy">("all")
  const [expandedProperties, setExpandedProperties] = useState<Record<string, boolean>>({})

  // Filter properties by type
  const filteredByType = type === "all" ? statuses : statuses.filter((property) => property.type === type)

  // Filter properties by status
  const filteredProperties = filteredByType.filter((property) => {
    if (filter === "all") return true
    if (filter === "healthy") return property.overallStatus === "green"
    if (filter === "warning") return property.overallStatus === "orange"
    if (filter === "critical") return property.overallStatus === "red"
    return true
  })

  const toggleExpand = (propertyId: string) => {
    setExpandedProperties((prev) => ({
      ...prev,
      [propertyId]: !prev[propertyId],
    }))
  }

  const getStatusIcon = (status: "green" | "orange" | "red") => {
    switch (status) {
      case "green":
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case "orange":
        return <AlertTriangle className="h-5 w-5 text-orange-500" />
      case "red":
        return <AlertCircle className="h-5 w-5 text-red-500" />
    }
  }

  const getFunctionalAreaIcon = (area: string) => {
    switch (area) {
      case "electricity":
        return <Lightbulb className="h-5 w-5" />
      case "water":
        return <Droplet className="h-5 w-5" />
      case "security":
        return <Shield className="h-5 w-5" />
      case "internet":
        return <Wifi className="h-5 w-5" />
      case "ott":
        return <Tv className="h-5 w-5" />
      case "maintenance":
        return <Tool className="h-5 w-5" />
      case "attendance":
        return <Users className="h-5 w-5" />
      default:
        return <Gauge className="h-5 w-5" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-2">
        <Badge
          variant={filter === "all" ? "default" : "outline"}
          className="cursor-pointer"
          onClick={() => setFilter("all")}
        >
          All
        </Badge>
        <Badge
          variant={filter === "healthy" ? "default" : "outline"}
          className="cursor-pointer bg-green-100 text-green-800 hover:bg-green-200"
          onClick={() => setFilter("healthy")}
        >
          <CheckCircle className="mr-1 h-3 w-3" /> Healthy
        </Badge>
        <Badge
          variant={filter === "warning" ? "default" : "outline"}
          className="cursor-pointer bg-orange-100 text-orange-800 hover:bg-orange-200"
          onClick={() => setFilter("warning")}
        >
          <AlertTriangle className="mr-1 h-3 w-3" /> Warning
        </Badge>
        <Badge
          variant={filter === "critical" ? "default" : "outline"}
          className="cursor-pointer bg-red-100 text-red-800 hover:bg-red-200"
          onClick={() => setFilter("critical")}
        >
          <AlertCircle className="mr-1 h-3 w-3" /> Critical
        </Badge>
      </div>

      {filteredProperties.length === 0 ? (
        <div className="text-center p-8 border rounded-lg bg-gray-50">
          <p className="text-muted-foreground">No properties match the selected filters.</p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredProperties.map((property) => (
            <Collapsible
              key={property.id}
              open={expandedProperties[property.id] || false}
              onOpenChange={() => toggleExpand(property.id)}
              className="border rounded-lg overflow-hidden"
            >
              <Card className="border-0 shadow-none">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(property.overallStatus)}
                      <CardTitle className="text-lg">{property.name}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {property.type}
                      </Badge>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-1">
                          {expandedProperties[property.id] ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </CollapsibleTrigger>
                    </div>
                  </div>
                </CardHeader>
              </Card>

              <CollapsibleContent>
                <CardContent className="pt-0 pb-4">
                  {/* Functional Areas Grid */}
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {Object.entries(property.functionalAreas).map(([area, data]) => (
                      <div
                        key={area}
                        className={`flex flex-col items-center justify-center p-2 rounded-md ${
                          data.status === "green"
                            ? "bg-green-50"
                            : data.status === "orange"
                              ? "bg-orange-50"
                              : "bg-red-50"
                        }`}
                      >
                        <div className="flex items-center gap-1">
                          {getFunctionalAreaIcon(area)}
                          {getStatusIcon(data.status)}
                        </div>
                        <span className="text-xs font-medium capitalize mt-1">{area}</span>
                        {data.issueCount > 0 && (
                          <Badge variant="outline" className="mt-1 text-xs">
                            {data.issueCount} issue{data.issueCount !== 1 ? "s" : ""}
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Detailed Tabs */}
                  <Tabs defaultValue="electricity" className="w-full">
                    <TabsList className="mb-4 flex flex-wrap h-auto">
                      {Object.entries(property.functionalAreas).map(([area, data]) => (
                        <TabsTrigger key={area} value={area} className="flex items-center gap-1 m-1">
                          {getFunctionalAreaIcon(area)}
                          <span className="capitalize">{area}</span>
                          {data.status !== "green" && getStatusIcon(data.status)}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                    {Object.entries(property.functionalAreas).map(([area, data]) => (
                      <TabsContent key={area} value={area}>
                        <div className="space-y-4">
                          <h4 className="font-medium capitalize flex items-center gap-2">
                            {getFunctionalAreaIcon(area)} {area} Status
                          </h4>
                          <div className="grid gap-2">
                            {data.metrics.map((metric) => (
                              <div
                                key={metric.name}
                                className={`p-3 rounded-md flex justify-between items-center ${
                                  metric.status === "green"
                                    ? "bg-green-50"
                                    : metric.status === "orange"
                                      ? "bg-orange-50"
                                      : "bg-red-50"
                                }`}
                              >
                                <div>
                                  <p className="font-medium">{metric.name.replace(/_/g, " ")}</p>
                                  <p className="text-sm text-muted-foreground">
                                    Last checked: {new Date(data.lastChecked).toLocaleString()}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="font-bold">
                                    {metric.displayValue || formatMetricValue(metric.value, metric.unit)}
                                  </span>
                                  {getStatusIcon(metric.status)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>
                    ))}
                  </Tabs>

                  {/* View Details Button */}
                  <div className="mt-4 pt-4 border-t">
                    <Link href={`/dashboard/home/<USER>
                      <Button variant="outline" size="sm" className="w-full">
                        View Details
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      )}
    </div>
  )
}
