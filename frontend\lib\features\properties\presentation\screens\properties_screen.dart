import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/property.dart';
import '../providers/properties_providers.dart';
import '../widgets/property_card.dart';
import '../widgets/property_filter_chip.dart';
import '../widgets/property_form_dialog.dart';

class PropertiesScreen extends ConsumerStatefulWidget {
  const PropertiesScreen({super.key});

  @override
  ConsumerState<PropertiesScreen> createState() => _PropertiesScreenState();
}

class _PropertiesScreenState extends ConsumerState<PropertiesScreen> {
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertiesAsync = ref.watch(propertiesProvider);

    return Scaffold(
      appBar: RoleBasedAppBar(
        title: 'Select Property',
        showRoleIndicator: true,
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['properties.create'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddPropertyDialog(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(propertiesProvider),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search properties...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      PropertyFilterChip(
                        label: 'All',
                        isSelected: _selectedFilter == 'all',
                        onSelected: () => setState(() => _selectedFilter = 'all'),
                      ),
                      const SizedBox(width: 8),
                      PropertyFilterChip(
                        label: 'Residential',
                        isSelected: _selectedFilter == 'residential',
                        onSelected: () => setState(() => _selectedFilter = 'residential'),
                      ),
                      const SizedBox(width: 8),
                      PropertyFilterChip(
                        label: 'Office',
                        isSelected: _selectedFilter == 'office',
                        onSelected: () => setState(() => _selectedFilter = 'office'),
                      ),
                      const SizedBox(width: 8),
                      PropertyFilterChip(
                        label: 'Active',
                        isSelected: _selectedFilter == 'active',
                        onSelected: () => setState(() => _selectedFilter = 'active'),
                      ),
                      const SizedBox(width: 8),
                      PropertyFilterChip(
                        label: 'Inactive',
                        isSelected: _selectedFilter == 'inactive',
                        onSelected: () => setState(() => _selectedFilter = 'inactive'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Properties List
          Expanded(
            child: propertiesAsync.when(
              data: (properties) {
                final filteredProperties = _filterProperties(properties);

                if (filteredProperties.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(propertiesProvider);
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredProperties.length,
                    itemBuilder: (context, index) {
                      final property = filteredProperties[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                        child: PropertyCard(
                          property: property,
                          onTap: () => _navigateToPropertyDetails(property),
                          onEdit: () => _showEditPropertyDialog(property),
                          onDelete: () => _showDeleteConfirmation(property),
                        ),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
      floatingActionButton: RoleBasedFAB(
        requiredPermissions: const ['properties.create'],
        onPressed: () => _showAddPropertyDialog(),
        tooltip: 'Add Property',
        child: const Icon(Icons.add),
      ),
    );
  }

  List<Property> _filterProperties(List<Property> properties) {
    var filtered = properties;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((property) {
        return property.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               property.type.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (property.address?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply category filter
    switch (_selectedFilter) {
      case 'residential':
        filtered = filtered.where((p) => p.isResidential).toList();
        break;
      case 'office':
        filtered = filtered.where((p) => p.isOffice).toList();
        break;
      case 'active':
        filtered = filtered.where((p) => p.isActive).toList();
        break;
      case 'inactive':
        filtered = filtered.where((p) => !p.isActive).toList();
        break;
    }

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all'
                ? 'No properties found'
                : 'No properties yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all'
                ? 'Try adjusting your search or filters'
                : 'Add your first property to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          if (_searchQuery.isEmpty && _selectedFilter == 'all')
            ElevatedButton.icon(
              onPressed: () => _showAddPropertyDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add Property'),
            ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load properties',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(propertiesProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _navigateToPropertyDetails(Property property) {
    // Navigate to property hub instead of property details for new navigation hierarchy
    context.push('/property/${property.id}/hub');
  }

  void _showAddPropertyDialog() {
    showDialog(
      context: context,
      builder: (context) => PropertyFormDialog(
        onSuccess: () {
          ref.invalidate(propertiesProvider);
        },
      ),
    );
  }

  void _showEditPropertyDialog(Property property) {
    showDialog(
      context: context,
      builder: (context) => PropertyFormDialog(
        property: property,
        onSuccess: () {
          ref.invalidate(propertiesProvider);
        },
      ),
    );
  }

  void _showDeleteConfirmation(Property property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Property'),
        content: Text('Are you sure you want to delete "${property.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteProperty(property);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteProperty(Property property) async {
    try {
      await ref.read(propertiesNotifierProvider.notifier).deleteProperty(property.id);
      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'Property "${property.name}" deleted successfully');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to delete property: $e');
      }
    }
  }
}


