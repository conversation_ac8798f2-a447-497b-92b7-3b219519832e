"use client"

import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"
import { SiteAttendanceForm } from "@/components/site-attendance-form"
import { SiteAttendanceReports } from "@/components/site-attendance-reports"
import { SiteMembersManagement } from "@/components/site-members-management"

// Mock data for initial site members
const mockSiteMembers = {
  "gandipet-1": [
    { id: "g1-001", name: "<PERSON><PERSON>", mobile: "+91 98765 43210", role: "<PERSON>", dutyTime: "09:00 - 18:00" },
    { id: "g1-002", name: "<PERSON><PERSON>", mobile: "+91 87654 32109", role: "Helper", dutyTime: "09:00 - 18:00" },
    { id: "g1-003", name: "<PERSON><PERSON><PERSON>", mobile: "+91 76543 21098", role: "<PERSON>", dutyTime: "09:00 - 18:00" },
    { id: "g1-004", name: "<PERSON>esh <PERSON>", mobile: "+91 65432 10987", role: "Electrician", dutyTime: "09:00 - 18:00" },
    { id: "g1-005", name: "Dinesh Patel", mobile: "+91 54321 09876", role: "Plumber", dutyTime: "09:00 - 18:00" },
  ],
  "gandipet-2": [
    { id: "g2-001", name: "Vikram Reddy", mobile: "+91 98765 67890", role: "Supervisor", dutyTime: "09:00 - 18:00" },
    { id: "g2-002", name: "Prakash Verma", mobile: "+91 87654 56789", role: "Mason", dutyTime: "09:00 - 18:00" },
    { id: "g2-003", name: "Sanjay Kumar", mobile: "+91 76543 45678", role: "Helper", dutyTime: "09:00 - 18:00" },
    { id: "g2-004", name: "Mohan Lal", mobile: "+91 65432 34567", role: "Security Guard", dutyTime: "06:00 - 18:00" },
    { id: "g2-005", name: "Ravi Kishan", mobile: "+91 54321 23456", role: "Gardener", dutyTime: "08:00 - 17:00" },
  ],
  "gandipet-3-4": [
    { id: "g34-001", name: "Ajay Singh", mobile: "+91 98765 12345", role: "Supervisor", dutyTime: "09:00 - 18:00" },
    { id: "g34-002", name: "Vijay Sharma", mobile: "+91 87654 23456", role: "Mason", dutyTime: "09:00 - 18:00" },
    { id: "g34-003", name: "Sanjay Patel", mobile: "+91 76543 34567", role: "Carpenter", dutyTime: "09:00 - 18:00" },
    { id: "g34-004", name: "Rahul Verma", mobile: "+91 65432 45678", role: "Electrician", dutyTime: "09:00 - 18:00" },
    { id: "g34-005", name: "Deepak Kumar", mobile: "+91 54321 56789", role: "Plumber", dutyTime: "09:00 - 18:00" },
    { id: "g34-006", name: "Gopal Rao", mobile: "+91 43210 67890", role: "Security Guard", dutyTime: "18:00 - 06:00" },
  ],
  bachupally: [
    { id: "b-001", name: "Rakesh Mishra", mobile: "+91 98765 98765", role: "Supervisor", dutyTime: "09:00 - 18:00" },
    { id: "b-002", name: "Sunil Kumar", mobile: "+91 87654 87654", role: "Mason", dutyTime: "09:00 - 18:00" },
    { id: "b-003", name: "Anil Sharma", mobile: "+91 76543 76543", role: "Helper", dutyTime: "09:00 - 18:00" },
    { id: "b-004", name: "Manoj Singh", mobile: "+91 65432 65432", role: "Carpenter", dutyTime: "09:00 - 18:00" },
    { id: "b-005", name: "Kamal Patel", mobile: "+91 54321 54321", role: "Painter", dutyTime: "09:00 - 18:00" },
    { id: "b-006", name: "Santosh Reddy", mobile: "+91 43210 43210", role: "Gardener", dutyTime: "08:00 - 17:00" },
  ],
}

export default function SiteDetailsPage() {
  const params = useParams()
  const siteId = params.site as string

  const siteNames = {
    "gandipet-1": "Gandipet 1",
    "gandipet-2": "Gandipet 2",
    "gandipet-3-4": "Gandipet 3 & 4",
    bachupally: "Bachupally",
  }

  const siteName = siteNames[siteId as keyof typeof siteNames] || "Site"
  // Fix the members state to use the correct type
  const [members, setMembers] = useState<any[]>([])

  // Update the useEffect to properly set the initial members
  useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    const siteMembersData = mockSiteMembers[siteId as keyof typeof mockSiteMembers] || []
    setMembers(siteMembersData)
  }, [siteId])

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/office">Office</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/office/sites">Sites</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/office/sites/${siteId}`}>{siteName}</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <h1 className="mb-8 text-3xl font-bold">{siteName} Site</h1>

        <Tabs defaultValue="submit-attendance" className="w-full">
          <TabsList className="mb-6 grid w-full grid-cols-3">
            <TabsTrigger value="submit-attendance">Submit Attendance</TabsTrigger>
            <TabsTrigger value="download-reports">Download Reports</TabsTrigger>
            <TabsTrigger value="add-remove-members">Add/Remove Members</TabsTrigger>
          </TabsList>

          <TabsContent value="submit-attendance">
            <Card>
              <CardContent className="pt-6">
                {/* Pass the members state to the SiteAttendanceForm component */}
                <SiteAttendanceForm siteId={siteId} siteName={siteName} members={members} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="download-reports">
            <Card>
              <CardContent className="pt-6">
                <SiteAttendanceReports siteId={siteId} siteName={siteName} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="add-remove-members">
            <Card>
              <CardContent className="pt-6">
                {/* Pass the setMembers function to the SiteMembersManagement component */}
                <SiteMembersManagement
                  siteId={siteId}
                  siteName={siteName}
                  initialMembers={members}
                  setMembers={setMembers}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
