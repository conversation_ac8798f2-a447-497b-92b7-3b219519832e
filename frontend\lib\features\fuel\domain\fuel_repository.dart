import '../../../shared/models/generator_fuel.dart';

abstract class FuelRepository {
  Future<List<GeneratorFuelLog>> getFuelLogsByProperty(String propertyId);
  Future<GeneratorFuelLog> getFuelLogById(String id);
  Future<GeneratorFuelLog> createFuelLog(GeneratorFuelLog log);
  Future<GeneratorFuelLog> updateFuelLog(GeneratorFuelLog log);
  Future<void> deleteFuelLog(String logId);
  Future<List<GeneratorFuelLog>> getAllFuelLogs();
  Future<void> recordFuelReading(
    String propertyId,
    double fuelLevel, {
    double? consumptionRate,
    String? notes,
    DateTime? recordedAt,
  });

  // Additional methods expected by providers
  Future<List<GeneratorFuelLog>> getFuelLogsByDateRange(DateTime startDate, DateTime endDate);
  Future<List<GeneratorFuelLog>> getFuelLogsByPropertyAndDateRange(String propertyId, DateTime startDate, DateTime endDate);
}
