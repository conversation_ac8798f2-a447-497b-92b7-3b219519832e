import { NextRequest } from 'next/server';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

export interface PaginationResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export function createApiResponse<T>(
  data?: T,
  error?: string,
  code?: string
): ApiResponse<T> {
  return {
    success: !error,
    ...(data && { data }),
    ...(error && { error }),
    ...(code && { code }),
  };
}

export function createPaginationResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number
): PaginationResponse<T> {
  const pages = Math.ceil(total / limit);

  return {
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      pages,
      has_next: page < pages,
      has_prev: page > 1,
    },
  };
}

export function getQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const params: Record<string, any> = {};

  searchParams.forEach((value, key) => {
    // Handle numeric values
    if (!isNaN(Number(value))) {
      params[key] = Number(value);
    } else if (value === 'true' || value === 'false') {
      // Handle boolean values
      params[key] = value === 'true';
    } else {
      params[key] = value;
    }
  });

  return params;
}

export async function getRequestBody(request: NextRequest) {
  try {
    return await request.json();
  } catch (error) {
    return null;
  }
}

export function handleError(error: any, defaultMessage = 'Internal server error', context?: string) {
  console.error(`API Error${context ? ` (${context})` : ''}:`, error);

  // Rate limiting errors (from V1 patterns)
  if (isRateLimitError(error)) {
    return Response.json(
      createApiResponse(null, 'Rate limit exceeded. Please try again in a moment.', 'RATE_LIMIT_EXCEEDED'),
      {
        status: 429,
        headers: {
          ...corsHeaders(),
          'Retry-After': '60', // Suggest retry after 60 seconds
        }
      }
    );
  }

  // JSON parsing errors (often related to rate limiting)
  if (isJsonParsingError(error)) {
    return Response.json(
      createApiResponse(null, 'Request parsing failed. Please try again.', 'PARSING_ERROR'),
      { status: 400, headers: corsHeaders() }
    );
  }

  // Database constraint errors
  if (error.code === 'P2002') {
    const field = error.meta?.target?.[0] || 'field';
    return Response.json(
      createApiResponse(null, `${field} already exists`, 'DUPLICATE_ENTRY'),
      { status: 409, headers: corsHeaders() }
    );
  }

  if (error.code === 'P2025') {
    return Response.json(
      createApiResponse(null, 'Resource not found', 'NOT_FOUND'),
      { status: 404, headers: corsHeaders() }
    );
  }

  // Database connection errors
  if (error.code === 'P1001' || error.code === 'P1008') {
    return Response.json(
      createApiResponse(null, 'Database connection failed. Please try again.', 'DATABASE_ERROR'),
      { status: 503, headers: corsHeaders() }
    );
  }

  // Validation errors
  if (error.name === 'ValidationError' || error.isJoi) {
    return Response.json(
      createApiResponse(null, error.message || 'Validation failed', 'VALIDATION_ERROR'),
      { status: 400, headers: corsHeaders() }
    );
  }

  // Network/timeout errors
  if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
    return Response.json(
      createApiResponse(null, 'Network error. Please try again.', 'NETWORK_ERROR'),
      { status: 503, headers: corsHeaders() }
    );
  }

  return Response.json(
    createApiResponse(null, defaultMessage, 'INTERNAL_ERROR'),
    { status: 500, headers: corsHeaders() }
  );
}

// Helper functions for error detection (from V1 patterns)
function isRateLimitError(error: any): boolean {
  const message = (error?.message || '').toLowerCase();
  return (
    message.includes('too many requests') ||
    message.includes('rate limit') ||
    message.includes('429') ||
    error?.response?.status === 429
  );
}

function isJsonParsingError(error: any): boolean {
  const message = (error?.message || '').toLowerCase();
  return (
    error instanceof SyntaxError ||
    message.includes('unexpected token') ||
    message.includes('json') ||
    message.includes('syntaxerror')
  );
}

export function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
}

// Date utilities
export function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

export function formatDateTime(date: Date): string {
  return date.toISOString();
}

export function parseDate(dateString: string): Date {
  return new Date(dateString);
}
