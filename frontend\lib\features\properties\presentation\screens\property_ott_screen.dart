import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/property.dart';
import '../../../../shared/models/ott_service.dart';
import '../providers/properties_providers.dart';
import '../widgets/ott_service_card.dart';
import '../widgets/ott_dues_summary_card.dart';

class PropertyOttScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const PropertyOttScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertyOttScreen> createState() => _PropertyOttScreenState();
}

class _PropertyOttScreenState extends ConsumerState<PropertyOttScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertyAsync = ref.watch(propertyByIdProvider(widget.propertyId));

    return propertyAsync.when(
      data: (property) => _buildOttScreen(context, property),
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading property: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(propertyByIdProvider(widget.propertyId)),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOttScreen(BuildContext context, Property property) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('OTT Services'),
            Text(
              property.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.white70,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['ott.create'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddOttServiceDialog(property),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshData(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Active', icon: Icon(Icons.play_circle, size: 20)),
            Tab(text: 'Dues', icon: Icon(Icons.payment, size: 20)),
            Tab(text: 'History', icon: Icon(Icons.history, size: 20)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildActiveServicesTab(property),
          _buildDuesTab(property),
          _buildHistoryTab(property),
        ],
      ),
    );
  }

  Widget _buildActiveServicesTab(Property property) {
    return RefreshIndicator(
      onRefresh: () async => _refreshData(),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Summary Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Service Summary',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Row(
                      children: [
                        Expanded(
                          child: _buildSummaryItem(
                            'Active Services',
                            '8',
                            Colors.green,
                            Icons.check_circle,
                          ),
                        ),
                        Expanded(
                          child: _buildSummaryItem(
                            'Total Services',
                            '10',
                            Colors.blue,
                            Icons.tv,
                          ),
                        ),
                        Expanded(
                          child: _buildSummaryItem(
                            'Monthly Cost',
                            '₹3,500',
                            Colors.orange,
                            Icons.currency_rupee,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Services List
            Text(
              'Active Services',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding / 2),
            
            // Mock OTT Services
            _buildOttServiceCard('Netflix', 'Premium Plan', '₹649/month', 'Active', Colors.red),
            _buildOttServiceCard('Amazon Prime', 'Annual Plan', '₹1,499/year', 'Active', Colors.blue),
            _buildOttServiceCard('Disney+ Hotstar', 'Super Plan', '₹899/year', 'Active', Colors.orange),
            _buildOttServiceCard('YouTube Premium', 'Family Plan', '₹189/month', 'Active', Colors.red),
            _buildOttServiceCard('Spotify', 'Premium Plan', '₹119/month', 'Active', Colors.green),
            _buildOttServiceCard('SonyLIV', 'Premium Plan', '₹699/year', 'Expired', Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildDuesTab(Property property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dues Summary
          OttDuesSummaryCard(
            totalDues: 2500.0,
            overdueCount: 2,
            upcomingCount: 3,
            onPayAll: () => _handlePayAllDues(),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Overdue Services
          Text(
            'Overdue Payments',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding / 2),
          _buildDueCard('Netflix', '₹649', '5 days overdue', Colors.red, true),
          _buildDueCard('Spotify', '₹119', '2 days overdue', Colors.red, true),
          
          const SizedBox(height: AppConstants.defaultPadding),

          // Upcoming Payments
          Text(
            'Upcoming Payments',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding / 2),
          _buildDueCard('Amazon Prime', '₹1,499', 'Due in 3 days', Colors.orange, false),
          _buildDueCard('Disney+ Hotstar', '₹899', 'Due in 7 days', Colors.orange, false),
          _buildDueCard('YouTube Premium', '₹189', 'Due in 12 days', Colors.blue, false),
        ],
      ),
    );
  }

  Widget _buildHistoryTab(Property property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment History',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Mock payment history
          _buildHistoryItem('Netflix', '₹649', 'Jan 15, 2025', 'Paid'),
          _buildHistoryItem('Amazon Prime', '₹1,499', 'Jan 10, 2025', 'Paid'),
          _buildHistoryItem('Spotify', '₹119', 'Jan 8, 2025', 'Failed'),
          _buildHistoryItem('Disney+ Hotstar', '₹899', 'Dec 28, 2024', 'Paid'),
          _buildHistoryItem('YouTube Premium', '₹189', 'Dec 25, 2024', 'Paid'),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOttServiceCard(String name, String plan, String cost, String status, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding / 2),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(Icons.tv, color: color),
        ),
        title: Text(name),
        subtitle: Text('$plan • $cost'),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: status == 'Active' ? Colors.green.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            status,
            style: TextStyle(
              color: status == 'Active' ? Colors.green : Colors.grey,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        onTap: () => _showServiceDetails(name),
      ),
    );
  }

  Widget _buildDueCard(String service, String amount, String dueInfo, Color color, bool isOverdue) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding / 2),
      child: ListTile(
        leading: Icon(
          isOverdue ? Icons.warning : Icons.schedule,
          color: color,
        ),
        title: Text(service),
        subtitle: Text(dueInfo),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              amount,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            TextButton(
              onPressed: () => _handlePayment(service, amount),
              child: Text('Pay Now'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryItem(String service, String amount, String date, String status) {
    final isPaid = status == 'Paid';
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding / 2),
      child: ListTile(
        leading: Icon(
          isPaid ? Icons.check_circle : Icons.error,
          color: isPaid ? Colors.green : Colors.red,
        ),
        title: Text(service),
        subtitle: Text(date),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              amount,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              status,
              style: TextStyle(
                color: isPaid ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _refreshData() {
    ref.invalidate(propertyByIdProvider(widget.propertyId));
  }

  void _showAddOttServiceDialog(Property property) {
    // Implementation for adding new OTT service
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add OTT Service dialog coming soon...')),
    );
  }

  void _handlePayAllDues() {
    // Implementation for paying all dues
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Pay all dues functionality coming soon...')),
    );
  }

  void _handlePayment(String service, String amount) {
    // Implementation for individual payment
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Payment for $service ($amount) initiated...')),
    );
  }

  void _showServiceDetails(String serviceName) {
    // Implementation for showing service details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$serviceName details coming soon...')),
    );
  }
}
