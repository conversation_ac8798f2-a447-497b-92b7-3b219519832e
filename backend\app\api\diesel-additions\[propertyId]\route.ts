import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createDieselAdditionSchema = Joi.object({
  quantity_liters: Joi.number().positive().required(),
  cost_per_liter: Joi.number().positive().optional(),
  total_cost: Joi.number().positive().optional(),
  supplier: Joi.string().optional(),
  receipt_number: Joi.string().optional(),
});

async function getDieselAdditionsHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Get diesel additions for the property
    const dieselAdditions = await prisma.dieselAddition.findMany({
      where: {
        propertyId,
      },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
      orderBy: {
        addedAt: 'desc',
      },
    });

    // Transform data to match API response format
    const transformedAdditions = dieselAdditions.map(addition => ({
      id: addition.id,
      property_id: addition.propertyId,
      quantity_liters: parseFloat(addition.quantityLiters.toString()),
      cost_per_liter: addition.costPerLiter ? parseFloat(addition.costPerLiter.toString()) : null,
      total_cost: addition.totalCost ? parseFloat(addition.totalCost.toString()) : null,
      supplier: addition.supplier,
      receipt_number: addition.receiptNumber,
      added_by: addition.addedBy,
      added_at: addition.addedAt,
      user: addition.user ? {
        id: addition.user.id,
        full_name: addition.user.fullName,
        email: addition.user.email,
      } : null,
    }));

    return Response.json(
      createApiResponse(transformedAdditions),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch diesel additions');
  }
}

async function createDieselAdditionHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createDieselAdditionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { quantity_liters, cost_per_liter, total_cost, supplier, receipt_number } = validation.data;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Calculate total cost if not provided
    let finalTotalCost = total_cost;
    if (!finalTotalCost && cost_per_liter) {
      finalTotalCost = quantity_liters * cost_per_liter;
    }

    // Create diesel addition entry
    const dieselAddition = await prisma.dieselAddition.create({
      data: {
        propertyId,
        quantityLiters: quantity_liters,
        costPerLiter: cost_per_liter,
        totalCost: finalTotalCost,
        supplier,
        receiptNumber: receipt_number,
        addedBy: currentUser.id,
        addedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Diesel addition recorded successfully',
        addition: {
          id: dieselAddition.id,
          property_id: dieselAddition.propertyId,
          quantity_liters: parseFloat(dieselAddition.quantityLiters.toString()),
          cost_per_liter: dieselAddition.costPerLiter ? parseFloat(dieselAddition.costPerLiter.toString()) : null,
          total_cost: dieselAddition.totalCost ? parseFloat(dieselAddition.totalCost.toString()) : null,
          supplier: dieselAddition.supplier,
          receipt_number: dieselAddition.receiptNumber,
          added_by: dieselAddition.addedBy,
          added_at: dieselAddition.addedAt,
          user: {
            id: dieselAddition.user!.id,
            full_name: dieselAddition.user!.fullName,
            email: dieselAddition.user!.email,
          },
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to record diesel addition');
  }
}

export const GET = requireAuth(getDieselAdditionsHandler);
export const POST = requireAuth(createDieselAdditionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
