import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/threshold_providers.dart';
import '../widgets/threshold_config_card.dart';
import '../../data/threshold_api_service.dart';

class ThresholdConfigScreen extends ConsumerStatefulWidget {
  const ThresholdConfigScreen({super.key});

  @override
  ConsumerState<ThresholdConfigScreen> createState() => _ThresholdConfigScreenState();
}

class _ThresholdConfigScreenState extends ConsumerState<ThresholdConfigScreen> {
  @override
  Widget build(BuildContext context) {
    final thresholdsAsync = ref.watch(thresholdConfigProvider);

    return Scaffold(
      appBar: const RoleBasedAppBar(
        title: 'Threshold Configuration',
        showRoleIndicator: true,
      ),
      body: PermissionWidget(
        permission: 'thresholds.configure',
        fallback: _buildNoPermissionView(),
        child: thresholdsAsync.when(
          data: (thresholds) => _buildThresholdsList(_convertThresholdsToMap(thresholds)),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorView(error),
        ),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to configure thresholds.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load thresholds',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(thresholdConfigProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildThresholdsList(Map<String, dynamic> thresholds) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(thresholdConfigProvider);
      },
      child: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Header
          Text(
            'System Thresholds',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Configure alert thresholds for various system metrics',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.largePadding),

          // Fuel Thresholds
          ThresholdConfigCard(
            title: 'Generator Fuel Levels',
            icon: Icons.local_gas_station,
            color: Colors.orange,
            description: 'Configure fuel level alerts for generators',
            thresholds: [
              ThresholdItem(
                name: 'Critical Level',
                key: 'fuel_critical',
                value: thresholds['fuel_critical']?.toDouble() ?? 50.0,
                unit: 'Liters',
                min: 0,
                max: 1000,
                description: 'Alert when fuel drops below this level',
              ),
              ThresholdItem(
                name: 'Warning Level',
                key: 'fuel_warning',
                value: thresholds['fuel_warning']?.toDouble() ?? 100.0,
                unit: 'Liters',
                min: 0,
                max: 1000,
                description: 'Warning when fuel drops below this level',
              ),
              ThresholdItem(
                name: 'Backup Hours Critical',
                key: 'backup_hours_critical',
                value: thresholds['backup_hours_critical']?.toDouble() ?? 6.0,
                unit: 'Hours',
                min: 0,
                max: 72,
                description: 'Critical alert when backup time is below this',
              ),
            ],
            onSave: (values) => _saveThresholds('fuel', values),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Maintenance Thresholds
          ThresholdConfigCard(
            title: 'Maintenance Escalation',
            icon: Icons.build,
            color: Colors.blue,
            description: 'Configure maintenance issue escalation times',
            thresholds: [
              ThresholdItem(
                name: 'Critical Issue Escalation',
                key: 'critical_escalation_hours',
                value: thresholds['critical_escalation_hours']?.toDouble() ?? 2.0,
                unit: 'Hours',
                min: 0.5,
                max: 24,
                description: 'Escalate critical issues after this time',
              ),
              ThresholdItem(
                name: 'High Priority Escalation',
                key: 'high_escalation_hours',
                value: thresholds['high_escalation_hours']?.toDouble() ?? 8.0,
                unit: 'Hours',
                min: 1,
                max: 72,
                description: 'Escalate high priority issues after this time',
              ),
              ThresholdItem(
                name: 'Medium Priority Escalation',
                key: 'medium_escalation_days',
                value: thresholds['medium_escalation_days']?.toDouble() ?? 2.0,
                unit: 'Days',
                min: 1,
                max: 14,
                description: 'Escalate medium priority issues after this time',
              ),
            ],
            onSave: (values) => _saveThresholds('maintenance', values),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Attendance Thresholds
          ThresholdConfigCard(
            title: 'Attendance Compliance',
            icon: Icons.people,
            color: Colors.green,
            description: 'Configure attendance compliance thresholds',
            thresholds: [
              ThresholdItem(
                name: 'Minimum Attendance Rate',
                key: 'min_attendance_rate',
                value: thresholds['min_attendance_rate']?.toDouble() ?? 80.0,
                unit: '%',
                min: 50,
                max: 100,
                description: 'Alert when attendance rate falls below this',
              ),
              ThresholdItem(
                name: 'Minimum Daily Hours',
                key: 'min_daily_hours',
                value: thresholds['min_daily_hours']?.toDouble() ?? 6.0,
                unit: 'Hours',
                min: 4,
                max: 12,
                description: 'Alert when daily hours are below this',
              ),
              ThresholdItem(
                name: 'Late Arrival Threshold',
                key: 'late_arrival_minutes',
                value: thresholds['late_arrival_minutes']?.toDouble() ?? 15.0,
                unit: 'Minutes',
                min: 5,
                max: 60,
                description: 'Mark as late when arrival exceeds this',
              ),
            ],
            onSave: (values) => _saveThresholds('attendance', values),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // System Performance Thresholds
          ThresholdConfigCard(
            title: 'System Performance',
            icon: Icons.speed,
            color: Colors.purple,
            description: 'Configure system performance monitoring',
            thresholds: [
              ThresholdItem(
                name: 'Internet Uptime Minimum',
                key: 'internet_uptime_min',
                value: thresholds['internet_uptime_min']?.toDouble() ?? 97.0,
                unit: '%',
                min: 90,
                max: 100,
                description: 'Alert when internet uptime falls below this',
              ),
              ThresholdItem(
                name: 'Response Time Threshold',
                key: 'response_time_max',
                value: thresholds['response_time_max']?.toDouble() ?? 1000.0,
                unit: 'ms',
                min: 100,
                max: 5000,
                description: 'Alert when response time exceeds this',
              ),
              ThresholdItem(
                name: 'Camera Offline Threshold',
                key: 'camera_offline_percent',
                value: thresholds['camera_offline_percent']?.toDouble() ?? 20.0,
                unit: '%',
                min: 5,
                max: 50,
                description: 'Alert when camera offline percentage exceeds this',
              ),
            ],
            onSave: (values) => _saveThresholds('system', values),
          ),

          // Bottom padding to prevent overflow with bottom navigation
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Map<String, dynamic> _convertThresholdsToMap(List<ThresholdConfig> thresholds) {
    final Map<String, dynamic> result = {};
    for (final threshold in thresholds) {
      // Use metric name as key and warning threshold as the default value
      result[threshold.metricName] = threshold.warningThreshold;
    }
    return result;
  }

  Future<void> _saveThresholds(String category, Map<String, double> values) async {
    try {
      // Convert the values map to a list of ThresholdConfig objects
      final List<ThresholdConfig> thresholdConfigs = values.entries.map((entry) {
        return ThresholdConfig(
          id: entry.key,
          serviceType: category,
          metricName: entry.key,
          warningThreshold: entry.value * 0.8,
          criticalThreshold: entry.value,
          unit: _getUnitForKey(entry.key),
          description: 'Threshold for ${entry.key}',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }).toList();

      await ref.read(thresholdConfigProvider.notifier).updateThresholds(thresholdConfigs);
      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'Thresholds updated successfully');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to update thresholds: $e');
      }
    }
  }

  String _getUnitForKey(String key) {
    if (key.contains('fuel')) return 'Liters';
    if (key.contains('hours')) return 'Hours';
    if (key.contains('days')) return 'Days';
    if (key.contains('rate') || key.contains('percent')) return '%';
    if (key.contains('minutes')) return 'Minutes';
    if (key.contains('response_time')) return 'ms';
    return '';
  }
}

class ThresholdItem {
  final String name;
  final String key;
  final double value;
  final String unit;
  final double min;
  final double max;
  final String description;

  const ThresholdItem({
    required this.name,
    required this.key,
    required this.value,
    required this.unit,
    required this.min,
    required this.max,
    required this.description,
  });
}
