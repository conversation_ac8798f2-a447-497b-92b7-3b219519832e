"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Check } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createFunctionProcess, updateFunctionProcess, type FunctionProcess } from "@/app/actions/function-process"

interface FunctionProcessFormProps {
  existingProcess?: FunctionProcess
  onSuccess?: () => void
  onCancel?: () => void
}

export function FunctionProcessForm({ existingProcess, onSuccess, onCancel }: FunctionProcessFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess(false)

    const formData = new FormData(e.currentTarget)

    try {
      const processData = {
        function_name: formData.get("function_name") as string,
        sub_function: formData.get("sub_function") as string,
        input: formData.get("input") as string,
        process: formData.get("process") as string,
        output: formData.get("output") as string,
        threshold_limits: formData.get("threshold_limits") as string,
        responsible_agent: formData.get("responsible_agent") as string,
      }

      let result

      if (existingProcess) {
        // Update existing process
        result = await updateFunctionProcess(existingProcess.id, processData)
      } else {
        // Create new process
        result = await createFunctionProcess(processData)
      }

      if (result.success) {
        setSuccess(true)
        if (!existingProcess) {
          e.currentTarget.reset()
        }
        if (onSuccess) {
          setTimeout(onSuccess, 1000)
        }
      } else {
        setError(result.error || "An error occurred")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{existingProcess ? "Edit Function Process" : "Add New Function Process"}</CardTitle>
        <CardDescription>
          {existingProcess
            ? "Update the details of this function process"
            : "Create a new function process to add to the matrix"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-700">
            <Check className="h-4 w-4" />
            <AlertDescription>
              {existingProcess ? "Function process updated successfully!" : "Function process added successfully!"}
            </AlertDescription>
          </Alert>
        )}

        <form id="function-process-form" onSubmit={handleSubmit} className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="function_name">Function Name</Label>
              <Input
                id="function_name"
                name="function_name"
                placeholder="e.g., Water, Electricity, Security"
                defaultValue={existingProcess?.function_name || ""}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sub_function">Sub-Function</Label>
              <Input
                id="sub_function"
                name="sub_function"
                placeholder="e.g., Tank Level Monitoring, Load Monitoring"
                defaultValue={existingProcess?.sub_function || ""}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="input">Input</Label>
            <Textarea
              id="input"
              name="input"
              placeholder="e.g., Sensor reading (%, time)"
              defaultValue={existingProcess?.input || ""}
              required
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="process">Process</Label>
            <Textarea
              id="process"
              name="process"
              placeholder="e.g., Fetch from sensor/API"
              defaultValue={existingProcess?.process || ""}
              required
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="output">Output</Label>
            <Textarea
              id="output"
              name="output"
              placeholder="e.g., Current water level (%)"
              defaultValue={existingProcess?.output || ""}
              required
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="threshold_limits">Threshold Limits</Label>
            <Textarea
              id="threshold_limits"
              name="threshold_limits"
              placeholder="e.g., Min: 20%, Max: 80%"
              defaultValue={existingProcess?.threshold_limits || ""}
              required
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="responsible_agent">Responsible Agent</Label>
            <Input
              id="responsible_agent"
              name="responsible_agent"
              placeholder="e.g., Facility Supervisor, Electrician"
              defaultValue={existingProcess?.responsible_agent || ""}
              required
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" form="function-process-form" disabled={loading}>
          {loading ? "Submitting..." : existingProcess ? "Update Process" : "Add Process"}
        </Button>
      </CardFooter>
    </Card>
  )
}
