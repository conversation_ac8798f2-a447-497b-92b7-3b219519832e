"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, Check, Loader2, Plus, UserPlus, Edit, Settings } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import {
  getUsers,
  getRoles,
  getUserRoles,
  updateUserStatus,
  updateUser<PERSON><PERSON>s,
  createUser,
  updateUser,
} from "@/app/actions/admin"
import type { User, Role } from "@/lib/auth"

export function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedUserRoles, setSelectedUserRoles] = useState<number[]>([])
  const [updatingStatus, setUpdatingStatus] = useState<Record<string, boolean>>({})
  const [updatingRoles, setUpdatingRoles] = useState(false)
  const [creatingUser, setCreatingUser] = useState(false)
  const [editingUser, setEditingUser] = useState(false)

  // New user form state
  const [newUser, setNewUser] = useState({
    username: "",
    email: "",
    fullName: "",
    password: "",
    confirmPassword: "",
    roleIds: [] as number[],
  })

  // Edit user form state
  const [editUser, setEditUser] = useState({
    username: "",
    email: "",
    fullName: "",
    password: "",
    confirmPassword: "",
    roleIds: [] as number[],
  })

  // Fetch users and roles
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        const usersData = await getUsers()
        const rolesData = await getRoles()
        setUsers(usersData)
        setRoles(rolesData)
      } catch (err) {
        setError("Failed to load users and roles")
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle status toggle
  const handleStatusToggle = async (userId: string, isActive: boolean) => {
    setUpdatingStatus((prev) => ({ ...prev, [userId]: true }))
    setError("")
    setSuccess("")

    try {
      const result = await updateUserStatus(userId, isActive)
      if (result.success) {
        setUsers(users.map((user) => (user.id === userId ? { ...user, is_active: isActive } : user)))
        setSuccess(`User status updated successfully`)
      } else {
        setError(result.error || "Failed to update user status")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setUpdatingStatus((prev) => ({ ...prev, [userId]: false }))
    }
  }

  // Open role dialog
  const openRoleDialog = async (user: User) => {
    setSelectedUser(user)
    setError("")

    try {
      const userRoles = await getUserRoles(user.id)
      setSelectedUserRoles(userRoles.map((role) => role.id))
    } catch (err) {
      setError("Failed to load user roles")
      console.error(err)
    }

    setIsRoleDialogOpen(true)
  }

  // Open edit dialog
  const openEditDialog = async (user: User) => {
    setSelectedUser(user)
    setError("")

    try {
      const userRoles = await getUserRoles(user.id)
      setEditUser({
        username: user.username || "",
        email: user.email || "",
        fullName: user.full_name || "",
        password: "",
        confirmPassword: "",
        roleIds: userRoles.map((role) => role.id),
      })
    } catch (err) {
      setError("Failed to load user data")
      console.error(err)
    }

    setIsEditDialogOpen(true)
  }

  // Handle role checkbox change
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedUserRoles((prev) => [...prev, roleId])
    } else {
      setSelectedUserRoles((prev) => prev.filter((id) => id !== roleId))
    }
  }

  // Handle new user role change
  const handleNewUserRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setNewUser((prev) => ({ ...prev, roleIds: [...prev.roleIds, roleId] }))
    } else {
      setNewUser((prev) => ({ ...prev, roleIds: prev.roleIds.filter((id) => id !== roleId) }))
    }
  }

  // Handle edit user role change
  const handleEditUserRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setEditUser((prev) => ({ ...prev, roleIds: [...prev.roleIds, roleId] }))
    } else {
      setEditUser((prev) => ({ ...prev, roleIds: prev.roleIds.filter((id) => id !== roleId) }))
    }
  }

  // Save user roles
  const saveUserRoles = async () => {
    if (!selectedUser) return

    setUpdatingRoles(true)
    setError("")
    setSuccess("")

    try {
      const result = await updateUserRoles(selectedUser.id, selectedUserRoles)
      if (result.success) {
        setIsRoleDialogOpen(false)
        setSuccess("User roles updated successfully")

        // Refresh user list to show updated roles
        const usersData = await getUsers()
        setUsers(usersData)
      } else {
        setError(result.error || "Failed to update user roles")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setUpdatingRoles(false)
    }
  }

  // Create new user
  const handleCreateUser = async () => {
    if (newUser.password !== newUser.confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (newUser.password.length < 6) {
      setError("Password must be at least 6 characters long")
      return
    }

    setCreatingUser(true)
    setError("")
    setSuccess("")

    try {
      const result = await createUser({
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.fullName,
        password: newUser.password,
        roleIds: newUser.roleIds,
      })

      if (result.success) {
        setIsCreateDialogOpen(false)
        setSuccess("User created successfully")
        setNewUser({
          username: "",
          email: "",
          fullName: "",
          password: "",
          confirmPassword: "",
          roleIds: [],
        })

        // Refresh user list
        const usersData = await getUsers()
        setUsers(usersData)
      } else {
        setError(result.error || "Failed to create user")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setCreatingUser(false)
    }
  }

  // Update user
  const handleUpdateUser = async () => {
    if (!selectedUser) return

    if (editUser.password && editUser.password !== editUser.confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (editUser.password && editUser.password.length < 6) {
      setError("Password must be at least 6 characters long")
      return
    }

    setEditingUser(true)
    setError("")
    setSuccess("")

    try {
      const result = await updateUser(selectedUser.id, {
        username: editUser.username,
        email: editUser.email,
        fullName: editUser.fullName,
        password: editUser.password || undefined,
        roleIds: editUser.roleIds,
      })

      if (result.success) {
        setIsEditDialogOpen(false)
        setSuccess("User updated successfully")

        // Refresh user list
        const usersData = await getUsers()
        setUsers(usersData)
      } else {
        setError(result.error || "Failed to update user")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setEditingUser(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 text-green-700">
          <Check className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Users</CardTitle>
              <CardDescription>Manage user accounts and permissions</CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-hidden flex flex-col">
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                  <DialogDescription>Add a new user to the system with roles and permissions.</DialogDescription>
                </DialogHeader>

                <div className="space-y-4 py-4 overflow-y-auto flex-1 px-1">
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      value={newUser.username}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, username: e.target.value }))}
                      placeholder="Enter username"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, email: e.target.value }))}
                      placeholder="Enter email address"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      value={newUser.fullName}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, fullName: e.target.value }))}
                      placeholder="Enter full name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, password: e.target.value }))}
                      placeholder="Enter password"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={newUser.confirmPassword}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="Confirm password"
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Assign Roles</Label>
                    {roles.map((role) => (
                      <div key={role.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`new-role-${role.id}`}
                          checked={newUser.roleIds.includes(role.id)}
                          onCheckedChange={(checked) => handleNewUserRoleChange(role.id, checked === true)}
                        />
                        <Label htmlFor={`new-role-${role.id}`} className="flex-1">
                          {role.name}
                          <span className="block text-xs text-slate-500">{role.description}</span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateUser} disabled={creatingUser}>
                    {creatingUser ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Create User
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="rounded-md bg-slate-50 p-8 text-center">
              <p className="text-slate-500">No users found</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Username</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Full Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.username}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.full_name}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={user.is_active}
                            onCheckedChange={(checked) => handleStatusToggle(user.id, checked)}
                            disabled={updatingStatus[user.id]}
                          />
                          <span>
                            {user.is_active ? (
                              <Badge variant="outline" className="bg-green-50 text-green-700">
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-red-50 text-red-700">
                                Inactive
                              </Badge>
                            )}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>{user.role}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" onClick={() => openEditDialog(user)}>
                            <Edit className="mr-1 h-3 w-3" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => openRoleDialog(user)}>
                            <Settings className="mr-1 h-3 w-3" />
                            Roles
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>{selectedUser && `Update details for ${selectedUser.username}`}</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4 overflow-y-auto flex-1 px-1">
            <div className="space-y-2">
              <Label htmlFor="edit-username">Username</Label>
              <Input
                id="edit-username"
                value={editUser.username}
                onChange={(e) => setEditUser((prev) => ({ ...prev, username: e.target.value }))}
                placeholder="Enter username"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editUser.email}
                onChange={(e) => setEditUser((prev) => ({ ...prev, email: e.target.value }))}
                placeholder="Enter email address"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-fullName">Full Name</Label>
              <Input
                id="edit-fullName"
                value={editUser.fullName}
                onChange={(e) => setEditUser((prev) => ({ ...prev, fullName: e.target.value }))}
                placeholder="Enter full name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-password">New Password (optional)</Label>
              <Input
                id="edit-password"
                type="password"
                value={editUser.password}
                onChange={(e) => setEditUser((prev) => ({ ...prev, password: e.target.value }))}
                placeholder="Leave blank to keep current password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-confirmPassword">Confirm New Password</Label>
              <Input
                id="edit-confirmPassword"
                type="password"
                value={editUser.confirmPassword}
                onChange={(e) => setEditUser((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="Confirm new password"
              />
            </div>

            <div className="space-y-3">
              <Label>Assign Roles</Label>
              {roles.map((role) => (
                <div key={role.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`edit-role-${role.id}`}
                    checked={editUser.roleIds.includes(role.id)}
                    onCheckedChange={(checked) => handleEditUserRoleChange(role.id, checked === true)}
                  />
                  <Label htmlFor={`edit-role-${role.id}`} className="flex-1">
                    {role.name}
                    <span className="block text-xs text-slate-500">{role.description}</span>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateUser} disabled={editingUser}>
              {editingUser ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Update User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Role Management Dialog */}
      <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Manage User Roles</DialogTitle>
            <DialogDescription>{selectedUser && `Assign roles to ${selectedUser.username}`}</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {roles.map((role) => (
              <div key={role.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`role-${role.id}`}
                  checked={selectedUserRoles.includes(role.id)}
                  onCheckedChange={(checked) => handleRoleChange(role.id, checked === true)}
                />
                <Label htmlFor={`role-${role.id}`} className="flex-1">
                  {role.name}
                  <span className="block text-xs text-slate-500">{role.description}</span>
                </Label>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveUserRoles} disabled={updatingRoles}>
              {updatingRoles ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
