import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../core/constants/api_constants.dart';
import '../../../shared/models/user.dart';
import '../../../shared/models/api_response.dart';

part 'user_api_service.g.dart';

@RestApi()
abstract class UserApiService {
  factory UserApiService(Dio dio) = _UserApiService;

  @GET(ApiConstants.users)
  Future<ApiResponse<List<User>>> getUsers({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('role') String? role,
    @Query('status') String? status,
  });

  @GET('${ApiConstants.users}/{id}')
  Future<ApiResponse<User>> getUserById(@Path('id') String id);

  @POST(ApiConstants.users)
  Future<HttpResponse<dynamic>> createUser(@Body() CreateUserRequest request);

  @PUT('${ApiConstants.users}/{id}')
  Future<ApiResponse<User>> updateUser(
    @Path('id') String id,
    @Body() UpdateUserRequest request,
  );

  @DELETE('${ApiConstants.users}/{id}')
  Future<ApiResponse<VoidResponse>> deleteUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/activate')
  Future<ApiResponse<User>> activateUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/deactivate')
  Future<ApiResponse<User>> deactivateUser(@Path('id') String id);

  @POST('${ApiConstants.users}/{id}/roles')
  Future<ApiResponse<User>> assignRoles(
    @Path('id') String id,
    @Body() AssignRolesRequest request,
  );
}

// Request models
class CreateUserRequest {
  final String email;
  final String password;
  @JsonKey(name: 'full_name')
  final String fullName;
  final String? phone;
  final List<String>? roles;

  const CreateUserRequest({
    required this.email,
    required this.password,
    required this.fullName,
    this.phone,
    this.roles,
  });

  Map<String, dynamic> toJson() => {
        'email': email,
        'password': password,
        'full_name': fullName,
        if (phone != null) 'phone': phone,
        if (roles != null) 'roles': roles,
      };
}

class UpdateUserRequest {
  final String? email;
  @JsonKey(name: 'full_name')
  final String? fullName;
  final String? phone;
  @JsonKey(name: 'is_active')
  final bool? isActive;

  const UpdateUserRequest({
    this.email,
    this.fullName,
    this.phone,
    this.isActive,
  });

  Map<String, dynamic> toJson() => {
        if (email != null) 'email': email,
        if (fullName != null) 'full_name': fullName,
        if (phone != null) 'phone': phone,
        if (isActive != null) 'is_active': isActive,
      };
}

class AssignRolesRequest {
  final List<String> roles;

  const AssignRolesRequest({
    required this.roles,
  });

  Map<String, dynamic> toJson() => {
        'roles': roles,
      };
}
