"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"

export interface SecurityGuardLog {
  id: string
  site_name: string
  guard_name: string
  shift: string
  in_time: string | null // Now represents when guard RETURNED to their post
  out_time: string // Now represents when guard LEFT their post
  replacement: string | null
  reason: string | null
  log_date: string
  away_minutes: number | null // NEW: Calculated away time in minutes
  created_at: string
}

export interface LogSummary {
  log_date: string
  logs: SecurityGuardLog[]
}

export interface OpenEntry {
  id: string
  guard_name: string
  shift: string
  in_time: string // Time they left their post
  log_date: string
}

export interface ShiftAwayTime {
  morningAwayMinutes: number
  nightAwayMinutes: number
}

// NEW: Calculate away time in minutes
function calculateAwayMinutes(leftTime: string, returnTime: string, shift: string): number {
  // This function is now only used as a fallback - the database should calculate away_minutes
  console.log(`Fallback calculation: left=${leftTime}, return=${returnTime}, shift=${shift}`)

  try {
    const leftMinutes = timeToMinutes(leftTime)
    const returnMinutes = timeToMinutes(returnTime)

    if (isNaN(leftMinutes) || isNaN(returnMinutes)) {
      return 0
    }

    let duration = returnMinutes - leftMinutes

    if (shift === "Night" && duration < 0) {
      duration += 24 * 60
    }

    if (duration < 0 || duration > 12 * 60) {
      return 0
    }

    return duration
  } catch (error) {
    console.error("Error in fallback calculation:", error)
    return 0
  }
}

// Helper function to convert time string to minutes
function timeToMinutes(timeStr: string): number {
  if (!timeStr || typeof timeStr !== "string") {
    return Number.NaN
  }

  // Handle various formats: "HH:MM", "HH:MM:SS", etc.
  const cleanTime = timeStr.trim()
  let timePart = cleanTime

  // Extract HH:MM part
  if (cleanTime.length >= 5) {
    timePart = cleanTime.substring(0, 5)
  }

  const parts = timePart.split(":")
  if (parts.length < 2) {
    return Number.NaN
  }

  const hours = Number.parseInt(parts[0], 10)
  const minutes = Number.parseInt(parts[1], 10)

  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    return Number.NaN
  }

  return hours * 60 + minutes
}

export async function getOpenEntry(siteName: string, guardName: string, logDate: string): Promise<OpenEntry | null> {
  const supabase = createClient()

  try {
    console.log("Checking for open entry:", { siteName, guardName, logDate })

    const { data, error } = await supabase
      .from("security_guard_logs")
      .select("id, guard_name, shift, out_time, log_date")
      .eq("site_name", siteName)
      .eq("guard_name", guardName)
      .eq("log_date", logDate)
      .is("in_time", null)
      .order("created_at", { ascending: false })
      .limit(1)

    if (error) {
      console.error("Error fetching open entry:", error)
      return null
    }

    console.log("Open entry result:", data)
    if (data.length > 0) {
      return {
        id: data[0].id,
        guard_name: data[0].guard_name,
        shift: data[0].shift,
        in_time: data[0].out_time,
        log_date: data[0].log_date,
      }
    }
    return null
  } catch (error) {
    console.error("Error fetching open entry:", error)
    return null
  }
}

export async function createOrUpdateSecurityGuardLog(formData: FormData) {
  const supabase = createClient()

  try {
    const siteName = formData.get("site_name") as string
    const guardName = formData.get("guard_name") as string
    const logDate = formData.get("log_date") as string
    const leftTime = formData.get("left_time") as string
    const returnTime = formData.get("return_time") as string
    const shift = formData.get("shift") as string
    const replacement = (formData.get("replacement") as string) || null
    const reason = (formData.get("reason") as string) || null
    const entryId = formData.get("entry_id") as string

    console.log("Form data received:", {
      siteName,
      guardName,
      logDate,
      leftTime,
      returnTime,
      shift,
      replacement,
      reason,
      entryId,
    })

    // Validate required fields
    if (!siteName || !guardName || !logDate || !leftTime || !shift) {
      console.error("Missing required fields")
      return { success: false, error: "Missing required fields" }
    }

    // Check if this is updating an existing open entry
    if (entryId && returnTime) {
      console.log("Updating existing entry with ID:", entryId)

      // Update existing entry with return time, replacement, reason
      // The database trigger will automatically calculate away_minutes
      const { data, error } = await supabase
        .from("security_guard_logs")
        .update({
          in_time: returnTime,
          replacement: replacement,
          reason: reason,
        })
        .eq("id", entryId)
        .select()

      if (error) {
        console.error("Error updating security guard log:", error)
        return { success: false, error: error.message }
      }

      console.log("Successfully updated entry, database calculated away time:", data)
    } else {
      console.log("Creating new entry")

      // Create new entry (left time only)
      const logData = {
        site_name: siteName,
        guard_name: guardName,
        shift: shift,
        out_time: leftTime,
        in_time: null,
        replacement: null,
        reason: null,
        log_date: logDate,
        away_minutes: null, // Will be calculated by database trigger when return time is added
      }

      console.log("Inserting log data:", logData)

      const { data, error } = await supabase.from("security_guard_logs").insert([logData]).select()

      if (error) {
        console.error("Error creating security guard log:", error)
        return { success: false, error: error.message }
      }

      console.log("Successfully created entry:", data)
    }

    revalidatePath("/dashboard/home/<USER>/security")
    return { success: true }
  } catch (error) {
    console.error("Error in createOrUpdateSecurityGuardLog:", error)
    return { success: false, error: "Failed to create/update log entry" }
  }
}

export async function deleteSecurityGuardLog(logId: string) {
  const supabase = createClient()

  try {
    console.log("Deleting log entry with ID:", logId)

    const { error } = await supabase.from("security_guard_logs").delete().eq("id", logId)

    if (error) {
      console.error("Error deleting security guard log:", error)
      return { success: false, error: error.message }
    }

    console.log("Successfully deleted entry")
    revalidatePath("/dashboard/home/<USER>/security")
    return { success: true }
  } catch (error) {
    console.error("Error in deleteSecurityGuardLog:", error)
    return { success: false, error: "Failed to delete log entry" }
  }
}

export async function updateSecurityGuardLog(formData: FormData) {
  const supabase = createClient()

  try {
    const logId = formData.get("log_id") as string
    const guardName = formData.get("guard_name") as string
    const shift = formData.get("shift") as string
    const leftTime = formData.get("left_time") as string
    const returnTime = formData.get("return_time") as string
    const replacement = (formData.get("replacement") as string) || null
    const reason = (formData.get("reason") as string) || null
    const logDate = formData.get("log_date") as string

    console.log("Updating log entry:", {
      logId,
      guardName,
      shift,
      leftTime,
      returnTime,
      replacement,
      reason,
      logDate,
    })

    // Validate required fields
    if (!logId || !guardName || !shift || !leftTime || !logDate) {
      console.error("Missing required fields for update")
      return { success: false, error: "Missing required fields" }
    }

    // Update the record - database trigger will automatically calculate away_minutes
    const updateData = {
      guard_name: guardName,
      shift: shift,
      out_time: leftTime,
      in_time: returnTime || null,
      replacement: replacement,
      reason: reason,
      log_date: logDate,
    }

    const { data, error } = await supabase.from("security_guard_logs").update(updateData).eq("id", logId).select()

    if (error) {
      console.error("Error updating security guard log:", error)
      return { success: false, error: error.message }
    }

    console.log("Successfully updated entry, database calculated away time:", data)
    revalidatePath("/dashboard/home/<USER>/security")
    return { success: true }
  } catch (error) {
    console.error("Error in updateSecurityGuardLog:", error)
    return { success: false, error: "Failed to update log entry" }
  }
}

export async function getSecurityGuardLogById(logId: string): Promise<SecurityGuardLog | null> {
  const supabase = createClient()

  try {
    console.log("Fetching log entry with ID:", logId)

    const { data, error } = await supabase.from("security_guard_logs").select("*").eq("id", logId).single()

    if (error) {
      console.error("Error fetching security guard log:", error)
      return null
    }

    console.log("Fetched log entry:", data)
    return data
  } catch (error) {
    console.error("Error in getSecurityGuardLogById:", error)
    return null
  }
}

export async function getSecurityGuardLogs(siteName: string): Promise<LogSummary[]> {
  const supabase = createClient()

  try {
    console.log("Fetching logs for site:", siteName)

    const { data, error } = await supabase
      .from("security_guard_logs")
      .select("*")
      .eq("site_name", siteName)
      .not("in_time", "is", null) // Only show completed entries
      .order("log_date", { ascending: false })
      .order("out_time", { ascending: true })

    if (error) {
      console.error("Error fetching security guard logs:", error)
      return []
    }

    console.log("Fetched logs with database-calculated away_minutes:", data)

    // Group logs by date
    const groupedLogs = data.reduce((acc: { [key: string]: SecurityGuardLog[] }, log) => {
      const date = log.log_date
      if (!acc[date]) {
        acc[date] = []
      }
      acc[date].push(log)
      return acc
    }, {})

    // Convert to array format
    return Object.entries(groupedLogs).map(([date, logs]) => ({
      log_date: date,
      logs: logs,
    }))
  } catch (error) {
    console.error("Error fetching security guard logs:", error)
    return []
  }
}

export async function testDatabaseConnection() {
  const supabase = createClient()

  try {
    console.log("Testing database connection...")

    const { data, error } = await supabase.from("security_guard_logs").select("count").limit(1)

    if (error) {
      console.error("Database connection error:", error)
      return { success: false, error: error.message }
    }

    console.log("Database connection successful")
    return { success: true, data }
  } catch (error) {
    console.error("Database connection failed:", error)
    return { success: false, error: "Connection failed" }
  }
}

// SIMPLIFIED: Use stored away_minutes from database
export function calculateShiftAwayTime(guardLogs: SecurityGuardLog[]): ShiftAwayTime {
  console.log("=== CALCULATING AWAY TIME FROM DATABASE ===")
  console.log("Input logs:", guardLogs)

  let morningAwayMinutes = 0
  let nightAwayMinutes = 0

  guardLogs.forEach((log, index) => {
    console.log(`\n--- Processing Log ${index + 1} ---`)
    console.log("Log data:", {
      id: log.id,
      shift: log.shift,
      away_minutes: log.away_minutes,
      away_minutes_type: typeof log.away_minutes,
    })

    // Use the stored away_minutes directly from database
    if (log.away_minutes && log.away_minutes > 0) {
      if (log.shift === "Morning") {
        morningAwayMinutes += log.away_minutes
        console.log(`✅ Added ${log.away_minutes} minutes to Morning total. New total: ${morningAwayMinutes}`)
      } else if (log.shift === "Night") {
        nightAwayMinutes += log.away_minutes
        console.log(`✅ Added ${log.away_minutes} minutes to Night total. New total: ${nightAwayMinutes}`)
      }
    } else {
      console.log(`❌ No valid away_minutes: ${log.away_minutes}`)
    }
  })

  console.log("=== FINAL TOTALS FROM DATABASE ===")
  console.log(`Morning: ${morningAwayMinutes} minutes`)
  console.log(`Night: ${nightAwayMinutes} minutes`)

  return { morningAwayMinutes, nightAwayMinutes }
}

export function formatAwayTimeDisplay(totalMinutes: number, shift?: string): string {
  console.log(`Formatting away time: ${totalMinutes} minutes for ${shift} shift`)

  if (isNaN(totalMinutes) || totalMinutes < 0) {
    console.log("Invalid totalMinutes, returning 0")
    return `Away 0m of 12-hour shift${shift ? ` (${shift} shift)` : ""}`
  }

  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  const shiftText = shift ? ` (${shift} shift)` : ""

  if (hours > 0) {
    return `Away ${hours}h ${minutes}m of 12-hour shift${shiftText}`
  } else {
    return `Away ${minutes}m of 12-hour shift${shiftText}`
  }
}

// NEW: Format individual away time for display
export function formatIndividualAwayTime(awayMinutes: number | null): string {
  if (!awayMinutes || awayMinutes <= 0) {
    return "0m"
  }

  const hours = Math.floor(awayMinutes / 60)
  const minutes = awayMinutes % 60

  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}

export function getCurrentTime(): string {
  const now = new Date()
  const hours = now.getHours().toString().padStart(2, "0")
  const minutes = now.getMinutes().toString().padStart(2, "0")
  return `${hours}:${minutes}`
}

export function getTodayDate(): string {
  const today = new Date()
  return today.toISOString().split("T")[0]
}
