# 🚀 SRSR Property Management - Backend Features

## 🆕 New Features Implemented

### 1. 🔐 Multi-field Login Support
- **Endpoint**: `POST /api/auth/login`
- **Features**:
  - Login with email, username, or phone number
  - Automatic login type detection
  - Backward compatible with existing email-only login
  - Returns user permissions in response

**Example Request**:
```json
{
  "identifier": "<EMAIL>",  // or username or phone
  "password": "password123",
  "login_type": "email"  // optional: auto-detected
}
```

### 2. 📡 Server-Sent Events (SSE) for Real-time Notifications
- **Endpoint**: `GET /api/notifications/sse`
- **Features**:
  - Real-time notification streaming
  - Automatic reconnection handling
  - User-specific notification filtering
  - Heartbeat mechanism for connection health

**Usage**:
```javascript
const eventSource = new EventSource('/api/notifications/sse', {
  headers: { Authorization: 'Bearer <token>' }
});
```

### 3. 📊 Threshold Monitoring System
- **Endpoints**:
  - `POST /api/monitoring` - Monitor single metric
  - `POST /api/monitoring/multiple` - Monitor multiple metrics
  - `GET /api/monitoring` - Get active alerts

**Features**:
- Automatic threshold checking
- Real-time alert generation
- Support for multiple service types (fuel, attendance, maintenance, etc.)
- Configurable warning and critical thresholds

**Example Request**:
```json
{
  "property_id": "uuid",
  "service_type": "fuel",
  "metric_name": "fuel_level",
  "value": 45.5,
  "unit": "liters"
}
```

### 4. 🛡️ Permission Configuration System
- **Endpoints**:
  - `GET /api/permissions/screens` - Get screen permissions
  - `PUT /api/permissions/screens/{screenName}` - Update screen permission
  - `GET /api/permissions/widgets` - Get widget permissions
  - `PUT /api/permissions/widgets/{screenName}/{widgetName}` - Update widget permission

**Features**:
- Runtime permission configuration
- Screen-level and widget-level permissions
- Role-based and explicit permission support
- Admin interface for permission management

### 5. 🔔 Enhanced Notification System
- **Endpoints**:
  - `GET /api/notifications` - Get user notifications
  - `POST /api/notifications/{id}/read` - Mark as read

**Features**:
- Multiple notification types (threshold alerts, system updates, etc.)
- Priority levels (low, normal, high, critical)
- User and role targeting
- Read/unread status tracking

## 🗄️ Database Schema Updates

### New Tables Added:
1. **monitoring_data** - Stores metric values and timestamps
2. **threshold_alerts** - Stores generated alerts
3. **screen_permissions** - Configurable screen access permissions
4. **widget_permissions** - Configurable widget visibility/access
5. **user_notifications** - User-specific notification read status

### Updated Tables:
- **users** - Added `permissions` array field for explicit permissions

## 🔧 Configuration & Setup

### 1. Database Migration
```bash
npm run db:push
# or
npx prisma db push
```

### 2. Seed Default Configurations
```bash
# Seed all configurations
node scripts/seed-all.js

# Or seed individually
node scripts/seed-permissions.js
node scripts/seed-thresholds.js
```

### 3. Test API Endpoints
```bash
node scripts/test-api.js
```

## 📋 Default Configurations

### Screen Permissions:
- `dashboard` - All authenticated users
- `properties` - Admin, Manager
- `maintenance` - Admin, Manager, Maintenance
- `attendance` - Admin, Manager
- `fuel` - Admin, Manager, Fuel Operator
- `admin` - Admin only
- `reports` - Admin, Manager

### Threshold Configurations:
- **Fuel**: fuel_level, fuel_percentage, backup_hours
- **Attendance**: attendance_percentage, daily_attendance
- **Maintenance**: open_issues, overdue_issues, response_time
- **Security**: security_incidents, access_violations
- **Electricity**: power_outage_duration, generator_runtime, battery_level
- **System**: efficiency_percentage, downtime_hours, failure_rate

## 🚀 Usage Examples

### Monitor Fuel Level
```javascript
const response = await fetch('/api/monitoring', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <token>',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    property_id: 'property-uuid',
    service_type: 'fuel',
    metric_name: 'fuel_level',
    value: 45.5,
    unit: 'liters'
  })
});
```

### Update Screen Permission
```javascript
const response = await fetch('/api/permissions/screens/dashboard', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer <token>',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    required_permissions: ['view_dashboard'],
    allowed_roles: ['admin', 'manager', 'user'],
    is_enabled: true
  })
});
```

### Connect to SSE Stream
```javascript
const eventSource = new EventSource('/api/notifications/sse', {
  headers: { Authorization: 'Bearer <token>' }
});

eventSource.onmessage = (event) => {
  const notification = JSON.parse(event.data);
  console.log('Received notification:', notification);
};
```

## 🔍 Monitoring & Alerts

### Alert Severity Levels:
- **Low**: Minor issues, informational
- **Normal**: Standard operational alerts
- **High**: Important issues requiring attention
- **Critical**: Urgent issues requiring immediate action

### Service Types Supported:
- `fuel` - Generator fuel monitoring
- `attendance` - Staff attendance tracking
- `maintenance` - Maintenance issue monitoring
- `security` - Security incident tracking
- `electricity` - Power system monitoring
- `system` - Overall system performance

## 🛠️ Development Notes

### Key Files:
- `lib/monitoring/threshold-monitor.ts` - Core monitoring logic
- `lib/monitoring/notification-service.ts` - Notification handling
- `lib/sse-manager.ts` - SSE connection management
- `app/api/monitoring/` - Monitoring endpoints
- `app/api/permissions/` - Permission configuration endpoints

### Environment Variables:
No additional environment variables required. Uses existing database connection.

## 🧪 Testing

Run the comprehensive test suite:
```bash
node scripts/test-api.js
```

This tests:
- Multi-field login functionality
- Permission configuration endpoints
- Monitoring and alerting system
- Notification endpoints
- SSE connectivity

## 🎯 Integration with Frontend

The backend now fully supports all the features implemented in the Flutter frontend:
- Multi-field login (email/username/phone)
- Real-time notifications via SSE
- Threshold monitoring with alerts
- Permission-based UI rendering
- Configurable screen and widget permissions

All endpoints follow the existing API patterns and are fully compatible with the Flutter app's expectations.
