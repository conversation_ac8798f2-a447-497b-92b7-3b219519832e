import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../../../core/constants/api_constants.dart';
import '../../../shared/models/user.dart';
import '../../../shared/models/api_response.dart';

part 'auth_api_service.g.dart';

@RestApi()
abstract class AuthApiService {
  factory AuthApiService(Dio dio) = _AuthApiService;

  @POST(ApiConstants.login)
  Future<LoginResponse> login(@Body() LoginRequest request);

  @POST(ApiConstants.publicRegister)
  Future<RegisterResponse> register(@Body() RegisterRequest request);

  @GET(ApiConstants.profile)
  Future<ApiResponse<User>> getCurrentUser();
}

class LoginRequest {
  final String? email;
  final String? identifier;
  final String password;
  final String? loginType;

  const LoginRequest({
    this.email,
    this.identifier,
    required this.password,
    this.loginType,
  });

  // Legacy constructor for backward compatibility
  const LoginRequest.email({
    required String email,
    required this.password,
  }) : email = email,
       identifier = null,
       loginType = null;

  // New multi-field constructor
  const LoginRequest.multiField({
    required String identifier,
    required this.password,
    String? loginType,
  }) : identifier = identifier,
       email = null,
       loginType = loginType;

  Map<String, dynamic> toJson() {
    if (email != null) {
      // Legacy format
      return {
        'email': email,
        'password': password,
      };
    } else {
      // New multi-field format
      return {
        'identifier': identifier,
        'password': password,
        if (loginType != null) 'login_type': loginType,
      };
    }
  }
}

class LoginResponse {
  final bool success;
  final String token;
  final User user;

  const LoginResponse({
    required this.success,
    required this.token,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        success: json['success'] as bool,
        token: json['token'] as String,
        user: User.fromJson(json['user'] as Map<String, dynamic>),
      );
}

class RegisterRequest {
  final String email;
  final String? username;
  final String password;
  final String fullName;
  final String? phone;

  const RegisterRequest({
    required this.email,
    this.username,
    required this.password,
    required this.fullName,
    this.phone,
  });

  Map<String, dynamic> toJson() => {
        'email': email,
        if (username != null) 'username': username,
        'password': password,
        'full_name': fullName,
        if (phone != null) 'phone': phone,
      };
}

class RegisterResponse {
  final bool success;
  final String? message;

  const RegisterResponse({
    required this.success,
    this.message,
  });

  factory RegisterResponse.fromJson(Map<String, dynamic> json) => RegisterResponse(
        success: json['success'] as bool,
        message: json['message'] as String?,
      );
}
