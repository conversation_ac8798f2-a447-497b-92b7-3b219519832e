import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/business_logic/status_calculator.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/property.dart' as property_model;
import '../../../../shared/models/maintenance_issue.dart';

class SystemHealthWidget extends ConsumerStatefulWidget {
  final List<property_model.Property> properties;
  final List<MaintenanceIssue> maintenanceIssues;

  const SystemHealthWidget({
    super.key,
    required this.properties,
    required this.maintenanceIssues,
  });

  @override
  ConsumerState<SystemHealthWidget> createState() => _SystemHealthWidgetState();
}

class _SystemHealthWidgetState extends ConsumerState<SystemHealthWidget> {
  bool _isExpanded = false;
  Map<String, FunctionalAreaStatus> _systemStatus = {};

  @override
  void initState() {
    super.initState();
    _calculateSystemHealth();
  }

  @override
  void didUpdateWidget(SystemHealthWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.properties != widget.properties ||
        oldWidget.maintenanceIssues != widget.maintenanceIssues) {
      _calculateSystemHealth();
    }
  }

  void _calculateSystemHealth() {
    final status = <String, FunctionalAreaStatus>{};

    // Calculate overall maintenance status
    final maintenanceData = widget.maintenanceIssues.map((issue) =>
      MaintenanceIssueData(
        id: issue.id,
        status: issue.status,
        priority: issue.priority,
        dueDate: issue.dueDate,
      )
    ).toList();

    status['maintenance'] = StatusCalculator.calculateMaintenanceStatus(
      issues: maintenanceData,
    );

    // Calculate property health (simplified)
    final activeProperties = widget.properties.where((p) => p.isActive).length;
    final totalProperties = widget.properties.length;
    final propertyHealthPercentage = totalProperties > 0 ? (activeProperties / totalProperties) * 100 : 100.0;

    status['properties'] = FunctionalAreaStatus(
      status: propertyHealthPercentage >= 90 ? StatusLevel.green :
              propertyHealthPercentage >= 70 ? StatusLevel.orange : StatusLevel.red,
      metrics: [
        StatusMetric.create('active_properties', activeProperties, '/$totalProperties',
          propertyHealthPercentage >= 90 ? StatusLevel.green : StatusLevel.orange),
        StatusMetric.create('health_percentage', propertyHealthPercentage, '%',
          propertyHealthPercentage >= 90 ? StatusLevel.green : StatusLevel.orange),
      ],
      issueCount: totalProperties - activeProperties,
      lastUpdated: DateTime.now(),
    );

    // Calculate overall system health
    final overallStatus = StatusCalculator.calculateOverallStatus(status);
    status['overall'] = FunctionalAreaStatus(
      status: overallStatus,
      metrics: [
        StatusMetric.create('system_health', _getHealthScore(status), '%', overallStatus),
      ],
      issueCount: status.values.map((s) => s.issueCount).reduce((a, b) => a + b),
      lastUpdated: DateTime.now(),
    );

    setState(() {
      _systemStatus = status;
    });
  }

  double _getHealthScore(Map<String, FunctionalAreaStatus> status) {
    if (status.isEmpty) return 100.0;

    final scores = status.values.map((s) {
      switch (s.status) {
        case StatusLevel.green:
          return 100.0;
        case StatusLevel.orange:
          return 70.0;
        case StatusLevel.red:
          return 30.0;
      }
    }).toList();

    return scores.reduce((a, b) => a + b) / scores.length;
  }

  @override
  Widget build(BuildContext context) {
    final overallStatus = _systemStatus['overall'];
    if (overallStatus == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    final healthScore = overallStatus.metrics.first.value as double;
    final statusColor = _getStatusColor(overallStatus.status);

    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              Icons.health_and_safety,
              color: statusColor,
              size: 32,
            ),
            title: Text(
              'System Health',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Text(
              '${healthScore.toStringAsFixed(1)}% Overall Health',
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusIndicator(overallStatus.status),
                IconButton(
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  onPressed: () => setState(() => _isExpanded = !_isExpanded),
                ),
              ],
            ),
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  // Health Score Chart
                  SizedBox(
                    height: 200,
                    child: _buildHealthChart(healthScore, statusColor),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  // Functional Area Status
                  _buildFunctionalAreasList(),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHealthChart(double healthScore, Color statusColor) {
    return Stack(
      alignment: Alignment.center,
      children: [
        PieChart(
          PieChartData(
            startDegreeOffset: -90,
            sectionsSpace: 2,
            centerSpaceRadius: 60,
            sections: [
              PieChartSectionData(
                value: healthScore,
                color: statusColor,
                radius: 20,
                showTitle: false,
              ),
              PieChartSectionData(
                value: 100 - healthScore,
                color: Colors.grey.shade200,
                radius: 20,
                showTitle: false,
              ),
            ],
          ),
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${healthScore.toStringAsFixed(0)}%',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: statusColor,
              ),
            ),
            Text(
              'Health Score',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFunctionalAreasList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Functional Areas',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        ..._systemStatus.entries
            .where((entry) => entry.key != 'overall')
            .map((entry) => _buildFunctionalAreaItem(entry.key, entry.value)),
      ],
    );
  }

  Widget _buildFunctionalAreaItem(String area, FunctionalAreaStatus status) {
    final statusColor = _getStatusColor(status.status);
    final areaName = _getAreaDisplayName(area);
    final icon = _getAreaIcon(area);

    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: statusColor, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  areaName,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (status.metrics.isNotEmpty)
                  Text(
                    status.metrics.map((m) => '${m.name}: ${m.displayValue}').join(', '),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
          _buildStatusIndicator(status.status),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(StatusLevel status) {
    final color = _getStatusColor(status);
    final label = _getStatusLabel(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(StatusLevel status) {
    switch (status) {
      case StatusLevel.green:
        return Colors.green;
      case StatusLevel.orange:
        return Colors.orange;
      case StatusLevel.red:
        return Colors.red;
    }
  }

  String _getStatusLabel(StatusLevel status) {
    switch (status) {
      case StatusLevel.green:
        return 'Good';
      case StatusLevel.orange:
        return 'Warning';
      case StatusLevel.red:
        return 'Critical';
    }
  }

  String _getAreaDisplayName(String area) {
    switch (area) {
      case 'maintenance':
        return 'Maintenance';
      case 'properties':
        return 'Properties';
      case 'fuel':
        return 'Generator Fuel';
      case 'security':
        return 'Security';
      case 'attendance':
        return 'Attendance';
      default:
        return area.toUpperCase();
    }
  }

  IconData _getAreaIcon(String area) {
    switch (area) {
      case 'maintenance':
        return Icons.build;
      case 'properties':
        return Icons.business;
      case 'fuel':
        return Icons.local_gas_station;
      case 'security':
        return Icons.security;
      case 'attendance':
        return Icons.people;
      default:
        return Icons.category;
    }
  }
}
