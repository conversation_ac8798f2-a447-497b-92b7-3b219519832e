import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/role_management_api_service.dart';
import '../../../../core/network/dio_client.dart';

// Role Management Service Provider
final roleManagementServiceProvider = Provider<RoleManagementApiService>((ref) {
  final dio = ref.watch(dioClientProvider);
  return RoleManagementApiService(dio);
});

// Roles Provider
final rolesProvider = FutureProvider<List<Role>>((ref) async {
  final service = ref.read(roleManagementServiceProvider);
  final response = await service.getRoles(
    includePermissions: true,
    includeUsers: true,
  );
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.error ?? 'Failed to fetch roles');
  }
});

// Permissions Provider
final permissionsProvider = FutureProvider<PermissionResponse>((ref) async {
  final service = ref.read(roleManagementServiceProvider);
  final response = await service.getPermissions(includeRoles: true);
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.error ?? 'Failed to fetch permissions');
  }
});

// Individual Role Provider
final roleProvider = FutureProvider.family<Role, String>((ref, roleId) async {
  final service = ref.read(roleManagementServiceProvider);
  final response = await service.getRoleById(roleId);
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.error ?? 'Failed to fetch role');
  }
});

// User Roles Provider
final userRolesProvider = FutureProvider.family<UserRolesResponse, String>((ref, userId) async {
  final service = ref.read(roleManagementServiceProvider);
  final response = await service.getUserRoles(userId);
  
  if (response.success && response.data != null) {
    return response.data!;
  } else {
    throw Exception(response.error ?? 'Failed to fetch user roles');
  }
});

// Role Management State Notifier
class RoleManagementNotifier extends StateNotifier<AsyncValue<List<Role>>> {
  final RoleManagementApiService _service;

  RoleManagementNotifier(this._service) : super(const AsyncValue.loading()) {
    loadRoles();
  }

  Future<void> loadRoles() async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.getRoles(
        includePermissions: true,
        includeUsers: true,
      );
      
      if (response.success && response.data != null) {
        state = AsyncValue.data(response.data!);
      } else {
        state = AsyncValue.error(
          Exception(response.error ?? 'Failed to fetch roles'),
          StackTrace.current,
        );
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> createRole(CreateRoleRequest request) async {
    try {
      final response = await _service.createRole(request);
      if (response.success) {
        await loadRoles(); // Refresh the list
      } else {
        throw Exception(response.error ?? 'Failed to create role');
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> updateRole(String roleId, UpdateRoleRequest request) async {
    try {
      final response = await _service.updateRole(roleId, request);
      if (response.success) {
        await loadRoles(); // Refresh the list
      } else {
        throw Exception(response.error ?? 'Failed to update role');
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteRole(String roleId) async {
    try {
      final response = await _service.deleteRole(roleId);
      if (response.success) {
        await loadRoles(); // Refresh the list
      } else {
        throw Exception(response.error ?? 'Failed to delete role');
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> assignUserRoles(String userId, AssignRolesRequest request) async {
    try {
      final response = await _service.assignUserRoles(userId, request);
      if (!response.success) {
        throw Exception(response.error ?? 'Failed to assign roles');
      }
    } catch (error) {
      rethrow;
    }
  }

  Future<void> removeUserRole(String userId, RemoveRoleRequest request) async {
    try {
      final response = await _service.removeUserRole(userId, request);
      if (!response.success) {
        throw Exception(response.error ?? 'Failed to remove role');
      }
    } catch (error) {
      rethrow;
    }
  }
}

// Role Management State Provider
final roleManagementProvider = StateNotifierProvider<RoleManagementNotifier, AsyncValue<List<Role>>>((ref) {
  final service = ref.watch(roleManagementServiceProvider);
  return RoleManagementNotifier(service);
});

// Permission Management State Notifier
class PermissionManagementNotifier extends StateNotifier<AsyncValue<PermissionResponse>> {
  final RoleManagementApiService _service;

  PermissionManagementNotifier(this._service) : super(const AsyncValue.loading()) {
    loadPermissions();
  }

  Future<void> loadPermissions() async {
    state = const AsyncValue.loading();
    try {
      final response = await _service.getPermissions(includeRoles: true);
      
      if (response.success && response.data != null) {
        state = AsyncValue.data(response.data!);
      } else {
        state = AsyncValue.error(
          Exception(response.error ?? 'Failed to fetch permissions'),
          StackTrace.current,
        );
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> createPermission(CreatePermissionRequest request) async {
    try {
      final response = await _service.createPermission(request);
      if (response.success) {
        await loadPermissions(); // Refresh the list
      } else {
        throw Exception(response.error ?? 'Failed to create permission');
      }
    } catch (error) {
      rethrow;
    }
  }
}

// Permission Management State Provider
final permissionManagementProvider = StateNotifierProvider<PermissionManagementNotifier, AsyncValue<PermissionResponse>>((ref) {
  final service = ref.watch(roleManagementServiceProvider);
  return PermissionManagementNotifier(service);
});

// Filtered Roles Provider
final filteredRolesProvider = Provider.family<List<Role>, RoleFilter>((ref, filter) {
  final rolesAsync = ref.watch(rolesProvider);
  
  return rolesAsync.when(
    data: (roles) {
      return roles.where((role) {
        final matchesSearch = filter.searchQuery.isEmpty ||
            role.name.toLowerCase().contains(filter.searchQuery.toLowerCase()) ||
            (role.description?.toLowerCase().contains(filter.searchQuery.toLowerCase()) ?? false);
        
        final matchesSystemFilter = filter.includeSystemRoles || !role.isSystemRole;
        
        return matchesSearch && matchesSystemFilter;
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Role Filter Model
class RoleFilter {
  final String searchQuery;
  final bool includeSystemRoles;

  const RoleFilter({
    this.searchQuery = '',
    this.includeSystemRoles = true,
  });

  RoleFilter copyWith({
    String? searchQuery,
    bool? includeSystemRoles,
  }) {
    return RoleFilter(
      searchQuery: searchQuery ?? this.searchQuery,
      includeSystemRoles: includeSystemRoles ?? this.includeSystemRoles,
    );
  }
}

// Role Filter State Provider
final roleFilterProvider = StateProvider<RoleFilter>((ref) {
  return const RoleFilter();
});

// Available Permissions for Role Assignment
final availablePermissionsProvider = FutureProvider<List<Permission>>((ref) async {
  final permissionsAsync = ref.watch(permissionsProvider);
  
  return permissionsAsync.when(
    data: (permissionResponse) => permissionResponse.permissions,
    loading: () => [],
    error: (_, __) => [],
  );
});

// Role Assignment Helper
final roleAssignmentHelperProvider = Provider<RoleAssignmentHelper>((ref) {
  final service = ref.read(roleManagementServiceProvider);
  return RoleAssignmentHelper(service);
});

class RoleAssignmentHelper {
  final RoleManagementApiService _service;

  RoleAssignmentHelper(this._service);

  Future<bool> assignRolesToUser(String userId, List<String> roleIds, {bool replaceExisting = true}) async {
    try {
      final request = AssignRolesRequest(
        roleIds: roleIds,
        replaceExisting: replaceExisting,
      );
      
      final response = await _service.assignUserRoles(userId, request);
      return response.success;
    } catch (e) {
      return false;
    }
  }

  Future<bool> removeRoleFromUser(String userId, String roleId) async {
    try {
      final request = RemoveRoleRequest(roleId: roleId);
      final response = await _service.removeUserRole(userId, request);
      return response.success;
    } catch (e) {
      return false;
    }
  }

  Future<List<Role>> getAvailableRolesForUser(String userId) async {
    try {
      final rolesResponse = await _service.getRoles();
      final userRolesResponse = await _service.getUserRoles(userId);
      
      if (rolesResponse.success && userRolesResponse.success) {
        final allRoles = rolesResponse.data ?? [];
        final userRoleIds = userRolesResponse.data?.roles.map((r) => r.id).toSet() ?? <String>{};
        
        return allRoles.where((role) => !userRoleIds.contains(role.id)).toList();
      }
      
      return [];
    } catch (e) {
      return [];
    }
  }
}
