"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Battery, Clock, Droplet } from "lucide-react"
import type { GeneratorFuelUpdate } from "@/app/actions/generator-fuel"

interface GeneratorStatusDisplayProps {
  latestUpdate: GeneratorFuelUpdate | null
}

export function GeneratorStatusDisplay({ latestUpdate }: GeneratorStatusDisplayProps) {
  if (!latestUpdate) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Generator Status</CardTitle>
          <CardDescription>Current status of backup power generator</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md bg-yellow-50 p-4 text-yellow-700">
            <h3 className="mb-2 font-medium">No Data Available</h3>
            <p>There is no recent generator fuel data available.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Generator capacity is 100 liters
  const generatorCapacity = 100

  // Calculate fuel in generator (liters)
  const fuelInGenerator = latestUpdate.fuel_in_generator_percentage
    ? (generatorCapacity * latestUpdate.fuel_in_generator_percentage) / 100
    : 0

  // Calculate total fuel available
  const fuelInTank = latestUpdate.fuel_in_tank_liters || 0
  const totalFuel = fuelInGenerator + fuelInTank

  // Average fuel consumption is 6.5 liters per hour (from the requirements: 6-7 liters per hour)
  const fuelConsumptionRate = 6.5

  // Calculate power backup hours
  const powerBackupHours = totalFuel > 0 ? Math.round((totalFuel / fuelConsumptionRate) * 10) / 10 : 0

  // Determine status color based on power backup hours
  let statusColor = "green"
  let statusText = "Excellent"

  if (powerBackupHours < 5) {
    statusColor = "red"
    statusText = "Critical"
  } else if (powerBackupHours < 12) {
    statusColor = "yellow"
    statusText = "Limited"
  } else if (powerBackupHours < 24) {
    statusColor = "blue"
    statusText = "Good"
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Generator Status Summary</CardTitle>
        <CardDescription>Last updated: {formatDate(latestUpdate.date)}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className={`rounded-md bg-${statusColor}-50 p-4 text-${statusColor}-700 mb-6`}>
          <h3 className="mb-2 font-medium flex items-center gap-2">
            <Battery className="h-5 w-5" />
            Power Backup Status: {statusText}
          </h3>
          <p className="text-sm mb-4">
            Current backup power available for <span className="font-bold text-lg">{powerBackupHours} hours</span>
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="rounded-md border p-4">
            <h3 className="mb-2 font-medium flex items-center gap-2">
              <Droplet className="h-4 w-4" />
              Fuel in Generator
            </h3>
            <p className="text-xl font-semibold">{latestUpdate.fuel_in_generator_percentage || 0}%</p>
            <p className="text-sm text-slate-500">({fuelInGenerator.toFixed(1)} liters)</p>
          </div>

          <div className="rounded-md border p-4">
            <h3 className="mb-2 font-medium flex items-center gap-2">
              <Droplet className="h-4 w-4" />
              Fuel outside Generator
            </h3>
            <p className="text-xl font-semibold">{(latestUpdate.fuel_in_tank_liters || 0).toFixed(1)} liters</p>
          </div>

          <div className="rounded-md border p-4">
            <h3 className="mb-2 font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Meter Reading
            </h3>
            <p className="text-xl font-semibold">{(latestUpdate.ending_reading || 0).toFixed(1)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
