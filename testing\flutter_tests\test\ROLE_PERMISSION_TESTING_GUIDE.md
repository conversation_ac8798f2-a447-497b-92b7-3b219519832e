# Role-Based Screen and Permission Testing Guide

This guide explains how to test role-based screens and permission checks in your Flutter application, covering both expected vs actual behavior and newly created roles/permissions.

## 📋 Overview

The testing suite includes two main test files:

1. **`role_based_screen_tests.dart`** - Tests role-based screen access and widget visibility
2. **`dynamic_permission_tests.dart`** - Tests newly created roles and permissions

## 🚀 Quick Start

### Prerequisites

1. **Backend Server Running**: Ensure your backend server is running at `http://192.168.1.3:3000`
2. **Test Users**: Have admin, manager, and viewer users available
3. **Flutter Dependencies**: Ensure all Flutter dependencies are installed

### Running Tests

#### Option 1: Run All Tests (Recommended)
```bash
cd frontend
dart test/run_role_permission_tests.dart --start-backend
```

#### Option 2: Run Individual Test Suites
```bash
# Role-based screen tests only
flutter test test/role_based_screen_tests.dart

# Dynamic permission tests only
flutter test test/dynamic_permission_tests.dart
```

#### Option 3: Run with Backend Management
```bash
# Start backend automatically and keep it running
dart test/run_role_permission_tests.dart --start-backend --keep-backend

# Skip specific test types
dart test/run_role_permission_tests.dart --skip-role-tests
dart test/run_role_permission_tests.dart --skip-permission-tests
```

## 🎭 Role-Based Screen Tests

### What It Tests

1. **Widget Visibility by Role**
   - Admin users see all content
   - Manager users see limited content
   - Viewer users see minimal content

2. **Permission-Based Access**
   - Tests specific permission requirements
   - Verifies fallback content for denied access

3. **Screen Navigation Access**
   - Tests route-level access control
   - Verifies role-based navigation restrictions

### Test Cases

#### Admin Role Tests
```dart
testWidgets('Admin role should have access to all widgets', (WidgetTester tester) async {
  // Tests that admin users can see:
  // - Admin-only content
  // - User creation buttons
  // - All property access
  // - All screen paths
});
```

#### Manager Role Tests
```dart
testWidgets('Manager role should have limited access', (WidgetTester tester) async {
  // Tests that manager users:
  // - Cannot see admin-only content
  // - Can see properties access
  // - Cannot create users
  // - Have limited screen access
});
```

#### Viewer Role Tests
```dart
testWidgets('Viewer role should have minimal access', (WidgetTester tester) async {
  // Tests that viewer users:
  // - Cannot see admin content
  // - Can see read-only content
  // - Cannot see creation buttons
  // - Have minimal screen access
});
```

### Expected vs Actual Behavior

The tests verify:

- **Expected**: Admin sees "Admin Only Content"
- **Actual**: Widget renders admin content
- **Expected**: Manager sees "Access Denied" for admin content
- **Actual**: Widget shows fallback content

## 🔐 Dynamic Permission Tests

### What It Tests

1. **Dynamic Role Creation**
   - Creates new roles with specific permissions
   - Verifies role creation through API

2. **User Role Assignment**
   - Assigns newly created roles to test users
   - Verifies role assignment persistence

3. **Permission Inheritance**
   - Tests that users inherit role permissions
   - Verifies permission updates propagate

4. **Real-time UI Updates**
   - Tests UI responsiveness to permission changes
   - Verifies widget visibility updates

### Test Flow

1. **Setup Phase**
   ```dart
   // Authenticate admin user
   // Create test role with specific permissions
   // Create test user
   ```

2. **Assignment Phase**
   ```dart
   // Assign role to user
   // Verify role assignment
   // Test permission inheritance
   ```

3. **Modification Phase**
   ```dart
   // Update role permissions
   // Verify user inherits changes
   // Test permission removal
   ```

4. **UI Testing Phase**
   ```dart
   // Test widget visibility changes
   // Verify real-time updates
   // Test permission enforcement
   ```

5. **Cleanup Phase**
   ```dart
   // Remove test data
   // Clean up test users and roles
   ```

## 📊 Test Results Interpretation

### Success Indicators

✅ **Role-Based Screen Tests**
- All role-specific widgets show/hide correctly
- Fallback content appears for denied access
- Screen navigation respects role permissions

✅ **Dynamic Permission Tests**
- New roles created successfully
- Permissions assigned and inherited
- UI updates reflect permission changes
- Real-time enforcement works

### Common Failure Scenarios

❌ **Authentication Issues**
- Backend server not running
- Invalid test credentials
- Token expiration

❌ **Permission Logic Errors**
- Incorrect permission strings
- Role hierarchy issues
- Widget permission mismatches

❌ **UI Update Failures**
- Provider state not updating
- Widget not rebuilding
- Cache invalidation issues

## 🛠️ Troubleshooting

### Backend Connection Issues

```bash
# Check if backend is running
curl http://192.168.1.3:3000/api

# Start backend manually
cd backend
npm run dev
```

### Test User Issues

```bash
# Verify test users exist in database
# Check user roles and permissions
# Reset test data if needed
```

### Flutter Test Issues

```bash
# Clean Flutter cache
flutter clean
flutter pub get

# Run with verbose output
flutter test --verbose test/role_based_screen_tests.dart
```

## 📝 Adding New Test Cases

### For Role-Based Screen Tests

1. **Add New Role Test**
   ```dart
   testWidgets('Custom role should have specific access', (WidgetTester tester) async {
     // Create custom role user
     // Test specific permissions
     // Verify expected behavior
   });
   ```

2. **Add New Widget Test**
   ```dart
   RoleBasedWidget(
     requiredPermissions: const ['custom.permission'],
     child: const Text('Custom Content'),
     fallback: const Text('No Access'),
   )
   ```

### For Dynamic Permission Tests

1. **Add Permission Modification Test**
   ```dart
   test('custom permission assignment works', () async {
     // Create custom permission
     // Assign to role
     // Verify inheritance
   });
   ```

2. **Add Real-time Update Test**
   ```dart
   testWidgets('UI updates for custom permissions', (WidgetTester tester) async {
     // Test permission changes
     // Verify UI updates
   });
   ```

## 🎯 Best Practices

1. **Test Isolation**: Each test should be independent
2. **Data Cleanup**: Always clean up test data
3. **Error Handling**: Handle authentication and network errors
4. **Realistic Scenarios**: Test real-world permission combinations
5. **Performance**: Monitor test execution time

## 📈 Continuous Integration

Add to your CI/CD pipeline:

```yaml
- name: Run Role Permission Tests
  run: |
    cd frontend
    dart test/run_role_permission_tests.dart --start-backend
```

This comprehensive testing approach ensures your role-based access control system works correctly for both existing and newly created roles and permissions.
