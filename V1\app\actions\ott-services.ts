"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"

export type OttService = {
  id: string
  property_id: string
  platform: string
  plan_and_duration: string
  username: string
  password: string
  next_recharge_date: string
  current_status: string
  fee?: string
  expiration_date?: string
}

export async function getOttServices(propertyId: string) {
  try {
    // Add initial delay to prevent rate limiting
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000 + 500))

    const supabase = createServerClient()

    const { data, error } = await supabase
      .from("ott_services")
      .select("*")
      .eq("property_id", propertyId)
      .order("platform")

    if (error) {
      console.error("Supabase error fetching OTT services:", error)

      // Handle rate limiting errors
      if (
        error.message?.includes("Too Many Requests") ||
        error.message?.includes("rate limit") ||
        error.message?.includes("429")
      ) {
        console.warn("Rate limit detected in Supabase error, returning empty array")
        return []
      }

      return []
    }

    console.log(`Successfully fetched ${data?.length || 0} OTT services for ${propertyId}`)
    return data as OttService[]
  } catch (error: any) {
    console.error("Error fetching OTT services:", error)

    // Handle network errors specifically
    if (error instanceof TypeError && error.message?.includes("Failed to fetch")) {
      console.warn("Network error fetching OTT services, returning empty array")
      return []
    }

    // Handle all possible rate limit and JSON parsing scenarios
    const errorMessage = String(error?.message || error || "")

    if (
      error instanceof SyntaxError ||
      errorMessage.includes("Too Many R") ||
      errorMessage.includes("rate limit") ||
      errorMessage.includes("JSON") ||
      errorMessage.includes("429") ||
      errorMessage.includes("Unexpected token")
    ) {
      console.warn("Rate limit or JSON parsing error detected, implementing retry with backoff")

      // Implement retry with exponential backoff
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          const delay = Math.pow(2, attempt) * 1000 + Math.random() * 1000
          console.log(`Retry attempt ${attempt} for OTT services after ${delay}ms delay`)
          await new Promise((resolve) => setTimeout(resolve, delay))

          const supabase = createServerClient()
          const { data, error: retryError } = await supabase
            .from("ott_services")
            .select("*")
            .eq("property_id", propertyId)
            .order("platform")

          if (!retryError && data) {
            console.log(`Retry ${attempt} successful: fetched ${data.length} OTT services`)
            return data as OttService[]
          }

          if (retryError?.message?.includes("Too Many Requests")) {
            console.log(`Retry ${attempt} still rate limited, continuing...`)
            continue
          }
        } catch (retryError) {
          console.log(`Retry ${attempt} failed:`, retryError)
          continue
        }
      }

      console.warn("All retry attempts failed, returning empty array")
      return []
    }

    // Handle network errors
    if (error instanceof TypeError || error.name === "TypeError") {
      console.warn("Network error fetching OTT services, returning empty array")
      return []
    }

    // Handle any other errors
    console.warn("Unknown error fetching OTT services, returning empty array")
    return []
  }
}

export async function updateOttService(id: string, updates: Partial<OttService>) {
  try {
    const supabase = createServerClient()

    const { data, error } = await supabase
      .from("ott_services")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()

    if (error) {
      console.error("Error updating OTT service:", error)

      if (error.message?.includes("Too Many Requests") || error.message?.includes("rate limit")) {
        return { success: false, error: "Rate limit exceeded. Please try again in a moment." }
      }

      return { success: false, error: error.message }
    }

    const propertyId = data[0].property_id
    revalidatePath(`/dashboard/home/<USER>/ott`)
    return { success: true, data }
  } catch (error) {
    console.error("Error updating OTT service:", error)

    if (error instanceof SyntaxError && error.message.includes("Too Many R")) {
      return { success: false, error: "Rate limit exceeded. Please try again in a moment." }
    }

    return { success: false, error: "Failed to update OTT service" }
  }
}

export async function createOttService(service: Omit<OttService, "id">) {
  try {
    const supabase = createServerClient()

    const { data, error } = await supabase
      .from("ott_services")
      .insert({
        ...service,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()

    if (error) {
      console.error("Error creating OTT service:", error)

      if (error.message?.includes("Too Many Requests") || error.message?.includes("rate limit")) {
        return { success: false, error: "Rate limit exceeded. Please try again in a moment." }
      }

      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/home/<USER>/ott`)
    return { success: true, data }
  } catch (error) {
    console.error("Error creating OTT service:", error)

    if (error instanceof SyntaxError && error.message.includes("Too Many R")) {
      return { success: false, error: "Rate limit exceeded. Please try again in a moment." }
    }

    return { success: false, error: "Failed to create OTT service" }
  }
}

export async function deleteOttService(id: string, propertyId: string) {
  try {
    const supabase = createServerClient()

    const { error } = await supabase.from("ott_services").delete().eq("id", id)

    if (error) {
      console.error("Error deleting OTT service:", error)

      if (error.message?.includes("Too Many Requests") || error.message?.includes("rate limit")) {
        return { success: false, error: "Rate limit exceeded. Please try again in a moment." }
      }

      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/home/<USER>/ott`)
    return { success: true }
  } catch (error) {
    console.error("Error deleting OTT service:", error)

    if (error instanceof SyntaxError && error.message.includes("Too Many R")) {
      return { success: false, error: "Rate limit exceeded. Please try again in a moment." }
    }

    return { success: false, error: "Failed to delete OTT service" }
  }
}
