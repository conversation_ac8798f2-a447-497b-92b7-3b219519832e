#!/usr/bin/env node

/**
 * API Completeness Validation Script
 *
 * This script validates that all required endpoints are implemented and accessible.
 * It checks the file system for endpoint implementations and validates the swagger documentation.
 */

const fs = require('fs');
const path = require('path');

// Expected endpoints based on our implementation
const EXPECTED_ENDPOINTS = {
  // Authentication
  'auth/login': { methods: ['POST'], implemented: true },
  'auth/register': { methods: ['POST'], implemented: true },
  'auth/logout': { methods: ['POST'], implemented: true, new: true },
  'auth/me': { methods: ['GET'], implemented: true },

  // Properties
  'properties': { methods: ['GET', 'POST'], implemented: true },
  'properties/[id]': { methods: ['GET', 'PUT'], implemented: true },
  'properties/[id]/members': { methods: ['GET', 'POST'], implemented: true },
  'properties/[id]/attendance': { methods: ['GET', 'POST'], implemented: true },

  // Maintenance
  'maintenance': { methods: ['GET', 'POST'], implemented: true },
  'maintenance/[id]': { methods: ['GET', 'PUT', 'PATCH', 'DELETE'], implemented: true },
  'maintenance/[id]/assign': { methods: ['POST'], implemented: true },
  'maintenance/[id]/escalate': { methods: ['POST'], implemented: true },

  // Users
  'users': { methods: ['GET', 'POST'], implemented: true },
  'users/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },
  'users/[id]/roles': { methods: ['GET', 'POST'], implemented: true },
  'users/bulk-approve': { methods: ['POST'], implemented: true, new: true },

  // Notifications
  'notifications': { methods: ['GET', 'POST'], implemented: true },
  'notifications/[id]/read': { methods: ['PATCH'], implemented: true },
  'notifications/mark-all-read': { methods: ['PATCH'], implemented: true, new: true },
  'notifications/sse': { methods: ['GET'], implemented: true },

  // Monitoring
  'monitoring': { methods: ['POST'], implemented: true },
  'monitoring/multiple': { methods: ['POST'], implemented: true },

  // Thresholds
  'thresholds': { methods: ['GET', 'POST'], implemented: true },
  'thresholds/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },
  'thresholds/bulk': { methods: ['POST', 'PUT'], implemented: true, new: true },

  // Attendance
  'attendance': { methods: ['GET', 'POST'], implemented: true },
  'attendance/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },
  'attendance/bulk': { methods: ['POST'], implemented: true, new: true },
  'attendance/reports/summary': { methods: ['GET'], implemented: true },
  'attendance/reports/user/[userId]': { methods: ['GET'], implemented: true },

  // Generator Fuel
  'generator-fuel/[propertyId]': { methods: ['GET', 'POST'], implemented: true },
  'generator-fuel/logs/[logId]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },

  // OTT Services
  'ott-services/[propertyId]': { methods: ['GET', 'POST'], implemented: true },
  'ott-services/item/[itemId]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },

  // Diesel Additions
  'diesel-additions/[propertyId]': { methods: ['GET', 'POST'], implemented: true },
  'diesel-additions/item/[itemId]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },

  // Uptime Reports
  'uptime-reports/[propertyId]': { methods: ['GET', 'POST'], implemented: true },
  'uptime-reports/item/[itemId]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },

  // Permissions
  'permissions': { methods: ['GET'], implemented: true },
  'permissions/screens': { methods: ['GET', 'POST'], implemented: true },
  'permissions/screens/[screenName]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },
  'permissions/widgets': { methods: ['GET'], implemented: true },
  'permissions/widgets/[screenName]/[widgetName]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },

  // Roles
  'roles': { methods: ['GET', 'POST'], implemented: true },
  'roles/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },
  'roles/[id]/permissions': { methods: ['GET', 'POST'], implemented: true },

  // Dashboard
  'dashboard/status': { methods: ['GET'], implemented: true },

  // Function Processes
  'function-processes': { methods: ['GET', 'POST'], implemented: true },
  'function-processes/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true },
  'function-processes/[id]/logs': { methods: ['GET', 'POST'], implemented: true },

  // Admin (Note: These are in /admin path, not /api/admin)
  'admin/widgets': { methods: ['GET', 'POST'], implemented: true, adminPath: true },
  'admin/widgets/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true, adminPath: true },
  'admin/widget-types': { methods: ['GET'], implemented: true, adminPath: true },
  'admin/screens': { methods: ['GET', 'POST'], implemented: true, adminPath: true },
  'admin/screens/[id]': { methods: ['GET', 'PUT', 'DELETE'], implemented: true, adminPath: true },
  'admin/screens/validate-route': { methods: ['POST'], implemented: true, adminPath: true },
};

/**
 * Check if endpoint file exists
 */
function checkEndpointFile(endpoint, config) {
  let filePath;

  if (config.adminPath) {
    // Admin endpoints are in /admin path, not /api/admin
    filePath = path.join(__dirname, '../app', endpoint, 'route.ts');
  } else {
    // Regular API endpoints are in /api path
    filePath = path.join(__dirname, '../app/api', endpoint, 'route.ts');
  }

  return fs.existsSync(filePath);
}

/**
 * Validate endpoint implementation
 */
function validateEndpoint(endpoint, config) {
  const exists = checkEndpointFile(endpoint, config);
  const status = exists ? '✅' : '❌';
  const newLabel = config.new ? ' (NEW)' : '';
  const pathPrefix = config.adminPath ? '' : '/api';

  return {
    endpoint,
    exists,
    status,
    methods: config.methods,
    new: config.new || false,
    adminPath: config.adminPath || false,
    label: `${status} ${pathPrefix}/${endpoint}${newLabel}`,
  };
}

/**
 * Generate implementation report
 */
function generateImplementationReport() {
  console.log('🔍 SRSR API Implementation Validation Report\n');
  console.log('=' * 60);

  const results = [];
  let totalEndpoints = 0;
  let implementedEndpoints = 0;
  let newEndpoints = 0;

  // Check each endpoint
  for (const [endpoint, config] of Object.entries(EXPECTED_ENDPOINTS)) {
    const result = validateEndpoint(endpoint, config);
    results.push(result);

    totalEndpoints++;
    if (result.exists) implementedEndpoints++;
    if (result.new) newEndpoints++;
  }

  // Group results by category
  const categories = {
    'Authentication': results.filter(r => r.endpoint.startsWith('auth')),
    'Properties': results.filter(r => r.endpoint.startsWith('properties')),
    'Maintenance': results.filter(r => r.endpoint.startsWith('maintenance')),
    'Users': results.filter(r => r.endpoint.startsWith('users')),
    'Notifications': results.filter(r => r.endpoint.startsWith('notifications')),
    'Monitoring': results.filter(r => r.endpoint.startsWith('monitoring')),
    'Thresholds': results.filter(r => r.endpoint.startsWith('thresholds')),
    'Attendance': results.filter(r => r.endpoint.startsWith('attendance')),
    'Generator Fuel': results.filter(r => r.endpoint.startsWith('generator-fuel')),
    'OTT Services': results.filter(r => r.endpoint.startsWith('ott-services')),
    'Diesel Additions': results.filter(r => r.endpoint.startsWith('diesel-additions')),
    'Uptime Reports': results.filter(r => r.endpoint.startsWith('uptime-reports')),
    'Permissions': results.filter(r => r.endpoint.startsWith('permissions')),
    'Roles': results.filter(r => r.endpoint.startsWith('roles')),
    'Dashboard': results.filter(r => r.endpoint.startsWith('dashboard')),
    'Function Processes': results.filter(r => r.endpoint.startsWith('function-processes')),
    'Admin': results.filter(r => r.endpoint.startsWith('admin')),
  };

  // Print results by category
  for (const [category, endpoints] of Object.entries(categories)) {
    if (endpoints.length === 0) continue;

    console.log(`\n📂 ${category}:`);
    endpoints.forEach(endpoint => {
      console.log(`   ${endpoint.label}`);
      if (endpoint.methods.length > 0) {
        console.log(`      Methods: ${endpoint.methods.join(', ')}`);
      }
    });
  }

  // Print summary
  console.log('\n' + '=' * 60);
  console.log('📊 IMPLEMENTATION SUMMARY:');
  console.log(`   Total Endpoints: ${totalEndpoints}`);
  console.log(`   Implemented: ${implementedEndpoints} (${Math.round((implementedEndpoints/totalEndpoints)*100)}%)`);
  console.log(`   Missing: ${totalEndpoints - implementedEndpoints}`);
  console.log(`   New Endpoints Added: ${newEndpoints}`);

  // Check for missing endpoints
  const missingEndpoints = results.filter(r => !r.exists);
  if (missingEndpoints.length > 0) {
    console.log('\n❌ MISSING ENDPOINTS:');
    missingEndpoints.forEach(endpoint => {
      console.log(`   - /api/${endpoint.endpoint}`);
    });
  } else {
    console.log('\n🎉 ALL ENDPOINTS IMPLEMENTED!');
  }

  // Highlight new endpoints
  const newEndpointsList = results.filter(r => r.new && r.exists);
  if (newEndpointsList.length > 0) {
    console.log('\n🆕 NEWLY IMPLEMENTED ENDPOINTS:');
    newEndpointsList.forEach(endpoint => {
      console.log(`   ✅ /api/${endpoint.endpoint}`);
    });
  }

  return {
    total: totalEndpoints,
    implemented: implementedEndpoints,
    missing: totalEndpoints - implementedEndpoints,
    new: newEndpoints,
    completionRate: Math.round((implementedEndpoints/totalEndpoints)*100),
    allImplemented: implementedEndpoints === totalEndpoints,
  };
}

/**
 * Validate swagger documentation
 */
function validateSwaggerDocumentation() {
  console.log('\n📚 SWAGGER DOCUMENTATION VALIDATION:');

  const swaggerPath = path.join(__dirname, '../swagger.json');

  if (!fs.existsSync(swaggerPath)) {
    console.log('❌ swagger.json not found');
    return false;
  }

  try {
    const swaggerContent = fs.readFileSync(swaggerPath, 'utf8');
    const swagger = JSON.parse(swaggerContent);

    const pathCount = Object.keys(swagger.paths || {}).length;
    const schemaCount = Object.keys(swagger.components?.schemas || {}).length;

    console.log(`✅ Swagger documentation found`);
    console.log(`   Documented Paths: ${pathCount}`);
    console.log(`   Schema Definitions: ${schemaCount}`);
    console.log(`   File Size: ${(fs.statSync(swaggerPath).size / 1024).toFixed(2)} KB`);

    return true;
  } catch (error) {
    console.log(`❌ Error reading swagger.json: ${error.message}`);
    return false;
  }
}

/**
 * Main validation function
 */
function main() {
  console.log('🚀 Starting API Implementation Validation...\n');

  const implementationResult = generateImplementationReport();
  const swaggerValid = validateSwaggerDocumentation();

  console.log('\n' + '=' * 60);
  console.log('🏆 FINAL VALIDATION RESULT:');

  if (implementationResult.allImplemented && swaggerValid) {
    console.log('🎉 IMPLEMENTATION COMPLETE!');
    console.log('✅ All endpoints implemented');
    console.log('✅ Swagger documentation valid');
    console.log(`✅ ${implementationResult.new} new endpoints added`);
  } else {
    console.log('⚠️ IMPLEMENTATION INCOMPLETE:');
    if (!implementationResult.allImplemented) {
      console.log(`❌ ${implementationResult.missing} endpoints missing`);
    }
    if (!swaggerValid) {
      console.log('❌ Swagger documentation issues');
    }
  }

  console.log(`\n📈 Completion Rate: ${implementationResult.completionRate}%`);

  return implementationResult.allImplemented && swaggerValid;
}

// Run validation if this script is executed directly
if (require.main === module) {
  const success = main();
  process.exit(success ? 0 : 1);
}

module.exports = {
  generateImplementationReport,
  validateSwaggerDocumentation,
  main,
};
