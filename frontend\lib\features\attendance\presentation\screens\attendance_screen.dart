import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/attendance.dart';
import '../providers/attendance_providers.dart';
import '../widgets/attendance_record_card.dart';
import '../widgets/add_attendance_dialog.dart';
import '../widgets/attendance_stats_card.dart';

class AttendanceScreen extends ConsumerStatefulWidget {
  const AttendanceScreen({super.key});

  @override
  ConsumerState<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends ConsumerState<AttendanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime _selectedDate = DateTime.now();
  DateTime _focusedDate = DateTime.now();
  String _selectedFilter = 'all'; // all, site, office
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoleBasedAppBar(
        title: 'Attendance Management',
        showRoleIndicator: true,
      ),
      body: PermissionWidget(
        permission: 'attendance.read',
        fallback: _buildNoPermissionView(),
        child: Column(
          children: [
            // Tab Bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'All Records'),
                Tab(text: 'Site Attendance'),
                Tab(text: 'Office Attendance'),
              ],
            ),

            // Tab Views with scrollable content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAllRecordsTabWithHeader(),
                  _buildSiteAttendanceTabWithHeader(),
                  _buildOfficeAttendanceTabWithHeader(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: PermissionWidget(
        permission: 'attendance.create',
        child: RoleBasedFAB(
          requiredPermissions: const ['attendance.create'],
          onPressed: () => _showAddAttendanceDialog(),
          tooltip: 'Add Attendance Record',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to view attendance records.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    final statsAsync = ref.watch(attendanceStatsMapProvider(_selectedDate));

    return statsAsync.when(
      data: (statsMap) => Row(
        children: [
          Expanded(
            child: AttendanceStatsCard(
              title: 'Present Today',
              value: statsMap['present']?.toString() ?? '0',
              icon: Icons.check_circle,
              color: Colors.green,
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: AttendanceStatsCard(
              title: 'Absent Today',
              value: statsMap['absent']?.toString() ?? '0',
              icon: Icons.cancel,
              color: Colors.red,
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: AttendanceStatsCard(
              title: 'Late Arrivals',
              value: statsMap['late']?.toString() ?? '0',
              icon: Icons.schedule,
              color: Colors.orange,
            ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildCalendar() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: TableCalendar<AttendanceRecord>(
          firstDay: DateTime.utc(2020, 1, 1),
          lastDay: DateTime.utc(2030, 12, 31),
          focusedDay: _focusedDate,
          selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
          calendarFormat: CalendarFormat.month,
          startingDayOfWeek: StartingDayOfWeek.monday,
          onDaySelected: (selectedDay, focusedDay) {
            setState(() {
              _selectedDate = selectedDay;
              _focusedDate = focusedDay;
            });
          },
          onPageChanged: (focusedDay) {
            _focusedDate = focusedDay;
          },
          calendarStyle: const CalendarStyle(
            outsideDaysVisible: false,
            weekendTextStyle: TextStyle(color: Colors.red),
            holidayTextStyle: TextStyle(color: Colors.red),
          ),
          headerStyle: const HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search workers...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        DropdownButton<String>(
          value: _selectedFilter,
          items: const [
            DropdownMenuItem(value: 'all', child: Text('All')),
            DropdownMenuItem(value: 'site', child: Text('Site Only')),
            DropdownMenuItem(value: 'office', child: Text('Office Only')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedFilter = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildAllRecordsTab() {
    final recordsAsync = ref.watch(attendanceRecordsByDateProvider(_selectedDate));
    return _buildRecordsList(recordsAsync);
  }

  Widget _buildSiteAttendanceTab() {
    final recordsAsync = ref.watch(siteAttendanceRecordsProvider(_selectedDate));
    return _buildRecordsList(recordsAsync);
  }

  Widget _buildOfficeAttendanceTab() {
    final recordsAsync = ref.watch(officeAttendanceRecordsProvider(_selectedDate));
    return _buildRecordsList(recordsAsync);
  }

  Widget _buildAllRecordsTabWithHeader() {
    final recordsAsync = ref.watch(attendanceRecordsByDateProvider(_selectedDate));
    return _buildRecordsListWithHeader(recordsAsync);
  }

  Widget _buildSiteAttendanceTabWithHeader() {
    final recordsAsync = ref.watch(siteAttendanceRecordsProvider(_selectedDate));
    return _buildRecordsListWithHeader(recordsAsync);
  }

  Widget _buildOfficeAttendanceTabWithHeader() {
    final recordsAsync = ref.watch(officeAttendanceRecordsProvider(_selectedDate));
    return _buildRecordsListWithHeader(recordsAsync);
  }

  Widget _buildRecordsList(AsyncValue<List<AttendanceRecord>> recordsAsync) {
    return recordsAsync.when(
      data: (records) {
        final filteredRecords = _filterRecords(records);

        if (filteredRecords.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(attendanceRecordsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: filteredRecords.length,
            itemBuilder: (context, index) {
              final record = filteredRecords[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: AttendanceRecordCard(
                  record: record,
                  onEdit: () => _editRecord(record),
                  onDelete: () => _deleteRecord(record),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildRecordsListWithHeader(AsyncValue<List<AttendanceRecord>> recordsAsync) {
    return recordsAsync.when(
      data: (records) {
        final filteredRecords = _filterRecords(records);

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(attendanceRecordsProvider);
          },
          child: CustomScrollView(
            slivers: [
              // Header content as sliver
              SliverToBoxAdapter(
                child: Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    children: [
                      // Stats Cards
                      _buildStatsSection(),
                      const SizedBox(height: AppConstants.defaultPadding),

                      // Calendar
                      _buildCalendar(),
                      const SizedBox(height: AppConstants.defaultPadding),

                      // Search and Filter
                      _buildSearchAndFilter(),
                      const SizedBox(height: AppConstants.defaultPadding),
                    ],
                  ),
                ),
              ),

              // Records list
              if (filteredRecords.isEmpty)
                SliverFillRemaining(
                  child: _buildEmptyState(),
                )
              else
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final record = filteredRecords[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.defaultPadding,
                          vertical: AppConstants.smallPadding,
                        ),
                        child: AttendanceRecordCard(
                          record: record,
                          onEdit: () => _editRecord(record),
                          onDelete: () => _deleteRecord(record),
                        ),
                      );
                    },
                    childCount: filteredRecords.length,
                  ),
                ),

              // Bottom padding for FAB
              const SliverToBoxAdapter(
                child: SizedBox(height: 80),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No attendance records',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'No attendance records found for ${_formatDate(_selectedDate)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load attendance records',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(attendanceRecordsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  List<AttendanceRecord> _filterRecords(List<AttendanceRecord> records) {
    var filtered = records;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((record) {
        return record.workerName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (record.workerRole?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply type filter
    switch (_selectedFilter) {
      case 'site':
        filtered = filtered.where((record) => record.isSiteAttendance).toList();
        break;
      case 'office':
        filtered = filtered.where((record) => record.isOfficeAttendance).toList();
        break;
    }

    return filtered;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showAddAttendanceDialog() {
    showDialog(
      context: context,
      builder: (context) => AddAttendanceDialog(selectedDate: _selectedDate),
    );
  }

  void _editRecord(AttendanceRecord record) {
    // TODO: Implement edit record dialog
    AppUtils.showInfoSnackBar(context, 'Edit attendance feature coming soon');
  }

  void _deleteRecord(AttendanceRecord record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Record'),
        content: Text('Are you sure you want to delete ${record.workerName}\'s attendance record?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final currentContext = context;
              try {
                await ref.read(attendanceRecordsProvider.notifier).deleteRecord(record.id);
                if (mounted) {
                  AppUtils.showSuccessSnackBar(currentContext, 'Attendance record deleted');
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showErrorSnackBar(currentContext, 'Failed to delete record: $e');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
