"use server"

import { createClient } from "@/lib/supabase/server"
import { getOttServices } from "./ott-services"
import { getUptimeStatistics } from "./uptime-reports"
import { createSafeMetric, safeToNumber, type SafeMetric } from "@/lib/metric-utils"

export type PropertyStatus = {
  id: string
  name: string
  type: "home" | "office"
  overallStatus: "green" | "orange" | "red"
  image?: string
  functionalAreas: {
    [key: string]: FunctionalAreaStatus
  }
  lastUpdated: string
}

export type FunctionalAreaStatus = {
  status: "green" | "orange" | "red"
  metrics: Array<SafeMetric>
  issueCount: number
}

export async function getDashboardStatuses(): Promise<PropertyStatus[]> {
  console.log("🏢 Loading dashboard statuses...")

  const properties = [
    { id: "jublee-hills", name: "Jublee Hills Home", type: "home" as const, image: "/modern-house.png" },
    { id: "gandipet-guest-house", name: "Gandipet Guest House", type: "home" as const, image: "/cozy-guest-house.png" },
    {
      id: "brane-back-office",
      name: "Brane - Back Office",
      type: "office" as const,
      image: "/modern-office-building.png",
    },
    { id: "brane-strf", name: "Brane - STRF", type: "office" as const, image: "/branch-office-building.png" },
    {
      id: "srsr-back-office",
      name: "SRSR - Back Office",
      type: "office" as const,
      image: "/modern-office-building.png",
    },
    {
      id: "srsr-rd-no-36",
      name: "SRSR - Rd No. 36 Office",
      type: "office" as const,
      image: "/branch-office-building.png",
    },
  ]

  const statuses: PropertyStatus[] = []

  for (const property of properties) {
    try {
      console.log(`🏠 Processing ${property.name}...`)

      const functionalAreas: { [key: string]: FunctionalAreaStatus } = {}

      // Electricity status
      const electricityStatus = await getElectricityStatus(property.id)
      functionalAreas.electricity = electricityStatus

      // Water status (placeholder)
      functionalAreas.water = {
        status: "green",
        metrics: [
          createSafeMetric("pressure", 45, " psi", "green"),
          createSafeMetric("flow_rate", 12, " gpm", "green"),
        ],
        issueCount: 0,
      }

      // Security status (placeholder)
      functionalAreas.security = {
        status: "green",
        metrics: [
          createSafeMetric("cameras_online", 8, " cameras", "green"),
          createSafeMetric("access_control", true, "", "green"),
        ],
        issueCount: 0,
      }

      // Internet status
      const internetStatus = await getInternetStatus(property.id)
      functionalAreas.internet = internetStatus

      // OTT status
      const ottStatus = await getOTTStatus(property.id)
      functionalAreas.ott = ottStatus

      // Maintenance status
      const maintenanceStatus = await getMaintenanceStatus(property.id)
      functionalAreas.maintenance = maintenanceStatus

      // Attendance status (placeholder for offices)
      if (property.type === "office") {
        functionalAreas.attendance = {
          status: "green",
          metrics: [
            createSafeMetric("present_today", 15, " people", "green"),
            createSafeMetric("attendance_rate", 85, "%", "green"),
          ],
          issueCount: 0,
        }
      }

      // Calculate overall status
      const areaStatuses = Object.values(functionalAreas).map((area) => area.status)
      let overallStatus: "green" | "orange" | "red" = "green"

      if (areaStatuses.includes("red")) {
        overallStatus = "red"
      } else if (areaStatuses.includes("orange")) {
        overallStatus = "orange"
      }

      const propertyStatus: PropertyStatus = {
        id: property.id,
        name: property.name,
        type: property.type,
        overallStatus,
        image: property.image,
        functionalAreas,
        lastUpdated: new Date().toISOString(),
      }

      statuses.push(propertyStatus)
      console.log(`✅ ${property.name}: Overall status = ${overallStatus}`)
    } catch (error) {
      console.error(`❌ Error getting status for ${property.name}:`, error)
      // Add a default status to prevent UI breaking
      statuses.push({
        id: property.id,
        name: property.name,
        type: property.type,
        overallStatus: "green",
        image: property.image,
        functionalAreas: {
          electricity: {
            status: "green",
            metrics: [createSafeMetric("status", "No data", "", "green")],
            issueCount: 0,
          },
        },
        lastUpdated: new Date().toISOString(),
      })
    }
  }

  console.log(`🏢 Dashboard statuses loaded: ${statuses.length} properties`)
  console.log(
    "Properties:",
    statuses.map((p) => `${p.name}:${p.overallStatus}`),
  )

  return statuses
}

async function getElectricityStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    const supabase = createClient()
    const { data: fuelData, error } = await supabase
      .from("generator_fuel_updates")
      .select("fuel_in_generator_percentage, fuel_in_tank_liters")
      .eq("property_id", propertyId)
      .order("date", { ascending: false })
      .limit(1)
      .single()

    if (error || !fuelData) {
      return {
        status: "green",
        metrics: [createSafeMetric("generator_status", "No data", "", "green")],
        issueCount: 0,
      }
    }

    const fuelPercentage = safeToNumber(fuelData.fuel_in_generator_percentage)
    const fuelInTank = safeToNumber(fuelData.fuel_in_tank_liters)
    const totalFuel = (fuelPercentage / 100) * 100 + fuelInTank
    const backupHours = totalFuel / 6.5

    let status: "green" | "orange" | "red" = "green"
    if (backupHours < 6) status = "red"
    else if (backupHours < 9) status = "orange"

    return {
      status,
      metrics: [
        createSafeMetric("fuel_percentage", fuelPercentage, "%", status),
        createSafeMetric("backup_hours", Math.round(backupHours * 10) / 10, " hrs", status),
        createSafeMetric("tank_liters", fuelInTank, " L", "green"),
      ],
      issueCount: status === "green" ? 0 : 1,
    }
  } catch (error) {
    console.error(`Error getting electricity status for ${propertyId}:`, error)
    return {
      status: "green",
      metrics: [createSafeMetric("generator_status", "Error", "", "green")],
      issueCount: 0,
    }
  }
}

async function getInternetStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    if (propertyId === "jublee-hills") {
      const statistics = await getUptimeStatistics(propertyId)
      const avgUptime = safeToNumber(statistics.weekly.averageUptime)
      const downtime = safeToNumber(statistics.weekly.totalDowntime)
      const disruptions = safeToNumber(statistics.weekly.disruptionCount)

      let status: "green" | "orange" | "red" = "green"
      if (avgUptime < 97) status = "red"
      else if (avgUptime < 99) status = "orange"

      return {
        status,
        metrics: [
          createSafeMetric("uptime", Math.round(avgUptime * 10) / 10, "%", status),
          createSafeMetric("downtime", Math.round(downtime), " mins", downtime > 60 ? "orange" : "green"),
          createSafeMetric("disruptions", Math.round(disruptions), " events", disruptions > 2 ? "orange" : "green"),
        ],
        issueCount: status === "green" ? 0 : 1,
      }
    }

    // Default for other properties
    return {
      status: "green",
      metrics: [
        createSafeMetric("connection", "Stable", "", "green"),
        createSafeMetric("speed", 100, " Mbps", "green"),
      ],
      issueCount: 0,
    }
  } catch (error) {
    console.error(`Error getting internet status for ${propertyId}:`, error)
    return {
      status: "green",
      metrics: [createSafeMetric("connection", "Unknown", "", "green")],
      issueCount: 0,
    }
  }
}

async function getOTTStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    const ottServices = await getOttServices(propertyId)

    if (!ottServices || ottServices.length === 0) {
      return {
        status: "green",
        metrics: [createSafeMetric("services", 0, " services", "green")],
        issueCount: 0,
      }
    }

    const activeServices = ottServices.filter((service) => service.current_status === "Active")
    let status: "green" | "orange" | "red" = "green"
    let issueCount = 0

    const today = new Date()
    for (const service of activeServices) {
      if (service.next_recharge_date) {
        const nextPaymentDate = new Date(service.next_recharge_date)
        const daysUntilPayment = Math.ceil((nextPaymentDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

        if (daysUntilPayment < 0) {
          status = "red"
          issueCount++
        } else if (daysUntilPayment <= 7 && status !== "red") {
          status = "orange"
          issueCount++
        }
      }
    }

    return {
      status,
      metrics: [
        createSafeMetric("active_services", activeServices.length, " active", "green"),
        createSafeMetric("total_services", ottServices.length, " total", "green"),
        createSafeMetric("payment_status", status === "green" ? "Up to date" : "Due soon", "", status),
      ],
      issueCount,
    }
  } catch (error) {
    console.error(`Error getting OTT status for ${propertyId}:`, error)
    return {
      status: "green",
      metrics: [createSafeMetric("services", "Error", "", "green")],
      issueCount: 0,
    }
  }
}

async function getMaintenanceStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    const supabase = createClient()
    const { data: issues, error } = await supabase
      .from("maintenance_issues")
      .select("status, priority")
      .eq("property_id", propertyId)

    if (error) {
      console.error(`Error getting maintenance status for ${propertyId}:`, error)
      return {
        status: "green",
        metrics: [createSafeMetric("issues", "Error", "", "green")],
        issueCount: 0,
      }
    }

    if (!issues || issues.length === 0) {
      return {
        status: "green",
        metrics: [
          createSafeMetric("open_issues", 0, " open", "green"),
          createSafeMetric("total_issues", 0, " total", "green"),
        ],
        issueCount: 0,
      }
    }

    const openIssues = issues.filter((issue) => issue.status === "Open" || issue.status === "On Hold")
    const inProgressIssues = issues.filter((issue) => issue.status === "In Progress")
    const highPriorityOpen = openIssues.filter((issue) => issue.priority === "High")

    let status: "green" | "orange" | "red" = "green"
    let issueCount = 0

    if (openIssues.length > 0) {
      status = "red"
      issueCount = openIssues.length
    } else if (inProgressIssues.length > 0) {
      status = "orange"
      issueCount = inProgressIssues.length
    }

    return {
      status,
      metrics: [
        createSafeMetric("open_issues", openIssues.length, " open", openIssues.length > 0 ? "red" : "green"),
        createSafeMetric(
          "in_progress",
          inProgressIssues.length,
          " in progress",
          inProgressIssues.length > 0 ? "orange" : "green",
        ),
        createSafeMetric(
          "high_priority",
          highPriorityOpen.length,
          " high priority",
          highPriorityOpen.length > 0 ? "red" : "green",
        ),
        createSafeMetric("total_issues", issues.length, " total", "green"),
      ],
      issueCount,
    }
  } catch (error) {
    console.error(`Error getting maintenance status for ${propertyId}:`, error)
    return {
      status: "green",
      metrics: [createSafeMetric("issues", "Error", "", "green")],
      issueCount: 0,
    }
  }
}

export async function getPropertyStatusSummary() {
  try {
    console.log("📊 Getting property status summary...")

    const statuses = await getDashboardStatuses()

    const totalProperties = statuses.length
    const healthyProperties = statuses.filter((p) => p.overallStatus === "green").length
    const warningProperties = statuses.filter((p) => p.overallStatus === "orange").length
    const criticalProperties = statuses.filter((p) => p.overallStatus === "red").length

    const summary = {
      totalProperties,
      healthyProperties,
      warningProperties,
      criticalProperties,
    }

    console.log("📊 Property summary:", summary)
    return summary
  } catch (error) {
    console.error("Error getting property status summary:", error)
    return {
      totalProperties: 0,
      healthyProperties: 0,
      warningProperties: 0,
      criticalProperties: 0,
    }
  }
}
