"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileText, Edit, Trash2, AlertTriangle, Loader2, RefreshCw } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { type MaintenanceIssue, deleteMaintenanceIssue, updateIssueStatus } from "@/app/actions/maintenance-issues"
import { MaintenanceIssueForm } from "./maintenance-issue-form"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RoleBasedUI } from "@/components/role-based-ui"
import { toast } from "@/components/ui/use-toast"

interface MaintenanceIssuesListProps {
  issues: MaintenanceIssue[]
  propertyId: string
  onRefresh: () => void
}

export function MaintenanceIssuesList({ issues, propertyId, onRefresh }: MaintenanceIssuesListProps) {
  const [selectedIssue, setSelectedIssue] = useState<MaintenanceIssue | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteError, setDeleteError] = useState("")
  const [statusUpdating, setStatusUpdating] = useState<Record<string, boolean>>({})

  const handleDelete = async (id: string) => {
    setDeleteLoading(true)
    setDeleteError("")

    try {
      const result = await deleteMaintenanceIssue(id, propertyId)
      if (result.success) {
        setIsDeleteDialogOpen(false)
        onRefresh()
      } else {
        setDeleteError(result.error || "An error occurred")
      }
    } catch (err) {
      setDeleteError("An unexpected error occurred")
      console.error(err)
    } finally {
      setDeleteLoading(false)
    }
  }

  const handleStatusChange = async (id: string, newStatus: string) => {
    setStatusUpdating((prev) => ({ ...prev, [id]: true }))

    try {
      const result = await updateIssueStatus(id, newStatus, propertyId)
      if (result.success) {
        toast({
          title: "Status updated",
          description: `Issue status changed to ${newStatus}`,
          variant: "default",
        })
        onRefresh()
      } else {
        toast({
          title: "Error updating status",
          description: result.error || "An error occurred",
          variant: "destructive",
        })
      }
    } catch (err) {
      toast({
        title: "Error updating status",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
      console.error(err)
    } finally {
      setStatusUpdating((prev) => ({ ...prev, [id]: false }))
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "Low":
        return <Badge variant="outline">Low</Badge>
      case "Medium":
        return <Badge variant="secondary">Medium</Badge>
      case "High":
        return (
          <Badge variant="default" className="bg-orange-500">
            High
          </Badge>
        )
      case "Emergency":
        return <Badge variant="destructive">Emergency</Badge>
      default:
        return <Badge variant="outline">{priority}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Open":
        return <Badge variant="outline">Open</Badge>
      case "In Process":
      case "In Progress":
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {status}
          </Badge>
        )
      case "On Hold":
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
            On Hold
          </Badge>
        )
      case "Resolved":
      case "Completed":
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            {status}
          </Badge>
        )
      case "Closed":
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800">
            Closed
          </Badge>
        )
      default:
        return <Badge variant="outline">{status || "Open"}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getRecurrenceBadge = (issue: MaintenanceIssue) => {
    if (!issue.is_recurring) return null

    return (
      <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
        <RefreshCw className="mr-1 h-3 w-3" />
        {issue.recurrence_period?.charAt(0).toUpperCase() + issue.recurrence_period?.slice(1) || "Recurring"}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Maintenance Issues
        </CardTitle>
        <CardDescription>Track the status of reported issues</CardDescription>
      </CardHeader>
      <CardContent>
        {issues.length === 0 ? (
          <div className="rounded-md bg-slate-50 p-8 text-center">
            <p className="text-slate-500">No maintenance issues found</p>
            <Button variant="outline" className="mt-4" onClick={() => onRefresh()}>
              Refresh
            </Button>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Issue</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Reported Date</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status / Type</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {issues.map((issue) => (
                  <TableRow key={issue.id}>
                    <TableCell className="font-medium">
                      {issue.issue_type}
                      {getRecurrenceBadge(issue)}
                    </TableCell>
                    <TableCell>{issue.category}</TableCell>
                    <TableCell>{formatDate(issue.issue_date)}</TableCell>
                    <TableCell>{getPriorityBadge(issue.priority)}</TableCell>
                    <TableCell>
                      {statusUpdating[issue.id as string] ? (
                        <div className="flex items-center space-x-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Updating...</span>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {/* Status and Type badges in a horizontal row */}
                          <div className="flex items-center gap-2 flex-wrap">
                            {getStatusBadge(issue.status || "Open")}
                            <Badge
                              variant="outline"
                              className={
                                issue.is_recurring
                                  ? "bg-purple-50 text-purple-700 border-purple-200 text-xs"
                                  : "bg-gray-50 text-gray-600 border-gray-200 text-xs"
                              }
                            >
                              {issue.is_recurring ? "Recurrent" : "One time"}
                            </Badge>
                          </div>

                          {/* Show dropdown only for admins */}
                          <RoleBasedUI roles={["admin", "maintenance_manager"]}>
                            <Select
                              value={issue.status || "Open"}
                              onValueChange={(value) => handleStatusChange(issue.id as string, value)}
                            >
                              <SelectTrigger className="w-[130px] h-8 text-xs">
                                <SelectValue placeholder="Change status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Open">Open</SelectItem>
                                <SelectItem value="In Process">In Process</SelectItem>
                                <SelectItem value="Completed">Completed</SelectItem>
                                <SelectItem value="In Progress">In Progress</SelectItem>
                                <SelectItem value="On Hold">On Hold</SelectItem>
                                <SelectItem value="Resolved">Resolved</SelectItem>
                                <SelectItem value="Closed">Closed</SelectItem>
                              </SelectContent>
                            </Select>
                          </RoleBasedUI>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedIssue(issue)
                            setIsViewDialogOpen(true)
                          }}
                        >
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedIssue(issue)
                            setIsEditDialogOpen(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            setSelectedIssue(issue)
                            setIsDeleteDialogOpen(true)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* View Issue Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Issue Details</DialogTitle>
              <DialogDescription>Detailed information about the maintenance issue</DialogDescription>
            </DialogHeader>
            {selectedIssue && (
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Issue Title</h3>
                    <p>{selectedIssue.issue_type}</p>
                  </div>
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Department</h3>
                    <p className="capitalize">{selectedIssue.category}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Start Date</h3>
                    <p>{formatDate(selectedIssue.issue_date)}</p>
                  </div>
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Expected End Date</h3>
                    <p>{selectedIssue.resolution_date ? formatDate(selectedIssue.resolution_date) : "Not specified"}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Priority</h3>
                    <p>{getPriorityBadge(selectedIssue.priority)}</p>
                  </div>
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Status</h3>
                    <p>{getStatusBadge(selectedIssue.status)}</p>
                  </div>
                </div>

                {/* Recurring information */}
                {selectedIssue.is_recurring && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="mb-1 text-sm font-medium">Recurrence</h3>
                      <p className="capitalize">{selectedIssue.recurrence_period || "Monthly"}</p>
                    </div>
                    {selectedIssue.next_due_date && (
                      <div>
                        <h3 className="mb-1 text-sm font-medium">Next Due Date</h3>
                        <p>{formatDate(selectedIssue.next_due_date)}</p>
                      </div>
                    )}
                  </div>
                )}

                <div>
                  <h3 className="mb-1 text-sm font-medium">Reported By</h3>
                  <p>{selectedIssue.reported_by}</p>
                </div>

                <div>
                  <h3 className="mb-1 text-sm font-medium">Remarks</h3>
                  <p className="whitespace-pre-wrap">{selectedIssue.remarks || "No remarks provided"}</p>
                </div>
              </div>
            )}
            <div className="flex justify-end">
              <DialogClose asChild>
                <Button variant="outline">Close</Button>
              </DialogClose>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Issue Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Issue</DialogTitle>
              <DialogDescription>Update the maintenance issue details</DialogDescription>
            </DialogHeader>
            {selectedIssue && (
              <MaintenanceIssueForm
                propertyId={propertyId}
                existingIssue={selectedIssue}
                onSuccess={() => {
                  setIsEditDialogOpen(false)
                  onRefresh()
                }}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Deletion</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this issue? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            {deleteError && (
              <div className="rounded-md bg-red-50 p-4 text-red-700">
                <div className="flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  <p>{deleteError}</p>
                </div>
              </div>
            )}
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => selectedIssue && handleDelete(selectedIssue.id as string)}
                disabled={deleteLoading}
              >
                {deleteLoading ? "Deleting..." : "Delete"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
