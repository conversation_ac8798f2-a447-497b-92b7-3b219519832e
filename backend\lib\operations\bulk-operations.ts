/**
 * Bulk operations with V1's performance patterns
 * Implements efficient batch processing with error resilience
 */

import { prisma } from '@/lib/prisma';
import { withBatchResilience, withDatabaseResilience } from '@/lib/resilience/retry-handler';

export interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    data: any;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
    successRate: number;
  };
}

export interface BulkAttendanceRecord {
  siteId: string;
  userId: string;
  date: Date;
  checkInTime?: string;
  checkOutTime?: string;
  hoursWorked?: number;
  notes?: string;
  recordedBy: string;
}

/**
 * Bulk attendance submission with V1's upsert pattern
 * Enhanced with better error handling and validation
 */
export async function bulkSubmitAttendance(
  records: BulkAttendanceRecord[]
): Promise<BulkOperationResult<any>> {
  const successful: any[] = [];
  const failed: Array<{ data: any; error: string }> = [];

  // Process in batches to avoid overwhelming the database
  const batchResults = await withBatchResilience(
    records,
    async (record) => {
      // Validate user exists
      const user = await prisma.user.findUnique({
        where: { id: record.userId },
        select: { id: true, fullName: true },
      });

      if (!user) {
        throw new Error(`User ${record.userId} not found`);
      }

      // Validate site exists
      const site = await prisma.site.findUnique({
        where: { id: record.siteId },
        select: { id: true, name: true },
      });

      if (!site) {
        throw new Error(`Site ${record.siteId} not found`);
      }

      // Upsert attendance record (V1 pattern)
      const attendanceRecord = await prisma.siteAttendance.upsert({
        where: {
          siteId_userId_date: {
            siteId: record.siteId,
            userId: record.userId,
            date: record.date,
          },
        },
        update: {
          checkInTime: record.checkInTime,
          checkOutTime: record.checkOutTime,
          hoursWorked: record.hoursWorked ? parseFloat(record.hoursWorked.toString()) : null,
          notes: record.notes,
          recordedBy: record.recordedBy,
          updatedAt: new Date(),
        },
        create: {
          siteId: record.siteId,
          userId: record.userId,
          date: record.date,
          checkInTime: record.checkInTime,
          checkOutTime: record.checkOutTime,
          hoursWorked: record.hoursWorked ? parseFloat(record.hoursWorked.toString()) : null,
          notes: record.notes,
          recordedBy: record.recordedBy,
        },
        include: {
          user: {
            select: { id: true, fullName: true },
          },
          site: {
            select: { id: true, name: true },
          },
        },
      });

      return attendanceRecord;
    },
    {
      batchSize: 10, // Process 10 records at a time
      continueOnError: true,
      retryOptions: {
        maxRetries: 2,
        baseDelay: 500,
      },
    }
  );

  // Separate successful and failed results
  batchResults.forEach(result => {
    if (result.error) {
      failed.push({
        data: result.item,
        error: result.error.message || 'Unknown error',
      });
    } else if (result.result) {
      successful.push(result.result);
    }
  });

  const total = records.length;
  const successfulCount = successful.length;
  const failedCount = failed.length;

  return {
    successful,
    failed,
    summary: {
      total,
      successful: successfulCount,
      failed: failedCount,
      successRate: total > 0 ? Math.round((successfulCount / total) * 100) : 0,
    },
  };
}

/**
 * Bulk maintenance issue creation
 */
export async function bulkCreateMaintenanceIssues(
  issues: Array<{
    propertyId: string;
    title: string;
    description: string;
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    serviceType?: string;
    department?: string;
    reportedBy: string;
    dueDate?: Date;
  }>
): Promise<BulkOperationResult<any>> {
  const successful: any[] = [];
  const failed: Array<{ data: any; error: string }> = [];

  const batchResults = await withBatchResilience(
    issues,
    async (issueData) => {
      // Validate property exists
      const property = await prisma.property.findUnique({
        where: { id: issueData.propertyId },
        select: { id: true, name: true },
      });

      if (!property) {
        throw new Error(`Property ${issueData.propertyId} not found`);
      }

      // Create maintenance issue
      const issue = await prisma.maintenanceIssue.create({
        data: {
          propertyId: issueData.propertyId,
          title: issueData.title,
          description: issueData.description,
          priority: issueData.priority,
          serviceType: issueData.serviceType,
          department: issueData.department,
          reportedBy: issueData.reportedBy,
          dueDate: issueData.dueDate,
          status: 'OPEN',
        },
        include: {
          property: {
            select: { id: true, name: true },
          },
          reporter: {
            select: { id: true, fullName: true },
          },
        },
      });

      return issue;
    },
    {
      batchSize: 5, // Smaller batches for complex operations
      continueOnError: true,
      retryOptions: {
        maxRetries: 3,
        baseDelay: 1000,
      },
    }
  );

  // Process results
  batchResults.forEach(result => {
    if (result.error) {
      failed.push({
        data: result.item,
        error: result.error.message || 'Unknown error',
      });
    } else if (result.result) {
      successful.push(result.result);
    }
  });

  const total = issues.length;
  const successfulCount = successful.length;
  const failedCount = failed.length;

  return {
    successful,
    failed,
    summary: {
      total,
      successful: successfulCount,
      failed: failedCount,
      successRate: total > 0 ? Math.round((successfulCount / total) * 100) : 0,
    },
  };
}

/**
 * Bulk property status updates
 */
export async function bulkUpdatePropertyStatuses(
  updates: Array<{
    propertyId: string;
    serviceType: string;
    status: 'OPERATIONAL' | 'WARNING' | 'CRITICAL' | 'MAINTENANCE';
    notes?: string;
    updatedBy: string;
  }>
): Promise<BulkOperationResult<any>> {
  const successful: any[] = [];
  const failed: Array<{ data: any; error: string }> = [];

  const batchResults = await withBatchResilience(
    updates,
    async (updateData) => {
      // Update or create property service status
      const propertyService = await prisma.propertyService.upsert({
        where: {
          propertyId_serviceType: {
            propertyId: updateData.propertyId,
            serviceType: updateData.serviceType,
          },
        },
        update: {
          status: updateData.status,
          notes: updateData.notes,
          lastChecked: new Date(),
        },
        create: {
          propertyId: updateData.propertyId,
          serviceType: updateData.serviceType,
          status: updateData.status,
          notes: updateData.notes,
          lastChecked: new Date(),
        },
        include: {
          property: {
            select: { id: true, name: true },
          },
        },
      });

      return propertyService;
    },
    {
      batchSize: 15, // Larger batches for simple updates
      continueOnError: true,
      retryOptions: {
        maxRetries: 2,
        baseDelay: 300,
      },
    }
  );

  // Process results
  batchResults.forEach(result => {
    if (result.error) {
      failed.push({
        data: result.item,
        error: result.error.message || 'Unknown error',
      });
    } else if (result.result) {
      successful.push(result.result);
    }
  });

  const total = updates.length;
  const successfulCount = successful.length;
  const failedCount = failed.length;

  return {
    successful,
    failed,
    summary: {
      total,
      successful: successfulCount,
      failed: failedCount,
      successRate: total > 0 ? Math.round((successfulCount / total) * 100) : 0,
    },
  };
}

/**
 * Batch fetch operations with resilience
 */
export async function batchFetchMaintenanceIssues(
  propertyIds: string[]
): Promise<Record<string, any[]>> {
  const result: Record<string, any[]> = {};

  // Initialize all properties with empty arrays
  propertyIds.forEach(id => {
    result[id] = [];
  });

  const batchResults = await withBatchResilience(
    propertyIds,
    async (propertyId) => {
      const issues = await withDatabaseResilience(
        () => prisma.maintenanceIssue.findMany({
          where: { propertyId },
          include: {
            property: {
              select: { name: true },
            },
            reporter: {
              select: { fullName: true },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        [] // Fallback to empty array
      );

      return { propertyId, issues };
    },
    {
      batchSize: 5, // Conservative batch size for complex queries
      continueOnError: true,
      retryOptions: {
        maxRetries: 3,
        baseDelay: 1000,
      },
    }
  );

  // Populate results
  batchResults.forEach(batchResult => {
    if (batchResult.result) {
      const { propertyId, issues } = batchResult.result;
      result[propertyId] = issues;
    } else if (batchResult.error) {
      console.warn(`Failed to fetch maintenance issues for property ${batchResult.item}:`, batchResult.error);
      // Keep empty array as fallback
    }
  });

  return result;
}

/**
 * Batch fetch with aggregation (V1 pattern)
 */
export async function batchFetchPropertyStatistics(
  propertyIds: string[]
): Promise<Record<string, {
  maintenanceIssues: { total: number; open: number; critical: number };
  services: { total: number; operational: number; warning: number; critical: number };
  lastUpdated: Date;
}>> {
  const result: Record<string, any> = {};

  const batchResults = await withBatchResilience(
    propertyIds,
    async (propertyId) => {
      // Fetch maintenance statistics
      const maintenanceStats = await prisma.maintenanceIssue.groupBy({
        by: ['status', 'priority'],
        where: { propertyId },
        _count: { id: true },
      });

      // Fetch service statistics
      const serviceStats = await prisma.propertyService.groupBy({
        by: ['status'],
        where: { propertyId },
        _count: { id: true },
      });

      // Process maintenance stats
      const maintenanceIssues = {
        total: maintenanceStats.reduce((sum, stat) => sum + stat._count.id, 0),
        open: maintenanceStats
          .filter(stat => stat.status === 'OPEN')
          .reduce((sum, stat) => sum + stat._count.id, 0),
        critical: maintenanceStats
          .filter(stat => stat.priority === 'CRITICAL')
          .reduce((sum, stat) => sum + stat._count.id, 0),
      };

      // Process service stats
      const services = {
        total: serviceStats.reduce((sum, stat) => sum + stat._count.id, 0),
        operational: serviceStats
          .filter(stat => stat.status === 'OPERATIONAL')
          .reduce((sum, stat) => sum + stat._count.id, 0),
        warning: serviceStats
          .filter(stat => stat.status === 'WARNING')
          .reduce((sum, stat) => sum + stat._count.id, 0),
        critical: serviceStats
          .filter(stat => stat.status === 'CRITICAL')
          .reduce((sum, stat) => sum + stat._count.id, 0),
      };

      return {
        propertyId,
        stats: {
          maintenanceIssues,
          services,
          lastUpdated: new Date(),
        },
      };
    },
    {
      batchSize: 8, // Moderate batch size for aggregations
      continueOnError: true,
      retryOptions: {
        maxRetries: 2,
        baseDelay: 800,
      },
    }
  );

  // Populate results with fallback values
  batchResults.forEach(batchResult => {
    if (batchResult.result) {
      const { propertyId, stats } = batchResult.result;
      result[propertyId] = stats;
    } else {
      // Provide fallback statistics
      result[batchResult.item] = {
        maintenanceIssues: { total: 0, open: 0, critical: 0 },
        services: { total: 0, operational: 0, warning: 0, critical: 0 },
        lastUpdated: new Date(),
      };
    }
  });

  return result;
}
