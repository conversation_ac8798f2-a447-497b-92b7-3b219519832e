import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Motors & Pumps tab - Water pumps, generators, and motor controls
class MotorsPumpsTab extends StatefulWidget {
  const MotorsPumpsTab({super.key});

  @override
  State<MotorsPumpsTab> createState() => _MotorsPumpsTabState();
}

class _MotorsPumpsTabState extends State<MotorsPumpsTab> {
  final List<MotorDevice> _motors = [
    MotorDevice(
      id: 'motor_001',
      name: 'Main Water Pump',
      type: MotorType.waterPump,
      location: 'Building A - Basement',
      status: MotorStatus.running,
      powerConsumption: 2.5,
      flowRate: 150.0,
      pressure: 45.0,
      temperature: 68.0,
      runningHours: 1250,
      isAutoMode: true,
      waterLevel: 85.0,
    ),
    MotorDevice(
      id: 'motor_002',
      name: 'Backup Generator',
      type: MotorType.generator,
      location: 'Building B - Generator Room',
      status: MotorStatus.standby,
      powerConsumption: 0.0,
      fuelLevel: 75.0,
      temperature: 32.0,
      runningHours: 450,
      isAutoMode: true,
    ),
    MotorDevice(
      id: 'motor_003',
      name: 'Circulation Pump',
      type: MotorType.circulationPump,
      location: 'Building A - Roof',
      status: MotorStatus.running,
      powerConsumption: 1.2,
      flowRate: 80.0,
      pressure: 25.0,
      temperature: 55.0,
      runningHours: 2100,
      isAutoMode: false,
    ),
    MotorDevice(
      id: 'motor_004',
      name: 'Sewage Pump',
      type: MotorType.sewagePump,
      location: 'Building A - Basement',
      status: MotorStatus.maintenance,
      powerConsumption: 0.0,
      temperature: 28.0,
      runningHours: 890,
      isAutoMode: false,
    ),
    MotorDevice(
      id: 'motor_005',
      name: 'Fire Pump',
      type: MotorType.firePump,
      location: 'Building B - Fire Station',
      status: MotorStatus.standby,
      powerConsumption: 0.0,
      pressure: 60.0,
      temperature: 30.0,
      runningHours: 25,
      isAutoMode: true,
    ),
    MotorDevice(
      id: 'motor_006',
      name: 'HVAC Blower',
      type: MotorType.hvacMotor,
      location: 'Building A - HVAC Room',
      status: MotorStatus.running,
      powerConsumption: 3.8,
      temperature: 72.0,
      runningHours: 3200,
      isAutoMode: true,
      airFlow: 2500.0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildControlPanel(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildQuickStats(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildMotorsList(context),
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Control Panel',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _startAllMotors,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Start All'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _stopAllMotors,
                    icon: const Icon(Icons.stop),
                    label: const Text('Stop All'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _enableAutoMode,
                    icon: const Icon(Icons.auto_mode),
                    label: const Text('Auto Mode'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    final runningCount = _motors.where((m) => m.status == MotorStatus.running).length;
    final totalPower = _motors.fold(0.0, (sum, motor) => sum + motor.powerConsumption);
    final maintenanceCount = _motors.where((m) => m.status == MotorStatus.maintenance).length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Running',
            '$runningCount/${_motors.length}',
            Icons.power,
            Colors.green,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildStatCard(
            'Power Usage',
            '${totalPower.toStringAsFixed(1)} kW',
            Icons.electrical_services,
            Colors.blue,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildStatCard(
            'Maintenance',
            maintenanceCount.toString(),
            Icons.build,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMotorsList(BuildContext context) {
    return ListView.separated(
      itemCount: _motors.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConstants.smallPadding),
      itemBuilder: (context, index) {
        return _buildMotorCard(_motors[index]);
      },
    );
  }

  Widget _buildMotorCard(MotorDevice motor) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getStatusColor(motor.status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getMotorIcon(motor.type),
            color: _getStatusColor(motor.status),
            size: 24,
          ),
        ),
        title: Text(
          motor.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(motor.location),
            const SizedBox(height: 2),
            Row(
              children: [
                _buildStatusChip(motor.status),
                const SizedBox(width: 8),
                if (motor.isAutoMode)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'AUTO',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        trailing: _buildMotorControls(motor),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: _buildMotorDetails(motor),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(MotorStatus status) {
    Color color = _getStatusColor(status);
    String text = status.name.toUpperCase();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  Widget _buildMotorControls(MotorDevice motor) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (motor.status == MotorStatus.stopped || motor.status == MotorStatus.standby)
          IconButton(
            icon: const Icon(Icons.play_arrow, color: Colors.green),
            onPressed: () => _startMotor(motor),
            tooltip: 'Start Motor',
          ),
        if (motor.status == MotorStatus.running)
          IconButton(
            icon: const Icon(Icons.stop, color: Colors.red),
            onPressed: () => _stopMotor(motor),
            tooltip: 'Stop Motor',
          ),
        IconButton(
          icon: Icon(
            motor.isAutoMode ? Icons.auto_mode : Icons.settings,
            color: motor.isAutoMode ? Colors.blue : Colors.grey,
          ),
          onPressed: () => _toggleAutoMode(motor),
          tooltip: motor.isAutoMode ? 'Disable Auto Mode' : 'Enable Auto Mode',
        ),
      ],
    );
  }

  Widget _buildMotorDetails(MotorDevice motor) {
    return Column(
      children: [
        _buildDetailRow('Power Consumption', '${motor.powerConsumption} kW'),
        if (motor.flowRate != null)
          _buildDetailRow('Flow Rate', '${motor.flowRate} L/min'),
        if (motor.pressure != null)
          _buildDetailRow('Pressure', '${motor.pressure} PSI'),
        if (motor.temperature != null)
          _buildDetailRow('Temperature', '${motor.temperature}°C'),
        if (motor.waterLevel != null)
          _buildDetailRow('Water Level', '${motor.waterLevel}%'),
        if (motor.fuelLevel != null)
          _buildDetailRow('Fuel Level', '${motor.fuelLevel}%'),
        if (motor.airFlow != null)
          _buildDetailRow('Air Flow', '${motor.airFlow} CFM'),
        _buildDetailRow('Running Hours', '${motor.runningHours} hrs'),
        const SizedBox(height: AppConstants.smallPadding),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showMotorSettings(motor),
                icon: const Icon(Icons.settings, size: 16),
                label: const Text('Settings'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.shade200,
                  foregroundColor: Colors.grey.shade700,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showMotorHistory(motor),
                icon: const Icon(Icons.history, size: 16),
                label: const Text('History'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade100,
                  foregroundColor: Colors.blue.shade700,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(color: Colors.grey.shade700),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(MotorStatus status) {
    switch (status) {
      case MotorStatus.running:
        return Colors.green;
      case MotorStatus.stopped:
        return Colors.red;
      case MotorStatus.standby:
        return Colors.blue;
      case MotorStatus.maintenance:
        return Colors.orange;
      case MotorStatus.error:
        return Colors.red.shade700;
    }
  }

  IconData _getMotorIcon(MotorType type) {
    switch (type) {
      case MotorType.waterPump:
        return Icons.water_drop;
      case MotorType.generator:
        return Icons.power;
      case MotorType.circulationPump:
        return Icons.loop;
      case MotorType.sewagePump:
        return Icons.cleaning_services;
      case MotorType.firePump:
        return Icons.local_fire_department;
      case MotorType.hvacMotor:
        return Icons.air;
    }
  }

  void _startAllMotors() {
    setState(() {
      for (var motor in _motors) {
        if (motor.status == MotorStatus.stopped || motor.status == MotorStatus.standby) {
          motor.status = MotorStatus.running;
          motor.powerConsumption = _getDefaultPowerConsumption(motor.type);
        }
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Starting all motors...')),
    );
  }

  void _stopAllMotors() {
    setState(() {
      for (var motor in _motors) {
        if (motor.status == MotorStatus.running) {
          motor.status = MotorStatus.stopped;
          motor.powerConsumption = 0.0;
        }
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Stopping all motors...')),
    );
  }

  void _enableAutoMode() {
    setState(() {
      for (var motor in _motors) {
        motor.isAutoMode = true;
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Auto mode enabled for all motors')),
    );
  }

  void _startMotor(MotorDevice motor) {
    setState(() {
      motor.status = MotorStatus.running;
      motor.powerConsumption = _getDefaultPowerConsumption(motor.type);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${motor.name} started')),
    );
  }

  void _stopMotor(MotorDevice motor) {
    setState(() {
      motor.status = MotorStatus.stopped;
      motor.powerConsumption = 0.0;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${motor.name} stopped')),
    );
  }

  void _toggleAutoMode(MotorDevice motor) {
    setState(() {
      motor.isAutoMode = !motor.isAutoMode;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${motor.name} auto mode ${motor.isAutoMode ? 'enabled' : 'disabled'}',
        ),
      ),
    );
  }

  double _getDefaultPowerConsumption(MotorType type) {
    switch (type) {
      case MotorType.waterPump:
        return 2.5;
      case MotorType.generator:
        return 15.0;
      case MotorType.circulationPump:
        return 1.2;
      case MotorType.sewagePump:
        return 1.8;
      case MotorType.firePump:
        return 5.0;
      case MotorType.hvacMotor:
        return 3.8;
    }
  }

  void _showMotorSettings(MotorDevice motor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${motor.name} Settings'),
        content: const Text('Motor configuration options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showMotorHistory(MotorDevice motor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${motor.name} History'),
        content: const Text('Motor operation history coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Motor device data model
class MotorDevice {
  final String id;
  final String name;
  final MotorType type;
  final String location;
  MotorStatus status;
  double powerConsumption;
  final double? flowRate;
  final double? pressure;
  final double? temperature;
  final double? waterLevel;
  final double? fuelLevel;
  final double? airFlow;
  final int runningHours;
  bool isAutoMode;

  MotorDevice({
    required this.id,
    required this.name,
    required this.type,
    required this.location,
    required this.status,
    required this.powerConsumption,
    this.flowRate,
    this.pressure,
    this.temperature,
    this.waterLevel,
    this.fuelLevel,
    this.airFlow,
    required this.runningHours,
    required this.isAutoMode,
  });
}

/// Motor type enum
enum MotorType {
  waterPump,
  generator,
  circulationPump,
  sewagePump,
  firePump,
  hvacMotor,
}

/// Motor status enum
enum MotorStatus {
  running,
  stopped,
  standby,
  maintenance,
  error,
}
