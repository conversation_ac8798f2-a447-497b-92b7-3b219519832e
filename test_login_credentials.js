// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://13.204.54.130:3000';
const LOGIN_ENDPOINT = '/api/auth/login';

// Test credentials from the Flutter Quick Login Profiles
const testCredentials = [
  { label: 'Admin User', email: '<EMAIL>', password: 'admin123' },
  { label: 'Property Manager', email: '<EMAIL>', password: 'admin123' },
  { label: 'Maintenance Staff', email: '<EMAIL>', password: 'admin123' },
  { label: 'Security Guard', email: '<EMAIL>', password: 'admin123' },
  { label: 'Office Manager', email: '<EMAIL>', password: 'admin123' },
  { label: 'Site Supervisor', email: '<EMAIL>', password: 'admin123' },

  // Additional test credentials that might exist
  { label: 'Househelp', email: '<EMAIL>', password: 'admin123' },
  { label: 'Worker 1', email: '<EMAIL>', password: 'admin123' },
  { label: 'Worker 2', email: '<EMAIL>', password: 'admin123' },
  { label: 'Staff 1', email: '<EMAIL>', password: 'admin123' },

  // Test with original passwords in case they're different
  { label: 'Property Manager (orig)', email: '<EMAIL>', password: 'manager123' },
  { label: 'Supervisor (orig)', email: '<EMAIL>', password: 'supervisor123' },
  { label: 'Regular User (orig)', email: '<EMAIL>', password: 'user123' },
];

async function testLogin(email, password) {
  try {
    const response = await fetch(`${BASE_URL}${LOGIN_ENDPOINT}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: email,
        password: password,
        loginType: 'email'
      }),
    });

    const data = await response.json();

    return {
      success: response.ok,
      status: response.status,
      data: data,
      error: response.ok ? null : data.message || data.error || 'Unknown error'
    };
  } catch (error) {
    return {
      success: false,
      status: 0,
      data: null,
      error: error.message
    };
  }
}

async function runTests() {
  console.log('🔐 Testing login credentials against remote backend...\n');
  console.log(`Backend URL: ${BASE_URL}${LOGIN_ENDPOINT}\n`);

  const results = [];

  for (const cred of testCredentials) {
    console.log(`Testing: ${cred.label} (${cred.email})`);

    const result = await testLogin(cred.email, cred.password);
    results.push({
      ...cred,
      ...result
    });

    if (result.success) {
      console.log(`✅ SUCCESS: ${cred.label}`);
      if (result.data && result.data.user) {
        console.log(`   User: ${result.data.user.fullName || result.data.user.full_name}`);
        console.log(`   Roles: ${JSON.stringify(result.data.user.roles || [])}`);
      }
    } else {
      console.log(`❌ FAILED: ${cred.label} - ${result.error}`);
    }
    console.log('');

    // Add small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n📊 SUMMARY:');
  console.log('='.repeat(50));

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log(`✅ Successful logins: ${successful.length}`);
  successful.forEach(r => {
    console.log(`   - ${r.label}: ${r.email}`);
  });

  console.log(`\n❌ Failed logins: ${failed.length}`);
  failed.forEach(r => {
    console.log(`   - ${r.label}: ${r.email} (${r.error})`);
  });

  console.log('\n🔧 RECOMMENDED FLUTTER QUICK LOGIN PROFILES:');
  console.log('='.repeat(50));
  successful.forEach(r => {
    console.log(`_buildQuickLoginButton(`);
    console.log(`  '${r.label}',`);
    console.log(`  '${r.email}',`);
    console.log(`  '${r.password}',`);
    console.log(`  Icons.admin_panel_settings, // Update icon as needed`);
    console.log(`  Colors.blue, // Update color as needed`);
    console.log(`),`);
    console.log('');
  });
}

runTests().catch(console.error);
