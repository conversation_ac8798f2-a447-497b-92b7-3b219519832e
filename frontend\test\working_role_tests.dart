import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

// Import core auth models and providers
import 'package:srsr_property_management/core/auth/models/user_role.dart' as core_models;
import 'package:srsr_property_management/features/auth/presentation/providers/auth_providers.dart';
import 'package:srsr_property_management/core/auth/widgets/role_based_widget.dart';

// Import models
import 'package:srsr_property_management/shared/models/user.dart';

/// Working role-based tests that use the existing admin user
/// and test role-based functionality without creating new data
void main() {
  group('Working Role-Based Tests', () {
    late Dio dio;
    late ProviderContainer container;

    // Test data
    String? adminToken;
    User? adminUser;

    setUpAll(() async {
      // Configure Dio for real API calls
      dio = Dio(BaseOptions(
        baseUrl: 'http://192.168.1.3:3000',
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {'Content-Type': 'application/json'},
      ));

      // Add logging interceptor
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('[ROLE_TEST] $obj'),
      ));
    });

    setUp(() {
      // Create fresh provider container for each test
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Admin Authentication and Role Testing', () {
      test('authenticate with existing admin user', () async {
        try {
          final response = await dio.post('/api/auth/login', data: {
            'email': '<EMAIL>',
            'password': 'admin123',
          });

          expect(response.statusCode, 200);
          
          final data = response.data;
          adminToken = data['token'];
          
          final userData = data['user'];
          adminUser = User(
            id: userData['id'],
            email: userData['email'],
            fullName: userData['full_name'],
            roles: List<String>.from(userData['roles']),
            isActive: userData['is_active'],
            createdAt: DateTime.parse(userData['created_at']),
          );

          expect(adminToken, isNotNull);
          expect(adminUser!.roles, contains('admin'));

          print('✅ Admin authenticated successfully');
          print('   Email: ${adminUser!.email}');
          print('   Roles: ${adminUser!.roles}');
          print('   Token: ${adminToken!.substring(0, 20)}...');
        } catch (e) {
          fail('❌ Admin authentication failed: $e');
        }
      });

      test('verify admin has all expected permissions', () async {
        if (adminUser == null) {
          fail('❌ Admin user not available');
        }

        // Test that admin role has all expected permissions
        final adminRole = core_models.UserRole.admin;
        
        final expectedPermissions = [
          'users.create',
          'users.read',
          'users.update',
          'users.delete',
          'properties.create',
          'properties.read',
          'properties.update',
          'properties.delete',
          'maintenance.create',
          'maintenance.read',
          'maintenance.update',
          'maintenance.delete',
          'roles.manage',
          'permissions.manage',
          'settings.configure',
          'reports.generate',
        ];

        for (final permission in expectedPermissions) {
          final hasPermission = adminRole.hasPermission(permission);
          expect(hasPermission, true, reason: 'Admin should have $permission');
        }

        print('✅ Admin role has all expected permissions');
      });

      test('verify admin can access all screens', () async {
        if (adminUser == null) {
          fail('❌ Admin user not available');
        }

        final adminRole = core_models.UserRole.admin;
        
        final screenPaths = [
          '/admin',
          '/users',
          '/roles',
          '/settings',
          '/properties',
          '/maintenance',
          '/attendance',
          '/fuel',
          '/reports',
          '/dashboard',
        ];

        for (final path in screenPaths) {
          final hasAccess = adminRole.canAccessScreen(path);
          expect(hasAccess, true, reason: 'Admin should access $path');
        }

        print('✅ Admin can access all screens');
      });
    });

    group('Role-Based Widget Tests', () {
      testWidgets('Admin role shows admin-only content', (WidgetTester tester) async {
        if (adminUser == null) {
          markTestSkipped('Admin user not available');
          return;
        }

        // Create auth state with admin user
        final authState = AuthState(
          isAuthenticated: true,
          user: adminUser!,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => 
                AuthNotifier(ref.read(authRepositoryProvider))..state = authState),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test admin-only widget
                    RoleBasedWidget(
                      allowedRoles: const [core_models.UserRole.admin],
                      child: const Text('Admin Only Content'),
                      fallback: const Text('Access Denied'),
                    ),
                    // Test permission-based widget
                    RoleBasedWidget(
                      requiredPermissions: const ['users.create'],
                      child: const Text('Create User Button'),
                      fallback: const Text('No Permission'),
                    ),
                    // Test properties access
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties Access'),
                      fallback: const Text('No Properties Access'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Admin should see all content
        expect(find.text('Admin Only Content'), findsOneWidget);
        expect(find.text('Create User Button'), findsOneWidget);
        expect(find.text('Properties Access'), findsOneWidget);

        // Should not see fallback content
        expect(find.text('Access Denied'), findsNothing);
        expect(find.text('No Permission'), findsNothing);
        expect(find.text('No Properties Access'), findsNothing);

        print('✅ Admin role widget test passed');
      });

      testWidgets('Manager role shows limited content', (WidgetTester tester) async {
        // Create a mock manager user for testing
        final managerUser = User(
          id: 'test-manager-id',
          email: '<EMAIL>',
          fullName: 'Test Manager',
          roles: ['manager'],
          isActive: true,
          createdAt: DateTime.now(),
        );

        final authState = AuthState(
          isAuthenticated: true,
          user: managerUser,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => 
                AuthNotifier(ref.read(authRepositoryProvider))..state = authState),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test admin-only widget (should be denied)
                    RoleBasedWidget(
                      allowedRoles: const [core_models.UserRole.admin],
                      child: const Text('Admin Only Content'),
                      fallback: const Text('Access Denied'),
                    ),
                    // Test properties access (should be allowed)
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties Access'),
                      fallback: const Text('No Properties Access'),
                    ),
                    // Test user creation (should be denied)
                    RoleBasedWidget(
                      requiredPermissions: const ['users.create'],
                      child: const Text('Create User Button'),
                      fallback: const Text('No Permission'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Manager should NOT see admin content
        expect(find.text('Admin Only Content'), findsNothing);
        expect(find.text('Access Denied'), findsOneWidget);

        // Manager should see properties access
        expect(find.text('Properties Access'), findsOneWidget);
        expect(find.text('No Properties Access'), findsNothing);

        // Manager should NOT see user creation
        expect(find.text('Create User Button'), findsNothing);
        expect(find.text('No Permission'), findsOneWidget);

        print('✅ Manager role widget test passed');
      });

      testWidgets('Viewer role shows minimal content', (WidgetTester tester) async {
        // Create a mock viewer user for testing
        final viewerUser = User(
          id: 'test-viewer-id',
          email: '<EMAIL>',
          fullName: 'Test Viewer',
          roles: ['viewer'],
          isActive: true,
          createdAt: DateTime.now(),
        );

        final authState = AuthState(
          isAuthenticated: true,
          user: viewerUser,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => 
                AuthNotifier(ref.read(authRepositoryProvider))..state = authState),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test admin-only widget (should be denied)
                    RoleBasedWidget(
                      allowedRoles: const [core_models.UserRole.admin],
                      child: const Text('Admin Only Content'),
                      fallback: const Text('Access Denied'),
                    ),
                    // Test read-only access
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties View'),
                      fallback: const Text('No Properties Access'),
                    ),
                    // Test creation permissions (should be denied)
                    RoleBasedWidget(
                      requiredPermissions: const ['properties.create'],
                      child: const Text('Create Property Button'),
                      fallback: const Text('No Create Permission'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Viewer should NOT see admin content
        expect(find.text('Admin Only Content'), findsNothing);
        expect(find.text('Access Denied'), findsOneWidget);

        // Viewer should see read-only content
        expect(find.text('Properties View'), findsOneWidget);
        expect(find.text('No Properties Access'), findsNothing);

        // Viewer should NOT see creation buttons
        expect(find.text('Create Property Button'), findsNothing);
        expect(find.text('No Create Permission'), findsOneWidget);

        print('✅ Viewer role widget test passed');
      });
    });

    group('API Integration Tests', () {
      test('admin can access protected endpoints', () async {
        if (adminToken == null) {
          fail('❌ Admin token not available');
        }

        dio.options.headers['Authorization'] = 'Bearer $adminToken';

        try {
          // Test users endpoint
          final usersResponse = await dio.get('/api/users');
          expect(usersResponse.statusCode, 200);
          print('✅ Admin can access /api/users');

          // Test roles endpoint
          final rolesResponse = await dio.get('/api/roles');
          expect(rolesResponse.statusCode, 200);
          print('✅ Admin can access /api/roles');

          // Test properties endpoint
          final propertiesResponse = await dio.get('/api/properties');
          expect(propertiesResponse.statusCode, 200);
          print('✅ Admin can access /api/properties');

          // Test maintenance endpoint
          final maintenanceResponse = await dio.get('/api/maintenance');
          expect(maintenanceResponse.statusCode, 200);
          print('✅ Admin can access /api/maintenance');

        } catch (e) {
          fail('❌ Admin API access failed: $e');
        }
      });

      test('unauthenticated requests are denied', () async {
        // Remove auth header
        dio.options.headers.remove('Authorization');

        try {
          await dio.get('/api/users');
          fail('❌ Unauthenticated request should have been denied');
        } catch (e) {
          if (e is DioException && e.response?.statusCode == 401) {
            print('✅ Unauthenticated request correctly denied');
          } else {
            fail('❌ Unexpected error: $e');
          }
        }
      });
    });
  });
}
