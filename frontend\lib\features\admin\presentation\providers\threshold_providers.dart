import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../data/threshold_api_service.dart';
import '../../data/threshold_repository_impl.dart';
import '../../domain/threshold_repository.dart';

// API Service Provider
final thresholdApiServiceProvider = Provider<ThresholdApiService>((ref) {
  return ThresholdApiService(DioClient.instance.dio);
});

// Repository Provider
final thresholdRepositoryProvider = Provider<ThresholdRepository>((ref) {
  return ThresholdRepositoryImpl(ref.watch(thresholdApiServiceProvider));
});

// Threshold Configuration Provider
final thresholdConfigProvider = StateNotifierProvider<ThresholdConfigNotifier, AsyncValue<List<ThresholdConfig>>>((ref) {
  return ThresholdConfigNotifier(ref.watch(thresholdRepositoryProvider));
});

class ThresholdConfigNotifier extends StateNotifier<AsyncValue<List<ThresholdConfig>>> {
  final ThresholdRepository _repository;

  ThresholdConfigNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadThresholds();
  }

  Future<void> loadThresholds() async {
    try {
      state = const AsyncValue.loading();
      final thresholds = await _repository.getThresholds();
      state = AsyncValue.data(thresholds);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> updateThresholds(List<ThresholdConfig> thresholds) async {
    try {
      await _repository.updateThresholds(thresholds);
      // Reload thresholds after update
      await loadThresholds();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> resetThresholds() async {
    try {
      await _repository.resetThresholds();
      await loadThresholds();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
