import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/generator_fuel.dart';
import '../providers/fuel_providers.dart';
import '../widgets/fuel_level_card.dart';
import '../widgets/fuel_chart_card.dart';
import '../widgets/add_fuel_log_dialog.dart';
import '../widgets/fuel_insights_card.dart';

class FuelMonitoringScreen extends ConsumerStatefulWidget {
  const FuelMonitoringScreen({super.key});

  @override
  ConsumerState<FuelMonitoringScreen> createState() => _FuelMonitoringScreenState();
}

class _FuelMonitoringScreenState extends ConsumerState<FuelMonitoringScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedProperty = 'all';
  String _timeRange = '7d'; // 7d, 30d, 90d

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const RoleBasedAppBar(
        title: 'Fuel Monitoring',
        showRoleIndicator: true,
      ),
      body: PermissionWidget(
        permission: 'fuel.read',
        fallback: _buildNoPermissionView(),
        child: Column(
          children: [
            // Filters Section
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  // Property Filter
                  Expanded(
                    child: _buildPropertyFilter(),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),

                  // Time Range Filter
                  _buildTimeRangeFilter(),
                ],
              ),
            ),

            // Tab Bar
            TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Current Levels'),
                Tab(text: 'Trends'),
                Tab(text: 'Logs'),
              ],
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCurrentLevelsTab(),
                  _buildTrendsTab(),
                  _buildLogsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: PermissionWidget(
        permission: 'fuel.create',
        child: RoleBasedFAB(
          requiredPermissions: const ['fuel.create'],
          onPressed: () => _showAddFuelLogDialog(),
          tooltip: 'Add Fuel Log',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to view fuel monitoring.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyFilter() {
    final propertiesAsync = ref.watch(propertiesProvider);

    return propertiesAsync.when(
      data: (properties) => DropdownButtonFormField<String>(
        value: _selectedProperty,
        decoration: const InputDecoration(
          labelText: 'Property',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.business),
        ),
        items: [
          const DropdownMenuItem(value: 'all', child: Text('All Properties')),
          ...properties.map((property) => DropdownMenuItem(
            value: property.id,
            child: Text(property.name),
          )),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _selectedProperty = value;
            });
          }
        },
      ),
      loading: () => const CircularProgressIndicator(),
      error: (_, __) => const Text('Failed to load properties'),
    );
  }

  Widget _buildTimeRangeFilter() {
    return DropdownButton<String>(
      value: _timeRange,
      items: const [
        DropdownMenuItem(value: '7d', child: Text('7 Days')),
        DropdownMenuItem(value: '30d', child: Text('30 Days')),
        DropdownMenuItem(value: '90d', child: Text('90 Days')),
      ],
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _timeRange = value;
          });
        }
      },
    );
  }

  Widget _buildCurrentLevelsTab() {
    final fuelLogsAsync = _selectedProperty == 'all'
        ? ref.watch(allFuelLogsProvider)
        : ref.watch(fuelLogsByPropertyProvider(_selectedProperty));

    return fuelLogsAsync.when(
      data: (logs) {
        if (logs.isEmpty) {
          return _buildEmptyState('No fuel data available');
        }

        // Group by property and get latest log for each
        final latestLogs = <String, GeneratorFuelLog>{};
        for (final log in logs) {
          if (!latestLogs.containsKey(log.propertyId) ||
              log.recordedAt.isAfter(latestLogs[log.propertyId]!.recordedAt)) {
            latestLogs[log.propertyId] = log;
          }
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(allFuelLogsProvider);
          },
          child: ListView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            children: [
              // Critical Alerts Section
              _buildCriticalAlertsSection(latestLogs.values.toList()),
              const SizedBox(height: AppConstants.defaultPadding),

              // Fuel Level Cards
              ...latestLogs.values.map((log) => Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: FuelLevelCard(
                  fuelLog: log,
                  onTap: () => _showFuelDetails(log),
                ),
              )),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildTrendsTab() {
    final fuelLogsAsync = _selectedProperty == 'all'
        ? ref.watch(fuelLogsTrendProvider(_timeRange))
        : ref.watch(fuelLogsTrendByPropertyProvider((_selectedProperty, _timeRange)));

    return fuelLogsAsync.when(
      data: (logs) {
        if (logs.isEmpty) {
          return _buildEmptyState('No trend data available');
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(fuelLogsTrendProvider(_timeRange));
          },
          child: ListView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            children: [
              // Fuel Level Trend Chart
              FuelChartCard(
                title: 'Fuel Level Trend',
                fuelLogs: logs,
                chartType: FuelChartType.level,
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Consumption Rate Chart
              FuelChartCard(
                title: 'Consumption Rate',
                fuelLogs: logs,
                chartType: FuelChartType.consumption,
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Efficiency Chart
              FuelChartCard(
                title: 'Generator Efficiency',
                fuelLogs: logs,
                chartType: FuelChartType.efficiency,
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Insights Card
              FuelInsightsCard(fuelLogs: logs),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildLogsTab() {
    final fuelLogsAsync = _selectedProperty == 'all'
        ? ref.watch(allFuelLogsProvider)
        : ref.watch(fuelLogsByPropertyProvider(_selectedProperty));

    return fuelLogsAsync.when(
      data: (logs) {
        if (logs.isEmpty) {
          return _buildEmptyState('No fuel logs available');
        }

        // Sort logs by date (newest first)
        final sortedLogs = List<GeneratorFuelLog>.from(logs)
          ..sort((a, b) => b.recordedAt.compareTo(a.recordedAt));

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(allFuelLogsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: sortedLogs.length,
            itemBuilder: (context, index) {
              final log = sortedLogs[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: _buildFuelLogCard(log),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildCriticalAlertsSection(List<GeneratorFuelLog> logs) {
    final criticalLogs = logs.where((log) => log.isCritical).toList();

    if (criticalLogs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Critical Fuel Alerts',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.red[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            ...criticalLogs.map((log) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '• Property ${log.propertyId}: ${log.fuelLevelLiters}L (${log.backupHoursDisplay} backup)',
                style: TextStyle(color: Colors.red[700]),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildFuelLogCard(GeneratorFuelLog log) {
    return Card(
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: log.statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.local_gas_station,
            color: log.statusColor,
          ),
        ),
        title: Text('${log.fuelLevelLiters}L'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Property: ${log.propertyId}'),
            Text('Recorded: ${log.ageDescription}'),
            if (log.consumptionRate != null)
              Text('Consumption: ${log.consumptionRate}L/hr'),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: log.statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            log.statusDescription,
            style: TextStyle(
              color: log.statusColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
        onTap: () => _showFuelDetails(log),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_gas_station_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load fuel data',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(allFuelLogsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showAddFuelLogDialog() {
    showDialog(
      context: context,
      builder: (context) => AddFuelLogDialog(
        propertyId: _selectedProperty == 'all' ? '' : _selectedProperty,
      ),
    );
  }

  void _showFuelDetails(GeneratorFuelLog log) {
    // TODO: Implement fuel details dialog
    AppUtils.showInfoSnackBar(context, 'Fuel details feature coming soon');
  }
}
