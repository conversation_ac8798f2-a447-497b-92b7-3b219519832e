import 'package:dio/dio.dart';
import '../../../shared/models/maintenance_issue.dart';
import '../domain/maintenance_repository.dart';
import 'maintenance_api_service.dart';

class MaintenanceRepositoryImpl implements MaintenanceRepository {
  final MaintenanceApiService _apiService;

  MaintenanceRepositoryImpl(this._apiService);

  @override
  Future<List<MaintenanceIssue>> getMaintenanceIssues() async {
    print('🔧 MAINTENANCE REPOSITORY: Starting getMaintenanceIssues...');
    try {
      print('🔧 MAINTENANCE REPOSITORY: Calling API service...');
      final response = await _apiService.getMaintenanceIssues();

      print('🔧 MAINTENANCE REPOSITORY: API response received');
      print('🔧 MAINTENANCE REPOSITORY: Response success: ${response.success}');
      print('🔧 MAINTENANCE REPOSITORY: Response data null: ${response.data == null}');

      if (response.success && response.data != null) {
        print('🔧 MAINTENANCE REPOSITORY: Data count: ${response.data!.length}');
        print('🔧 MAINTENANCE REPOSITORY: First issue ID: ${response.data!.isNotEmpty ? response.data!.first.id : 'N/A'}');
        return response.data!;
      } else {
        print('🔧 MAINTENANCE REPOSITORY: API response failed');
        print('🔧 MAINTENANCE REPOSITORY: Error message: ${response.message}');
        throw Exception(response.message ?? 'Failed to fetch maintenance issues');
      }
    } catch (e) {
      print('🔧 MAINTENANCE REPOSITORY: Exception caught: $e');
      print('🔧 MAINTENANCE REPOSITORY: Exception type: ${e.runtimeType}');
      throw Exception('Failed to fetch maintenance issues: $e');
    }
  }

  @override
  Future<MaintenanceIssue> getMaintenanceIssueById(String id) async {
    try {
      final response = await _apiService.getMaintenanceIssueById(id);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch maintenance issue');
      }
    } catch (e) {
      throw Exception('Failed to fetch maintenance issue: $e');
    }
  }

  @override
  Future<MaintenanceIssue> createMaintenanceIssue(MaintenanceIssue issue) async {
    try {
      final request = CreateMaintenanceIssueRequest(
        propertyId: issue.propertyId,
        title: issue.title,
        description: issue.description,
        priority: issue.priority,
        status: issue.status,
        serviceType: issue.serviceType,
        department: issue.department,
        reportedBy: issue.reportedBy,
        dueDate: issue.dueDate,
      );

      final response = await _apiService.createMaintenanceIssue(request);

      if (response.response.statusCode == 201 && response.data != null) {
        // Handle nested response structure from backend
        final responseData = response.data as Map<String, dynamic>;

        // Check if it's a successful API response
        if (responseData['success'] == true && responseData['data'] != null) {
          final data = responseData['data'] as Map<String, dynamic>;

          if (data.containsKey('issue')) {
            // Extract maintenance issue data from nested structure
            final issueData = data['issue'] as Map<String, dynamic>;
            return MaintenanceIssue.fromJson(issueData);
          } else {
            // Fallback: try to parse the entire data as MaintenanceIssue
            return MaintenanceIssue.fromJson(data);
          }
        } else {
          throw Exception(responseData['error'] ?? 'Failed to create maintenance issue');
        }
      } else {
        throw Exception('Failed to create maintenance issue: HTTP ${response.response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to create maintenance issue: $e');
    }
  }

  @override
  Future<MaintenanceIssue> updateMaintenanceIssue(MaintenanceIssue issue) async {
    print('🔧 MAINTENANCE REPOSITORY: Starting updateMaintenanceIssue...');
    print('🔧 MAINTENANCE REPOSITORY: Issue ID: ${issue.id}');
    print('🔧 MAINTENANCE REPOSITORY: Issue title: ${issue.title}');

    try {
      final request = UpdateMaintenanceIssueRequest(
        title: issue.title,
        description: issue.description,
        priority: issue.priority,
        status: issue.status,
        serviceType: issue.serviceType,
        department: issue.department,
        assignedTo: issue.assignedTo,
        dueDate: issue.dueDate,
      );

      print('🔧 MAINTENANCE REPOSITORY: Update request: ${request.toJson()}');
      print('🔧 MAINTENANCE REPOSITORY: Calling API service...');

      final response = await _apiService.updateMaintenanceIssue(issue.id, request);

      print('🔧 MAINTENANCE REPOSITORY: API response received');
      print('🔧 MAINTENANCE REPOSITORY: Response success: ${response.success}');
      print('🔧 MAINTENANCE REPOSITORY: Response data null: ${response.data == null}');
      print('🔧 MAINTENANCE REPOSITORY: Response message: ${response.message}');

      if (response.success && response.data != null) {
        print('🔧 MAINTENANCE REPOSITORY: Success - returning updated issue');
        return response.data!;
      } else {
        print('🔧 MAINTENANCE REPOSITORY: API response failed');
        throw Exception(response.message ?? 'Failed to update maintenance issue');
      }
    } on DioException catch (e) {
      print('🔧 MAINTENANCE REPOSITORY: DioException caught: ${e.message}');
      print('🔧 MAINTENANCE REPOSITORY: DioException type: ${e.type}');
      print('🔧 MAINTENANCE REPOSITORY: DioException response: ${e.response?.data}');
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('🔧 MAINTENANCE REPOSITORY: Exception caught: $e');
      print('🔧 MAINTENANCE REPOSITORY: Exception type: ${e.runtimeType}');
      throw Exception('Failed to update maintenance issue: $e');
    }
  }

  @override
  Future<void> deleteMaintenanceIssue(String id) async {
    try {
      final response = await _apiService.deleteMaintenanceIssue(id);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete maintenance issue');
      }
    } catch (e) {
      throw Exception('Failed to delete maintenance issue: $e');
    }
  }

  @override
  Future<List<MaintenanceIssue>> getMaintenanceIssuesByProperty(String propertyId) async {
    try {
      final response = await _apiService.getMaintenanceIssuesByProperty(propertyId);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch property maintenance issues');
      }
    } catch (e) {
      throw Exception('Failed to fetch property maintenance issues: $e');
    }
  }

  @override
  Future<MaintenanceIssue> assignMaintenanceIssue(String issueId, String assignedTo, {String? notes}) async {
    try {
      final request = AssignMaintenanceIssueRequest(
        assignedTo: assignedTo,
        notes: notes,
      );

      final response = await _apiService.assignMaintenanceIssue(issueId, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to assign maintenance issue');
      }
    } catch (e) {
      throw Exception('Failed to assign maintenance issue: $e');
    }
  }

  @override
  Future<MaintenanceIssue> escalateMaintenanceIssue(String issueId) async {
    try {
      final response = await _apiService.escalateMaintenanceIssue(issueId);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to escalate maintenance issue');
      }
    } catch (e) {
      throw Exception('Failed to escalate maintenance issue: $e');
    }
  }

  @override
  Future<MaintenanceIssue> getIssueById(String id) async {
    return getMaintenanceIssueById(id);
  }
}
