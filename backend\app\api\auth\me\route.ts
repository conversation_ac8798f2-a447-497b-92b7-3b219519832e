import { NextRequest } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, corsHeaders } from '@/lib/utils';

async function getMeHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const userData = {
      id: currentUser.id,
      email: currentUser.email,
      full_name: currentUser.fullName,
      phone: currentUser.phone,
      is_active: currentUser.isActive,
      roles: currentUser.roles,
      created_at: currentUser.createdAt,
    };

    return Response.json(
      createApiResponse(userData),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return Response.json(
      createApiResponse(null, 'Failed to get user profile', 'INTERNAL_ERROR'),
      { status: 500 }
    );
  }
}

export const GET = requireAuth(getMeHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
