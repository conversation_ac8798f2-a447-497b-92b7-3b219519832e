import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/user.dart';
import '../../data/user_management_api_service.dart';
import '../../data/user_management_repository_impl.dart';
import '../../domain/user_management_repository.dart';

// API Service Provider
final userManagementApiServiceProvider = Provider<UserManagementApiService>((ref) {
  return UserManagementApiService(DioClient.instance.dio);
});

// Repository Provider
final userManagementRepositoryProvider = Provider<UserManagementRepository>((ref) {
  return UserManagementRepositoryImpl(ref.watch(userManagementApiServiceProvider));
});

// User Management Provider
final userManagementProvider = StateNotifierProvider<UserManagementNotifier, AsyncValue<List<User>>>((ref) {
  return UserManagementNotifier(ref.watch(userManagementRepositoryProvider));
});

// Specific user list providers
final allUsersProvider = Provider<AsyncValue<List<User>>>((ref) {
  return ref.watch(userManagementProvider);
});

final pendingUsersProvider = Provider<AsyncValue<List<User>>>((ref) {
  final usersAsync = ref.watch(userManagementProvider);
  return usersAsync.when(
    data: (users) => AsyncValue.data(users.where((user) => !user.isApproved).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

final activeUsersProvider = Provider<AsyncValue<List<User>>>((ref) {
  final usersAsync = ref.watch(userManagementProvider);
  return usersAsync.when(
    data: (users) => AsyncValue.data(users.where((user) => user.isActive && user.isApproved).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

final inactiveUsersProvider = Provider<AsyncValue<List<User>>>((ref) {
  final usersAsync = ref.watch(userManagementProvider);
  return usersAsync.when(
    data: (users) => AsyncValue.data(users.where((user) => !user.isActive).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

class UserManagementNotifier extends StateNotifier<AsyncValue<List<User>>> {
  final UserManagementRepository _repository;

  UserManagementNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadUsers();
  }

  Future<void> loadUsers() async {
    try {
      state = const AsyncValue.loading();
      final users = await _repository.getAllUsers();
      state = AsyncValue.data(users);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> approveUser(User user) async {
    try {
      await _repository.approveUser(user.id);
      await loadUsers(); // Reload users after approval
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> rejectUser(String userId) async {
    try {
      await _repository.deleteUser(userId);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> activateUser(String userId) async {
    try {
      await _repository.activateUser(userId);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> deactivateUser(String userId) async {
    try {
      await _repository.deactivateUser(userId);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> updateUserRole(String userId, String newRole) async {
    try {
      await _repository.updateUserRole(userId, newRole);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      await _repository.deleteUser(userId);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> createUser(CreateUserRequest request) async {
    try {
      await _repository.createUser(request);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> updateUser(String userId, UpdateUserRequest request) async {
    try {
      await _repository.updateUser(userId, request);
      await loadUsers();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
