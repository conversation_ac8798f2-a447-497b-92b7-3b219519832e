import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/property.dart';
import '../../../../shared/models/property_service.dart';
import '../providers/properties_providers.dart';
import '../widgets/property_service_card.dart';
import '../widgets/property_stats_card.dart';
import '../widgets/property_info_card.dart';
import '../widgets/property_form_dialog.dart';

class PropertyDetailsScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const PropertyDetailsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertyDetailsScreen> createState() => _PropertyDetailsScreenState();
}

class _PropertyDetailsScreenState extends ConsumerState<PropertyDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final propertyAsync = ref.watch(propertyByIdProvider(widget.propertyId));

    return Scaffold(
      body: propertyAsync.when(
        data: (property) => _buildPropertyDetails(property),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildPropertyDetails(Property property) {
    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                property.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      property.primaryColor,
                      property.primaryColor.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: property.imageUrl != null
                    ? Image.network(
                        property.imageUrl!,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              property.primaryColor,
                              property.secondaryColor,
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            property.icon,
                            size: 80,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
              ),
            ),
            actions: [
              RoleBasedWidget(
                requiredPermissions: const ['properties.update'],
                child: IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditPropertyDialog(property),
                ),
              ),
              PopupMenuButton<String>(
                itemBuilder: (context) => [
                  const PopupMenuItem<String>(
                    value: 'refresh',
                    child: Row(
                      children: [
                        Icon(Icons.refresh, size: 20),
                        SizedBox(width: 8),
                        Text('Refresh'),
                      ],
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(Icons.share, size: 20),
                        SizedBox(width: 8),
                        Text('Share'),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) {
                  switch (value) {
                    case 'refresh':
                      ref.invalidate(propertyByIdProvider(widget.propertyId));
                      break;
                    case 'share':
                      _shareProperty(property);
                      break;
                  }
                },
              ),
            ],
          ),
        ];
      },
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: TabBar(
              controller: _tabController,
              labelColor: property.primaryColor,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: property.primaryColor,
              tabs: const [
                Tab(text: 'Overview', icon: Icon(Icons.dashboard, size: 20)),
                Tab(text: 'Services', icon: Icon(Icons.miscellaneous_services, size: 20)),
                Tab(text: 'Activity', icon: Icon(Icons.timeline, size: 20)),
                Tab(text: 'Reports', icon: Icon(Icons.analytics, size: 20)),
              ],
            ),
          ),
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(property),
                _buildServicesTab(property),
                _buildActivityTab(property),
                _buildReportsTab(property),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(Property property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Info Card
          PropertyInfoCard(property: property),
          const SizedBox(height: AppConstants.defaultPadding),

          // Stats Cards
          PropertyStatsCard(property: property),
          const SizedBox(height: AppConstants.defaultPadding),

          // Quick Actions
          _buildQuickActions(property),
          const SizedBox(height: AppConstants.defaultPadding),

          // Recent Activity
          _buildRecentActivity(property),
        ],
      ),
    );
  }

  Widget _buildServicesTab(Property property) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Property Services',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              RoleBasedWidget(
                requiredPermissions: const ['properties.update'],
                child: ElevatedButton.icon(
                  onPressed: () => _showAddServiceDialog(property),
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add Service'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: property.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          if (property.services?.isNotEmpty == true)
            ...property.services!.map((service) => Padding(
              padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
              child: PropertyServiceCard(
                service: service,
                property: property,
                onTap: () => _navigateToServiceDetails(property, service),
              ),
            ))
          else
            _buildEmptyServicesState(property),
        ],
      ),
    );
  }

  Widget _buildActivityTab(Property property) {
    return const Center(
      child: Text('Activity Tab - Coming Soon'),
    );
  }

  Widget _buildReportsTab(Property property) {
    return const Center(
      child: Text('Reports Tab - Coming Soon'),
    );
  }

  Widget _buildQuickActions(Property property) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionChip(
                  'Maintenance',
                  Icons.build,
                  () => context.push('/maintenance?propertyId=${property.id}'),
                  property.primaryColor,
                ),
                _buildActionChip(
                  'Attendance',
                  Icons.people,
                  () => context.push('/attendance?propertyId=${property.id}'),
                  property.primaryColor,
                ),
                _buildActionChip(
                  'Fuel Monitor',
                  Icons.local_gas_station,
                  () => context.push('/fuel?propertyId=${property.id}'),
                  property.primaryColor,
                ),
                _buildActionChip(
                  'Reports',
                  Icons.analytics,
                  () => _showReportsDialog(property),
                  property.primaryColor,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionChip(String label, IconData icon, VoidCallback onTap, Color color) {
    return ActionChip(
      avatar: Icon(icon, size: 18, color: color),
      label: Text(label),
      onPressed: onTap,
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color.withValues(alpha: 0.3)),
    );
  }

  Widget _buildRecentActivity(Property property) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(2),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            const Text('No recent activity'),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyServicesState(Property property) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          children: [
            Icon(
              Icons.miscellaneous_services,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No Services Added',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Add services to monitor and manage this property',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            RoleBasedWidget(
              requiredPermissions: const ['properties.update'],
              child: ElevatedButton.icon(
                onPressed: () => _showAddServiceDialog(property),
                icon: const Icon(Icons.add),
                label: const Text('Add First Service'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: property.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load property',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(propertyByIdProvider(widget.propertyId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showEditPropertyDialog(Property property) {
    showDialog(
      context: context,
      builder: (context) => PropertyFormDialog(
        property: property,
        onSuccess: () {
          ref.invalidate(propertyByIdProvider(widget.propertyId));
        },
      ),
    );
  }

  void _shareProperty(Property property) {
    // TODO: Implement share property
    AppUtils.showInfoSnackBar(context, 'Share property feature coming soon');
  }

  void _showAddServiceDialog(Property property) {
    // TODO: Implement add service dialog
    AppUtils.showInfoSnackBar(context, 'Add service feature coming soon');
  }

  void _navigateToServiceDetails(Property property, PropertyService service) {
    // TODO: Navigate to service details
    AppUtils.showInfoSnackBar(context, 'Service details feature coming soon');
  }

  void _showReportsDialog(Property property) {
    // TODO: Show reports dialog
    AppUtils.showInfoSnackBar(context, 'Reports feature coming soon');
  }
}
