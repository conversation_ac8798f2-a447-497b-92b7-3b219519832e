<html><head><meta charset="utf-8"/><title>SRSR Admin API Test Report</title><style type="text/css">html,
body {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1rem;
  margin: 0;
  padding: 0;
  color: #333;
}
body {
  padding: 2rem 1rem;
  font-size: 0.85rem;
}
.jesthtml-content {
  margin: 0 auto;
  max-width: 70rem;
}
header {
  display: flex;
  align-items: center;
}
#title {
  margin: 0;
  flex-grow: 1;
}
#logo {
  height: 4rem;
}
#timestamp {
  color: #777;
  margin-top: 0.5rem;
}

/** SUMMARY */
#summary {
  color: #333;
  margin: 2rem 0;
  display: flex;
  font-family: monospace;
  font-size: 1rem;
}
#summary > div {
  margin-right: 2rem;
  background: #eee;
  padding: 1rem;
  min-width: 15rem;
}
#summary > div:last-child {
  margin-right: 0;
}
@media only screen and (max-width: 720px) {
  #summary {
    flex-direction: column;
  }
  #summary > div {
    margin-right: 0;
    margin-top: 2rem;
  }
  #summary > div:first-child {
    margin-top: 0;
  }
}

.summary-total {
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.summary-passed {
  color: #4f8a10;
  border-left: 0.4rem solid #4f8a10;
  padding-left: 0.5rem;
}
.summary-failed,
.summary-obsolete-snapshots {
  color: #d8000c;
  border-left: 0.4rem solid #d8000c;
  padding-left: 0.5rem;
}
.summary-pending {
  color: #9f6000;
  border-left: 0.4rem solid #9f6000;
  padding-left: 0.5rem;
}
.summary-empty {
  color: #999;
  border-left: 0.4rem solid #999;
}

.test-result {
  padding: 1rem;
  margin-bottom: 0.25rem;
}
.test-result:last-child {
  border: 0;
}
.test-result.passed {
  background-color: #dff2bf;
  color: #4f8a10;
}
.test-result.failed {
  background-color: #ffbaba;
  color: #d8000c;
}
.test-result.pending {
  background-color: #ffdf61;
  color: #9f6000;
}

.test-info {
  display: flex;
  justify-content: space-between;
}
.test-suitename {
  width: 20%;
  text-align: left;
  font-weight: bold;
  word-break: break-word;
}
.test-title {
  width: 40%;
  text-align: left;
  font-style: italic;
}
.test-status {
  width: 20%;
  text-align: right;
}
.test-duration {
  width: 10%;
  text-align: right;
  font-size: 0.75rem;
}

.failureMessages {
  padding: 0 1rem;
  margin-top: 1rem;
  border-top: 1px dashed #d8000c;
}
.failureMessages.suiteFailure {
  border-top: none;
}
.failureMsg {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}

.suite-container {
  margin-bottom: 2rem;
}
.suite-container > input[type="checkbox"] {
  position: absolute;
  left: -100vw;
}
.suite-container label {
  display: block;
}
.suite-container .suite-tests {
  overflow-y: hidden;
  height: 0;
}
.suite-container > input[type="checkbox"]:checked ~ .suite-tests {
  height: auto;
  overflow: visible;
}
.suite-info {
  padding: 1rem;
  background-color: #eee;
  color: #777;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}
.suite-info:hover {
  background-color: #ddd;
  cursor: pointer;
}
.suite-info .suite-path {
  word-break: break-all;
  flex-grow: 1;
  font-family: monospace;
  font-size: 1rem;
}
.suite-info .suite-time {
  margin-left: 0.5rem;
  padding: 0.2rem 0.3rem;
  font-size: 0.75rem;
}
.suite-info .suite-time.warn {
  background-color: #d8000c;
  color: #fff;
}
.suite-info:before {
  content: "\2303";
  display: inline-block;
  margin-right: 0.5rem;
  transform: rotate(0deg);
}
.suite-container > input[type="checkbox"]:checked ~ label .suite-info:before {
  transform: rotate(180deg);
}

/* CONSOLE LOGS */
.suite-consolelog {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: #efefef;
}
.suite-consolelog-header {
  font-weight: bold;
}
.suite-consolelog-item {
  padding: 0.5rem;
}
.suite-consolelog-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-consolelog-item-origin {
  color: #777;
  font-weight: bold;
}
.suite-consolelog-item-message {
  color: #000;
  font-size: 1rem;
  padding: 0 0.5rem;
}

/* OBSOLETE SNAPSHOTS */
.suite-obsolete-snapshots {
  margin-bottom: 0.25rem;
  padding: 1rem;
  background-color: #ffbaba;
  color: #d8000c;
}
.suite-obsolete-snapshots-header {
  font-weight: bold;
}
.suite-obsolete-snapshots-item {
  padding: 0.5rem;
}
.suite-obsolete-snapshots-item pre {
  margin: 0.5rem 0;
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
}
.suite-obsolete-snapshots-item-message {
  color: #000;
  font-size: 1rem;
  padding: 0 0.5rem;
}
</style></head><body><div class="jesthtml-content"><header><h1 id="title">SRSR Admin API Test Report</h1></header><div id="metadata-container"><div id="timestamp">Started: 2025-06-01 16:51:51</div><div id="summary"><div id="suite-summary"><div class="summary-total">Suites (1)</div><div class="summary-passed  summary-empty">0 passed</div><div class="summary-failed ">1 failed</div><div class="summary-pending  summary-empty">0 pending</div></div><div id="test-summary"><div class="summary-total">Tests (29)</div><div class="summary-passed ">6 passed</div><div class="summary-failed ">23 failed</div><div class="summary-pending  summary-empty">0 pending</div></div></div></div><div id="suite-1" class="suite-container"><input id="collapsible-0" type="checkbox" class="toggle" checked="checked"/><label for="collapsible-0"><div class="suite-info"><div class="suite-path">D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js</div><div class="suite-time">3.573s</div></div></label><div class="suite-tests"><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create custom role with valid data</div><div class="test-status">failed</div><div class="test-duration">0.035s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:52:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create facilities manager role</div><div class="test-status">failed</div><div class="test-duration">0.026s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:72:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create security supervisor role</div><div class="test-status">failed</div><div class="test-duration">0.023s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:90:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create escalation manager role</div><div class="test-status">failed</div><div class="test-duration">0.028s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:108:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should reject role with invalid name</div><div class="test-status">failed</div><div class="test-duration">0.038s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toMatch(expected)

Expected pattern: /name/i
Received string:  "Validation failed"
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:127:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should reject role with missing required fields</div><div class="test-status">passed</div><div class="test-duration">0.036s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should reject duplicate role name</div><div class="test-status">failed</div><div class="test-duration">0.034s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:159:36)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create role with minimal permissions</div><div class="test-status">failed</div><div class="test-duration">0.037s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:185:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Creation</div><div class="test-title">should create role with maximum permissions</div><div class="test-status">failed</div><div class="test-duration">0.037s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: 201
Received: 400
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:202:31)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should get all roles</div><div class="test-status">failed</div><div class="test-duration">0.036s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:235:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should get role by ID</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:248:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should return 404 for non-existent role</div><div class="test-status">passed</div><div class="test-duration">0.442s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should filter roles by type</div><div class="test-status">failed</div><div class="test-duration">0.035s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:279:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Retrieval</div><div class="test-title">should search roles by name</div><div class="test-status">failed</div><div class="test-duration">0s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'name')
    at Object.name (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:291:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should update role details</div><div class="test-status">failed</div><div class="test-duration">0.031s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'permissions')
    at Object.permissions (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:320:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should add permissions to role</div><div class="test-status">failed</div><div class="test-duration">0.028s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'permissions')
    at Object.permissions (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:339:35)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should remove permissions from role</div><div class="test-status">failed</div><div class="test-duration">0.023s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'permissions')
    at Object.permissions (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:355:47)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should reject update with invalid data</div><div class="test-status">failed</div><div class="test-duration">0.02s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:385:28)
    at Promise.then.completed (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\node_modules\jest-runner\build\runTest.js:444:34)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Updates</div><div class="test-title">should prevent updating system roles</div><div class="test-status">failed</div><div class="test-duration">0.042s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:406:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Management</div><div class="test-title">should get all available permissions</div><div class="test-status">failed</div><div class="test-duration">0.194s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:432:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Management</div><div class="test-title">should get permissions by category</div><div class="test-status">failed</div><div class="test-duration">0.028s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.toBe (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:451:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Management</div><div class="test-title">should validate permission assignments</div><div class="test-status">failed</div><div class="test-duration">0.028s</div></div><div class="failureMessages"> <pre class="failureMsg">Error: expect(received).toMatch(expected)

Expected pattern: /invalid permission|permission not found/i
Received string:  "Validation failed"
    at Object.toMatch (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:472:35)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce role creation permissions</div><div class="test-status">passed</div><div class="test-duration">0.629s</div></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce role update permissions</div><div class="test-status">passed</div><div class="test-duration">0.042s</div></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Permission Enforcement</div><div class="test-title">should enforce role deletion permissions</div><div class="test-status">passed</div><div class="test-duration">0.022s</div></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should delete custom role</div><div class="test-status">failed</div><div class="test-duration">0.021s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: Cannot read properties of undefined (reading 'id')
    at Object.id (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:533:47)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should prevent deletion of system roles</div><div class="test-status">failed</div><div class="test-duration">0.022s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:563:49)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result failed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should prevent deletion of role assigned to users</div><div class="test-status">failed</div><div class="test-duration">0.02s</div></div><div class="failureMessages"> <pre class="failureMsg">TypeError: rolesResponse.data.data.find is not a function
    at Object.find (D:\workspaces\nsl\back\SrsrMan\testing\api_tests\tests\admin\role_management.test.js:586:59)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</pre></div></div><div class="test-result passed"><div class="test-info"><div class="test-suitename">Admin Role Management API Tests &gt; Role Deletion</div><div class="test-title">should return 404 when deleting non-existent role</div><div class="test-status">passed</div><div class="test-duration">0.028s</div></div></div></div></div></div></body></html>