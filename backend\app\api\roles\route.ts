import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, getQueryParams, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const createRoleSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  description: Joi.string().max(255).optional(),
  is_system_role: Joi.boolean().default(false),
  permissions: Joi.array().items(Joi.string().uuid()).optional(),
});

async function getRolesHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const params = getQueryParams(request);
    const { include_permissions, include_users, system_roles } = params;

    const includeOptions: any = {};
    
    if (include_permissions === 'true') {
      includeOptions.rolePermissions = {
        include: {
          permission: true,
        },
      };
    }
    
    if (include_users === 'true') {
      includeOptions.userRoles = {
        include: {
          user: {
            select: {
              id: true,
              email: true,
              fullName: true,
            },
          },
        },
      };
    }

    const where: any = {};
    if (system_roles === 'false') {
      where.isSystemRole = false;
    } else if (system_roles === 'true') {
      where.isSystemRole = true;
    }

    const roles = await prisma.role.findMany({
      where,
      include: includeOptions,
      orderBy: [
        { isSystemRole: 'desc' },
        { name: 'asc' },
      ],
    });

    const transformedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      is_system_role: role.isSystemRole,
      permissions: role.rolePermissions?.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description,
      })) || [],
      users: role.userRoles?.map(ur => ({
        id: ur.user.id,
        email: ur.user.email,
        full_name: ur.user.fullName,
      })) || [],
      user_count: role.userRoles?.length || 0,
      permission_count: role.rolePermissions?.length || 0,
      created_at: role.createdAt,
      updated_at: role.updatedAt,
    }));

    return Response.json(
      createApiResponse({
        roles: transformedRoles,
        total: transformedRoles.length,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch roles');
  }
}

async function createRoleHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    const validation = validateRequest(createRoleSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { name, description, is_system_role, permissions } = validation.data;

    // Check if role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name },
    });

    if (existingRole) {
      return Response.json(
        createApiResponse(null, 'Role with this name already exists', 'DUPLICATE_ROLE'),
        { status: 409 }
      );
    }

    // Create role
    const role = await prisma.role.create({
      data: {
        name,
        description,
        isSystemRole: is_system_role || false,
      },
    });

    // Assign permissions if provided
    if (permissions && permissions.length > 0) {
      // Verify permissions exist
      const existingPermissions = await prisma.permission.findMany({
        where: {
          id: { in: permissions },
        },
      });

      if (existingPermissions.length !== permissions.length) {
        console.warn('Some permissions not found:', permissions.filter(p => !existingPermissions.find(ep => ep.id === p)));
      }

      // Create role-permission assignments
      const rolePermissionData = existingPermissions.map(permission => ({
        roleId: role.id,
        permissionId: permission.id,
      }));

      await prisma.rolePermission.createMany({
        data: rolePermissionData,
      });
    }

    // Fetch created role with permissions
    const roleWithPermissions = await prisma.role.findUnique({
      where: { id: role.id },
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Role created successfully',
        role: {
          id: roleWithPermissions!.id,
          name: roleWithPermissions!.name,
          description: roleWithPermissions!.description,
          is_system_role: roleWithPermissions!.isSystemRole,
          permissions: roleWithPermissions!.rolePermissions.map(rp => ({
            id: rp.permission.id,
            name: rp.permission.name,
            resource: rp.permission.resource,
            action: rp.permission.action,
          })),
          created_at: roleWithPermissions!.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create role');
  }
}

export const GET = requireAuth(getRolesHandler);
export const POST = requireRole(['admin'])(createRoleHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
