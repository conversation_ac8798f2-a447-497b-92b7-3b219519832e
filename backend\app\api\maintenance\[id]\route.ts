import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const updateMaintenanceIssueSchema = Joi.object({
  title: Joi.string().min(2).optional(),
  description: Joi.string().min(5).optional(),
  priority: Joi.string().valid('low', 'medium', 'high', 'critical').optional(),
  status: Joi.string().valid('open', 'in_progress', 'resolved', 'closed').optional(),
  service_type: Joi.string().optional(),
  department: Joi.string().optional(),
  assigned_to: Joi.string().uuid().optional(),
  due_date: Joi.date().optional(),
  estimated_cost: Joi.number().positive().optional(),
  actual_cost: Joi.number().positive().optional(),
});

const updateStatusSchema = Joi.object({
  status: Joi.string().valid('open', 'in_progress', 'resolved', 'closed').required(),
});

async function getMaintenanceIssueHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    const issue = await prisma.maintenanceIssue.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        reporter: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        assignee: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        resolver: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        escalationLogs: {
          select: {
            id: true,
            escalationLevel: true,
            escalationReason: true,
            escalatedAt: true,
            resolvedAt: true,
          },
          orderBy: {
            escalatedAt: 'desc',
          },
        },
      },
    });

    if (!issue) {
      return Response.json(
        createApiResponse(null, 'Maintenance issue not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const transformedIssue = {
      id: issue.id,
      property: {
        id: issue.property.id,
        name: issue.property.name,
        type: issue.property.type.toLowerCase(),
      },
      title: issue.title,
      description: issue.description,
      priority: issue.priority.toLowerCase(),
      status: issue.status.toLowerCase(),
      service_type: issue.serviceType,
      department: issue.department,
      reporter: issue.reporter,
      assignee: issue.assignee,
      resolver: issue.resolver,
      estimated_cost: issue.estimatedCost,
      actual_cost: issue.actualCost,
      due_date: issue.dueDate,
      resolved_at: issue.resolvedAt,
      created_at: issue.createdAt,
      updated_at: issue.updatedAt,
      escalation_logs: issue.escalationLogs,
    };

    return Response.json(
      createApiResponse(transformedIssue),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch maintenance issue');
  }
}

async function updateMaintenanceIssueHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(updateMaintenanceIssueSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify issue exists
    const existingIssue = await prisma.maintenanceIssue.findUnique({
      where: { id },
    });

    if (!existingIssue) {
      return Response.json(
        createApiResponse(null, 'Maintenance issue not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const updateData: any = {};

    if (validation.data.title) updateData.title = validation.data.title;
    if (validation.data.description) updateData.description = validation.data.description;
    if (validation.data.priority) updateData.priority = validation.data.priority.toUpperCase();
    if (validation.data.status) {
      updateData.status = validation.data.status.toUpperCase();
      if (validation.data.status === 'resolved') {
        updateData.resolvedAt = new Date();
        updateData.resolvedBy = currentUser.id;
      }
    }
    if (validation.data.service_type) updateData.serviceType = validation.data.service_type;
    if (validation.data.department) updateData.department = validation.data.department;
    if (validation.data.assigned_to) updateData.assignedTo = validation.data.assigned_to;
    if (validation.data.due_date) updateData.dueDate = new Date(validation.data.due_date);
    if (validation.data.estimated_cost) updateData.estimatedCost = validation.data.estimated_cost;
    if (validation.data.actual_cost) updateData.actualCost = validation.data.actual_cost;

    // Update issue
    const updatedIssue = await prisma.maintenanceIssue.update({
      where: { id },
      data: updateData,
      include: {
        property: {
          select: {
            name: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Maintenance issue updated successfully',
        issue: {
          id: updatedIssue.id,
          title: updatedIssue.title,
          status: updatedIssue.status.toLowerCase(),
          priority: updatedIssue.priority.toLowerCase(),
          property_name: updatedIssue.property.name,
          updated_at: updatedIssue.updatedAt,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update maintenance issue');
  }
}

async function updateIssueStatusHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(updateStatusSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { status } = validation.data;

    // Verify issue exists
    const existingIssue = await prisma.maintenanceIssue.findUnique({
      where: { id },
    });

    if (!existingIssue) {
      return Response.json(
        createApiResponse(null, 'Maintenance issue not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const updateData: any = {
      status: status.toUpperCase(),
    };

    if (status === 'resolved') {
      updateData.resolvedAt = new Date();
      updateData.resolvedBy = currentUser.id;
    }

    // Update issue status
    const updatedIssue = await prisma.maintenanceIssue.update({
      where: { id },
      data: updateData,
    });

    return Response.json(
      createApiResponse({
        message: 'Issue status updated successfully',
        issue: {
          id: updatedIssue.id,
          status: updatedIssue.status.toLowerCase(),
          resolved_at: updatedIssue.resolvedAt,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update issue status');
  }
}

async function deleteMaintenanceIssueHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Verify issue exists
    const existingIssue = await prisma.maintenanceIssue.findUnique({
      where: { id },
      select: {
        id: true,
        title: true,
        status: true,
      },
    });

    if (!existingIssue) {
      return Response.json(
        createApiResponse(null, 'Maintenance issue not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Check if issue can be deleted (only open or resolved issues)
    if (!['OPEN', 'RESOLVED', 'CLOSED'].includes(existingIssue.status)) {
      return Response.json(
        createApiResponse(null, 'Cannot delete issue in current status', 'INVALID_OPERATION'),
        { status: 400 }
      );
    }

    // Delete related records first (due to foreign key constraints)
    await prisma.escalationLog.deleteMany({
      where: { issueId: id },
    });

    // Delete the maintenance issue
    await prisma.maintenanceIssue.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'Maintenance issue deleted successfully',
        deleted_issue: {
          id: existingIssue.id,
          title: existingIssue.title,
        },
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete maintenance issue');
  }
}

export const GET = requireAuth(getMaintenanceIssueHandler);
export const PUT = requireAuth(updateMaintenanceIssueHandler);
export const PATCH = requireAuth(updateIssueStatusHandler);
export const DELETE = requireAuth(deleteMaintenanceIssueHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
