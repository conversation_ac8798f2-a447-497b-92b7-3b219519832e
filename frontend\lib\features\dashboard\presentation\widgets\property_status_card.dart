import 'package:flutter/material.dart';
import '../../../../core/business_logic/status_calculator.dart';
import '../../../../core/constants/app_constants.dart';

class PropertyStatusCard extends StatelessWidget {
  final PropertyStatus propertyStatus;
  final VoidCallback? onTap;

  const PropertyStatusCard({
    super.key,
    required this.propertyStatus,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          propertyStatus.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              propertyStatus.type.toLowerCase() == 'residential'
                                  ? Icons.home_outlined
                                  : Icons.business_outlined,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              propertyStatus.type,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Overall Status Indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(propertyStatus.overallStatus).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getStatusColor(propertyStatus.overallStatus),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: _getStatusColor(propertyStatus.overallStatus),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getStatusText(propertyStatus.overallStatus),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _getStatusColor(propertyStatus.overallStatus),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Functional Areas
              if (propertyStatus.functionalAreas.isNotEmpty) ...[
                Text(
                  'System Status',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),

                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: propertyStatus.functionalAreas.entries.map((entry) {
                    return _buildFunctionalAreaChip(
                      context,
                      entry.key,
                      entry.value,
                    );
                  }).toList(),
                ),
              ],

              const SizedBox(height: AppConstants.smallPadding),

              // Last Updated
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Updated ${_formatLastUpdated(propertyStatus.lastUpdated)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFunctionalAreaChip(
    BuildContext context,
    String areaName,
    FunctionalAreaStatus areaStatus,
  ) {
    final icon = _getAreaIcon(areaName);
    final color = _getStatusColor(areaStatus.status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            _formatAreaName(areaName),
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
          if (areaStatus.issueCount > 0) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                areaStatus.issueCount.toString(),
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(StatusLevel status) {
    switch (status) {
      case StatusLevel.green:
        return Colors.green;
      case StatusLevel.orange:
        return Colors.orange;
      case StatusLevel.red:
        return Colors.red;
    }
  }

  String _getStatusText(StatusLevel status) {
    switch (status) {
      case StatusLevel.green:
        return 'Good';
      case StatusLevel.orange:
        return 'Warning';
      case StatusLevel.red:
        return 'Critical';
    }
  }

  IconData _getAreaIcon(String areaName) {
    switch (areaName.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'maintenance':
        return Icons.build;
      case 'internet':
        return Icons.wifi;
      case 'security':
        return Icons.security;
      case 'attendance':
        return Icons.people;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String _formatAreaName(String areaName) {
    switch (areaName.toLowerCase()) {
      case 'electricity':
        return 'Power';
      case 'maintenance':
        return 'Maintenance';
      case 'internet':
        return 'Internet';
      case 'security':
        return 'Security';
      case 'attendance':
        return 'Attendance';
      default:
        return areaName[0].toUpperCase() + areaName.substring(1);
    }
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
