import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'attendance.g.dart';

enum AttendanceStatus {
  present,
  absent,
  late,
  earlyLeave,
}

extension AttendanceStatusExtension on AttendanceStatus {
  String get value {
    switch (this) {
      case AttendanceStatus.present:
        return 'present';
      case AttendanceStatus.absent:
        return 'absent';
      case AttendanceStatus.late:
        return 'late';
      case AttendanceStatus.earlyLeave:
        return 'early_leave';
    }
  }

  String get displayName {
    switch (this) {
      case AttendanceStatus.present:
        return 'Present';
      case AttendanceStatus.absent:
        return 'Absent';
      case AttendanceStatus.late:
        return 'Late';
      case AttendanceStatus.earlyLeave:
        return 'Early Leave';
    }
  }

  Color get color {
    switch (this) {
      case AttendanceStatus.present:
        return Colors.green;
      case AttendanceStatus.absent:
        return Colors.red;
      case AttendanceStatus.late:
        return Colors.orange;
      case AttendanceStatus.earlyLeave:
        return Colors.blue;
    }
  }

  static AttendanceStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'present':
        return AttendanceStatus.present;
      case 'absent':
        return AttendanceStatus.absent;
      case 'late':
        return AttendanceStatus.late;
      case 'early_leave':
      case 'earlyleave':
        return AttendanceStatus.earlyLeave;
      default:
        return AttendanceStatus.absent;
    }
  }
}

@JsonSerializable()
class AttendanceRecord {
  final String id;
  @JsonKey(name: 'property_id')
  final String? propertyId;
  @JsonKey(name: 'office_id')
  final String? officeId;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'worker_name')
  final String workerName;
  @JsonKey(name: 'worker_role')
  final String? workerRole;
  final DateTime date;
  @JsonKey(name: 'check_in_time')
  final String? checkInTime;
  @JsonKey(name: 'check_out_time')
  final String? checkOutTime;
  @JsonKey(name: 'hours_worked')
  final double? hoursWorked;
  final String? notes;
  final String status; // present, absent, late, half_day
  @JsonKey(name: 'recorded_by')
  final String? recordedBy;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  const AttendanceRecord({
    required this.id,
    this.propertyId,
    this.officeId,
    required this.userId,
    required this.workerName,
    this.workerRole,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    this.hoursWorked,
    this.notes,
    required this.status,
    this.recordedBy,
    required this.createdAt,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) => _$AttendanceRecordFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceRecordToJson(this);

  AttendanceRecord copyWith({
    String? id,
    String? propertyId,
    String? officeId,
    String? userId,
    String? workerName,
    String? workerRole,
    DateTime? date,
    String? checkInTime,
    String? checkOutTime,
    double? hoursWorked,
    String? notes,
    String? status,
    String? recordedBy,
    DateTime? createdAt,
  }) {
    return AttendanceRecord(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      officeId: officeId ?? this.officeId,
      userId: userId ?? this.userId,
      workerName: workerName ?? this.workerName,
      workerRole: workerRole ?? this.workerRole,
      date: date ?? this.date,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      recordedBy: recordedBy ?? this.recordedBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttendanceRecord && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Business logic methods
  bool get isPresent => status.toLowerCase() == 'present';
  bool get isAbsent => status.toLowerCase() == 'absent';
  bool get isLate => status.toLowerCase() == 'late';
  bool get isHalfDay => status.toLowerCase() == 'half_day';

  bool get isSiteAttendance => propertyId != null;
  bool get isOfficeAttendance => officeId != null;

  String get attendanceType => isSiteAttendance ? 'Site' : 'Office';

  // Alias for workerName for backward compatibility
  String get userName => workerName;

  Color get statusColor {
    switch (status.toLowerCase()) {
      case 'present':
        return Colors.green;
      case 'late':
        return Colors.orange;
      case 'half_day':
        return Colors.blue;
      case 'absent':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData get statusIcon {
    switch (status.toLowerCase()) {
      case 'present':
        return Icons.check_circle;
      case 'late':
        return Icons.schedule;
      case 'half_day':
        return Icons.access_time;
      case 'absent':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String get displayStatus {
    switch (status.toLowerCase()) {
      case 'present':
        return 'Present';
      case 'late':
        return 'Late';
      case 'half_day':
        return 'Half Day';
      case 'absent':
        return 'Absent';
      default:
        return status.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  String get dayOfWeek {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[date.weekday - 1];
  }

  bool get isWeekend => date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;

  bool get hasCheckInTime => checkInTime != null && checkInTime!.isNotEmpty;
  bool get hasCheckOutTime => checkOutTime != null && checkOutTime!.isNotEmpty;
  bool get hasCompleteTimeRecord => hasCheckInTime && hasCheckOutTime;

  double get calculatedHours {
    if (!hasCompleteTimeRecord) return hoursWorked ?? 0;

    try {
      final checkIn = _parseTime(checkInTime!);
      final checkOut = _parseTime(checkOutTime!);

      if (checkIn != null && checkOut != null) {
        final duration = checkOut.difference(checkIn);
        return duration.inMinutes / 60.0;
      }
    } catch (e) {
      // Fall back to recorded hours if time parsing fails
    }

    return hoursWorked ?? 0;
  }

  DateTime? _parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        return DateTime(date.year, date.month, date.day, hour, minute);
      }
    } catch (e) {
      // Return null if parsing fails
    }
    return null;
  }

  String get hoursWorkedDisplay {
    final hours = calculatedHours;
    if (hours == 0) return 'N/A';

    final wholeHours = hours.floor();
    final minutes = ((hours - wholeHours) * 60).round();

    if (minutes == 0) {
      return '${wholeHours}h';
    } else {
      return '${wholeHours}h ${minutes}m';
    }
  }

  bool get isFullDay {
    final hours = calculatedHours;
    return hours >= 7.5; // Assuming 7.5+ hours is full day
  }

  bool get isOvertime {
    final hours = calculatedHours;
    return hours > 8; // Assuming 8+ hours is overtime
  }

  double get overtimeHours {
    final hours = calculatedHours;
    return hours > 8 ? hours - 8 : 0;
  }

  bool get isLateArrival {
    if (!hasCheckInTime) return false;

    try {
      final checkIn = _parseTime(checkInTime!);
      if (checkIn != null) {
        // Assuming 9:00 AM is standard start time
        final standardStart = DateTime(date.year, date.month, date.day, 9, 0);
        return checkIn.isAfter(standardStart);
      }
    } catch (e) {
      // Fall back to status
    }

    return isLate;
  }

  Duration? get lateBy {
    if (!isLateArrival || !hasCheckInTime) return null;

    try {
      final checkIn = _parseTime(checkInTime!);
      if (checkIn != null) {
        final standardStart = DateTime(date.year, date.month, date.day, 9, 0);
        return checkIn.difference(standardStart);
      }
    } catch (e) {
      // Return null if parsing fails
    }

    return null;
  }

  String? get lateByDescription {
    final lateDuration = lateBy;
    if (lateDuration == null) return null;

    final minutes = lateDuration.inMinutes;
    if (minutes < 60) {
      return '$minutes minutes late';
    } else {
      final hours = lateDuration.inHours;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '$hours hours late';
      } else {
        return '$hours hours $remainingMinutes minutes late';
      }
    }
  }

  // Validation methods
  bool get isValid => userId.isNotEmpty && workerName.isNotEmpty && status.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (userId.isEmpty) errors.add('User ID is required');
    if (workerName.isEmpty) errors.add('Worker name is required');
    if (status.isEmpty) errors.add('Status is required');
    if (propertyId == null && officeId == null) {
      errors.add('Either property or office must be specified');
    }

    const validStatuses = ['present', 'absent', 'late', 'half_day'];
    if (!validStatuses.contains(status.toLowerCase())) {
      errors.add('Invalid status');
    }

    if (hoursWorked != null) {
      if (hoursWorked! < 0) errors.add('Hours worked cannot be negative');
      if (hoursWorked! > 24) errors.add('Hours worked cannot exceed 24');
    }

    if (hasCheckInTime && hasCheckOutTime) {
      final checkIn = _parseTime(checkInTime!);
      final checkOut = _parseTime(checkOutTime!);
      if (checkIn != null && checkOut != null && checkOut.isBefore(checkIn)) {
        errors.add('Check-out time cannot be before check-in time');
      }
    }

    return errors;
  }

  // Helper methods
  static AttendanceRecord create({
    String? id,
    String? propertyId,
    String? officeId,
    required String userId,
    required String workerName,
    String? workerRole,
    required DateTime date,
    String? checkInTime,
    String? checkOutTime,
    double? hoursWorked,
    String? notes,
    required String status,
    String? recordedBy,
  }) {
    return AttendanceRecord(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      propertyId: propertyId,
      officeId: officeId,
      userId: userId,
      workerName: workerName,
      workerRole: workerRole,
      date: date,
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      hoursWorked: hoursWorked,
      notes: notes,
      status: status,
      recordedBy: recordedBy,
      createdAt: DateTime.now(),
    );
  }

  static AttendanceRecord createSiteAttendance({
    required String propertyId,
    required String userId,
    required String workerName,
    String? workerRole,
    required DateTime date,
    String? checkInTime,
    String? checkOutTime,
    double? hoursWorked,
    String? notes,
    required String status,
    String? recordedBy,
  }) {
    return AttendanceRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      propertyId: propertyId,
      userId: userId,
      workerName: workerName,
      workerRole: workerRole,
      date: date,
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      hoursWorked: hoursWorked,
      notes: notes,
      status: status,
      recordedBy: recordedBy,
      createdAt: DateTime.now(),
    );
  }

  static AttendanceRecord createOfficeAttendance({
    required String officeId,
    required String userId,
    required String workerName,
    String? workerRole,
    required DateTime date,
    String? checkInTime,
    String? checkOutTime,
    double? hoursWorked,
    String? notes,
    required String status,
    String? recordedBy,
  }) {
    return AttendanceRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      officeId: officeId,
      userId: userId,
      workerName: workerName,
      workerRole: workerRole,
      date: date,
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      hoursWorked: hoursWorked,
      notes: notes,
      status: status,
      recordedBy: recordedBy,
      createdAt: DateTime.now(),
    );
  }

  // Status update methods
  AttendanceRecord markPresent({String? checkInTime, String? checkOutTime, double? hoursWorked}) {
    return copyWith(
      status: 'present',
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      hoursWorked: hoursWorked,
    );
  }

  AttendanceRecord markAbsent({String? notes}) {
    return copyWith(
      status: 'absent',
      notes: notes,
      checkInTime: null,
      checkOutTime: null,
      hoursWorked: 0,
    );
  }

  AttendanceRecord markLate({String? checkInTime, String? checkOutTime, double? hoursWorked}) {
    return copyWith(
      status: 'late',
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      hoursWorked: hoursWorked,
    );
  }

  AttendanceRecord markHalfDay({String? checkInTime, String? checkOutTime, double? hoursWorked}) {
    return copyWith(
      status: 'half_day',
      checkInTime: checkInTime,
      checkOutTime: checkOutTime,
      hoursWorked: hoursWorked,
    );
  }
}

@JsonSerializable()
class AttendanceStats {
  final int totalCount;
  final int presentCount;
  final int absentCount;
  final int lateCount;
  final int halfDayCount;
  final double attendanceRate;
  final double averageHours;
  final DateTime date;

  const AttendanceStats({
    required this.totalCount,
    required this.presentCount,
    required this.absentCount,
    required this.lateCount,
    required this.halfDayCount,
    required this.attendanceRate,
    required this.averageHours,
    required this.date,
  });

  factory AttendanceStats.fromJson(Map<String, dynamic> json) {
    return AttendanceStats(
      totalCount: json['totalCount'] as int? ?? 0,
      presentCount: json['presentCount'] as int? ?? 0,
      absentCount: json['absentCount'] as int? ?? 0,
      lateCount: json['lateCount'] as int? ?? 0,
      halfDayCount: json['halfDayCount'] as int? ?? 0,
      attendanceRate: (json['attendanceRate'] as num?)?.toDouble() ?? 0.0,
      averageHours: (json['averageHours'] as num?)?.toDouble() ?? 0.0,
      date: DateTime.parse(json['date'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCount': totalCount,
      'presentCount': presentCount,
      'absentCount': absentCount,
      'lateCount': lateCount,
      'halfDayCount': halfDayCount,
      'attendanceRate': attendanceRate,
      'averageHours': averageHours,
      'date': date.toIso8601String(),
    };
  }

  factory AttendanceStats.fromRecords(List<AttendanceRecord> records, DateTime date) {
    final totalCount = records.length;
    final presentCount = records.where((r) => r.isPresent).length;
    final absentCount = records.where((r) => r.isAbsent).length;
    final lateCount = records.where((r) => r.isLate).length;
    final halfDayCount = records.where((r) => r.isHalfDay).length;

    final attendanceRate = totalCount > 0 ? (presentCount + lateCount + halfDayCount) / totalCount : 0.0;
    final totalHours = records.fold<double>(0.0, (sum, record) => sum + record.calculatedHours);
    final averageHours = totalCount > 0 ? totalHours / totalCount : 0.0;

    return AttendanceStats(
      totalCount: totalCount,
      presentCount: presentCount,
      absentCount: absentCount,
      lateCount: lateCount,
      halfDayCount: halfDayCount,
      attendanceRate: attendanceRate,
      averageHours: averageHours,
      date: date,
    );
  }

  AttendanceStats copyWith({
    int? totalCount,
    int? presentCount,
    int? absentCount,
    int? lateCount,
    int? halfDayCount,
    double? attendanceRate,
    double? averageHours,
    DateTime? date,
  }) {
    return AttendanceStats(
      totalCount: totalCount ?? this.totalCount,
      presentCount: presentCount ?? this.presentCount,
      absentCount: absentCount ?? this.absentCount,
      lateCount: lateCount ?? this.lateCount,
      halfDayCount: halfDayCount ?? this.halfDayCount,
      attendanceRate: attendanceRate ?? this.attendanceRate,
      averageHours: averageHours ?? this.averageHours,
      date: date ?? this.date,
    );
  }
}
