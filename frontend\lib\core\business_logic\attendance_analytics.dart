/// Attendance analytics and business logic
/// Ported from V1's sophisticated attendance management system
library;

import 'dart:math';
import 'package:flutter/foundation.dart';

/// Attendance calculation constants
class AttendanceConstants {
  static const double standardWorkingHours = 8.0;
  static const double halfDayThreshold = 4.0;
  static const double overtimeThreshold = 8.0;
  static const String standardStartTime = '09:00';
  static const String standardEndTime = '17:00';
  static const int lateThresholdMinutes = 15;
  static const double excellentAttendanceRate = 95.0;
  static const double goodAttendanceRate = 85.0;
  static const double poorAttendanceRate = 70.0;
}

/// Attendance record model
class AttendanceRecord {
  final String id;
  final String userId;
  final String userName;
  final DateTime date;
  final String? checkInTime;
  final String? checkOutTime;
  final double? hoursWorked;
  final String status; // present, absent, late, half_day
  final String? notes;
  final String propertyId;

  const AttendanceRecord({
    required this.id,
    required this.userId,
    required this.userName,
    required this.date,
    this.checkInTime,
    this.checkOutTime,
    this.hoursWorked,
    required this.status,
    this.notes,
    required this.propertyId,
  });

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) {
    return AttendanceRecord(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      userName: json['user_name'] ?? '',
      date: DateTime.parse(json['date']),
      checkInTime: json['check_in_time'],
      checkOutTime: json['check_out_time'],
      hoursWorked: json['hours_worked']?.toDouble(),
      status: json['status'] ?? 'absent',
      notes: json['notes'],
      propertyId: json['property_id'] ?? '',
    );
  }
}

/// Individual attendance analytics
class IndividualAttendanceAnalytics {
  final String userId;
  final String userName;
  final int totalDays;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final int halfDays;
  final double totalHoursWorked;
  final double averageHoursPerDay;
  final double attendanceRate;
  final double punctualityRate;
  final String performanceGrade;
  final List<String> insights;
  final Map<String, dynamic> trends;

  const IndividualAttendanceAnalytics({
    required this.userId,
    required this.userName,
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.halfDays,
    required this.totalHoursWorked,
    required this.averageHoursPerDay,
    required this.attendanceRate,
    required this.punctualityRate,
    required this.performanceGrade,
    required this.insights,
    required this.trends,
  });
}

/// Team attendance analytics
class TeamAttendanceAnalytics {
  final int totalMembers;
  final double teamAttendanceRate;
  final double teamPunctualityRate;
  final double averageHoursPerMember;
  final int totalAbsentDays;
  final int totalLateDays;
  final List<IndividualAttendanceAnalytics> memberAnalytics;
  final Map<String, dynamic> teamInsights;
  final List<String> recommendations;

  const TeamAttendanceAnalytics({
    required this.totalMembers,
    required this.teamAttendanceRate,
    required this.teamPunctualityRate,
    required this.averageHoursPerMember,
    required this.totalAbsentDays,
    required this.totalLateDays,
    required this.memberAnalytics,
    required this.teamInsights,
    required this.recommendations,
  });
}

/// Enhanced attendance analytics with V1 business logic
class AttendanceAnalytics {
  /// Calculate working hours from check-in and check-out times
  /// Ported from V1's office-attendance-form.tsx
  static double calculateHoursWorked(String checkInTime, String checkOutTime) {
    if (checkInTime.isEmpty || checkOutTime.isEmpty) {
      return AttendanceConstants.standardWorkingHours;
    }

    try {
      final checkInParts = checkInTime.split(':');
      final checkOutParts = checkOutTime.split(':');

      if (checkInParts.length != 2 || checkOutParts.length != 2) {
        return AttendanceConstants.standardWorkingHours;
      }

      final checkInHour = int.parse(checkInParts[0]);
      final checkInMinute = int.parse(checkInParts[1]);
      final checkOutHour = int.parse(checkOutParts[0]);
      final checkOutMinute = int.parse(checkOutParts[1]);

      final checkInMinutes = checkInHour * 60 + checkInMinute;
      final checkOutMinutes = checkOutHour * 60 + checkOutMinute;

      // If check-out is earlier than check-in, assume next day
      final diffMinutes = checkOutMinutes >= checkInMinutes
          ? checkOutMinutes - checkInMinutes
          : 24 * 60 - checkInMinutes + checkOutMinutes;

      return double.parse((diffMinutes / 60).toStringAsFixed(2));
    } catch (e) {
      debugPrint('Error calculating hours worked: $e');
      return AttendanceConstants.standardWorkingHours;
    }
  }

  /// Determine if employee is late
  static bool isLate(String checkInTime, [String standardStart = AttendanceConstants.standardStartTime]) {
    if (checkInTime.isEmpty) return false;

    try {
      final checkInParts = checkInTime.split(':');
      final standardParts = standardStart.split(':');

      if (checkInParts.length != 2 || standardParts.length != 2) return false;

      final checkInMinutes = int.parse(checkInParts[0]) * 60 + int.parse(checkInParts[1]);
      final standardMinutes = int.parse(standardParts[0]) * 60 + int.parse(standardParts[1]);

      return checkInMinutes > standardMinutes + AttendanceConstants.lateThresholdMinutes;
    } catch (e) {
      debugPrint('Error checking late status: $e');
      return false;
    }
  }

  /// Calculate individual attendance analytics
  /// Enhanced from V1's attendance analytics patterns
  static IndividualAttendanceAnalytics calculateIndividualAnalytics(
    String userId,
    String userName,
    List<AttendanceRecord> records,
    DateTime startDate,
    DateTime endDate,
  ) {
    final totalDays = endDate.difference(startDate).inDays + 1;

    int presentDays = 0;
    int absentDays = 0;
    int lateDays = 0;
    int halfDays = 0;
    double totalHoursWorked = 0.0;

    // Process each attendance record
    for (final record in records) {
      if (record.checkInTime != null && record.checkOutTime != null) {
        presentDays++;

        // Calculate hours worked
        final hours = record.hoursWorked ?? calculateHoursWorked(
          record.checkInTime!,
          record.checkOutTime!,
        );
        totalHoursWorked += hours;

        // Check if late
        if (isLate(record.checkInTime!)) {
          lateDays++;
        }

        // Check for half day
        if (hours < AttendanceConstants.halfDayThreshold) {
          halfDays++;
        }
      } else {
        absentDays++;
      }
    }

    // Calculate rates and metrics
    final attendanceRate = totalDays > 0 ? (presentDays / totalDays) * 100 : 0.0;
    final punctualityRate = presentDays > 0 ? ((presentDays - lateDays) / presentDays) * 100 : 100.0;
    final averageHoursPerDay = presentDays > 0 ? totalHoursWorked / presentDays : 0.0;

    // Determine performance grade
    final performanceGrade = _calculatePerformanceGrade(attendanceRate, punctualityRate, averageHoursPerDay);

    // Generate insights
    final insights = _generateIndividualInsights(
      attendanceRate,
      punctualityRate,
      averageHoursPerDay,
      lateDays,
      halfDays,
      totalDays,
    );

    // Calculate trends
    final trends = _calculateIndividualTrends(records);

    return IndividualAttendanceAnalytics(
      userId: userId,
      userName: userName,
      totalDays: totalDays,
      presentDays: presentDays,
      absentDays: absentDays,
      lateDays: lateDays,
      halfDays: halfDays,
      totalHoursWorked: _roundToDecimal(totalHoursWorked, 2),
      averageHoursPerDay: _roundToDecimal(averageHoursPerDay, 2),
      attendanceRate: _roundToDecimal(attendanceRate, 1),
      punctualityRate: _roundToDecimal(punctualityRate, 1),
      performanceGrade: performanceGrade,
      insights: insights,
      trends: trends,
    );
  }

  /// Calculate team attendance analytics
  static TeamAttendanceAnalytics calculateTeamAnalytics(
    List<AttendanceRecord> allRecords,
    DateTime startDate,
    DateTime endDate,
  ) {
    // Group records by user
    final userRecords = <String, List<AttendanceRecord>>{};
    final userNames = <String, String>{};

    for (final record in allRecords) {
      userRecords.putIfAbsent(record.userId, () => []).add(record);
      userNames[record.userId] = record.userName;
    }

    // Calculate individual analytics for each member
    final memberAnalytics = <IndividualAttendanceAnalytics>[];
    for (final userId in userRecords.keys) {
      final analytics = calculateIndividualAnalytics(
        userId,
        userNames[userId] ?? 'Unknown',
        userRecords[userId]!,
        startDate,
        endDate,
      );
      memberAnalytics.add(analytics);
    }

    // Calculate team metrics
    final totalMembers = memberAnalytics.length;
    final teamAttendanceRate = totalMembers > 0
        ? memberAnalytics.map((a) => a.attendanceRate).reduce((a, b) => a + b) / totalMembers
        : 0.0;
    final teamPunctualityRate = totalMembers > 0
        ? memberAnalytics.map((a) => a.punctualityRate).reduce((a, b) => a + b) / totalMembers
        : 0.0;
    final averageHoursPerMember = totalMembers > 0
        ? memberAnalytics.map((a) => a.averageHoursPerDay).reduce((a, b) => a + b) / totalMembers
        : 0.0;
    final totalAbsentDays = memberAnalytics.map((a) => a.absentDays).reduce((a, b) => a + b);
    final totalLateDays = memberAnalytics.map((a) => a.lateDays).reduce((a, b) => a + b);

    // Generate team insights
    final teamInsights = _generateTeamInsights(memberAnalytics, teamAttendanceRate, teamPunctualityRate);

    // Generate recommendations
    final recommendations = _generateTeamRecommendations(memberAnalytics, teamAttendanceRate, teamPunctualityRate);

    return TeamAttendanceAnalytics(
      totalMembers: totalMembers,
      teamAttendanceRate: _roundToDecimal(teamAttendanceRate, 1),
      teamPunctualityRate: _roundToDecimal(teamPunctualityRate, 1),
      averageHoursPerMember: _roundToDecimal(averageHoursPerMember, 2),
      totalAbsentDays: totalAbsentDays,
      totalLateDays: totalLateDays,
      memberAnalytics: memberAnalytics,
      teamInsights: teamInsights,
      recommendations: recommendations,
    );
  }

  /// Calculate performance grade based on multiple factors
  static String _calculatePerformanceGrade(double attendanceRate, double punctualityRate, double averageHours) {
    final attendanceScore = attendanceRate >= AttendanceConstants.excellentAttendanceRate ? 4 :
                           attendanceRate >= AttendanceConstants.goodAttendanceRate ? 3 :
                           attendanceRate >= AttendanceConstants.poorAttendanceRate ? 2 : 1;

    final punctualityScore = punctualityRate >= 95 ? 4 :
                            punctualityRate >= 85 ? 3 :
                            punctualityRate >= 70 ? 2 : 1;

    final hoursScore = averageHours >= AttendanceConstants.standardWorkingHours ? 4 :
                      averageHours >= 6 ? 3 :
                      averageHours >= 4 ? 2 : 1;

    final totalScore = (attendanceScore + punctualityScore + hoursScore) / 3;

    if (totalScore >= 3.5) return 'A';
    if (totalScore >= 2.5) return 'B';
    if (totalScore >= 1.5) return 'C';
    return 'D';
  }

  /// Generate individual insights
  static List<String> _generateIndividualInsights(
    double attendanceRate,
    double punctualityRate,
    double averageHours,
    int lateDays,
    int halfDays,
    int totalDays,
  ) {
    final insights = <String>[];

    if (attendanceRate >= AttendanceConstants.excellentAttendanceRate) {
      insights.add('Excellent attendance record');
    } else if (attendanceRate < AttendanceConstants.poorAttendanceRate) {
      insights.add('Attendance needs improvement');
    }

    if (punctualityRate < 80) {
      insights.add('Frequent late arrivals detected');
    } else if (punctualityRate >= 95) {
      insights.add('Excellent punctuality');
    }

    if (averageHours < AttendanceConstants.halfDayThreshold) {
      insights.add('Consistently working short hours');
    } else if (averageHours > AttendanceConstants.overtimeThreshold) {
      insights.add('Frequently working overtime');
    }

    if (halfDays > totalDays * 0.2) {
      insights.add('High frequency of half days');
    }

    return insights;
  }

  /// Calculate individual trends
  static Map<String, dynamic> _calculateIndividualTrends(List<AttendanceRecord> records) {
    if (records.length < 7) return {};

    // Sort records by date
    final sortedRecords = List<AttendanceRecord>.from(records)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Calculate weekly trends
    final recentWeek = sortedRecords.length >= 7
        ? sortedRecords.sublist(sortedRecords.length - 7)
        : sortedRecords;
    final previousWeek = sortedRecords.length >= 14
        ? sortedRecords.skip(sortedRecords.length - 14).take(7).toList()
        : <AttendanceRecord>[];

    final trends = <String, dynamic>{};

    if (previousWeek.isNotEmpty) {
      final recentAttendance = recentWeek.where((r) => r.status == 'present').length / 7 * 100;
      final previousAttendance = previousWeek.where((r) => r.status == 'present').length / 7 * 100;

      trends['attendance_trend'] = recentAttendance > previousAttendance ? 'improving' :
                                  recentAttendance < previousAttendance ? 'declining' : 'stable';
    }

    return trends;
  }

  /// Generate team insights
  static Map<String, dynamic> _generateTeamInsights(
    List<IndividualAttendanceAnalytics> memberAnalytics,
    double teamAttendanceRate,
    double teamPunctualityRate,
  ) {
    final insights = <String, dynamic>{};

    // Performance distribution
    final gradeDistribution = <String, int>{};
    for (final member in memberAnalytics) {
      gradeDistribution[member.performanceGrade] =
          (gradeDistribution[member.performanceGrade] ?? 0) + 1;
    }
    insights['grade_distribution'] = gradeDistribution;

    // Top performers
    final topPerformers = memberAnalytics
        .where((m) => m.attendanceRate >= AttendanceConstants.excellentAttendanceRate)
        .map((m) => m.userName)
        .toList();
    insights['top_performers'] = topPerformers;

    // Improvement needed
    final needsImprovement = memberAnalytics
        .where((m) => m.attendanceRate < AttendanceConstants.poorAttendanceRate)
        .map((m) => m.userName)
        .toList();
    insights['needs_improvement'] = needsImprovement;

    return insights;
  }

  /// Generate team recommendations
  static List<String> _generateTeamRecommendations(
    List<IndividualAttendanceAnalytics> memberAnalytics,
    double teamAttendanceRate,
    double teamPunctualityRate,
  ) {
    final recommendations = <String>[];

    if (teamAttendanceRate < AttendanceConstants.goodAttendanceRate) {
      recommendations.add('Implement attendance improvement program');
    }

    if (teamPunctualityRate < 85) {
      recommendations.add('Address punctuality issues with flexible timing or incentives');
    }

    final lowPerformers = memberAnalytics
        .where((m) => m.performanceGrade == 'D')
        .length;

    if (lowPerformers > memberAnalytics.length * 0.2) {
      recommendations.add('Provide additional support and training for underperforming members');
    }

    return recommendations;
  }

  /// Round number to specified decimal places
  static double _roundToDecimal(double value, int decimals) {
    final factor = pow(10, decimals);
    return (value * factor).round() / factor;
  }
}
