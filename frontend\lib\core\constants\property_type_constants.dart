enum PropertyType {
  residential,
  office,
  constructionSite,
}

class PropertyTypeConstants {
  // Property Type Definitions
  static const Map<PropertyType, String> typeNames = {
    PropertyType.residential: 'Residential',
    PropertyType.office: 'Office',
    PropertyType.constructionSite: 'Construction Site',
  };

  static const Map<PropertyType, String> typeDescriptions = {
    PropertyType.residential: 'Residential properties including homes, apartments, and housing complexes',
    PropertyType.office: 'Office properties including corporate buildings, co-working spaces, and business centers',
    PropertyType.constructionSite: 'Construction sites and development projects with project management features',
  };

  // Property Type Specific Services
  static const Map<PropertyType, List<String>> defaultServices = {
    PropertyType.residential: [
      'electricity',
      'water',
      'internet',
      'security',
      'ott',
      'generator',
      'maintenance',
    ],
    PropertyType.office: [
      'electricity',
      'water',
      'internet',
      'security',
      'ott',
      'generator',
      'maintenance',
      'hvac',
      'fire_safety',
      'access_control',
    ],
    PropertyType.constructionSite: [
      'electricity',
      'water',
      'security',
      'generator',
      'maintenance',
      'safety_equipment',
      'material_storage',
      'equipment_monitoring',
    ],
  };

  // Property Type Specific Features
  static const Map<PropertyType, List<String>> availableFeatures = {
    PropertyType.residential: [
      'fuel_monitoring',
      'security_logs',
      'maintenance_tracking',
      'ott_services',
      'uptime_reports',
      'diesel_additions',
    ],
    PropertyType.office: [
      'fuel_monitoring',
      'security_logs',
      'maintenance_tracking',
      'ott_services',
      'uptime_reports',
      'diesel_additions',
      'office_management',
      'attendance_tracking',
      'meeting_rooms',
      'visitor_management',
    ],
    PropertyType.constructionSite: [
      'fuel_monitoring',
      'security_logs',
      'maintenance_tracking',
      'diesel_additions',
      'project_management',
      'attendance_tracking',
      'equipment_tracking',
      'safety_monitoring',
      'material_management',
      'progress_reporting',
    ],
  };

  // Service Priority by Property Type
  static const Map<PropertyType, Map<String, int>> servicePriority = {
    PropertyType.residential: {
      'electricity': 1,
      'water': 2,
      'security': 3,
      'internet': 4,
      'ott': 5,
      'generator': 6,
      'maintenance': 7,
    },
    PropertyType.office: {
      'electricity': 1,
      'internet': 2,
      'security': 3,
      'hvac': 4,
      'water': 5,
      'access_control': 6,
      'fire_safety': 7,
      'generator': 8,
      'ott': 9,
      'maintenance': 10,
    },
    PropertyType.constructionSite: {
      'safety_equipment': 1,
      'security': 2,
      'electricity': 3,
      'generator': 4,
      'water': 5,
      'equipment_monitoring': 6,
      'material_storage': 7,
      'maintenance': 8,
    },
  };

  // Maintenance Categories by Property Type
  static const Map<PropertyType, List<String>> maintenanceCategories = {
    PropertyType.residential: [
      'electrical',
      'plumbing',
      'security',
      'internet',
      'generator',
      'general',
    ],
    PropertyType.office: [
      'electrical',
      'plumbing',
      'hvac',
      'security',
      'internet',
      'fire_safety',
      'access_control',
      'generator',
      'office_equipment',
      'general',
    ],
    PropertyType.constructionSite: [
      'electrical',
      'plumbing',
      'security',
      'generator',
      'safety_equipment',
      'construction_equipment',
      'material_handling',
      'site_infrastructure',
      'general',
    ],
  };

  // Default Thresholds by Property Type
  static const Map<PropertyType, Map<String, dynamic>> defaultThresholds = {
    PropertyType.residential: {
      'fuel_level_critical': 20.0, // percentage
      'fuel_level_warning': 40.0,
      'power_backup_critical': 6.0, // hours
      'power_backup_warning': 12.0,
      'maintenance_overdue_days': 7,
      'security_check_interval': 24, // hours
    },
    PropertyType.office: {
      'fuel_level_critical': 25.0, // percentage
      'fuel_level_warning': 50.0,
      'power_backup_critical': 8.0, // hours
      'power_backup_warning': 16.0,
      'maintenance_overdue_days': 3,
      'security_check_interval': 12, // hours
      'hvac_temperature_min': 20.0, // celsius
      'hvac_temperature_max': 26.0,
      'office_capacity_warning': 80.0, // percentage
    },
    PropertyType.constructionSite: {
      'fuel_level_critical': 15.0, // percentage
      'fuel_level_warning': 30.0,
      'power_backup_critical': 4.0, // hours
      'power_backup_warning': 8.0,
      'maintenance_overdue_days': 2,
      'security_check_interval': 8, // hours
      'safety_inspection_interval': 24, // hours
      'equipment_check_interval': 12, // hours
      'material_stock_warning': 20.0, // percentage
    },
  };

  // UI Colors by Property Type
  static const Map<PropertyType, Map<String, int>> typeColors = {
    PropertyType.residential: {
      'primary': 0xFF2E7D32, // Green
      'secondary': 0xFF66BB6A,
      'accent': 0xFF4CAF50,
    },
    PropertyType.office: {
      'primary': 0xFF1565C0, // Blue
      'secondary': 0xFF42A5F5,
      'accent': 0xFF2196F3,
    },
    PropertyType.constructionSite: {
      'primary': 0xFFE65100, // Orange
      'secondary': 0xFFFF9800,
      'accent': 0xFFFF6F00,
    },
  };

  // Icons by Property Type
  static const Map<PropertyType, Map<String, int>> typeIcons = {
    PropertyType.residential: {
      'main': 0xe318, // Icons.home
      'services': 0xe3ab, // Icons.home_repair_service
      'maintenance': 0xe3ac, // Icons.handyman
    },
    PropertyType.office: {
      'main': 0xe1af, // Icons.business
      'services': 0xe1b0, // Icons.business_center
      'maintenance': 0xe3ac, // Icons.handyman
    },
    PropertyType.constructionSite: {
      'main': 0xe1c6, // Icons.construction
      'services': 0xe1c7, // Icons.engineering
      'maintenance': 0xe1c8, // Icons.build
    },
  };

  // Helper Methods
  static PropertyType fromString(String type) {
    switch (type.toLowerCase()) {
      case 'residential':
        return PropertyType.residential;
      case 'office':
        return PropertyType.office;
      case 'construction_site':
        return PropertyType.constructionSite;
      default:
        throw ArgumentError('Invalid property type: $type');
    }
  }

  static String typeToString(PropertyType type) {
    switch (type) {
      case PropertyType.residential:
        return 'residential';
      case PropertyType.office:
        return 'office';
      case PropertyType.constructionSite:
        return 'construction_site';
    }
  }

  static String getDisplayName(PropertyType type) {
    return typeNames[type] ?? 'Unknown';
  }

  static String getDescription(PropertyType type) {
    return typeDescriptions[type] ?? '';
  }

  static List<String> getDefaultServices(PropertyType type) {
    return defaultServices[type] ?? [];
  }

  static List<String> getAvailableFeatures(PropertyType type) {
    return availableFeatures[type] ?? [];
  }

  static Map<String, int> getServicePriority(PropertyType type) {
    return servicePriority[type] ?? {};
  }

  static List<String> getMaintenanceCategories(PropertyType type) {
    return maintenanceCategories[type] ?? [];
  }

  static Map<String, dynamic> getDefaultThresholds(PropertyType type) {
    return defaultThresholds[type] ?? {};
  }

  static bool hasFeature(PropertyType type, String feature) {
    return getAvailableFeatures(type).contains(feature);
  }

  static bool hasService(PropertyType type, String service) {
    return getDefaultServices(type).contains(service);
  }

  static int getServicePriorityValue(PropertyType type, String service) {
    return getServicePriority(type)[service] ?? 999;
  }

  // Enhanced Business Logic Methods

  /// Calculate property-specific risk score
  static double calculateRiskScore(PropertyType type, Map<String, dynamic> metrics) {
    double riskScore = 0.0;

    switch (type) {
      case PropertyType.office:
        // Office-specific risk factors
        final occupancyRate = metrics['occupancy_rate'] ?? 0.0;
        final hvacEfficiency = metrics['hvac_efficiency'] ?? 100.0;
        final securityScore = metrics['security_score'] ?? 100.0;

        riskScore += occupancyRate > 90 ? 20 : 0; // Overcrowding risk
        riskScore += hvacEfficiency < 70 ? 30 : 0; // HVAC failure risk
        riskScore += securityScore < 80 ? 25 : 0; // Security risk
        break;

      case PropertyType.residential:
        // Residential-specific risk factors
        final maintenanceBacklog = metrics['maintenance_backlog'] ?? 0;
        final tenantComplaints = metrics['tenant_complaints'] ?? 0;
        final utilityIssues = metrics['utility_issues'] ?? 0;

        riskScore += maintenanceBacklog > 5 ? 25 : 0;
        riskScore += tenantComplaints > 3 ? 20 : 0;
        riskScore += utilityIssues > 2 ? 30 : 0;
        break;

      case PropertyType.constructionSite:
        // Construction site-specific risk factors
        final safetyIncidents = metrics['safety_incidents'] ?? 0;
        final equipmentDowntime = metrics['equipment_downtime'] ?? 0.0;
        final weatherDelays = metrics['weather_delays'] ?? 0;

        riskScore += safetyIncidents > 0 ? 40 : 0; // Safety is critical
        riskScore += equipmentDowntime > 10 ? 25 : 0;
        riskScore += weatherDelays > 2 ? 15 : 0;
        break;
    }

    return riskScore.clamp(0.0, 100.0);
  }

  /// Get property-specific maintenance schedule
  static Map<String, int> getMaintenanceSchedule(PropertyType type) {
    switch (type) {
      case PropertyType.office:
        return {
          'hvac_inspection': 30, // days
          'electrical_check': 90,
          'plumbing_maintenance': 60,
          'security_system_test': 7,
          'fire_safety_check': 30,
          'elevator_service': 30,
        };

      case PropertyType.residential:
        return {
          'general_maintenance': 90,
          'plumbing_check': 180,
          'electrical_inspection': 365,
          'pest_control': 90,
          'garden_maintenance': 14,
          'common_area_cleaning': 7,
        };

      case PropertyType.constructionSite:
        return {
          'safety_inspection': 7,
          'equipment_maintenance': 30,
          'site_security_check': 1,
          'environmental_check': 14,
          'progress_review': 7,
          'quality_control': 14,
        };
    }
  }

  /// Validate property-specific business rules
  static List<String> validateBusinessRules(PropertyType type, Map<String, dynamic> data) {
    final violations = <String>[];

    switch (type) {
      case PropertyType.office:
        // Office-specific validations
        final capacity = data['capacity'] as int?;
        final currentOccupancy = data['current_occupancy'] as int?;

        if (capacity != null && capacity < 1) {
          violations.add('Office capacity must be at least 1');
        }

        if (capacity != null && currentOccupancy != null && currentOccupancy > capacity) {
          violations.add('Current occupancy cannot exceed office capacity');
        }
        break;

      case PropertyType.residential:
        // Residential-specific validations
        final units = data['units'] as int?;
        final parkingSpaces = data['parking_spaces'] as int?;

        if (units != null && units < 1) {
          violations.add('Residential property must have at least 1 unit');
        }

        if (units != null && parkingSpaces != null && parkingSpaces < units * 0.5) {
          violations.add('Insufficient parking spaces (minimum 0.5 spaces per unit)');
        }
        break;

      case PropertyType.constructionSite:
        // Construction site-specific validations
        final startDate = data['start_date'] as DateTime?;
        final endDate = data['end_date'] as DateTime?;
        final safetyOfficer = data['safety_officer'] as String?;

        if (startDate != null && endDate != null && endDate.isBefore(startDate)) {
          violations.add('End date cannot be before start date');
        }

        if (safetyOfficer == null || safetyOfficer.isEmpty) {
          violations.add('Construction site must have an assigned safety officer');
        }
        break;
    }

    return violations;
  }

  /// Calculate property-specific KPIs
  static Map<String, double> calculateKPIs(PropertyType type, Map<String, dynamic> data) {
    final kpis = <String, double>{};

    switch (type) {
      case PropertyType.office:
        // Office KPIs
        final capacity = data['capacity'] as int? ?? 1;
        final currentOccupancy = data['current_occupancy'] as int? ?? 0;
        final monthlyRevenue = data['monthly_revenue'] as double? ?? 0.0;
        final operatingCosts = data['operating_costs'] as double? ?? 0.0;

        kpis['occupancy_rate'] = (currentOccupancy / capacity) * 100;
        kpis['revenue_per_sqft'] = capacity > 0 ? monthlyRevenue / (capacity * 100) : 0.0;
        kpis['profit_margin'] = monthlyRevenue > 0 ? ((monthlyRevenue - operatingCosts) / monthlyRevenue) * 100 : 0.0;
        break;

      case PropertyType.residential:
        // Residential KPIs
        final units = data['units'] as int? ?? 1;
        final occupiedUnits = data['occupied_units'] as int? ?? 0;
        final monthlyRent = data['monthly_rent'] as double? ?? 0.0;
        final maintenanceCosts = data['maintenance_costs'] as double? ?? 0.0;

        kpis['occupancy_rate'] = (occupiedUnits / units) * 100;
        kpis['rent_per_unit'] = units > 0 ? monthlyRent / units : 0.0;
        kpis['maintenance_cost_ratio'] = monthlyRent > 0 ? (maintenanceCosts / monthlyRent) * 100 : 0.0;
        break;

      case PropertyType.constructionSite:
        // Construction site KPIs
        final totalBudget = data['total_budget'] as double? ?? 1.0;
        final spentBudget = data['spent_budget'] as double? ?? 0.0;
        final plannedProgress = data['planned_progress'] as double? ?? 0.0;
        final actualProgress = data['actual_progress'] as double? ?? 0.0;

        kpis['budget_utilization'] = (spentBudget / totalBudget) * 100;
        kpis['schedule_performance'] = plannedProgress > 0 ? (actualProgress / plannedProgress) * 100 : 0.0;
        kpis['cost_performance'] = actualProgress > 0 ? (actualProgress / (spentBudget / totalBudget)) * 100 : 0.0;
        break;
    }

    return kpis;
  }
}
