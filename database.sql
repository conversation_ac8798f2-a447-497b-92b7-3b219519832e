-- SRSR Property Management System - Complete Database Backup
-- Generated: 2025-01-29
-- Database: PostgreSQL

-- ============================================================================
-- DROP EXISTING TABLES (for clean restore)
-- ============================================================================

DROP TABLE IF EXISTS escalation_logs CASCADE;
DROP TABLE IF EXISTS function_process_logs CASCADE;
DROP TABLE IF EXISTS security_guard_logs CASCADE;
DROP TABLE IF EXISTS diesel_additions CASCADE;
DROP TABLE IF EXISTS generator_fuel_logs CASCADE;
DROP TABLE IF EXISTS maintenance_issues CASCADE;
DROP TABLE IF EXISTS ott_services CASCADE;
DROP TABLE IF EXISTS uptime_reports CASCADE;
DROP TABLE IF EXISTS service_status_logs CASCADE;
DROP TABLE IF EXISTS office_attendance CASCADE;
DROP TABLE IF EXISTS site_attendance CASCADE;
DROP TABLE IF EXISTS site_members CASCADE;
DROP TABLE IF EXISTS office_members CASCADE;
DROP TABLE IF EXISTS sites CASCADE;
DROP TABLE IF EXISTS offices CASCADE;
DROP TABLE IF EXISTS property_services CASCADE;
DROP TABLE IF EXISTS properties CASCADE;
DROP TABLE IF EXISTS threshold_configs CASCADE;
DROP TABLE IF EXISTS function_processes CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS role_permissions CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- ============================================================================
-- CREATE TABLES
-- ============================================================================

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Roles table
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Permissions table
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Role permissions junction table
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- User roles junction table
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by UUID REFERENCES users(id),
    UNIQUE(user_id, role_id)
);

-- Properties table
CREATE TABLE properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('residential', 'office')),
    address TEXT,
    description TEXT,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Property services table
CREATE TABLE property_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('electricity', 'water', 'internet', 'security', 'ott')),
    status VARCHAR(20) DEFAULT 'operational' CHECK (status IN ('operational', 'warning', 'critical', 'maintenance')),
    last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(property_id, service_type)
);

-- Offices table
CREATE TABLE offices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    capacity INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sites table (construction/project sites)
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    office_id UUID NOT NULL REFERENCES offices(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    project_type VARCHAR(100),
    start_date DATE,
    expected_end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Office members table
CREATE TABLE office_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    office_id UUID NOT NULL REFERENCES offices(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    position VARCHAR(100),
    department VARCHAR(100),
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(office_id, user_id)
);

-- Site members table
CREATE TABLE site_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(100),
    hourly_rate DECIMAL(10,2),
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(site_id, user_id)
);

-- Site attendance table
CREATE TABLE site_attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    check_in_time TIME,
    check_out_time TIME,
    hours_worked DECIMAL(4,2),
    notes TEXT,
    recorded_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(site_id, user_id, date)
);

-- Office attendance table
CREATE TABLE office_attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    office_id UUID NOT NULL REFERENCES offices(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    check_in_time TIME,
    check_out_time TIME,
    hours_worked DECIMAL(4,2),
    notes TEXT,
    recorded_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(office_id, user_id, date)
);

-- Service status logs table
CREATE TABLE service_status_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_service_id UUID NOT NULL REFERENCES property_services(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL,
    message TEXT,
    details JSONB,
    logged_by UUID REFERENCES users(id),
    logged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Uptime reports table
CREATE TABLE uptime_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    service_type VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    uptime_percentage DECIMAL(5,2),
    downtime_minutes INTEGER DEFAULT 0,
    incidents_count INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(property_id, service_type, date)
);

-- OTT services table
CREATE TABLE ott_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    service_name VARCHAR(100) NOT NULL,
    subscription_type VARCHAR(50),
    monthly_cost DECIMAL(10,2),
    renewal_date DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired')),
    login_credentials JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Generator fuel logs table
CREATE TABLE generator_fuel_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    fuel_level_liters DECIMAL(8,2) NOT NULL,
    consumption_rate DECIMAL(6,2),
    runtime_hours DECIMAL(8,2),
    efficiency_percentage DECIMAL(5,2),
    last_maintenance DATE,
    next_maintenance DATE,
    notes TEXT,
    recorded_by UUID REFERENCES users(id),
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Diesel additions table
CREATE TABLE diesel_additions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    quantity_liters DECIMAL(8,2) NOT NULL,
    cost_per_liter DECIMAL(6,2),
    total_cost DECIMAL(10,2),
    supplier VARCHAR(255),
    receipt_number VARCHAR(100),
    added_by UUID REFERENCES users(id),
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Maintenance issues table
CREATE TABLE maintenance_issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    service_type VARCHAR(50),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
    department VARCHAR(100),
    assigned_to UUID REFERENCES users(id),
    reported_by UUID NOT NULL REFERENCES users(id),
    resolved_by UUID REFERENCES users(id),
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    due_date DATE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security guard logs table
CREATE TABLE security_guard_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
    guard_name VARCHAR(255) NOT NULL,
    shift_start TIMESTAMP WITH TIME ZONE NOT NULL,
    shift_end TIMESTAMP WITH TIME ZONE,
    patrol_rounds INTEGER DEFAULT 0,
    incidents_reported INTEGER DEFAULT 0,
    visitors_logged INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function processes table
CREATE TABLE function_processes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    input_parameters JSONB,
    output_parameters JSONB,
    execution_frequency VARCHAR(50),
    last_executed TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function process logs table
CREATE TABLE function_process_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    function_process_id UUID NOT NULL REFERENCES function_processes(id) ON DELETE CASCADE,
    execution_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failure', 'warning')),
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    execution_duration_ms INTEGER,
    executed_by UUID REFERENCES users(id)
);

-- Threshold configs table
CREATE TABLE threshold_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    warning_threshold DECIMAL(10,2),
    critical_threshold DECIMAL(10,2),
    unit VARCHAR(20),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(service_type, metric_name)
);

-- Escalation logs table
CREATE TABLE escalation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    issue_id UUID NOT NULL REFERENCES maintenance_issues(id) ON DELETE CASCADE,
    escalation_level INTEGER NOT NULL,
    escalated_to UUID REFERENCES users(id),
    escalated_by UUID REFERENCES users(id),
    escalation_reason TEXT,
    escalated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT
);

-- ============================================================================
-- CREATE INDEXES
-- ============================================================================

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_properties_type ON properties(type);
CREATE INDEX idx_property_services_property_id ON property_services(property_id);
CREATE INDEX idx_property_services_status ON property_services(status);
CREATE INDEX idx_maintenance_issues_property_id ON maintenance_issues(property_id);
CREATE INDEX idx_maintenance_issues_status ON maintenance_issues(status);
CREATE INDEX idx_maintenance_issues_priority ON maintenance_issues(priority);
CREATE INDEX idx_site_attendance_date ON site_attendance(date);
CREATE INDEX idx_office_attendance_date ON office_attendance(date);
CREATE INDEX idx_generator_fuel_logs_property_id ON generator_fuel_logs(property_id);
CREATE INDEX idx_uptime_reports_date ON uptime_reports(date);

-- ============================================================================
-- INSERT SAMPLE DATA
-- ============================================================================

-- Insert Roles
INSERT INTO roles (id, name, description, is_system_role) VALUES
('550e8400-e29b-41d4-a716-************', 'admin', 'System Administrator with full access', true),
('550e8400-e29b-41d4-a716-************', 'property_manager', 'Property Manager with property management access', false),
('550e8400-e29b-41d4-a716-************', 'maintenance_staff', 'Maintenance Staff with limited access', false),
('550e8400-e29b-41d4-a716-446655440004', 'security_guard', 'Security Guard with security-related access', false),
('550e8400-e29b-41d4-a716-446655440005', 'househelp', 'House Help with basic property access', false),
('550e8400-e29b-41d4-a716-446655440006', 'office_manager', 'Office Manager with office management access', false),
('550e8400-e29b-41d4-a716-446655440007', 'site_supervisor', 'Site Supervisor with site management access', false);

-- Insert Permissions
INSERT INTO permissions (id, name, description, resource, action) VALUES
('660e8400-e29b-41d4-a716-************', 'view_dashboard', 'View main dashboard', 'dashboard', 'read'),
('660e8400-e29b-41d4-a716-************', 'manage_users', 'Manage system users', 'users', 'write'),
('660e8400-e29b-41d4-a716-************', 'manage_roles', 'Manage user roles', 'roles', 'write'),
('660e8400-e29b-41d4-a716-446655440004', 'manage_permissions', 'Manage permissions', 'permissions', 'write'),
('660e8400-e29b-41d4-a716-446655440005', 'view_properties', 'View properties', 'properties', 'read'),
('660e8400-e29b-41d4-a716-446655440006', 'manage_properties', 'Manage properties', 'properties', 'write'),
('660e8400-e29b-41d4-a716-446655440007', 'view_maintenance', 'View maintenance issues', 'maintenance', 'read'),
('660e8400-e29b-41d4-a716-446655440008', 'manage_maintenance', 'Manage maintenance issues', 'maintenance', 'write'),
('660e8400-e29b-41d4-a716-446655440009', 'view_attendance', 'View attendance records', 'attendance', 'read'),
('660e8400-e29b-41d4-a716-446655440010', 'manage_attendance', 'Manage attendance records', 'attendance', 'write'),
('660e8400-e29b-41d4-a716-446655440011', 'view_security', 'View security logs', 'security', 'read'),
('660e8400-e29b-41d4-a716-446655440012', 'manage_security', 'Manage security logs', 'security', 'write'),
('660e8400-e29b-41d4-a716-446655440013', 'view_reports', 'View system reports', 'reports', 'read'),
('660e8400-e29b-41d4-a716-446655440014', 'manage_thresholds', 'Manage system thresholds', 'thresholds', 'write'),
('660e8400-e29b-41d4-a716-446655440015', 'view_function_processes', 'View function processes', 'function_processes', 'read'),
('660e8400-e29b-41d4-a716-446655440016', 'manage_function_processes', 'Manage function processes', 'function_processes', 'write');

-- Insert Role Permissions (Admin gets all permissions)
INSERT INTO role_permissions (role_id, permission_id) 
SELECT '550e8400-e29b-41d4-a716-************', id FROM permissions;

-- Property Manager permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440005'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440006'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440007'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440008'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440013');

-- Maintenance Staff permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440005'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440007'),
('550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-446655440008');

-- Security Guard permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440005'),
('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440011'),
('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440012');

-- Househelp permissions
INSERT INTO role_permissions (role_id, permission_id) VALUES
('550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-************'),
('550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440005'),
('550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440007');

-- Insert Users
INSERT INTO users (id, email, password_hash, full_name, phone, is_active) VALUES
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'System Administrator', '+91-9876543210', true),
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Property Manager', '+91-9876543211', true),
('770e8400-e29b-41d4-a716-************', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Maintenance Staff', '+91-9876543212', true),
('770e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Security Guard', '+91-9876543213', true),
('770e8400-e29b-41d4-a716-446655440005', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'House Help', '+91-9876543214', true),
('770e8400-e29b-41d4-a716-446655440006', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Office Manager', '+91-9876543215', true),
('770e8400-e29b-41d4-a716-446655440007', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Site Supervisor', '+91-9876543216', true),
('770e8400-e29b-41d4-a716-446655440008', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Construction Worker 1', '+91-9876543217', true),
('770e8400-e29b-41d4-a716-446655440009', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Construction Worker 2', '+91-9876543218', true),
('770e8400-e29b-41d4-a716-446655440010', '<EMAIL>', '$2b$10$rQZ8kHp0rQZ8kHp0rQZ8kO', 'Office Staff 1', '+91-9876543219', true);

-- Insert User Roles
INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************'),
('770e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', '770e8400-e29b-41d4-a716-************'),
('770e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440005', '770e8400-e29b-41d4-a716-************'),
('770e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440006', '770e8400-e29b-41d4-a716-************'),
('770e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-************');

-- Insert Properties
INSERT INTO properties (id, name, type, address, description, image_url, is_active) VALUES
('880e8400-e29b-41d4-a716-************', 'Main House', 'residential', '123 Main Street, Bangalore', 'Primary residential property with all amenities', '/modern-house.png', true),
('880e8400-e29b-41d4-a716-************', 'Guest House', 'residential', '125 Main Street, Bangalore', 'Guest accommodation facility', '/cozy-guest-house.png', true),
('880e8400-e29b-41d4-a716-************', 'Staff Quarters', 'residential', '127 Main Street, Bangalore', 'Staff accommodation facility', '/modern-house.png', true),
('880e8400-e29b-41d4-a716-446655440004', 'Head Office', 'office', '456 Business District, Bangalore', 'Main office building', '/modern-office-building.png', true),
('880e8400-e29b-41d4-a716-446655440005', 'Branch Office A', 'office', '789 Tech Park, Bangalore', 'Branch office location A', '/branch-office-building.png', true),
('880e8400-e29b-41d4-a716-446655440006', 'Branch Office B', 'office', '321 Industrial Area, Bangalore', 'Branch office location B', '/branch-office-building.png', true);

-- Insert Property Services
INSERT INTO property_services (property_id, service_type, status, notes) VALUES
('880e8400-e29b-41d4-a716-************', 'electricity', 'operational', 'Generator fuel at 75%'),
('880e8400-e29b-41d4-a716-************', 'water', 'operational', 'All systems normal'),
('880e8400-e29b-41d4-a716-************', 'internet', 'operational', 'Primary connection stable'),
('880e8400-e29b-41d4-a716-************', 'security', 'operational', 'All cameras functional'),
('880e8400-e29b-41d4-a716-************', 'ott', 'operational', 'All subscriptions active'),
('880e8400-e29b-41d4-a716-************', 'electricity', 'warning', 'Generator fuel low at 25%'),
('880e8400-e29b-41d4-a716-************', 'water', 'operational', 'Pump working normally'),
('880e8400-e29b-41d4-a716-************', 'internet', 'operational', 'Backup connection active'),
('880e8400-e29b-41d4-a716-************', 'security', 'operational', 'Security system active'),
('880e8400-e29b-41d4-a716-************', 'electricity', 'operational', 'Grid power stable'),
('880e8400-e29b-41d4-a716-************', 'water', 'critical', 'Water pump failure'),
('880e8400-e29b-41d4-a716-446655440004', 'electricity', 'operational', 'UPS systems normal'),
('880e8400-e29b-41d4-a716-446655440004', 'internet', 'operational', 'High-speed connection'),
('880e8400-e29b-41d4-a716-446655440004', 'security', 'operational', 'Access control active');

-- Insert Offices
INSERT INTO offices (id, property_id, name, location, capacity, is_active) VALUES
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-446655440004', 'Head Office Main Floor', 'Ground Floor', 50, true),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-446655440004', 'Head Office Second Floor', 'Second Floor', 30, true),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-446655440005', 'Branch A Operations', 'Main Floor', 25, true),
('990e8400-e29b-41d4-a716-446655440004', '880e8400-e29b-41d4-a716-446655440006', 'Branch B Operations', 'Main Floor', 20, true);

-- Insert Sites
INSERT INTO sites (id, office_id, name, location, project_type, start_date, expected_end_date, is_active) VALUES
('aa0e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'Residential Complex A', 'Whitefield, Bangalore', 'Residential Construction', '2024-01-15', '2025-06-30', true),
('aa0e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'Commercial Plaza B', 'Electronic City, Bangalore', 'Commercial Construction', '2024-03-01', '2025-12-31', true),
('aa0e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'Infrastructure Project C', 'Hebbal, Bangalore', 'Infrastructure', '2024-02-01', '2025-08-15', true);

-- Insert Office Members
INSERT INTO office_members (office_id, user_id, position, department, start_date, is_active) VALUES
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440006', 'Office Manager', 'Administration', '2024-01-01', true),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440010', 'Administrative Assistant', 'Administration', '2024-01-15', true),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440006', 'Branch Manager', 'Operations', '2024-02-01', true);

-- Insert Site Members
INSERT INTO site_members (site_id, user_id, role, hourly_rate, start_date, is_active) VALUES
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440007', 'Site Supervisor', 500.00, '2024-01-15', true),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440008', 'Construction Worker', 300.00, '2024-01-20', true),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440009', 'Construction Worker', 300.00, '2024-01-20', true),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440007', 'Site Supervisor', 500.00, '2024-03-01', true),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440008', 'Construction Worker', 300.00, '2024-03-05', true);

-- Insert Site Attendance (last 7 days)
INSERT INTO site_attendance (site_id, user_id, date, check_in_time, check_out_time, hours_worked, recorded_by) VALUES
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440008', '2025-01-22', '08:00:00', '17:00:00', 8.0, '770e8400-e29b-41d4-a716-446655440007'),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440009', '2025-01-22', '08:15:00', '17:00:00', 7.75, '770e8400-e29b-41d4-a716-446655440007'),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440008', '2025-01-23', '08:00:00', '17:00:00', 8.0, '770e8400-e29b-41d4-a716-446655440007'),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440009', '2025-01-23', '08:00:00', '16:30:00', 7.5, '770e8400-e29b-41d4-a716-446655440007'),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440008', '2025-01-24', '08:00:00', '17:00:00', 8.0, '770e8400-e29b-41d4-a716-446655440007'),
('aa0e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440009', '2025-01-24', '08:30:00', '17:00:00', 7.5, '770e8400-e29b-41d4-a716-446655440007');

-- Insert Office Attendance (last 7 days)
INSERT INTO office_attendance (office_id, user_id, date, check_in_time, check_out_time, hours_worked, recorded_by) VALUES
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440010', '2025-01-22', '09:00:00', '18:00:00', 8.0, '770e8400-e29b-41d4-a716-446655440006'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440010', '2025-01-23', '09:15:00', '18:00:00', 7.75, '770e8400-e29b-41d4-a716-446655440006'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440010', '2025-01-24', '09:00:00', '18:00:00', 8.0, '770e8400-e29b-41d4-a716-446655440006');

-- Insert Generator Fuel Logs
INSERT INTO generator_fuel_logs (property_id, fuel_level_liters, consumption_rate, runtime_hours, efficiency_percentage, last_maintenance, next_maintenance, recorded_by) VALUES
('880e8400-e29b-41d4-a716-************', 750.00, 12.5, 156.5, 85.2, '2024-12-15', '2025-03-15', '770e8400-e29b-41d4-a716-************'),
('880e8400-e29b-41d4-a716-************', 250.00, 15.0, 89.2, 82.1, '2024-11-20', '2025-02-20', '770e8400-e29b-41d4-a716-************'),
('880e8400-e29b-41d4-a716-************', 500.00, 10.8, 203.7, 87.5, '2025-01-10', '2025-04-10', '770e8400-e29b-41d4-a716-************');

-- Insert Diesel Additions
INSERT INTO diesel_additions (property_id, quantity_liters, cost_per_liter, total_cost, supplier, receipt_number, added_by) VALUES
('880e8400-e29b-41d4-a716-************', 500.00, 85.50, 42750.00, 'Bharat Petroleum', 'BP2025001234', '770e8400-e29b-41d4-a716-************'),
('880e8400-e29b-41d4-a716-************', 300.00, 86.00, 25800.00, 'Indian Oil', 'IO2025001567', '770e8400-e29b-41d4-a716-************'),
('880e8400-e29b-41d4-a716-************', 400.00, 85.75, 34300.00, 'Hindustan Petroleum', 'HP2025001890', '770e8400-e29b-41d4-a716-************');

-- Insert OTT Services
INSERT INTO ott_services (property_id, service_name, subscription_type, monthly_cost, renewal_date, status, notes) VALUES
('880e8400-e29b-41d4-a716-************', 'Netflix', 'Premium', 799.00, '2025-02-15', 'active', '4K streaming plan'),
('880e8400-e29b-41d4-a716-************', 'Amazon Prime Video', 'Annual', 1499.00, '2025-08-20', 'active', 'Annual subscription'),
('880e8400-e29b-41d4-a716-************', 'Disney+ Hotstar', 'Super', 899.00, '2025-03-10', 'active', 'Sports and movies'),
('880e8400-e29b-41d4-a716-************', 'Netflix', 'Basic', 199.00, '2025-02-28', 'active', 'Basic plan'),
('880e8400-e29b-41d4-a716-************', 'YouTube Premium', 'Individual', 129.00, '2025-02-05', 'active', 'Ad-free experience');

-- Insert Maintenance Issues
INSERT INTO maintenance_issues (property_id, service_type, title, description, priority, status, department, reported_by, assigned_to, due_date) VALUES
('880e8400-e29b-41d4-a716-************', 'water', 'Water Pump Failure', 'Main water pump has stopped working, no water supply to the building', 'critical', 'open', 'Plumbing', '770e8400-e29b-41d4-a716-446655440005', '770e8400-e29b-41d4-a716-************', '2025-01-30'),
('880e8400-e29b-41d4-a716-************', 'electricity', 'Generator Fuel Low', 'Generator fuel level is critically low, needs immediate refilling', 'high', 'in_progress', 'Electrical', '770e8400-e29b-41d4-a716-446655440004', '770e8400-e29b-41d4-a716-************', '2025-01-29'),
('880e8400-e29b-41d4-a716-************', 'security', 'CCTV Camera Malfunction', 'Camera #3 in the garden area is not recording properly', 'medium', 'open', 'Security', '770e8400-e29b-41d4-a716-446655440004', '770e8400-e29b-41d4-a716-************', '2025-02-05'),
('880e8400-e29b-41d4-a716-446655440004', 'internet', 'Slow Internet Speed', 'Internet connection is slower than usual in the second floor', 'low', 'open', 'IT', '770e8400-e29b-41d4-a716-446655440010', '770e8400-e29b-41d4-a716-************', '2025-02-10');

-- Insert Security Guard Logs
INSERT INTO security_guard_logs (property_id, guard_name, shift_start, shift_end, patrol_rounds, incidents_reported, visitors_logged, notes) VALUES
('880e8400-e29b-41d4-a716-************', 'Rajesh Kumar', '2025-01-28 22:00:00+05:30', '2025-01-29 06:00:00+05:30', 4, 0, 2, 'Quiet night, two late visitors logged'),
('880e8400-e29b-41d4-a716-************', 'Suresh Babu', '2025-01-28 18:00:00+05:30', '2025-01-29 02:00:00+05:30', 3, 1, 1, 'Minor incident: stray dog in premises, safely removed'),
('880e8400-e29b-41d4-a716-************', 'Rajesh Kumar', '2025-01-29 22:00:00+05:30', '2025-01-30 06:00:00+05:30', 4, 0, 0, 'All systems normal, no incidents'),
('880e8400-e29b-41d4-a716-446655440004', 'Mohan Singh', '2025-01-29 20:00:00+05:30', '2025-01-30 08:00:00+05:30', 6, 0, 3, 'Office security normal, late working staff logged');

-- Insert Uptime Reports
INSERT INTO uptime_reports (property_id, service_type, date, uptime_percentage, downtime_minutes, incidents_count, notes) VALUES
('880e8400-e29b-41d4-a716-************', 'electricity', '2025-01-28', 99.5, 7, 1, 'Brief power fluctuation at 14:30'),
('880e8400-e29b-41d4-a716-************', 'internet', '2025-01-28', 100.0, 0, 0, 'Stable connection throughout the day'),
('880e8400-e29b-41d4-a716-************', 'electricity', '2025-01-28', 95.2, 69, 2, 'Generator maintenance caused downtime'),
('880e8400-e29b-41d4-a716-************', 'internet', '2025-01-28', 98.8, 17, 1, 'Router restart required once'),
('880e8400-e29b-41d4-a716-446655440004', 'electricity', '2025-01-28', 100.0, 0, 0, 'UPS systems working perfectly'),
('880e8400-e29b-41d4-a716-446655440004', 'internet', '2025-01-28', 99.9, 1, 0, 'Excellent connectivity');

-- Insert Function Processes
INSERT INTO function_processes (id, name, description, category, input_parameters, output_parameters, execution_frequency, status) VALUES
('bb0e8400-e29b-41d4-a716-************', 'Daily Status Check', 'Automated daily status check for all property services', 'Monitoring', '{"properties": "all", "services": "all"}', '{"status_summary": "object", "alerts": "array"}', 'daily', 'active'),
('bb0e8400-e29b-41d4-a716-************', 'Fuel Level Monitor', 'Monitor generator fuel levels and trigger alerts', 'Monitoring', '{"threshold_warning": 30, "threshold_critical": 15}', '{"fuel_levels": "array", "alerts": "array"}', 'hourly', 'active'),
('bb0e8400-e29b-41d4-a716-************', 'Maintenance Escalation', 'Escalate overdue maintenance issues', 'Maintenance', '{"overdue_hours": 24, "escalation_levels": 3}', '{"escalated_issues": "array"}', 'hourly', 'active'),
('bb0e8400-e29b-41d4-a716-446655440004', 'Attendance Report Generator', 'Generate daily attendance reports', 'Reporting', '{"date": "string", "location_type": "string"}', '{"attendance_summary": "object"}', 'daily', 'active');

-- Insert Function Process Logs
INSERT INTO function_process_logs (function_process_id, status, input_data, output_data, execution_duration_ms) VALUES
('bb0e8400-e29b-41d4-a716-************', 'success', '{"date": "2025-01-29", "properties": "all"}', '{"total_properties": 6, "operational": 4, "warning": 1, "critical": 1}', 2340),
('bb0e8400-e29b-41d4-a716-************', 'success', '{"threshold_warning": 30, "threshold_critical": 15}', '{"alerts": [{"property": "Guest House", "fuel_level": 25, "status": "warning"}]}', 1250),
('bb0e8400-e29b-41d4-a716-************', 'success', '{"overdue_hours": 24}', '{"escalated_issues": [{"issue_id": "maintenance_001", "escalation_level": 1}]}', 890),
('bb0e8400-e29b-41d4-a716-446655440004', 'success', '{"date": "2025-01-29"}', '{"total_attendance": 15, "sites": 8, "offices": 7}', 1560);

-- Insert Threshold Configs
INSERT INTO threshold_configs (service_type, metric_name, warning_threshold, critical_threshold, unit, description, is_active) VALUES
('electricity', 'generator_fuel_level', 30.0, 15.0, 'percentage', 'Generator fuel level thresholds', true),
('electricity', 'ups_battery_level', 25.0, 10.0, 'percentage', 'UPS battery level thresholds', true),
('water', 'tank_level', 20.0, 10.0, 'percentage', 'Water tank level thresholds', true),
('water', 'pressure', 15.0, 10.0, 'psi', 'Water pressure thresholds', true),
('internet', 'uptime', 95.0, 90.0, 'percentage', 'Internet uptime thresholds', true),
('internet', 'speed', 50.0, 25.0, 'mbps', 'Internet speed thresholds', true),
('security', 'camera_uptime', 95.0, 85.0, 'percentage', 'Security camera uptime thresholds', true),
('maintenance', 'response_time', 4.0, 8.0, 'hours', 'Maintenance response time thresholds', true);

-- Insert Service Status Logs (recent activity)
INSERT INTO service_status_logs (property_service_id, status, message, details, logged_by) 
SELECT 
    ps.id,
    'operational',
    'System check completed successfully',
    '{"last_check": "2025-01-29T10:00:00Z", "all_systems": "normal"}',
    '770e8400-e29b-41d4-a716-************'
FROM property_services ps 
WHERE ps.status = 'operational'
LIMIT 5;

INSERT INTO service_status_logs (property_service_id, status, message, details, logged_by) 
SELECT 
    ps.id,
    'warning',
    'Warning condition detected',
    '{"issue": "fuel_low", "current_level": "25%", "threshold": "30%"}',
    '770e8400-e29b-41d4-a716-************'
FROM property_services ps 
WHERE ps.status = 'warning'
LIMIT 2;

INSERT INTO service_status_logs (property_service_id, status, message, details, logged_by) 
SELECT 
    ps.id,
    'critical',
    'Critical issue requires immediate attention',
    '{"issue": "pump_failure", "impact": "no_water_supply", "priority": "critical"}',
    '770e8400-e29b-41d4-a716-************'
FROM property_services ps 
WHERE ps.status = 'critical'
LIMIT 1;

-- Insert Escalation Logs
INSERT INTO escalation_logs (issue_id, escalation_level, escalated_to, escalated_by, escalation_reason) 
SELECT 
    mi.id,
    1,
    '770e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    'Issue not resolved within 24 hours'
FROM maintenance_issues mi 
WHERE mi.priority = 'critical' AND mi.status = 'open'
LIMIT 1;

-- ============================================================================
-- CREATE VIEWS FOR COMMON QUERIES
-- ============================================================================

-- View for user permissions
CREATE OR REPLACE VIEW user_permissions AS
SELECT 
    u.id as user_id,
    u.email,
    u.full_name,
    r.name as role_name,
    p.name as permission_name,
    p.resource,
    p.action
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.is_active = true;

-- View for property status summary
CREATE OR REPLACE VIEW property_status_summary AS
SELECT 
    p.id as property_id,
    p.name as property_name,
    p.type as property_type,
    COUNT(ps.id) as total_services,
    COUNT(CASE WHEN ps.status = 'operational' THEN 1 END) as operational_count,
    COUNT(CASE WHEN ps.status = 'warning' THEN 1 END) as warning_count,
    COUNT(CASE WHEN ps.status = 'critical' THEN 1 END) as critical_count,
    COUNT(CASE WHEN ps.status = 'maintenance' THEN 1 END) as maintenance_count
FROM properties p
LEFT JOIN property_services ps ON p.id = ps.property_id
WHERE p.is_active = true
GROUP BY p.id, p.name, p.type;

-- View for maintenance issues summary
CREATE OR REPLACE VIEW maintenance_summary AS
SELECT 
    p.name as property_name,
    mi.service_type,
    mi.priority,
    mi.status,
    COUNT(*) as issue_count,
    AVG(EXTRACT(EPOCH FROM (COALESCE(mi.resolved_at, NOW()) - mi.created_at))/3600) as avg_resolution_hours
FROM maintenance_issues mi
JOIN properties p ON mi.property_id = p.id
GROUP BY p.name, mi.service_type, mi.priority, mi.status;

-- ============================================================================
-- CREATE FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_properties_updated_at BEFORE UPDATE ON properties FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_property_services_updated_at BEFORE UPDATE ON property_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_issues_updated_at BEFORE UPDATE ON maintenance_issues FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ott_services_updated_at BEFORE UPDATE ON ott_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- GRANT PERMISSIONS (for application user)
-- ============================================================================

-- Note: Replace 'app_user' with your actual application database user
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO app_user;

-- ============================================================================
-- BACKUP COMPLETION SUMMARY
-- ============================================================================

SELECT 
    'Database backup completed successfully' as status,
    NOW() as completed_at,
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM properties) as total_properties,
    (SELECT COUNT(*) FROM maintenance_issues) as total_maintenance_issues,
    (SELECT COUNT(*) FROM property_services) as total_services;