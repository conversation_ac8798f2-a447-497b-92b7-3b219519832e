// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_functions_api_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PropertyFunctionsResponse _$PropertyFunctionsResponseFromJson(
        Map<String, dynamic> json) =>
    PropertyFunctionsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data:
          PropertyFunctionsData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PropertyFunctionsResponseToJson(
        PropertyFunctionsResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

PropertyFunctionsData _$PropertyFunctionsDataFromJson(
        Map<String, dynamic> json) =>
    PropertyFunctionsData(
      property: PropertyInfo.fromJson(json['property'] as Map<String, dynamic>),
      functions: (json['functions'] as List<dynamic>)
          .map((e) => PropertyFunction.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PropertyFunctionsDataToJson(
        PropertyFunctionsData instance) =>
    <String, dynamic>{
      'property': instance.property,
      'functions': instance.functions,
    };

PropertyInfo _$PropertyInfoFromJson(Map<String, dynamic> json) => PropertyInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$PropertyInfoToJson(PropertyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };

PropertyFunction _$PropertyFunctionFromJson(Map<String, dynamic> json) =>
    PropertyFunction(
      id: json['id'] as String?,
      functionName: json['function_name'] as String,
      isEnabled: json['is_enabled'] as bool,
      configuration: json['configuration'] as Map<String, dynamic>,
      displayOrder: (json['display_order'] as num).toInt(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$PropertyFunctionToJson(PropertyFunction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'function_name': instance.functionName,
      'is_enabled': instance.isEnabled,
      'configuration': instance.configuration,
      'display_order': instance.displayOrder,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

UpdatePropertyFunctionsRequest _$UpdatePropertyFunctionsRequestFromJson(
        Map<String, dynamic> json) =>
    UpdatePropertyFunctionsRequest(
      functions: (json['functions'] as List<dynamic>)
          .map(
              (e) => PropertyFunctionUpdate.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$UpdatePropertyFunctionsRequestToJson(
        UpdatePropertyFunctionsRequest instance) =>
    <String, dynamic>{
      'functions': instance.functions,
    };

PropertyFunctionUpdate _$PropertyFunctionUpdateFromJson(
        Map<String, dynamic> json) =>
    PropertyFunctionUpdate(
      functionName: json['function_name'] as String,
      isEnabled: json['is_enabled'] as bool,
      configuration: json['configuration'] as Map<String, dynamic>?,
      displayOrder: (json['display_order'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PropertyFunctionUpdateToJson(
        PropertyFunctionUpdate instance) =>
    <String, dynamic>{
      'function_name': instance.functionName,
      'is_enabled': instance.isEnabled,
      'configuration': instance.configuration,
      'display_order': instance.displayOrder,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _PropertyFunctionsApiService implements PropertyFunctionsApiService {
  _PropertyFunctionsApiService(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<PropertyFunctionsResponse> getPropertyFunctions(
      String propertyId) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<PropertyFunctionsResponse>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${propertyId}/functions',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late PropertyFunctionsResponse _value;
    try {
      _value = PropertyFunctionsResponse.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<PropertyFunctionsResponse> updatePropertyFunctions(
    String propertyId,
    UpdatePropertyFunctionsRequest request,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(request.toJson());
    final _options = _setStreamType<PropertyFunctionsResponse>(Options(
      method: 'PUT',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/properties/${propertyId}/functions',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late PropertyFunctionsResponse _value;
    try {
      _value = PropertyFunctionsResponse.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
