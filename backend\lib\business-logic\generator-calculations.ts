/**
 * Generator fuel calculation utilities
 * Ported from V1 business logic with improvements
 */

export interface GeneratorStats {
  fuelInGenerator: number;
  totalFuel: number;
  powerBackupHours: number;
  consumptionRate: number;
  efficiency: number;
}

export interface FuelLogData {
  fuelLevelLiters: number;
  consumptionRate?: number;
  runtimeHours?: number;
  efficiencyPercentage?: number;
}

// Constants - should be configurable per property in production
const DEFAULT_GENERATOR_CAPACITY = 100; // liters
const DEFAULT_CONSUMPTION_RATE = 6.5; // liters per hour
const DEFAULT_EFFICIENCY = 85; // percentage

/**
 * Calculate comprehensive generator statistics
 * Enhanced version of V1's calculateGeneratorStats
 */
export function calculateGeneratorStats(
  fuelData: FuelLogData,
  generatorCapacity: number = DEFAULT_GENERATOR_CAPACITY
): GeneratorStats {
  if (!fuelData) {
    return {
      fuelInGenerator: 0,
      totalFuel: 0,
      powerBackupHours: 0,
      consumptionRate: DEFAULT_CONSUMPTION_RATE,
      efficiency: DEFAULT_EFFICIENCY,
    };
  }

  const fuelInGenerator = fuelData.fuelLevelLiters || 0;
  const totalFuel = fuelInGenerator;
  
  // Use provided consumption rate or calculate from runtime data
  let consumptionRate = fuelData.consumptionRate || DEFAULT_CONSUMPTION_RATE;
  
  if (fuelData.runtimeHours && fuelData.runtimeHours > 0) {
    // Calculate actual consumption rate from runtime data
    const fuelConsumed = generatorCapacity - fuelInGenerator;
    consumptionRate = fuelConsumed / fuelData.runtimeHours;
  }

  const efficiency = fuelData.efficiencyPercentage || DEFAULT_EFFICIENCY;
  
  // Calculate power backup hours with efficiency factor
  const effectiveConsumptionRate = consumptionRate * (100 / efficiency);
  const powerBackupHours = totalFuel > 0 
    ? Math.round((totalFuel / effectiveConsumptionRate) * 10) / 10 
    : 0;

  return {
    fuelInGenerator,
    totalFuel,
    powerBackupHours,
    consumptionRate,
    efficiency,
  };
}

/**
 * Determine fuel status based on backup hours
 * Enhanced version with configurable thresholds
 */
export function getFuelStatus(
  backupHours: number,
  criticalThreshold: number = 6,
  warningThreshold: number = 9
): 'critical' | 'warning' | 'good' {
  if (backupHours < criticalThreshold) return 'critical';
  if (backupHours < warningThreshold) return 'warning';
  return 'good';
}

/**
 * Calculate fuel consumption prediction
 * New feature not in V1
 */
export function predictFuelConsumption(
  currentFuelLevel: number,
  consumptionRate: number,
  hoursToPredict: number = 24
): {
  predictedLevel: number;
  hoursUntilEmpty: number;
  refuelRecommended: boolean;
} {
  const fuelConsumedInPeriod = consumptionRate * hoursToPredict;
  const predictedLevel = Math.max(0, currentFuelLevel - fuelConsumedInPeriod);
  const hoursUntilEmpty = currentFuelLevel / consumptionRate;
  const refuelRecommended = hoursUntilEmpty < 12; // Recommend refuel if less than 12 hours

  return {
    predictedLevel,
    hoursUntilEmpty: Math.round(hoursUntilEmpty * 10) / 10,
    refuelRecommended,
  };
}

/**
 * Validate fuel log data
 * Enhanced validation beyond schema validation
 */
export function validateFuelLogData(data: FuelLogData): {
  isValid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];

  // Check for impossible values
  if (data.fuelLevelLiters < 0) {
    errors.push('Fuel level cannot be negative');
  }

  if (data.fuelLevelLiters > DEFAULT_GENERATOR_CAPACITY) {
    warnings.push(`Fuel level (${data.fuelLevelLiters}L) exceeds typical generator capacity (${DEFAULT_GENERATOR_CAPACITY}L)`);
  }

  if (data.consumptionRate && data.consumptionRate > 20) {
    warnings.push(`Consumption rate (${data.consumptionRate}L/h) seems unusually high`);
  }

  if (data.efficiencyPercentage && data.efficiencyPercentage > 100) {
    errors.push('Efficiency percentage cannot exceed 100%');
  }

  if (data.runtimeHours && data.runtimeHours > 24) {
    warnings.push(`Runtime hours (${data.runtimeHours}h) exceeds 24 hours`);
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  };
}

/**
 * Generate fuel log insights
 * New feature for enhanced reporting
 */
export function generateFuelInsights(
  currentLog: FuelLogData,
  previousLogs: FuelLogData[]
): {
  trend: 'improving' | 'declining' | 'stable';
  averageConsumption: number;
  efficiencyTrend: 'improving' | 'declining' | 'stable';
  recommendations: string[];
} {
  const recommendations: string[] = [];
  
  if (previousLogs.length === 0) {
    return {
      trend: 'stable',
      averageConsumption: currentLog.consumptionRate || DEFAULT_CONSUMPTION_RATE,
      efficiencyTrend: 'stable',
      recommendations: ['Collect more data for trend analysis'],
    };
  }

  // Calculate average consumption from previous logs
  const consumptionRates = previousLogs
    .filter(log => log.consumptionRate && log.consumptionRate > 0)
    .map(log => log.consumptionRate!);
  
  const averageConsumption = consumptionRates.length > 0
    ? consumptionRates.reduce((sum, rate) => sum + rate, 0) / consumptionRates.length
    : DEFAULT_CONSUMPTION_RATE;

  // Determine trends
  const currentConsumption = currentLog.consumptionRate || averageConsumption;
  const consumptionTrend = currentConsumption > averageConsumption * 1.1 ? 'declining' :
                          currentConsumption < averageConsumption * 0.9 ? 'improving' : 'stable';

  const efficiencies = previousLogs
    .filter(log => log.efficiencyPercentage && log.efficiencyPercentage > 0)
    .map(log => log.efficiencyPercentage!);
  
  const currentEfficiency = currentLog.efficiencyPercentage || DEFAULT_EFFICIENCY;
  const averageEfficiency = efficiencies.length > 0
    ? efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length
    : DEFAULT_EFFICIENCY;

  const efficiencyTrend = currentEfficiency > averageEfficiency * 1.05 ? 'improving' :
                         currentEfficiency < averageEfficiency * 0.95 ? 'declining' : 'stable';

  // Generate recommendations
  if (consumptionTrend === 'declining') {
    recommendations.push('Consider generator maintenance - fuel consumption is increasing');
  }
  
  if (efficiencyTrend === 'declining') {
    recommendations.push('Generator efficiency is declining - schedule maintenance check');
  }

  if (currentLog.fuelLevelLiters < DEFAULT_GENERATOR_CAPACITY * 0.25) {
    recommendations.push('Fuel level is low - consider refueling soon');
  }

  return {
    trend: consumptionTrend,
    averageConsumption: Math.round(averageConsumption * 100) / 100,
    efficiencyTrend,
    recommendations,
  };
}
