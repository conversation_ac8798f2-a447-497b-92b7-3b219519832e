import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest, createMaintenanceIssueSchema, paginationSchema } from '@/lib/validation';
import { createApiResponse, createPaginationResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';

async function getMaintenanceIssuesHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const params = getQueryParams(request);
    
    // Validate pagination parameters
    const paginationValidation = validateRequest(paginationSchema, {
      page: params.page || 1,
      limit: params.limit || 10,
    });
    
    if (!paginationValidation.isValid) {
      return Response.json(
        createApiResponse(null, 'Invalid pagination parameters', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { page, limit } = paginationValidation.data;
    const { property_id, status, priority } = params;

    // Build where clause
    const where: any = {};
    
    if (property_id) {
      where.propertyId = property_id;
    }
    
    if (status && ['open', 'in_progress', 'resolved', 'closed'].includes(status)) {
      where.status = status.toUpperCase();
    }
    
    if (priority && ['low', 'medium', 'high', 'critical'].includes(priority)) {
      where.priority = priority.toUpperCase();
    }

    // Get total count for pagination
    const total = await prisma.maintenanceIssue.count({ where });

    // Get maintenance issues with pagination
    const issues = await prisma.maintenanceIssue.findMany({
      where,
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        reporter: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        assignee: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Transform data to match API response format
    const transformedIssues = issues.map(issue => ({
      id: issue.id,
      property_id: issue.propertyId,
      title: issue.title,
      description: issue.description,
      priority: issue.priority.toLowerCase(),
      status: issue.status.toLowerCase(),
      service_type: issue.serviceType,
      department: issue.department,
      reported_by: issue.reportedBy,
      assigned_to: issue.assignedTo,
      due_date: issue.dueDate,
      created_at: issue.createdAt,
      property: {
        id: issue.property.id,
        name: issue.property.name,
        type: issue.property.type.toLowerCase(),
      },
      reporter: {
        id: issue.reporter.id,
        full_name: issue.reporter.fullName,
        email: issue.reporter.email,
      },
      assignee: issue.assignee ? {
        id: issue.assignee.id,
        full_name: issue.assignee.fullName,
        email: issue.assignee.email,
      } : null,
    }));

    return Response.json(
      createPaginationResponse(transformedIssues, page, limit, total),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch maintenance issues');
  }
}

async function createMaintenanceIssueHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createMaintenanceIssueSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { property_id, title, description, priority, service_type, department, due_date } = validation.data;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: property_id },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Create maintenance issue
    const issue = await prisma.maintenanceIssue.create({
      data: {
        propertyId: property_id,
        title,
        description,
        priority: priority.toUpperCase(),
        serviceType: service_type,
        department,
        reportedBy: currentUser.id,
        dueDate: due_date ? new Date(due_date) : null,
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        reporter: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Maintenance issue created successfully',
        issue: {
          id: issue.id,
          property_id: issue.propertyId,
          title: issue.title,
          description: issue.description,
          priority: issue.priority.toLowerCase(),
          status: issue.status.toLowerCase(),
          service_type: issue.serviceType,
          department: issue.department,
          reported_by: issue.reportedBy,
          due_date: issue.dueDate,
          created_at: issue.createdAt,
          property: {
            id: issue.property.id,
            name: issue.property.name,
            type: issue.property.type.toLowerCase(),
          },
          reporter: {
            id: issue.reporter.id,
            full_name: issue.reporter.fullName,
            email: issue.reporter.email,
          },
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create maintenance issue');
  }
}

export const GET = requireAuth(getMaintenanceIssuesHandler);
export const POST = requireAuth(createMaintenanceIssueHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
