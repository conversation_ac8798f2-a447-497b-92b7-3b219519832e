import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../core/constants/api_constants.dart';
import '../../../shared/models/property.dart';
import '../../../shared/models/property_service.dart';
import '../../../shared/models/api_response.dart';

part 'properties_api_service.g.dart';

@RestApi()
abstract class PropertiesApiService {
  factory PropertiesApiService(Dio dio) = _PropertiesApiService;

  @GET(ApiConstants.properties)
  Future<ApiResponse<List<Property>>> getProperties();

  @GET('${ApiConstants.properties}/{id}')
  Future<ApiResponse<Property>> getPropertyById(@Path('id') String id);

  @POST(ApiConstants.properties)
  Future<HttpResponse<dynamic>> createProperty(@Body() CreatePropertyRequest request);

  @PUT('${ApiConstants.properties}/{id}')
  Future<ApiResponse<Property>> updateProperty(
    @Path('id') String id,
    @Body() UpdatePropertyRequest request,
  );

  @DELETE('${ApiConstants.properties}/{id}')
  Future<ApiResponse<VoidResponse>> deleteProperty(@Path('id') String id);

  @GET('${ApiConstants.properties}/{id}/services')
  Future<ApiResponse<List<PropertyService>>> getPropertyServices(@Path('id') String id);

  @POST('${ApiConstants.properties}/{id}/services')
  Future<ApiResponse<PropertyService>> addPropertyService(
    @Path('id') String id,
    @Body() CreatePropertyServiceRequest request,
  );

  @PUT('${ApiConstants.properties}/{propertyId}/services/{serviceId}')
  Future<ApiResponse<PropertyService>> updatePropertyService(
    @Path('propertyId') String propertyId,
    @Path('serviceId') String serviceId,
    @Body() UpdatePropertyServiceRequest request,
  );

  @DELETE('${ApiConstants.properties}/{propertyId}/services/{serviceId}')
  Future<ApiResponse<VoidResponse>> deletePropertyService(
    @Path('propertyId') String propertyId,
    @Path('serviceId') String serviceId,
  );
}

// Request models
@JsonSerializable()
class CreatePropertyRequest {
  final String name;
  final String type;
  @JsonKey(name: 'parent_property_id')
  final String? parentPropertyId;
  final String? address;
  final String? description;
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  final String? location;

  // Office-specific fields
  final int? capacity;
  final String? department;

  // Construction site-specific fields
  @JsonKey(name: 'project_type')
  final String? projectType;
  @JsonKey(name: 'start_date')
  final DateTime? startDate;
  @JsonKey(name: 'expected_end_date')
  final DateTime? expectedEndDate;
  @JsonKey(name: 'hourly_rate_standard')
  final double? hourlyRateStandard;

  const CreatePropertyRequest({
    required this.name,
    required this.type,
    this.parentPropertyId,
    this.address,
    this.description,
    this.imageUrl,
    this.location,
    // Office fields
    this.capacity,
    this.department,
    // Construction site fields
    this.projectType,
    this.startDate,
    this.expectedEndDate,
    this.hourlyRateStandard,
  });

  factory CreatePropertyRequest.fromJson(Map<String, dynamic> json) =>
      _$CreatePropertyRequestFromJson(json);

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
      'type': type,
    };

    // Add optional common fields
    if (parentPropertyId != null) json['parent_property_id'] = parentPropertyId;
    if (address != null) json['address'] = address;
    if (description != null) json['description'] = description;
    if (imageUrl != null) json['image_url'] = imageUrl;
    if (location != null) json['location'] = location;

    // Add type-specific fields based on property type
    if (type == 'office') {
      // Office-specific fields (optional for office type)
      if (capacity != null) json['capacity'] = capacity;
      if (department != null) json['department'] = department;
    } else if (type == 'construction_site') {
      // Construction site-specific fields (required for construction_site type)
      if (projectType != null) json['project_type'] = projectType;
      if (startDate != null) json['start_date'] = startDate!.toIso8601String();
      if (expectedEndDate != null) json['expected_end_date'] = expectedEndDate!.toIso8601String();
      if (hourlyRateStandard != null) json['hourly_rate_standard'] = hourlyRateStandard;
    }
    // For residential type, no additional fields are needed

    return json;
  }

  // Validation helper
  bool get isValid {
    if (name.isEmpty || type.isEmpty) return false;

    // Validate construction site required fields
    if (type == 'construction_site') {
      return projectType != null &&
             projectType!.isNotEmpty &&
             startDate != null &&
             expectedEndDate != null;
    }

    return true;
  }

  List<String> get validationErrors {
    final errors = <String>[];

    if (name.isEmpty) errors.add('Property name is required');
    if (type.isEmpty) errors.add('Property type is required');

    if (type == 'construction_site') {
      if (projectType == null || projectType!.isEmpty) {
        errors.add('Project type is required for construction sites');
      }
      if (startDate == null) {
        errors.add('Start date is required for construction sites');
      }
      if (expectedEndDate == null) {
        errors.add('Expected end date is required for construction sites');
      }
    }

    return errors;
  }

  // Factory methods for different property types
  factory CreatePropertyRequest.office({
    required String name,
    String? address,
    String? description,
    String? location,
    int? capacity,
    String? department,
  }) {
    return CreatePropertyRequest(
      name: name,
      type: 'office',
      address: address,
      description: description,
      location: location,
      capacity: capacity,
      department: department,
    );
  }

  factory CreatePropertyRequest.residential({
    required String name,
    String? address,
    String? description,
    String? location,
  }) {
    return CreatePropertyRequest(
      name: name,
      type: 'residential',
      address: address,
      description: description,
      location: location,
    );
  }

  factory CreatePropertyRequest.constructionSite({
    required String name,
    required String projectType,
    required DateTime startDate,
    required DateTime expectedEndDate,
    String? address,
    String? description,
    String? location,
    double? hourlyRateStandard,
  }) {
    return CreatePropertyRequest(
      name: name,
      type: 'construction_site',
      address: address,
      description: description,
      location: location,
      projectType: projectType,
      startDate: startDate,
      expectedEndDate: expectedEndDate,
      hourlyRateStandard: hourlyRateStandard,
    );
  }
}

@JsonSerializable()
class UpdatePropertyRequest {
  final String? name;
  final String? type;
  final String? address;
  final String? description;
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @JsonKey(name: 'is_active')
  final bool? isActive;

  const UpdatePropertyRequest({
    this.name,
    this.type,
    this.address,
    this.description,
    this.imageUrl,
    this.isActive,
  });

  factory UpdatePropertyRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePropertyRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdatePropertyRequestToJson(this);
}

@JsonSerializable()
class CreatePropertyServiceRequest {
  @JsonKey(name: 'service_type')
  final String serviceType;
  final String status;
  final String? notes;

  const CreatePropertyServiceRequest({
    required this.serviceType,
    required this.status,
    this.notes,
  });

  factory CreatePropertyServiceRequest.fromJson(Map<String, dynamic> json) =>
      _$CreatePropertyServiceRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreatePropertyServiceRequestToJson(this);
}

@JsonSerializable()
class UpdatePropertyServiceRequest {
  final String? status;
  final String? notes;

  const UpdatePropertyServiceRequest({
    this.status,
    this.notes,
  });

  factory UpdatePropertyServiceRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePropertyServiceRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdatePropertyServiceRequestToJson(this);
}
