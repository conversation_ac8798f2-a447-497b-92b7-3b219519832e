"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Check } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createMaintenanceIssue, updateMaintenanceIssue, type MaintenanceIssue } from "@/app/actions/maintenance-issues"
import { RoleBasedUI } from "@/components/role-based-ui"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface MaintenanceIssueFormProps {
  propertyId: string
  existingIssue?: MaintenanceIssue
  onSuccess?: () => void
}

export function MaintenanceIssueForm({ propertyId, existingIssue, onSuccess }: MaintenanceIssueFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [isRecurring, setIsRecurring] = useState(existingIssue?.is_recurring || false)
  const [recurrencePeriod, setRecurrencePeriod] = useState(existingIssue?.recurrence_period || "monthly")
  const [showNextDueDate, setShowNextDueDate] = useState(!!existingIssue?.next_due_date)

  // Calculate default next due date based on recurrence period
  const calculateNextDueDate = () => {
    const today = new Date()
    const nextDate = new Date()

    switch (recurrencePeriod) {
      case "weekly":
        nextDate.setDate(today.getDate() + 7)
        break
      case "monthly":
        nextDate.setMonth(today.getMonth() + 1)
        break
      case "quarterly":
        nextDate.setMonth(today.getMonth() + 3)
        break
      case "yearly":
        nextDate.setFullYear(today.getFullYear() + 1)
        break
      default:
        nextDate.setMonth(today.getMonth() + 1)
    }

    return nextDate.toISOString().split("T")[0]
  }

  const [nextDueDate, setNextDueDate] = useState(existingIssue?.next_due_date || calculateNextDueDate())

  // Update next due date when recurrence period changes
  useEffect(() => {
    if (isRecurring && !existingIssue?.next_due_date) {
      setNextDueDate(calculateNextDueDate())
    }
  }, [recurrencePeriod, isRecurring])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess(false)

    const formData = new FormData(e.currentTarget)
    formData.append("property_id", propertyId)

    // Add recurring information
    formData.append("is_recurring", isRecurring.toString())
    if (isRecurring) {
      formData.append("recurrence_period", recurrencePeriod)
      formData.append("next_due_date", nextDueDate)
    } else {
      formData.append("recurrence_period", "")
      formData.append("next_due_date", "")
    }

    try {
      const result = existingIssue ? await updateMaintenanceIssue(formData) : await createMaintenanceIssue(formData)

      if (result.success) {
        setSuccess(true)
        if (onSuccess) {
          onSuccess()
        } else {
          setTimeout(() => {
            router.push(`/dashboard/home/<USER>/maintenance?tab=issue-status`)
            router.refresh()
          }, 1500)
        }
      } else {
        setError(result.error || "An error occurred")
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">{existingIssue ? "Edit Issue" : "Submit an Issue"}</CardTitle>
        <CardDescription>
          {existingIssue ? "Update the maintenance issue details" : "Report a new maintenance issue or request"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-700">
            <Check className="h-4 w-4" />
            <AlertDescription>
              {existingIssue ? "Issue updated successfully!" : "Issue submitted successfully!"}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {existingIssue && <input type="hidden" name="id" value={existingIssue.id} />}

          <div className="space-y-2">
            <Label htmlFor="issue-title">Issue Title</Label>
            <Input
              id="issue-title"
              name="issue_title"
              placeholder="Brief description of the issue"
              defaultValue={existingIssue?.issue_type || ""}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <select
              id="department"
              name="department"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={existingIssue?.category || ""}
              required
            >
              <option value="">Select a department</option>
              <option value="water">Water</option>
              <option value="electricity">Electricity</option>
              <option value="security">Security</option>
              <option value="internet">Internet</option>
              <option value="plumbing">Plumbing</option>
              <option value="electrical">Electrical</option>
              <option value="hvac">HVAC</option>
              <option value="structural">Structural</option>
              <option value="appliance">Appliance</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                name="start_date"
                type="date"
                defaultValue={existingIssue?.issue_date || new Date().toISOString().split("T")[0]}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end-date">Expected End Date</Label>
              <Input id="end-date" name="end_date" type="date" defaultValue={existingIssue?.resolution_date || ""} />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <select
              id="priority"
              name="priority"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={existingIssue?.priority || "Medium"}
              required
            >
              <option value="Low">Low - Not urgent</option>
              <option value="Medium">Medium - Needs attention soon</option>
              <option value="High">High - Urgent issue</option>
              <option value="Emergency">Emergency - Immediate attention required</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <select
              id="status"
              name="status"
              className="w-full rounded-md border border-input bg-background px-3 py-2"
              defaultValue={existingIssue?.status || "Open"}
              required
            >
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="On Hold">On Hold</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reported-by">Reported By</Label>
            <Input
              id="reported-by"
              name="reported_by"
              placeholder="Your name"
              defaultValue={existingIssue?.reported_by || ""}
              required
            />
          </div>

          {/* Recurring Issue Options */}
          <div className="space-y-4 rounded-md border p-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-recurring"
                checked={isRecurring}
                onCheckedChange={(checked) => setIsRecurring(checked as boolean)}
              />
              <Label htmlFor="is-recurring" className="font-medium">
                Recurring Issue
              </Label>
            </div>

            {isRecurring && (
              <div className="space-y-4 pl-6">
                <div className="space-y-2">
                  <Label htmlFor="recurrence-period">Recurrence Period</Label>
                  <Select value={recurrencePeriod} onValueChange={setRecurrencePeriod}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select recurrence period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show-next-due-date"
                    checked={showNextDueDate}
                    onCheckedChange={(checked) => setShowNextDueDate(checked as boolean)}
                  />
                  <Label htmlFor="show-next-due-date">Specify next due date</Label>
                </div>

                {showNextDueDate && (
                  <div className="space-y-2">
                    <Label htmlFor="next-due-date">Next Due Date</Label>
                    <Input
                      id="next-due-date"
                      type="date"
                      value={nextDueDate}
                      onChange={(e) => setNextDueDate(e.target.value)}
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              name="remarks"
              placeholder="Please provide detailed information about the issue"
              className="min-h-[120px]"
              defaultValue={existingIssue?.remarks || ""}
            />
          </div>

          <RoleBasedUI
            roles={["admin", "maintenance_manager", "user"]}
            fallback={
              <Alert className="bg-yellow-50 text-yellow-700">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>You don't have permission to submit maintenance issues.</AlertDescription>
              </Alert>
            }
          >
            <div className="pt-2">
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? "Submitting..." : existingIssue ? "Update Issue" : "Submit Issue"}
              </Button>
            </div>
          </RoleBasedUI>
        </form>
      </CardContent>
    </Card>
  )
}
