import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/property.dart';
import '../providers/properties_providers.dart';
import '../providers/property_functions_providers.dart';

class PropertyFunctionsManagementScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const PropertyFunctionsManagementScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertyFunctionsManagementScreen> createState() => _PropertyFunctionsManagementScreenState();
}

class _PropertyFunctionsManagementScreenState extends ConsumerState<PropertyFunctionsManagementScreen> {
  final Map<String, bool> _functionStates = {};
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final propertyAsync = ref.watch(propertyByIdProvider(widget.propertyId));
    final functionsAsync = ref.watch(propertyFunctionsProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Property Functions'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['properties.manage'],
            child: TextButton(
              onPressed: _isLoading ? null : _saveFunctions,
              child: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
            ),
          ),
        ],
      ),
      body: propertyAsync.when(
        data: (property) => functionsAsync.when(
          data: (functionsData) => _buildFunctionsManagement(property, functionsData.functions),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorState(error),
      ),
    );
  }

  Widget _buildFunctionsManagement(Property property, List<PropertyFunction> functions) {
    // Initialize function states if not already done
    if (_functionStates.isEmpty) {
      for (final function in functions) {
        _functionStates[function.functionName] = function.isEnabled;
      }
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Info Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    property.name,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Property Type: ${property.type.toUpperCase()}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  if (property.address != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      property.address!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Functions Configuration
          Text(
            'Available Functions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enable or disable functions for this property based on its specific needs.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          // Function Cards
          ...functions.map((function) => _buildFunctionCard(function)),
        ],
      ),
    );
  }

  Widget _buildFunctionCard(PropertyFunction function) {
    final isEnabled = _functionStates[function.functionName] ?? function.isEnabled;
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding / 2),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getFunctionIcon(function.functionName),
                  color: isEnabled ? _getFunctionColor(function.functionName) : Colors.grey,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        function.displayName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isEnabled ? null : Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        function.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: isEnabled,
                  onChanged: (value) {
                    setState(() {
                      _functionStates[function.functionName] = value;
                    });
                  },
                ),
              ],
            ),
            if (function.configuration.isNotEmpty && isEnabled) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Configuration',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatConfiguration(function.configuration),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getFunctionIcon(String functionName) {
    switch (functionName) {
      case 'attendance':
        return Icons.people;
      case 'maintenance':
        return Icons.build;
      case 'fuel':
        return Icons.electrical_services;
      case 'ott':
        return Icons.tv;
      case 'security':
        return Icons.security;
      case 'uptime':
        return Icons.wifi;
      case 'diesel':
        return Icons.local_gas_station;
      case 'equipment':
        return Icons.construction;
      case 'safety':
        return Icons.health_and_safety;
      case 'materials':
        return Icons.inventory;
      case 'progress':
        return Icons.timeline;
      case 'meeting_rooms':
        return Icons.meeting_room;
      case 'visitor_management':
        return Icons.badge;
      default:
        return Icons.settings;
    }
  }

  Color _getFunctionColor(String functionName) {
    switch (functionName) {
      case 'attendance':
        return Colors.blue;
      case 'maintenance':
        return Colors.orange;
      case 'fuel':
        return Colors.amber;
      case 'ott':
        return Colors.purple;
      case 'security':
        return Colors.indigo;
      case 'uptime':
        return Colors.teal;
      case 'diesel':
        return Colors.brown;
      case 'equipment':
        return Colors.grey;
      case 'safety':
        return Colors.red;
      case 'materials':
        return Colors.green;
      case 'progress':
        return Colors.cyan;
      case 'meeting_rooms':
        return Colors.deepPurple;
      case 'visitor_management':
        return Colors.pink;
      default:
        return Colors.grey;
    }
  }

  String _formatConfiguration(Map<String, dynamic> config) {
    if (config.isEmpty) return 'No configuration';
    
    final entries = config.entries.map((entry) {
      if (entry.value is Map) {
        return '${entry.key}: ${(entry.value as Map).entries.map((e) => '${e.key}=${e.value}').join(', ')}';
      }
      return '${entry.key}: ${entry.value}';
    }).toList();
    
    return entries.join('\n');
  }

  Widget _buildErrorState(dynamic error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            'Error Loading Functions',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.red[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(propertyFunctionsProvider(widget.propertyId));
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveFunctions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final functionsData = ref.read(propertyFunctionsProvider(widget.propertyId)).value;
      if (functionsData == null) return;

      final updatedFunctions = functionsData.functions.map((function) {
        return UpdatePropertyFunctionRequest(
          functionName: function.functionName,
          isEnabled: _functionStates[function.functionName] ?? function.isEnabled,
          configuration: function.configuration,
          displayOrder: function.displayOrder,
        );
      }).toList();

      final apiService = ref.read(propertyFunctionsApiServiceProvider);
      await apiService.updatePropertyFunctions(
        widget.propertyId,
        UpdatePropertyFunctionsRequest(functions: updatedFunctions),
      );

      // Refresh the data
      ref.invalidate(propertyFunctionsProvider(widget.propertyId));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Property functions updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update functions: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
