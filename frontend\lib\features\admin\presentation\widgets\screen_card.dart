import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/dynamic_role_based_widget.dart';
import '../../data/models/custom_screen.dart';

class ScreenCard extends StatelessWidget {
  final CustomScreen screen;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onToggleActive;
  final VoidCallback? onPreview;

  const ScreenCard({
    super.key,
    required this.screen,
    this.onEdit,
    this.onDelete,
    this.onToggleActive,
    this.onPreview,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppConstants.smallPadding),
            _buildContent(context),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Screen Icon
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: screen.isActive ? Colors.green[100] : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getScreenIcon(),
            color: screen.isActive ? Colors.green[700] : Colors.grey[600],
            size: 24,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),

        // Screen Title and Status
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      screen.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                screen.route,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        ),

        // Actions Menu
        _buildActionsMenu(context),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (screen.description != null) ...[
          Text(
            screen.description!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[700],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppConstants.smallPadding),
        ],

        // Permissions and Roles
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: [
            ...screen.requiredPermissions.take(3).map((permission) =>
              _buildPermissionChip(context, permission, Colors.blue)),
            ...screen.allowedRoles.take(2).map((role) =>
              _buildPermissionChip(context, role, Colors.purple)),
            if (screen.requiredPermissions.length > 3 || screen.allowedRoles.length > 2)
              _buildMoreChip(context,
                (screen.requiredPermissions.length - 3) + (screen.allowedRoles.length - 2)),
          ],
        ),

        const SizedBox(height: AppConstants.smallPadding),

        // Widget Count
        Row(
          children: [
            Icon(Icons.widgets, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              '${screen.widgets.length} widgets',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const Spacer(),
            Text(
              'Created ${_formatDate(screen.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      children: [
        // Preview Button
        if (onPreview != null)
          TextButton.icon(
            onPressed: onPreview,
            icon: const Icon(Icons.preview, size: 16),
            label: const Text('Preview'),
          ),

        const Spacer(),

        // Action Buttons
        DynamicRoleBasedWidget(
          requiredPermissions: const ['screens.manage'],
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Toggle Active Button
              if (onToggleActive != null)
                IconButton(
                  onPressed: onToggleActive,
                  icon: Icon(
                    screen.isActive ? Icons.visibility_off : Icons.visibility,
                    size: 20,
                  ),
                  tooltip: screen.isActive ? 'Deactivate' : 'Activate',
                ),

              // Edit Button
              if (onEdit != null)
                IconButton(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit, size: 20),
                  tooltip: 'Edit',
                ),

              // Delete Button
              if (onDelete != null)
                IconButton(
                  onPressed: onDelete,
                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                  tooltip: 'Delete',
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: screen.isActive ? Colors.green[100] : Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        screen.isActive ? 'Active' : 'Inactive',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: screen.isActive ? Colors.green[700] : Colors.grey[600],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPermissionChip(BuildContext context, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getColorShade(color),
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildMoreChip(BuildContext context, int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '+$count more',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildActionsMenu(BuildContext context) {
    return DynamicRoleBasedWidget(
      requiredPermissions: const ['screens.manage'],
      child: PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert),
        onSelected: (value) {
          switch (value) {
            case 'edit':
              onEdit?.call();
              break;
            case 'duplicate':
              // TODO: Implement duplicate functionality
              break;
            case 'export':
              // TODO: Implement export functionality
              break;
            case 'toggle':
              onToggleActive?.call();
              break;
            case 'delete':
              onDelete?.call();
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit),
              title: Text('Edit'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'duplicate',
            child: ListTile(
              leading: Icon(Icons.copy),
              title: Text('Duplicate'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'export',
            child: ListTile(
              leading: Icon(Icons.download),
              title: Text('Export'),
              dense: true,
            ),
          ),
          PopupMenuItem(
            value: 'toggle',
            child: ListTile(
              leading: Icon(screen.isActive ? Icons.visibility_off : Icons.visibility),
              title: Text(screen.isActive ? 'Deactivate' : 'Activate'),
              dense: true,
            ),
          ),
          const PopupMenuDivider(),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('Delete', style: TextStyle(color: Colors.red)),
              dense: true,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getScreenIcon() {
    if (screen.icon != null) {
      // Try to parse icon from string
      switch (screen.icon) {
        case 'dashboard':
          return Icons.dashboard;
        case 'analytics':
          return Icons.analytics;
        case 'settings':
          return Icons.settings;
        case 'people':
          return Icons.people;
        case 'business':
          return Icons.business;
        case 'report':
          return Icons.assessment;
        default:
          return Icons.web;
      }
    }
    return Icons.web;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 7) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }

  Color _getColorShade(Color color) {
    if (color is MaterialColor) {
      return color.shade700;
    }
    // For non-MaterialColor, return a darker version
    return Color.fromRGBO(
      (color.red * 0.7).round(),
      (color.green * 0.7).round(),
      (color.blue * 0.7).round(),
      color.opacity,
    );
  }
}
