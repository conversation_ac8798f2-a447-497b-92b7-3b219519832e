"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"
import type { Role } from "@/lib/auth"

// Get all roles
export async function getRoles(): Promise<Role[]> {
  const supabase = createServerClient()

  const { data, error } = await supabase.from("roles").select("*").order("name")

  if (error) {
    console.error("Error fetching roles:", error)
    throw new Error("Failed to fetch roles")
  }

  return data as Role[]
}

// Create role
export async function createRole({
  name,
  description,
}: {
  name: string
  description: string
}) {
  const supabase = createServerClient()

  // Check if role already exists
  const { data: existingRole } = await supabase.from("roles").select("id").eq("name", name).single()

  if (existingRole) {
    return { success: false, error: "Role with this name already exists" }
  }

  const { error } = await supabase.from("roles").insert({
    name,
    description,
  })

  if (error) {
    console.error("Error creating role:", error)
    return { success: false, error: "Failed to create role" }
  }

  revalidatePath("/admin/roles")
  return { success: true }
}

// Update role
export async function updateRole({
  id,
  name,
  description,
}: {
  id: number
  name: string
  description: string
}) {
  const supabase = createServerClient()

  // Check if another role with the same name exists
  const { data: existingRole } = await supabase.from("roles").select("id").eq("name", name).neq("id", id).single()

  if (existingRole) {
    return { success: false, error: "Another role with this name already exists" }
  }

  const { error } = await supabase
    .from("roles")
    .update({
      name,
      description,
    })
    .eq("id", id)

  if (error) {
    console.error("Error updating role:", error)
    return { success: false, error: "Failed to update role" }
  }

  revalidatePath("/admin/roles")
  return { success: true }
}

// Delete role
export async function deleteRole(id: number) {
  const supabase = createServerClient()

  // Check if role is in use
  const { data: userRoles, error: checkError } = await supabase
    .from("user_roles")
    .select("id")
    .eq("role_id", id)
    .limit(1)

  if (checkError) {
    console.error("Error checking role usage:", checkError)
    return { success: false, error: "Failed to check if role is in use" }
  }

  if (userRoles && userRoles.length > 0) {
    return { success: false, error: "Cannot delete role that is assigned to users" }
  }

  // Delete role
  const { error } = await supabase.from("roles").delete().eq("id", id)

  if (error) {
    console.error("Error deleting role:", error)
    return { success: false, error: "Failed to delete role" }
  }

  revalidatePath("/admin/roles")
  return { success: true }
}
