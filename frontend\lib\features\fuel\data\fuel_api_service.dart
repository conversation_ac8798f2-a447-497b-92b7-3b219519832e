import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../shared/models/generator_fuel.dart';
import '../../../shared/models/api_response.dart';

part 'fuel_api_service.g.dart';

@RestApi()
abstract class FuelApiService {
  factory FuelApiService(Dio dio) = _FuelApiService;

  // Get fuel logs for a property
  @GET('/api/generator-fuel/{propertyId}')
  Future<ApiResponse<List<GeneratorFuelLog>>> getFuelLogs(@Path('propertyId') String propertyId);

  // Create new fuel log entry
  @POST('/api/generator-fuel/{propertyId}')
  Future<HttpResponse<dynamic>> createFuelLog(
    @Path('propertyId') String propertyId,
    @Body() CreateFuelLogRequest request,
  );

  // Get specific fuel log by ID
  @GET('/api/generator-fuel/logs/{id}')
  Future<ApiResponse<GeneratorFuelLog>> getFuelLogById(@Path('id') String id);

  // Update fuel log
  @PUT('/api/generator-fuel/logs/{id}')
  Future<ApiResponse<GeneratorFuelLog>> updateFuelLog(
    @Path('id') String id,
    @Body() UpdateFuelLogRequest request,
  );

  // Delete fuel log
  @DELETE('/api/generator-fuel/logs/{id}')
  Future<ApiResponse<VoidResponse>> deleteFuelLog(@Path('id') String id);
}

// Request models
@JsonSerializable()
class CreateFuelLogRequest {
  @JsonKey(name: 'fuel_level_liters')
  final double fuelLevelLiters;
  @JsonKey(name: 'consumption_rate')
  final double? consumptionRate;
  @JsonKey(name: 'runtime_hours')
  final double? runtimeHours;
  @JsonKey(name: 'efficiency_percentage')
  final double? efficiencyPercentage;
  final String? notes;

  const CreateFuelLogRequest({
    required this.fuelLevelLiters,
    this.consumptionRate,
    this.runtimeHours,
    this.efficiencyPercentage,
    this.notes,
  });

  factory CreateFuelLogRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateFuelLogRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateFuelLogRequestToJson(this);
}

@JsonSerializable()
class UpdateFuelLogRequest {
  @JsonKey(name: 'fuel_level_liters')
  final double? fuelLevelLiters;
  @JsonKey(name: 'consumption_rate')
  final double? consumptionRate;
  @JsonKey(name: 'runtime_hours')
  final double? runtimeHours;
  @JsonKey(name: 'efficiency_percentage')
  final double? efficiencyPercentage;
  final String? notes;

  const UpdateFuelLogRequest({
    this.fuelLevelLiters,
    this.consumptionRate,
    this.runtimeHours,
    this.efficiencyPercentage,
    this.notes,
  });

  factory UpdateFuelLogRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateFuelLogRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateFuelLogRequestToJson(this);
}
