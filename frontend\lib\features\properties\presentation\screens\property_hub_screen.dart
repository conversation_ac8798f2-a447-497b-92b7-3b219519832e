import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/property.dart';
import '../providers/properties_providers.dart';
import '../providers/property_functions_providers.dart';
import '../widgets/property_functional_area_card.dart';
import '../widgets/property_quick_stats_card.dart';

class PropertyHubScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const PropertyHubScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertyHubScreen> createState() => _PropertyHubScreenState();
}

class _PropertyHubScreenState extends ConsumerState<PropertyHubScreen> {
  @override
  Widget build(BuildContext context) {
    final propertyAsync = ref.watch(propertyByIdProvider(widget.propertyId));

    return propertyAsync.when(
      data: (property) => _buildPropertyHub(context, property),
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading property: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(propertyByIdProvider(widget.propertyId)),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPropertyHub(BuildContext context, Property property) {
    return Scaffold(
      appBar: AppBar(
        title: Text(property.name),
        backgroundColor: property.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(propertyByIdProvider(widget.propertyId)),
          ),
          PopupMenuButton<String>(
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'details',
                child: Row(
                  children: [
                    Icon(Icons.info, size: 20),
                    SizedBox(width: 8),
                    Text('Property Details'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'functions',
                child: Row(
                  children: [
                    Icon(Icons.tune, size: 20),
                    SizedBox(width: 8),
                    Text('Manage Functions'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('Settings'),
                  ],
                ),
              ),
            ],
            onSelected: (value) {
              switch (value) {
                case 'details':
                  context.push('/properties/${property.id}');
                  break;
                case 'functions':
                  context.push('/property/${property.id}/functions/manage');
                  break;
                case 'settings':
                  // Navigate to property settings
                  break;
              }
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(propertyByIdProvider(widget.propertyId));
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Property Quick Stats
              PropertyQuickStatsCard(property: property),
              const SizedBox(height: AppConstants.defaultPadding),

              // Functional Areas Header
              Text(
                'Functional Areas',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding / 2),
              Text(
                'Only areas with changes from default are shown',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: AppConstants.defaultPadding),

              // Functional Area Cards
              _buildFunctionalAreaCards(property),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFunctionalAreaCards(Property property) {
    final propertyFunctionsAsync = ref.watch(propertyFunctionsProvider(property.id));

    return propertyFunctionsAsync.when(
      data: (functionsData) => _buildFunctionCards(property, functionsData.functions),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorFunctionCards(property),
    );
  }

  Widget _buildFunctionCards(Property property, List<PropertyFunction> functions) {
    final enabledFunctions = functions.where((f) => f.isEnabled).toList();

    if (enabledFunctions.isEmpty) {
      return _buildNoFunctionsCard();
    }

    return Column(
      children: enabledFunctions.map((function) =>
        _buildFunctionCard(property, function)
      ).toList(),
    );
  }

  Widget _buildFunctionCard(Property property, PropertyFunction function) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: _getFunctionCard(property, function),
    );
  }

  Widget _getFunctionCard(Property property, PropertyFunction function) {
    switch (function.functionName) {
      case 'attendance':
        return PropertyFunctionalAreaCard(
          title: 'Members & Staff',
          icon: Icons.people,
          color: Colors.blue,
          hasChanges: true, // This would be calculated based on actual data
          stats: [
            FunctionalAreaStat('Present Today', '12/15', Colors.green),
            FunctionalAreaStat('Attendance Rate', '85%', Colors.orange),
            FunctionalAreaStat('Late Arrivals', '3', Colors.red),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'attendance'),
        );
      case 'maintenance':
        return PropertyFunctionalAreaCard(
          title: 'Maintenance & Issues',
          icon: Icons.build,
          color: Colors.orange,
          hasChanges: true,
          stats: [
            FunctionalAreaStat('Open Issues', '5', Colors.red),
            FunctionalAreaStat('In Progress', '3', Colors.orange),
            FunctionalAreaStat('Critical', '1', Colors.red),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'maintenance'),
        );
      case 'fuel':
        return PropertyFunctionalAreaCard(
          title: 'Electricity & Generator',
          icon: Icons.electrical_services,
          color: Colors.amber,
          hasChanges: true,
          stats: [
            FunctionalAreaStat('Fuel Level', '75%', Colors.green),
            FunctionalAreaStat('Runtime Today', '6.5h', Colors.blue),
            FunctionalAreaStat('Efficiency', '92%', Colors.green),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'fuel'),
        );
      case 'ott':
        return PropertyFunctionalAreaCard(
          title: 'OTT Services',
          icon: Icons.tv,
          color: Colors.purple,
          hasChanges: true,
          stats: [
            FunctionalAreaStat('Active Services', '8/10', Colors.green),
            FunctionalAreaStat('Dues Pending', '₹2,500', Colors.red),
            FunctionalAreaStat('Expiring Soon', '2', Colors.orange),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'ott'),
        );
      case 'security':
        return PropertyFunctionalAreaCard(
          title: 'Security & Monitoring',
          icon: Icons.security,
          color: Colors.indigo,
          hasChanges: false,
          stats: [
            FunctionalAreaStat('Cameras Online', '8/8', Colors.green),
            FunctionalAreaStat('Incidents Today', '0', Colors.green),
            FunctionalAreaStat('Last Patrol', '2h ago', Colors.blue),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'security'),
        );
      case 'uptime':
        return PropertyFunctionalAreaCard(
          title: 'Internet & Connectivity',
          icon: Icons.wifi,
          color: Colors.teal,
          hasChanges: false,
          stats: [
            FunctionalAreaStat('Uptime', '99.8%', Colors.green),
            FunctionalAreaStat('Speed', '100 Mbps', Colors.green),
            FunctionalAreaStat('Devices', '25', Colors.blue),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'uptime'),
        );
      case 'diesel':
        return PropertyFunctionalAreaCard(
          title: 'Diesel Management',
          icon: Icons.local_gas_station,
          color: Colors.brown,
          hasChanges: true,
          stats: [
            FunctionalAreaStat('Current Stock', '150L', Colors.green),
            FunctionalAreaStat('Daily Usage', '25L', Colors.blue),
            FunctionalAreaStat('Days Remaining', '6', Colors.orange),
          ],
          onTap: () => _navigateToFunctionalArea(property, 'diesel'),
        );
      default:
        return PropertyFunctionalAreaCard(
          title: function.displayName,
          icon: Icons.settings,
          color: Colors.grey,
          hasChanges: false,
          stats: [
            FunctionalAreaStat('Status', 'Active', Colors.green),
          ],
          onTap: () => _navigateToFunctionalArea(property, function.functionName),
        );
    }
  }

  Widget _buildNoFunctionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(Icons.info_outline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No Functions Enabled',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This property has no functions enabled. Contact your administrator to configure property functions.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorFunctionCards(Property property) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error Loading Functions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Unable to load property functions. Please try again.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.invalidate(propertyFunctionsProvider(property.id));
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToFunctionalArea(Property property, String area) {
    switch (area) {
      case 'attendance':
        context.push('/property/${property.id}/hub/attendance');
        break;
      case 'maintenance':
        context.push('/property/${property.id}/hub/maintenance');
        break;
      case 'fuel':
        context.push('/property/${property.id}/hub/fuel');
        break;
      case 'ott':
        context.push('/property/${property.id}/hub/ott');
        break;
      case 'diesel':
        context.push('/property/${property.id}/hub/diesel');
        break;
      case 'security':
        context.push('/property/${property.id}/hub/security');
        break;
      case 'uptime':
        context.push('/property/${property.id}/hub/uptime');
        break;
      case 'equipment':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Equipment management coming soon...')),
        );
        break;
      case 'safety':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Safety management coming soon...')),
        );
        break;
      case 'materials':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Materials management coming soon...')),
        );
        break;
      case 'progress':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Progress tracking coming soon...')),
        );
        break;
      case 'meeting_rooms':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Meeting room management coming soon...')),
        );
        break;
      case 'visitor_management':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Visitor management coming soon...')),
        );
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${area.replaceAll('_', ' ').toUpperCase()} coming soon...')),
        );
        break;
    }
  }
}
