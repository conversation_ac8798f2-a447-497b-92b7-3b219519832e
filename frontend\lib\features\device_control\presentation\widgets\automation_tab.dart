import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Automation tab - Smart rules, schedules, and automated actions
class AutomationTab extends StatefulWidget {
  const AutomationTab({super.key});

  @override
  State<AutomationTab> createState() => _AutomationTabState();
}

class _AutomationTabState extends State<AutomationTab> {
  final List<AutomationRule> _rules = [
    AutomationRule(
      id: 'rule_001',
      name: 'Water Level Auto-Fill',
      description: 'Turn on water pump when level drops below 30%',
      trigger: 'Water Level < 30%',
      action: 'Start Main Water Pump',
      isEnabled: true,
      lastTriggered: DateTime.now().subtract(const Duration(hours: 6)),
    ),
    AutomationRule(
      id: 'rule_002',
      name: 'Night Mode Lighting',
      description: 'Dim lights to 30% after 10 PM',
      trigger: 'Time = 10:00 PM',
      action: 'Set Lighting to Night Mode',
      isEnabled: true,
      lastTriggered: DateTime.now().subtract(const Duration(hours: 10)),
    ),
    AutomationRule(
      id: 'rule_003',
      name: 'Generator Auto-Start',
      description: 'Start backup generator on power failure',
      trigger: 'Main Power = OFF',
      action: 'Start Backup Generator',
      isEnabled: true,
      lastTriggered: null,
    ),
    AutomationRule(
      id: 'rule_004',
      name: 'HVAC Energy Saver',
      description: 'Reduce HVAC when no motion detected for 2 hours',
      trigger: 'No Motion for 2 hours',
      action: 'Set HVAC to Energy Save Mode',
      isEnabled: false,
      lastTriggered: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildAutomationOverview(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildQuickActions(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildRulesList(context),
          ),
        ],
      ),
    );
  }

  Widget _buildAutomationOverview(BuildContext context) {
    final activeRules = _rules.where((r) => r.isEnabled).length;
    final totalRules = _rules.length;
    final recentlyTriggered = _rules.where((r) => 
      r.lastTriggered != null && 
      DateTime.now().difference(r.lastTriggered!).inHours < 24
    ).length;

    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Active Rules',
            '$activeRules/$totalRules',
            Icons.rule,
            Colors.blue,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildOverviewCard(
            'Triggered Today',
            recentlyTriggered.toString(),
            Icons.flash_on,
            Colors.orange,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildOverviewCard(
            'System Status',
            'ONLINE',
            Icons.check_circle,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _createNewRule,
            icon: const Icon(Icons.add),
            label: const Text('New Rule'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _enableAllRules,
            icon: const Icon(Icons.play_arrow),
            label: const Text('Enable All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showSchedules,
            icon: const Icon(Icons.schedule),
            label: const Text('Schedules'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRulesList(BuildContext context) {
    return ListView.separated(
      itemCount: _rules.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConstants.smallPadding),
      itemBuilder: (context, index) {
        return _buildRuleCard(_rules[index]);
      },
    );
  }

  Widget _buildRuleCard(AutomationRule rule) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: rule.isEnabled 
                        ? Colors.blue.shade100 
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    rule.isEnabled ? Icons.rule : Icons.rule_outlined,
                    color: rule.isEnabled 
                        ? Colors.blue.shade700 
                        : Colors.grey.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        rule.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        rule.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: rule.isEnabled,
                  onChanged: (value) => _toggleRule(rule),
                  activeColor: Colors.blue.shade700,
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Container(
              padding: const EdgeInsets.all(AppConstants.smallPadding),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.flash_on,
                        color: Colors.orange.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'TRIGGER: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade600,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          rule.trigger,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.play_arrow,
                        color: Colors.green.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'ACTION: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade600,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          rule.action,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  rule.lastTriggered != null
                      ? 'Last triggered: ${_formatLastTriggered(rule.lastTriggered!)}'
                      : 'Never triggered',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, size: 16),
                      onPressed: () => _editRule(rule),
                      tooltip: 'Edit Rule',
                    ),
                    IconButton(
                      icon: const Icon(Icons.play_arrow, size: 16),
                      onPressed: () => _testRule(rule),
                      tooltip: 'Test Rule',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 16),
                      onPressed: () => _deleteRule(rule),
                      tooltip: 'Delete Rule',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatLastTriggered(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _toggleRule(AutomationRule rule) {
    setState(() {
      rule.isEnabled = !rule.isEnabled;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${rule.name} ${rule.isEnabled ? 'enabled' : 'disabled'}',
        ),
      ),
    );
  }

  void _createNewRule() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Rule'),
        content: const Text('Rule creation wizard coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _enableAllRules() {
    setState(() {
      for (var rule in _rules) {
        rule.isEnabled = true;
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All automation rules enabled')),
    );
  }

  void _showSchedules() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Automation Schedules'),
        content: const Text('Schedule management coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _editRule(AutomationRule rule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${rule.name}'),
        content: const Text('Rule editor coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _testRule(AutomationRule rule) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Testing ${rule.name}...')),
    );
  }

  void _deleteRule(AutomationRule rule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Rule'),
        content: Text('Are you sure you want to delete "${rule.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _rules.remove(rule);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${rule.name} deleted')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

/// Automation rule data model
class AutomationRule {
  final String id;
  final String name;
  final String description;
  final String trigger;
  final String action;
  bool isEnabled;
  final DateTime? lastTriggered;

  AutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.trigger,
    required this.action,
    required this.isEnabled,
    this.lastTriggered,
  });
}
