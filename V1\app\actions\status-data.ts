"use server"

import { createClient } from "@/lib/supabase/server"
import type { StatusLevel, SystemMetric, SystemStatus } from "@/app/types"
import { getInternetStatusFromUptime, getUptimeStatistics } from "./uptime-reports"
import { getOttServices } from "./ott-services"

export type StatusData = {
  healthy: number
  warning: number
  critical: number
  total: number
}

export type StatusDetail = {
  propertyName: string
  systemType: string
  status: "green" | "orange" | "red"
  details: string[]
  metrics?: Array<{
    name: string
    value: number | boolean
    unit: string
    status: string
  }>
}

const PROPERTIES = [
  { id: "jublee-hills", name: "Jublee Hills Home", type: "home" },
  { id: "gandipet-guest-house", name: "Gandipet Guest House", type: "home" },
  { id: "brane-back-office", name: "Brane - Back Office", type: "office" },
  { id: "brane-strf", name: "Brane - STRF", type: "office" },
  { id: "srsr-back-office", name: "SRSR - Back Office", type: "office" },
  { id: "srsr-rd-no-36", name: "SRSR - Rd No. 36 Office", type: "office" },
]

// Cache for status data to avoid repeated API calls
let statusCache: {
  data: StatusData | null
  timestamp: number
} = {
  data: null,
  timestamp: 0,
}

const detailsCache: {
  [key: string]: {
    data: StatusDetail[]
    timestamp: number
  }
} = {}

const CACHE_DURATION = 120000 // 2 minutes cache (increased from 30 seconds)

function getPropertyDisplayName(propertyId: string): string {
  const propertyNames: { [key: string]: string } = {
    "jublee-hills": "Jublee Hills Home",
    "gandipet-guest-house": "Gandipet Guest House",
    "brane-back-office": "Brane - Back Office",
    "brane-strf": "Brane - STRF",
    "srsr-back-office": "SRSR - Back Office",
    "srsr-rd-no-36": "SRSR - Rd No. 36 Office",
  }
  return propertyNames[propertyId] || propertyId
}

export async function getSystemStatuses(propertyId: string): Promise<SystemStatus[]> {
  const systems: SystemStatus[] = []

  // Placeholder data - replace with actual data fetching logic
  const getRandomStatus = (): StatusLevel => {
    const statuses: StatusLevel[] = ["green", "orange", "red"]
    return statuses[Math.floor(Math.random() * statuses.length)]
  }

  const getRandomNumber = (min: number, max: number): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  // Power status
  const powerStatus: StatusLevel = getRandomStatus()
  const powerMetrics: SystemMetric[] = [
    {
      name: "voltage",
      value: getRandomNumber(220, 240),
      unit: "V",
      status: powerStatus,
    },
    {
      name: "current",
      value: getRandomNumber(5, 10),
      unit: "A",
      status: powerStatus,
    },
  ]

  systems.push({
    id: `${propertyId}-power`,
    propertyId,
    propertyName: getPropertyDisplayName(propertyId),
    systemType: "Power",
    status: powerStatus,
    metrics: powerMetrics,
    lastUpdated: new Date().toISOString(),
  })

  // Temperature status
  const temperatureStatus: StatusLevel = getRandomStatus()
  const temperatureMetrics: SystemMetric[] = [
    {
      name: "temperature",
      value: getRandomNumber(20, 30),
      unit: "°C",
      status: temperatureStatus,
    },
    {
      name: "humidity",
      value: getRandomNumber(40, 60),
      unit: "%",
      status: temperatureStatus,
    },
  ]

  systems.push({
    id: `${propertyId}-temperature`,
    propertyId,
    propertyName: getPropertyDisplayName(propertyId),
    systemType: "Temperature",
    status: temperatureStatus,
    metrics: temperatureMetrics,
    lastUpdated: new Date().toISOString(),
  })

  // Internet status
  let internetStatus: StatusLevel = "green"
  let internetMetrics: SystemMetric[] = []

  if (propertyId === "jublee-hills") {
    // For Jublee Hills, get actual uptime data
    internetStatus = await getInternetStatusFromUptime(propertyId)
    const statistics = await getUptimeStatistics(propertyId)

    internetMetrics = [
      {
        name: "weekly_uptime",
        value: statistics.weekly.averageUptime,
        unit: "%",
        status: internetStatus,
      },
      {
        name: "weekly_downtime",
        value: statistics.weekly.totalDowntime,
        unit: "mins",
        status: statistics.weekly.totalDowntime > 60 ? "orange" : "green",
      },
      {
        name: "disruption_count",
        value: statistics.weekly.disruptionCount,
        unit: "count",
        status: statistics.weekly.disruptionCount > 2 ? "orange" : "green",
      },
    ]
  } else {
    // For other properties, use default green status
    internetMetrics = [
      {
        name: "connection_status",
        value: 1,
        unit: "boolean",
        status: "green",
      },
    ]
  }

  systems.push({
    id: `${propertyId}-internet`,
    propertyId,
    propertyName: getPropertyDisplayName(propertyId),
    systemType: "Internet",
    status: internetStatus,
    metrics: internetMetrics,
    lastUpdated: new Date().toISOString(),
  })

  return systems
}

export async function getStatusData(location = "all"): Promise<StatusData> {
  try {
    // Check cache first
    const now = Date.now()
    if (statusCache.data && now - statusCache.timestamp < CACHE_DURATION) {
      console.log("📦 Returning cached status data")
      return statusCache.data
    }

    console.log("🔄 Fetching fresh status data...")

    // Only process home properties for status calculations
    const homeProperties = PROPERTIES.filter((property) => property.type === "home")

    let healthyCount = 0
    let warningCount = 0
    let criticalCount = 0
    let totalCount = 0

    // Process each home property
    for (const property of homeProperties) {
      if (location !== "all" && location !== property.id) {
        continue
      }

      try {
        console.log(`🏠 Checking ${property.name}...`)

        // For electricity, OTT, and internet - count as 1 system each (property-level)
        const electricityStatus = await safeGetElectricityStatus(property.id)
        const ottStatus = await safeGetOTTStatus(property.id)
        const internetStatus = await safeGetInternetStatus(property.id)

        // Count electricity, OTT, and internet as single systems
        totalCount += 3
        if (electricityStatus === "green") healthyCount++
        else if (electricityStatus === "orange") warningCount++
        else if (electricityStatus === "red") criticalCount++

        if (ottStatus === "green") healthyCount++
        else if (ottStatus === "orange") warningCount++
        else if (ottStatus === "red") criticalCount++

        if (internetStatus === "green") healthyCount++
        else if (internetStatus === "orange") warningCount++
        else if (internetStatus === "red") criticalCount++

        // For maintenance, count individual issues AND recurring due dates
        const maintenanceIssueCounts = await getMaintenanceIssueCounts(property.id)
        const recurringDueCounts = await getRecurringMaintenanceDueCounts(property.id)

        totalCount += maintenanceIssueCounts.total + recurringDueCounts.total
        healthyCount += maintenanceIssueCounts.healthy + recurringDueCounts.healthy
        warningCount += maintenanceIssueCounts.warning + recurringDueCounts.warning
        criticalCount += maintenanceIssueCounts.critical + recurringDueCounts.critical

        console.log(`✅ ${property.name}: E:${electricityStatus} O:${ottStatus} I:${internetStatus}`)
        console.log(
          `🔧 ${property.name} maintenance: ${maintenanceIssueCounts.critical} critical, ${maintenanceIssueCounts.warning} warning, ${maintenanceIssueCounts.healthy} healthy`,
        )
        console.log(
          `🔄 ${property.name} recurring due: ${recurringDueCounts.critical} overdue, ${recurringDueCounts.warning} due soon, ${recurringDueCounts.healthy} on track`,
        )
        console.log(`📊 Running totals: H:${healthyCount} W:${warningCount} C:${criticalCount} T:${totalCount}`)
      } catch (error) {
        console.warn(`⚠️ Error processing ${property.name}:`, error)
        // Add as healthy to avoid breaking the UI
        totalCount += 3
        healthyCount += 3
      }
    }

    const statusData = {
      healthy: healthyCount,
      warning: warningCount,
      critical: criticalCount,
      total: totalCount,
    }

    // Cache the result
    statusCache = {
      data: statusData,
      timestamp: now,
    }

    console.log("✅ Final status data:", statusData)
    return statusData
  } catch (error) {
    console.error("Error fetching status data:", error)
    // Return cached data if available, otherwise fallback
    if (statusCache.data) {
      console.log("📦 Returning stale cached data due to error")
      return statusCache.data
    }
    return {
      healthy: 0,
      warning: 0,
      critical: 0,
      total: 0,
    }
  }
}

export async function getDetailedStatusBreakdown(
  statusType: "healthy" | "warning" | "critical",
): Promise<StatusDetail[]> {
  try {
    const cacheKey = statusType
    const now = Date.now()

    // Check cache first
    if (detailsCache[cacheKey] && now - detailsCache[cacheKey].timestamp < CACHE_DURATION) {
      console.log(`📦 Returning cached ${statusType} details`)
      return detailsCache[cacheKey].data
    }

    console.log(`🔄 Fetching fresh ${statusType} details...`)
    const details: StatusDetail[] = []

    // Process only HOME properties for status details
    const homeProperties = PROPERTIES.filter((property) => property.type === "home")
    console.log(`🏠 Processing ${homeProperties.length} home properties for ${statusType} details`)

    // Batch fetch all data for all properties at once
    const allMaintenanceIssues = await batchGetMaintenanceIssues(homeProperties.map((p) => p.id))
    const allRecurringIssues = await batchGetRecurringMaintenanceIssues(homeProperties.map((p) => p.id))

    for (const property of homeProperties) {
      try {
        console.log(`🏠 Getting details for ${property.name}...`)

        // Get the actual status for each system using the SAME logic as the main status calculation
        const electricityStatus = await safeGetElectricityStatus(property.id)
        const ottStatus = await safeGetOTTStatus(property.id)
        const internetStatus = await safeGetInternetStatus(property.id)

        console.log(`📊 ${property.name} system statuses: E:${electricityStatus} O:${ottStatus} I:${internetStatus}`)

        // Add matching details based on ACTUAL status for main systems
        if (
          (statusType === "healthy" && electricityStatus === "green") ||
          (statusType === "warning" && electricityStatus === "orange") ||
          (statusType === "critical" && electricityStatus === "red")
        ) {
          const electricityDetails = await safeGetElectricityStatusDetails(property.id)
          details.push({
            propertyName: property.name,
            systemType: "Electricity",
            status: electricityStatus,
            details: electricityDetails.details,
            metrics: electricityDetails.metrics,
          })
          console.log(`✅ Added ${property.name} Electricity (${electricityStatus}) to ${statusType} details`)
        }

        if (
          (statusType === "healthy" && ottStatus === "green") ||
          (statusType === "warning" && ottStatus === "orange") ||
          (statusType === "critical" && ottStatus === "red")
        ) {
          const ottDetails = await safeGetOTTStatusDetails(property.id)
          details.push({
            propertyName: property.name,
            systemType: "OTT Services",
            status: ottStatus,
            details: ottDetails.details,
            metrics: ottDetails.metrics,
          })
          console.log(`✅ Added ${property.name} OTT (${ottStatus}) to ${statusType} details`)
        }

        if (
          (statusType === "healthy" && internetStatus === "green") ||
          (statusType === "warning" && internetStatus === "orange") ||
          (statusType === "critical" && internetStatus === "red")
        ) {
          const internetDetails = await safeGetInternetStatusDetails(property.id)
          details.push({
            propertyName: property.name,
            systemType: "Internet",
            status: internetStatus,
            details: internetDetails.details,
            metrics: internetDetails.metrics,
          })
          console.log(`✅ Added ${property.name} Internet (${internetStatus}) to ${statusType} details`)
        }

        // Use batched data for maintenance issues
        const maintenanceIssues = allMaintenanceIssues.filter((issue) => issue.property_id === property.id)
        console.log(`🔧 ${property.name} individual maintenance issues:`, maintenanceIssues.length)

        maintenanceIssues.forEach((issue, index) => {
          let issueStatus: "green" | "orange" | "red"
          if (issue.status === "Open" || issue.status === "On Hold") {
            issueStatus = "red"
          } else if (issue.status === "In Progress") {
            issueStatus = "orange"
          } else {
            issueStatus = "green"
          }

          if (
            (statusType === "healthy" && issueStatus === "green") ||
            (statusType === "warning" && issueStatus === "orange") ||
            (statusType === "critical" && issueStatus === "red")
          ) {
            details.push({
              propertyName: property.name,
              systemType: `Maintenance: ${issue.issue_type}`,
              status: issueStatus,
              details: [
                `Status: ${issue.status}`,
                `Priority: ${issue.priority}`,
                `Category: ${issue.category}`,
                `Reported: ${new Date(issue.issue_date).toLocaleDateString()}`,
                ...(issue.resolution_date ? [`Resolved: ${new Date(issue.resolution_date).toLocaleDateString()}`] : []),
              ],
              metrics: [
                { name: "status", value: issue.status, unit: "status", status: issueStatus },
                { name: "priority", value: issue.priority, unit: "priority", status: issueStatus },
              ],
            })
            console.log(
              `✅ Added ${property.name} Maintenance Issue "${issue.issue_type}" (${issueStatus}) to ${statusType} details`,
            )
          }
        })

        // Use batched data for recurring maintenance items
        const recurringIssues = allRecurringIssues.filter((issue) => issue.property_id === property.id)
        console.log(`🔄 ${property.name} individual recurring maintenance issues:`, recurringIssues.length)

        recurringIssues.forEach((issue, index) => {
          const today = new Date()
          const dueDate = new Date(issue.next_due_date)
          const isOverdue = dueDate < today

          let issueStatus: "green" | "orange" | "red"
          if (isOverdue) {
            issueStatus = "orange" // Requires Action
          } else {
            issueStatus = "green" // Operational
          }

          if (
            (statusType === "healthy" && issueStatus === "green") ||
            (statusType === "warning" && issueStatus === "orange") ||
            (statusType === "critical" && issueStatus === "red")
          ) {
            const daysDifference = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
            details.push({
              propertyName: property.name,
              systemType: `Recurring: ${issue.issue_type}`,
              status: issueStatus,
              details: [
                `Recurrence: ${issue.recurrence_period}`,
                `Next Due: ${dueDate.toLocaleDateString()}`,
                isOverdue ? `Overdue by ${Math.abs(daysDifference)} days` : `Due in ${daysDifference} days`,
              ],
              metrics: [
                { name: "days_until_due", value: daysDifference, unit: "days", status: issueStatus },
                { name: "recurrence", value: issue.recurrence_period, unit: "period", status: issueStatus },
              ],
            })
            console.log(
              `✅ Added ${property.name} Recurring Maintenance "${issue.issue_type}" (${issueStatus}) to ${statusType} details`,
            )
          }
        })
      } catch (error) {
        console.warn(`⚠️ Error getting details for ${property.name}:`, error)
        // Continue with other properties
      }
    }

    console.log(
      `✅ Found ${details.length} ${statusType} items:`,
      details.map((d) => `${d.propertyName}:${d.systemType}:${d.status}`),
    )

    // Cache the result
    detailsCache[cacheKey] = {
      data: details,
      timestamp: now,
    }

    return details
  } catch (error) {
    console.error("Error fetching detailed status breakdown:", error)
    // Return cached data if available
    if (detailsCache[statusType]) {
      console.log(`📦 Returning stale cached ${statusType} details due to error`)
      return detailsCache[statusType].data
    }
    return []
  }
}

// New function to get individual maintenance issues
async function getIndividualMaintenanceIssues(propertyId: string) {
  try {
    const supabase = createClient()
    const { data: issues, error } = await supabase
      .from("maintenance_issues")
      .select("id, issue_type, status, priority, category, issue_date, resolution_date")
      .eq("property_id", propertyId)

    if (error) {
      console.warn(`Individual maintenance query error for ${propertyId}:`, error)
      return []
    }

    return issues || []
  } catch (error) {
    console.warn(`Individual maintenance error for ${propertyId}:`, error)
    return []
  }
}

// New function to get individual recurring maintenance issues
async function getIndividualRecurringMaintenanceIssues(propertyId: string) {
  try {
    const supabase = createClient()
    const { data: recurringIssues, error } = await supabase
      .from("maintenance_issues")
      .select("id, issue_type, next_due_date, recurrence_period, priority")
      .eq("property_id", propertyId)
      .eq("is_recurring", true)
      .not("next_due_date", "is", null)

    if (error) {
      console.warn(`Individual recurring maintenance query error for ${propertyId}:`, error)
      return []
    }

    return recurringIssues || []
  } catch (error) {
    console.warn(`Individual recurring maintenance error for ${propertyId}:`, error)
    return []
  }
}

// Safe wrapper functions with timeout and error handling
async function safeGetElectricityStatus(propertyId: string): Promise<"green" | "orange" | "red"> {
  try {
    const timeoutPromise = new Promise<"green">((_, reject) =>
      setTimeout(() => reject(new Error("Electricity status timeout")), 2000),
    )

    return await Promise.race([getElectricityStatusFast(propertyId), timeoutPromise])
  } catch (error) {
    console.warn(`⚠️ Electricity status error for ${propertyId}:`, error)
    return "green" // Fail safe
  }
}

async function safeGetOTTStatus(propertyId: string): Promise<"green" | "orange" | "red"> {
  try {
    const timeoutPromise = new Promise<"green">(
      (_, reject) => setTimeout(() => reject(new Error("OTT status timeout")), 2000), // Reduced from 3000 to 2000
    )

    return await Promise.race([getOTTStatusFast(propertyId), timeoutPromise])
  } catch (error) {
    console.warn(`⚠️ OTT status error for ${propertyId}:`, error)
    return "green" // Fail safe
  }
}

async function safeGetInternetStatus(propertyId: string): Promise<"green" | "orange" | "red"> {
  try {
    const timeoutPromise = new Promise<"green">((_, reject) =>
      setTimeout(() => reject(new Error("Internet status timeout")), 2000),
    )

    return await Promise.race([getInternetStatusFast(propertyId), timeoutPromise])
  } catch (error) {
    console.warn(`⚠️ Internet status error for ${propertyId}:`, error)
    return "green" // Fail safe
  }
}

async function safeGetElectricityStatusDetails(propertyId: string) {
  try {
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Electricity details timeout")), 1500),
    )

    return await Promise.race([getElectricityStatusDetailsFast(propertyId), timeoutPromise])
  } catch (error) {
    console.warn(`⚠️ Electricity details error for ${propertyId}:`, error)
    return {
      status: "green" as const,
      details: ["Status unavailable"],
      metrics: [{ name: "status", value: "unknown", unit: "status", status: "green" }],
    }
  }
}

async function safeGetOTTStatusDetails(propertyId: string) {
  try {
    const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error("OTT details timeout")), 1500))

    return await Promise.race([getOTTStatusDetailsFast(propertyId), timeoutPromise])
  } catch (error) {
    console.warn(`⚠️ OTT details error for ${propertyId}:`, error)
    return {
      status: "green" as const,
      details: ["Status unavailable"],
      metrics: [{ name: "status", value: "unknown", unit: "status", status: "green" }],
    }
  }
}

async function safeGetInternetStatusDetails(propertyId: string) {
  try {
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Internet details timeout")), 1500),
    )

    return await Promise.race([getInternetStatusDetailsFast(propertyId), timeoutPromise])
  } catch (error) {
    console.warn(`⚠️ Internet details error for ${propertyId}:`, error)
    return {
      status: "green" as const,
      details: ["Status unavailable"],
      metrics: [{ name: "status", value: "unknown", unit: "status", status: "green" }],
    }
  }
}

// Fast status check functions with minimal API calls
async function getElectricityStatusFast(propertyId: string): Promise<"green" | "orange" | "red"> {
  try {
    const supabase = createClient()
    const { data: fuelData, error } = await supabase
      .from("generator_fuel_updates")
      .select("fuel_in_generator_percentage, fuel_in_tank_liters")
      .eq("property_id", propertyId)
      .order("date", { ascending: false })
      .limit(1)
      .single()

    if (error || !fuelData) {
      console.log(`No fuel data for ${propertyId}, defaulting to green`)
      return "green"
    }

    const fuelPercentage = fuelData.fuel_in_generator_percentage || 0
    const fuelInTank = Number.parseFloat(fuelData.fuel_in_tank_liters) || 0
    const totalFuel = (fuelPercentage / 100) * 100 + fuelInTank
    const backupHours = totalFuel / 6.5

    if (backupHours >= 9) return "green"
    else if (backupHours >= 6) return "orange"
    else return "red"
  } catch (error) {
    console.warn(`Electricity status error for ${propertyId}:`, error)
    return "green" // Fail safe
  }
}

async function getOTTStatusFast(propertyId: string): Promise<"green" | "orange" | "red"> {
  try {
    const ottServices = await getOttServices(propertyId)

    // Handle case where getOttServices returns empty array due to errors
    if (!ottServices || ottServices.length === 0) {
      console.log(`No OTT services data available for ${propertyId}, defaulting to green`)
      return "green"
    }

    const activeServices = ottServices.filter((service) => service.current_status === "Active")

    if (!activeServices || activeServices.length === 0) {
      console.log(`No active OTT services for ${propertyId}`)
      return "green"
    }

    const today = new Date()
    for (const service of activeServices) {
      if (service.next_recharge_date) {
        const nextPaymentDate = new Date(service.next_recharge_date)
        const daysUntilPayment = Math.ceil((nextPaymentDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

        if (daysUntilPayment < 0) return "red"
        else if (daysUntilPayment <= 7) return "orange"
      }
    }
    return "green"
  } catch (error) {
    console.warn(`OTT status error for ${propertyId}:`, error)
    return "green" // Fail safe
  }
}

async function getInternetStatusFast(propertyId: string): Promise<"green" | "orange" | "red"> {
  try {
    if (propertyId === "jublee-hills") {
      // Import uptime functions dynamically to avoid circular dependencies
      const { getUptimeStatistics } = await import("./uptime-reports")
      const statistics = await getUptimeStatistics(propertyId)

      const avgUptime = statistics.weekly.averageUptime

      if (avgUptime < 97) return "red"
      else if (avgUptime < 99) return "orange"
      else return "green"
    }

    // For other properties, default to green
    return "green"
  } catch (error) {
    console.warn(`Internet status error for ${propertyId}:`, error)
    return "green" // Fail safe
  }
}

// Fast detail functions (simplified versions of the full detail functions)
async function getElectricityStatusDetailsFast(propertyId: string) {
  const status = await getElectricityStatusFast(propertyId)

  return {
    status,
    details:
      status === "green"
        ? ["Generator operating normally"]
        : status === "orange"
          ? ["Generator fuel level low"]
          : ["Critical generator fuel level"],
    metrics: [{ name: "status", value: status, unit: "status", status }],
  }
}

async function getOTTStatusDetailsFast(propertyId: string) {
  const status = await getOTTStatusFast(propertyId)

  return {
    status,
    details:
      status === "green"
        ? ["OTT services up to date"]
        : status === "orange"
          ? ["OTT payment due soon"]
          : ["OTT payment overdue"],
    metrics: [{ name: "status", value: status, unit: "status", status }],
  }
}

async function getInternetStatusDetailsFast(propertyId: string) {
  const status = await getInternetStatusFast(propertyId)

  try {
    if (propertyId === "jublee-hills") {
      const { getUptimeStatistics } = await import("./uptime-reports")
      const statistics = await getUptimeStatistics(propertyId)

      return {
        status,
        details: [
          `Weekly uptime: ${statistics.weekly.averageUptime.toFixed(2)}%`,
          `Total downtime: ${statistics.weekly.totalDowntime} minutes`,
          `Disruption count: ${statistics.weekly.disruptionCount}`,
        ],
        metrics: [
          { name: "weekly_uptime", value: Number(statistics.weekly.averageUptime), unit: "%", status },
          { name: "downtime", value: Number(statistics.weekly.totalDowntime), unit: "mins", status },
        ],
      }
    }
  } catch (error) {
    console.warn(`Internet details error for ${propertyId}:`, error)
  }

  return {
    status,
    details:
      status === "green"
        ? ["Internet connection stable"]
        : status === "orange"
          ? ["Internet connection unstable"]
          : ["Internet connection critical"],
    metrics: [{ name: "status", value: status, unit: "status", status }],
  }
}

// New function to count individual maintenance issues by status
async function getMaintenanceIssueCounts(propertyId: string): Promise<{
  total: number
  healthy: number
  warning: number
  critical: number
}> {
  try {
    const supabase = createClient()
    const { data: issues, error } = await supabase
      .from("maintenance_issues")
      .select("status, priority, issue_type")
      .eq("property_id", propertyId)

    if (error) {
      console.warn(`Maintenance query error for ${propertyId}:`, error)
      return { total: 0, healthy: 0, warning: 0, critical: 0 }
    }

    if (!issues || issues.length === 0) {
      console.log(`🔧 ${propertyId}: No maintenance issues found`)
      return { total: 0, healthy: 0, warning: 0, critical: 0 }
    }

    console.log(`🔧 ${propertyId}: Found ${issues.length} maintenance issues`)

    let criticalCount = 0
    let warningCount = 0
    let healthyCount = 0

    issues.forEach((issue) => {
      console.log(`🔧 ${propertyId}: Issue "${issue.issue_type}" has status "${issue.status}"`)

      if (issue.status === "Open" || issue.status === "On Hold") {
        criticalCount++
      } else if (issue.status === "In Progress") {
        warningCount++
      } else if (issue.status === "Resolved" || issue.status === "Closed" || issue.status === "Completed") {
        healthyCount++
      }
    })

    console.log(`🔧 ${propertyId}: Critical: ${criticalCount}, Warning: ${warningCount}, Healthy: ${healthyCount}`)

    return {
      total: issues.length,
      healthy: healthyCount,
      warning: warningCount,
      critical: criticalCount,
    }
  } catch (error) {
    console.warn(`Maintenance count error for ${propertyId}:`, error)
    return { total: 0, healthy: 0, warning: 0, critical: 0 }
  }
}

// Updated function to count recurring maintenance due dates with new logic
async function getRecurringMaintenanceDueCounts(propertyId: string): Promise<{
  total: number
  healthy: number
  warning: number
  critical: number
}> {
  try {
    const supabase = createClient()
    const { data: recurringIssues, error } = await supabase
      .from("maintenance_issues")
      .select("issue_type, next_due_date, recurrence_period")
      .eq("property_id", propertyId)
      .eq("is_recurring", true)
      .not("next_due_date", "is", null)

    if (error) {
      console.warn(`Recurring maintenance query error for ${propertyId}:`, error)
      return { total: 0, healthy: 0, warning: 0, critical: 0 }
    }

    if (!recurringIssues || recurringIssues.length === 0) {
      console.log(`🔄 ${propertyId}: No recurring maintenance issues found`)
      return { total: 0, healthy: 0, warning: 0, critical: 0 }
    }

    console.log(`🔄 ${propertyId}: Found ${recurringIssues.length} recurring maintenance issues`)

    const today = new Date()
    let warningCount = 0 // Overdue (requires action)
    let healthyCount = 0 // Not overdue (operational)

    recurringIssues.forEach((issue) => {
      const dueDate = new Date(issue.next_due_date)
      const isOverdue = dueDate < today
      const daysDifference = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

      console.log(`🔄 ${propertyId}: Issue "${issue.issue_type}" due in ${daysDifference} days (overdue: ${isOverdue})`)

      if (isOverdue) {
        warningCount++ // Requires Action
      } else {
        healthyCount++ // Operational
      }
    })

    console.log(`🔄 ${propertyId}: Warning (overdue): ${warningCount}, Healthy (on time): ${healthyCount}`)

    return {
      total: recurringIssues.length,
      healthy: healthyCount,
      warning: warningCount,
      critical: 0, // No critical category for recurring maintenance
    }
  } catch (error) {
    console.warn(`Recurring maintenance count error for ${propertyId}:`, error)
    return { total: 0, healthy: 0, warning: 0, critical: 0 }
  }
}

// New function to get detailed maintenance issues for the modal
export async function getDetailedMaintenanceIssues(
  statusType: "healthy" | "warning" | "critical",
): Promise<MaintenanceIssue[]> {
  try {
    const supabase = createClient()

    // Only process home properties
    const homeProperties = PROPERTIES.filter((property) => property.type === "home")
    const allIssues: MaintenanceIssue[] = []

    for (const property of homeProperties) {
      const { data: issues, error } = await supabase
        .from("maintenance_issues")
        .select("id, issue_type, status, priority, category, issue_date, resolution_date")
        .eq("property_id", property.id)

      if (error) {
        console.warn(`Error fetching maintenance issues for ${property.id}:`, error)
        continue
      }

      if (!issues || issues.length === 0) continue

      // Filter issues based on status type
      const filteredIssues = issues.filter((issue) => {
        if (statusType === "critical") {
          return issue.status === "Open" || issue.status === "On Hold"
        } else if (statusType === "warning") {
          return issue.status === "In Progress"
        } else if (statusType === "healthy") {
          return issue.status === "Resolved" || issue.status === "Closed" || issue.status === "Completed"
        }
        return false
      })

      // Convert to our format
      const formattedIssues: MaintenanceIssue[] = filteredIssues.map((issue) => ({
        id: issue.id,
        propertyName: property.name,
        issueType: issue.issue_type,
        status: issue.status,
        priority: issue.priority,
        category: issue.category,
        issueDate: issue.issue_date,
        resolutionDate: issue.resolution_date,
      }))

      allIssues.push(...formattedIssues)
    }

    console.log(`🔧 Fetched ${allIssues.length} ${statusType} maintenance issues for modal`)
    return allIssues
  } catch (error) {
    console.error("Error fetching detailed maintenance issues:", error)
    return []
  }
}

export type MaintenanceIssue = {
  id: string
  propertyName: string
  issueType: string
  status: string
  priority: string
  category: string
  issueDate: string
  resolutionDate?: string
}

// Batch query functions for better performance
async function batchGetMaintenanceIssues(propertyIds: string[]) {
  try {
    const supabase = createClient()
    const { data: issues, error } = await supabase
      .from("maintenance_issues")
      .select("property_id, id, issue_type, status, priority, category, issue_date, resolution_date")
      .in("property_id", propertyIds)

    if (error) {
      console.warn(`Batch maintenance query error:`, error)
      return []
    }

    return issues || []
  } catch (error) {
    console.warn(`Batch maintenance error:`, error)
    return []
  }
}

async function batchGetRecurringMaintenanceIssues(propertyIds: string[]) {
  try {
    const supabase = createClient()
    const { data: recurringIssues, error } = await supabase
      .from("maintenance_issues")
      .select("property_id, id, issue_type, next_due_date, recurrence_period, priority")
      .in("property_id", propertyIds)
      .eq("is_recurring", true)
      .not("next_due_date", "is", null)

    if (error) {
      console.warn(`Batch recurring maintenance query error:`, error)
      return []
    }

    return recurringIssues || []
  } catch (error) {
    console.warn(`Batch recurring maintenance error:`, error)
    return []
  }
}
