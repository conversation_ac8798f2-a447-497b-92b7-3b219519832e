"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"
import { hashPassword } from "@/lib/auth"
import type { User, Role } from "@/lib/auth"

// Get all users
export async function getUsers(): Promise<User[]> {
  const supabase = createServerClient()

  const { data, error } = await supabase.from("users").select("*").order("username")

  if (error) {
    console.error("Error fetching users:", error)
    throw new Error("Failed to fetch users")
  }

  return data as User[]
}

// Get all roles
export async function getRoles(): Promise<Role[]> {
  const supabase = createServerClient()

  const { data, error } = await supabase.from("roles").select("*").order("name")

  if (error) {
    console.error("Error fetching roles:", error)
    throw new Error("Failed to fetch roles")
  }

  return data as Role[]
}

// Get user roles
export async function getUserRoles(userId: string): Promise<Role[]> {
  const supabase = createServerClient()

  const { data: userRoles, error: userRolesError } = await supabase
    .from("user_roles")
    .select("role_id")
    .eq("user_id", userId)

  if (userRolesError) {
    console.error("Error fetching user roles:", userRolesError)
    throw new Error("Failed to fetch user roles")
  }

  if (!userRoles || userRoles.length === 0) {
    return []
  }

  const roleIds = userRoles.map((ur) => ur.role_id)

  const { data: roles, error: rolesError } = await supabase.from("roles").select("*").in("id", roleIds)

  if (rolesError) {
    console.error("Error fetching roles:", rolesError)
    throw new Error("Failed to fetch roles")
  }

  return roles as Role[]
}

// Create new user
export async function createUser(userData: {
  username: string
  email: string
  fullName: string
  password: string
  roleIds: number[]
}) {
  const supabase = createServerClient()

  try {
    // Check if username already exists
    const { data: existingUser } = await supabase.from("users").select("id").eq("username", userData.username).single()

    if (existingUser) {
      return { success: false, error: "Username already exists" }
    }

    // Check if email already exists
    const { data: existingEmail } = await supabase.from("users").select("id").eq("email", userData.email).single()

    if (existingEmail) {
      return { success: false, error: "Email already exists" }
    }

    // Hash password
    const hashedPassword = await hashPassword(userData.password)

    // Create user with correct column name
    const { data: newUser, error: userError } = await supabase
      .from("users")
      .insert({
        username: userData.username,
        email: userData.email,
        full_name: userData.fullName,
        password: hashedPassword, // Using the actual 'password' column
        role: "user", // Default role
        is_active: true,
      })
      .select()
      .single()

    if (userError) {
      console.error("Error creating user:", userError)
      return { success: false, error: "Failed to create user" }
    }

    // Assign roles if provided
    if (userData.roleIds.length > 0) {
      const userRoles = userData.roleIds.map((roleId) => ({
        user_id: newUser.id,
        role_id: roleId,
      }))

      const { error: rolesError } = await supabase.from("user_roles").insert(userRoles)

      if (rolesError) {
        console.error("Error assigning roles:", rolesError)
        // Don't fail the entire operation, just log the error
      }
    }

    revalidatePath("/admin/users")
    return { success: true, user: newUser }
  } catch (error) {
    console.error("Error in createUser:", error)
    return { success: false, error: "An unexpected error occurred" }
  }
}

// Update user details
export async function updateUser(
  userId: string,
  userData: {
    username: string
    email: string
    fullName: string
    password?: string // Optional password update
    roleIds: number[]
  },
) {
  const supabase = createServerClient()

  try {
    // Check if username already exists (excluding current user)
    const { data: existingUser } = await supabase
      .from("users")
      .select("id")
      .eq("username", userData.username)
      .neq("id", userId)
      .single()

    if (existingUser) {
      return { success: false, error: "Username already exists" }
    }

    // Check if email already exists (excluding current user)
    const { data: existingEmail } = await supabase
      .from("users")
      .select("id")
      .eq("email", userData.email)
      .neq("id", userId)
      .single()

    if (existingEmail) {
      return { success: false, error: "Email already exists" }
    }

    // Prepare update data
    const updateData: any = {
      username: userData.username,
      email: userData.email,
      full_name: userData.fullName,
    }

    // Hash and include password if provided
    if (userData.password && userData.password.trim() !== "") {
      updateData.password = await hashPassword(userData.password)
    }

    // Update user
    const { error: userError } = await supabase.from("users").update(updateData).eq("id", userId)

    if (userError) {
      console.error("Error updating user:", userError)
      return { success: false, error: "Failed to update user" }
    }

    // Update user roles
    const rolesResult = await updateUserRoles(userId, userData.roleIds)
    if (!rolesResult.success) {
      return { success: false, error: "User updated but failed to update roles" }
    }

    revalidatePath("/admin/users")
    return { success: true }
  } catch (error) {
    console.error("Error in updateUser:", error)
    return { success: false, error: "An unexpected error occurred" }
  }
}

// Update user status (active/inactive)
export async function updateUserStatus(userId: string, isActive: boolean) {
  const supabase = createServerClient()

  const { error } = await supabase.from("users").update({ is_active: isActive }).eq("id", userId)

  if (error) {
    console.error("Error updating user status:", error)
    return { success: false, error: "Failed to update user status" }
  }

  revalidatePath("/admin/users")
  return { success: true }
}

// Update user roles
export async function updateUserRoles(userId: string, roleIds: number[]) {
  const supabase = createServerClient()

  // First, delete all existing user roles
  const { error: deleteError } = await supabase.from("user_roles").delete().eq("user_id", userId)

  if (deleteError) {
    console.error("Error deleting user roles:", deleteError)
    return { success: false, error: "Failed to update user roles" }
  }

  // Then, insert new user roles
  if (roleIds.length > 0) {
    const userRoles = roleIds.map((roleId) => ({
      user_id: userId,
      role_id: roleId,
    }))

    const { error: insertError } = await supabase.from("user_roles").insert(userRoles)

    if (insertError) {
      console.error("Error inserting user roles:", insertError)
      return { success: false, error: "Failed to update user roles" }
    }
  }

  revalidatePath("/admin/users")
  return { success: true }
}
