import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/permissions/configurable_permission_widget.dart';
import '../providers/dashboard_providers.dart';
import '../providers/dashboard_config_provider.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../widgets/v2/adaptive_dashboard_layout.dart';
import '../widgets/v2/critical_alerts_panel.dart';
import '../widgets/v2/system_health_section.dart';
import '../widgets/v2/enhanced_stat_card.dart';
import '../widgets/v2/quick_actions_widget.dart';
import '../widgets/v2/insights_carousel.dart';
import '../widgets/v2/device_control_hub_card.dart';

/// Dashboard v2 - Enhanced dashboard with modern UI and advanced features
class DashboardV2Screen extends ConsumerWidget {
  const DashboardV2Screen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print('🚀 DASHBOARD V2: Building enhanced dashboard...');

    final dashboardAsync = ref.watch(dashboardDataProvider);
    final currentUserAsync = ref.watch(currentUserProvider);
    final dashboardConfig = ref.watch(dashboardConfigProvider);

    return Scaffold(
      appBar: RoleBasedAppBar(
        title: 'Dashboard v2',
        showRoleIndicator: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(dashboardDataProvider);
            },
          ),
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              ref.read(dashboardConfigProvider.notifier).toggleDashboardVersion();
            },
            tooltip: 'Switch to Legacy Dashboard',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) => _handleMenuAction(context, ref, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Dashboard Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'layout',
                child: Row(
                  children: [
                    Icon(Icons.view_module),
                    SizedBox(width: 8),
                    Text('Change Layout'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export Data'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(dashboardDataProvider);
        },
        child: currentUserAsync.when(
          data: (user) {
            if (user == null) {
              return const Center(
                child: Text('Please log in to view dashboard'),
              );
            }

            return dashboardAsync.when(
              data: (dashboard) => _buildDashboardContent(
                context,
                ref,
                dashboard,
                user,
                dashboardConfig,
              ),
              loading: () => _buildLoadingState(),
              error: (error, stack) => _buildErrorState(error),
            );
          },
          loading: () => _buildLoadingState(),
          error: (error, stack) => _buildErrorState(error),
        ),
      ),
      bottomNavigationBar: RoleBasedBottomNavBar(
        currentIndex: 0,
        onTap: (index) => _handleBottomNavTap(context, index),
      ),
    );
  }

  Widget _buildDashboardContent(
    BuildContext context,
    WidgetRef ref,
    dynamic dashboard,
    dynamic user,
    DashboardConfig config,
  ) {
    return AdaptiveDashboardLayout(
      layout: config.layout,
      children: [
        // Critical Alerts - Always at top for immediate attention
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'critical_alerts',
          title: 'Critical Alerts',
          children: [
            CriticalAlertsPanel(
              alerts: dashboard.data.recentAlerts
                  .where((alert) => alert.severity == 'critical')
                  .toList(),
            ),
          ],
        ),

        // System Health Overview
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'system_health',
          title: 'System Health',
          children: [
            SystemHealthSection(
              healthScore: 85.0, // This would come from dashboard.data
              properties: dashboard.data.properties,
              maintenanceIssues: dashboard.data.maintenanceIssues,
            ),
          ],
        ),

        // Enhanced Property Statistics
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'property_stats_v2',
          title: 'Property Overview',
          children: [
            _buildEnhancedPropertyStats(dashboard.data.properties),
          ],
        ),

        // Enhanced Maintenance Statistics
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'maintenance_stats_v2',
          title: 'Maintenance Overview',
          children: [
            _buildEnhancedMaintenanceStats(dashboard.data.maintenanceIssues),
          ],
        ),

        // Quick Actions
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'quick_actions',
          title: 'Quick Actions',
          children: [
            QuickActionsWidget(userRole: user.role),
          ],
        ),

        // Device Control Hub
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'device_control_hub',
          title: 'Device Control Hub',
          children: [
            const DeviceControlHubCard(),
          ],
        ),

        // Insights Carousel (if enabled)
        if (config.showInsights)
          ConfigurablePermissionSection(
            screenName: 'dashboard',
            widgetName: 'insights',
            title: 'Insights & Recommendations',
            children: [
              InsightsCarousel(
                insights: _generateInsights(dashboard.data),
              ),
            ],
          ),

        // Recent Alerts (non-critical)
        ConfigurablePermissionSection(
          screenName: 'dashboard',
          widgetName: 'recent_alerts_v2',
          title: 'Recent Activity',
          children: [
            _buildRecentAlertsV2(
              context,
              dashboard.data.recentAlerts
                  .where((alert) => alert.severity != 'critical')
                  .toList(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEnhancedPropertyStats(dynamic properties) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: AppConstants.smallPadding,
      mainAxisSpacing: AppConstants.smallPadding,
      childAspectRatio: 1.1,
      children: [
        EnhancedStatCard(
          title: 'Total Properties',
          value: properties.total.toString(),
          icon: Icons.business,
          statusColor: Colors.blue,
          trend: TrendDirection.up,
          trendValue: '+2.5%',
          sparklineData: [20, 22, 21, 23, 25], // Mock data
          onTap: () => print('Navigate to properties'),
        ),
        EnhancedStatCard(
          title: 'Operational',
          value: properties.operational.toString(),
          icon: Icons.check_circle,
          statusColor: Colors.green,
          trend: TrendDirection.stable,
          trendValue: '0%',
          sparklineData: [18, 19, 18, 19, 19],
          onTap: () => print('Navigate to operational properties'),
        ),
        EnhancedStatCard(
          title: 'Warning',
          value: properties.warning.toString(),
          icon: Icons.warning,
          statusColor: Colors.orange,
          trend: TrendDirection.down,
          trendValue: '-1.2%',
          sparklineData: [5, 4, 5, 3, 3],
          onTap: () => print('Navigate to warning properties'),
        ),
        EnhancedStatCard(
          title: 'Critical',
          value: properties.critical.toString(),
          icon: Icons.error,
          statusColor: Colors.red,
          trend: TrendDirection.up,
          trendValue: '+0.8%',
          sparklineData: [1, 2, 1, 2, 3],
          onTap: () => print('Navigate to critical properties'),
        ),
      ],
    );
  }

  Widget _buildEnhancedMaintenanceStats(dynamic maintenanceIssues) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: AppConstants.smallPadding,
      mainAxisSpacing: AppConstants.smallPadding,
      childAspectRatio: 1.1,
      children: [
        EnhancedStatCard(
          title: 'Total Issues',
          value: maintenanceIssues.total.toString(),
          icon: Icons.build,
          statusColor: Colors.blue,
          trend: TrendDirection.down,
          trendValue: '-5.2%',
          sparklineData: [15, 12, 10, 8, 8],
          onTap: () => print('Navigate to maintenance'),
        ),
        EnhancedStatCard(
          title: 'Open',
          value: maintenanceIssues.open.toString(),
          icon: Icons.pending,
          statusColor: Colors.orange,
          trend: TrendDirection.stable,
          trendValue: '0%',
          sparklineData: [5, 5, 4, 5, 5],
          onTap: () => print('Navigate to open issues'),
        ),
        EnhancedStatCard(
          title: 'In Progress',
          value: maintenanceIssues.inProgress.toString(),
          icon: Icons.engineering,
          statusColor: Colors.blue,
          trend: TrendDirection.up,
          trendValue: '+2.1%',
          sparklineData: [2, 3, 2, 3, 3],
          onTap: () => print('Navigate to in progress'),
        ),
        EnhancedStatCard(
          title: 'Critical',
          value: maintenanceIssues.critical.toString(),
          icon: Icons.priority_high,
          statusColor: Colors.red,
          trend: TrendDirection.down,
          trendValue: '-1.5%',
          sparklineData: [3, 2, 1, 1, 0],
          onTap: () => print('Navigate to critical issues'),
        ),
      ],
    );
  }

  Widget _buildRecentAlertsV2(BuildContext context, List<dynamic> alerts) {
    if (alerts.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Icon(
                Icons.check_circle,
                size: 48,
                color: Colors.green.shade300,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                'All Clear!',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                'No recent alerts to display',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: alerts.length > 5 ? 5 : alerts.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final alert = alerts[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: _getAlertColor(alert.severity).withOpacity(0.1),
              child: Icon(
                _getAlertIcon(alert.type),
                color: _getAlertColor(alert.severity),
                size: 20,
              ),
            ),
            title: Text(alert.message),
            subtitle: Text(alert.propertyName),
            trailing: Chip(
              label: Text(
                alert.severity.toUpperCase(),
                style: const TextStyle(fontSize: 10),
              ),
              backgroundColor: _getAlertColor(alert.severity).withOpacity(0.1),
            ),
          );
        },
      ),
    );
  }

  List<String> _generateInsights(dynamic dashboardData) {
    // Mock insights - in real app, this would be generated from data analysis
    return [
      'Property maintenance costs decreased by 15% this month',
      'Generator fuel efficiency improved by 8% across all sites',
      'Attendance rate is 5% above target for Q4',
      'Recommend scheduling preventive maintenance for 3 properties',
    ];
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading dashboard...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(dynamic error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('Error loading dashboard: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Retry logic would go here
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Color _getAlertColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'warning':
        return Colors.orange;
      case 'info':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getAlertIcon(String type) {
    switch (type.toLowerCase()) {
      case 'maintenance':
        return Icons.build;
      case 'security':
        return Icons.security;
      case 'fuel':
        return Icons.local_gas_station;
      case 'power':
        return Icons.power;
      default:
        return Icons.info;
    }
  }

  void _handleMenuAction(BuildContext context, WidgetRef ref, String action) {
    switch (action) {
      case 'settings':
        _showDashboardSettings(context, ref);
        break;
      case 'layout':
        _showLayoutOptions(context, ref);
        break;
      case 'export':
        _exportDashboardData(context);
        break;
    }
  }

  void _showDashboardSettings(BuildContext context, WidgetRef ref) {
    // Implementation for dashboard settings dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dashboard Settings'),
        content: const Text('Dashboard settings coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLayoutOptions(BuildContext context, WidgetRef ref) {
    // Implementation for layout selection
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choose Layout'),
        content: const Text('Layout options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportDashboardData(BuildContext context) {
    // Implementation for data export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export feature coming soon...')),
    );
  }

  void _handleBottomNavTap(BuildContext context, int index) {
    final routes = [
      '/dashboard',
      '/properties',
      '/maintenance',
      '/attendance',
      '/admin',
    ];

    if (index < routes.length) {
      context.go(routes[index]);
    }
  }
}
