import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';
import '../../core/business_logic/status_calculator.dart';

part 'generator_fuel.g.dart';

@JsonSerializable()
class GeneratorFuelLog {
  final String id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'property_id')
  final String propertyId;
  @J<PERSON><PERSON>ey(name: 'fuel_level_liters')
  final double fuelLevelLiters;
  @JsonKey(name: 'consumption_rate')
  final double? consumptionRate;
  @Json<PERSON>ey(name: 'runtime_hours')
  final double? runtimeHours;
  @Json<PERSON>ey(name: 'efficiency_percentage')
  final double? efficiencyPercentage;
  final String? notes;
  @J<PERSON><PERSON>ey(name: 'recorded_at')
  final DateTime recordedAt;

  const GeneratorFuelLog({
    required this.id,
    required this.propertyId,
    required this.fuelLevelLiters,
    this.consumptionRate,
    this.runtimeHours,
    this.efficiencyPercentage,
    this.notes,
    required this.recordedAt,
  });

  factory GeneratorFuelLog.fromJson(Map<String, dynamic> json) => _$GeneratorFuelLogFromJson(json);
  Map<String, dynamic> toJson() => _$GeneratorFuelLogToJson(this);

  GeneratorFuelLog copyWith({
    String? id,
    String? propertyId,
    double? fuelLevelLiters,
    double? consumptionRate,
    double? runtimeHours,
    double? efficiencyPercentage,
    String? notes,
    DateTime? recordedAt,
  }) {
    return GeneratorFuelLog(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      fuelLevelLiters: fuelLevelLiters ?? this.fuelLevelLiters,
      consumptionRate: consumptionRate ?? this.consumptionRate,
      runtimeHours: runtimeHours ?? this.runtimeHours,
      efficiencyPercentage: efficiencyPercentage ?? this.efficiencyPercentage,
      notes: notes ?? this.notes,
      recordedAt: recordedAt ?? this.recordedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GeneratorFuelLog && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'GeneratorFuelLog(id: $id, propertyId: $propertyId, fuelLevelLiters: $fuelLevelLiters, consumptionRate: $consumptionRate, runtimeHours: $runtimeHours, efficiencyPercentage: $efficiencyPercentage, notes: $notes, recordedAt: $recordedAt)';
  }

  // Business logic methods
  double get backupHours {
    if (consumptionRate == null || consumptionRate! <= 0) return 0;
    return fuelLevelLiters / consumptionRate!;
  }

  StatusLevel get fuelStatus {
    if (backupHours < 6) return StatusLevel.red;
    if (backupHours < 12) return StatusLevel.orange;
    return StatusLevel.green;
  }

  Color get statusColor {
    switch (fuelStatus) {
      case StatusLevel.green:
        return Colors.green;
      case StatusLevel.orange:
        return Colors.orange;
      case StatusLevel.red:
        return Colors.red;
    }
  }

  String get statusDescription {
    switch (fuelStatus) {
      case StatusLevel.green:
        return 'Good';
      case StatusLevel.orange:
        return 'Low';
      case StatusLevel.red:
        return 'Critical';
    }
  }

  bool get isCritical => fuelStatus == StatusLevel.red;
  bool get isLow => fuelStatus == StatusLevel.orange;
  bool get isGood => fuelStatus == StatusLevel.green;

  double get fuelPercentage {
    // Assuming 1000L is full tank capacity
    const maxCapacity = 1000.0;
    return (fuelLevelLiters / maxCapacity) * 100;
  }

  String get fuelPercentageDisplay => '${fuelPercentage.toStringAsFixed(1)}%';

  String get backupHoursDisplay {
    if (backupHours < 1) {
      return '${(backupHours * 60).toStringAsFixed(0)} minutes';
    } else if (backupHours < 24) {
      return '${backupHours.toStringAsFixed(1)} hours';
    } else {
      final days = backupHours / 24;
      return '${days.toStringAsFixed(1)} days';
    }
  }

  String get efficiencyDisplay {
    if (efficiencyPercentage == null) return 'N/A';
    return '${efficiencyPercentage!.toStringAsFixed(1)}%';
  }

  StatusLevel get efficiencyStatus {
    if (efficiencyPercentage == null) return StatusLevel.orange;
    if (efficiencyPercentage! >= 80) return StatusLevel.green;
    if (efficiencyPercentage! >= 60) return StatusLevel.orange;
    return StatusLevel.red;
  }

  Duration get age => DateTime.now().difference(recordedAt);

  String get ageDescription {
    final hours = age.inHours;
    final minutes = age.inMinutes;

    if (hours > 24) {
      return '${age.inDays} days ago';
    } else if (hours > 0) {
      return '$hours hours ago';
    } else if (minutes > 0) {
      return '$minutes minutes ago';
    } else {
      return 'Just now';
    }
  }

  bool get isStale => age.inHours > 24;

  // Validation methods
  bool get isValid => fuelLevelLiters >= 0 && propertyId.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (propertyId.isEmpty) errors.add('Property is required');
    if (fuelLevelLiters < 0) errors.add('Fuel level cannot be negative');
    if (fuelLevelLiters > 1000) errors.add('Fuel level cannot exceed 1000 liters');
    
    if (consumptionRate != null) {
      if (consumptionRate! < 0) errors.add('Consumption rate cannot be negative');
      if (consumptionRate! > 100) errors.add('Consumption rate seems too high');
    }
    
    if (runtimeHours != null) {
      if (runtimeHours! < 0) errors.add('Runtime hours cannot be negative');
      if (runtimeHours! > 24) errors.add('Runtime hours cannot exceed 24 per day');
    }
    
    if (efficiencyPercentage != null) {
      if (efficiencyPercentage! < 0) errors.add('Efficiency cannot be negative');
      if (efficiencyPercentage! > 100) errors.add('Efficiency cannot exceed 100%');
    }
    
    return errors;
  }

  // Helper methods
  static GeneratorFuelLog create({
    required String propertyId,
    required double fuelLevelLiters,
    double? consumptionRate,
    double? runtimeHours,
    double? efficiencyPercentage,
    String? notes,
  }) {
    return GeneratorFuelLog(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      propertyId: propertyId,
      fuelLevelLiters: fuelLevelLiters,
      consumptionRate: consumptionRate,
      runtimeHours: runtimeHours,
      efficiencyPercentage: efficiencyPercentage,
      notes: notes,
      recordedAt: DateTime.now(),
    );
  }

  // Calculate fuel consumption between two logs
  static double? calculateConsumption(GeneratorFuelLog previous, GeneratorFuelLog current) {
    if (previous.propertyId != current.propertyId) return null;
    if (current.recordedAt.isBefore(previous.recordedAt)) return null;
    
    final fuelDifference = previous.fuelLevelLiters - current.fuelLevelLiters;
    final timeDifference = current.recordedAt.difference(previous.recordedAt).inHours;
    
    if (timeDifference <= 0 || fuelDifference <= 0) return null;
    
    return fuelDifference / timeDifference;
  }

  // Predict when fuel will run out
  DateTime? predictFuelDepletion() {
    if (consumptionRate == null || consumptionRate! <= 0) return null;
    
    final hoursRemaining = fuelLevelLiters / consumptionRate!;
    return recordedAt.add(Duration(hours: hoursRemaining.round()));
  }

  // Generate fuel insights
  Map<String, dynamic> generateInsights(List<GeneratorFuelLog> historicalLogs) {
    final insights = <String, dynamic>{};
    
    // Current status
    insights['status'] = statusDescription;
    insights['backup_hours'] = backupHours;
    insights['fuel_percentage'] = fuelPercentage;
    
    // Trends (if historical data available)
    if (historicalLogs.isNotEmpty) {
      final recentLogs = historicalLogs
          .where((log) => log.recordedAt.isAfter(DateTime.now().subtract(const Duration(days: 7))))
          .toList();
      
      if (recentLogs.length > 1) {
        final avgConsumption = recentLogs
            .map((log) => log.consumptionRate ?? 0)
            .reduce((a, b) => a + b) / recentLogs.length;
        
        final avgEfficiency = recentLogs
            .where((log) => log.efficiencyPercentage != null)
            .map((log) => log.efficiencyPercentage!)
            .fold(0.0, (a, b) => a + b) / recentLogs.length;
        
        insights['avg_consumption_7d'] = avgConsumption;
        insights['avg_efficiency_7d'] = avgEfficiency;
        
        // Trend analysis
        if (recentLogs.length >= 3) {
          final firstHalf = recentLogs.take(recentLogs.length ~/ 2).toList();
          final secondHalf = recentLogs.skip(recentLogs.length ~/ 2).toList();
          
          final firstAvgConsumption = firstHalf
              .map((log) => log.consumptionRate ?? 0)
              .reduce((a, b) => a + b) / firstHalf.length;
          
          final secondAvgConsumption = secondHalf
              .map((log) => log.consumptionRate ?? 0)
              .reduce((a, b) => a + b) / secondHalf.length;
          
          if (secondAvgConsumption > firstAvgConsumption * 1.1) {
            insights['consumption_trend'] = 'increasing';
          } else if (secondAvgConsumption < firstAvgConsumption * 0.9) {
            insights['consumption_trend'] = 'decreasing';
          } else {
            insights['consumption_trend'] = 'stable';
          }
        }
      }
    }
    
    // Recommendations
    final recommendations = <String>[];
    if (isCritical) {
      recommendations.add('Immediate fuel refill required');
    } else if (isLow) {
      recommendations.add('Schedule fuel refill soon');
    }
    
    if (efficiencyPercentage != null && efficiencyPercentage! < 70) {
      recommendations.add('Generator maintenance recommended');
    }
    
    if (isStale) {
      recommendations.add('Update fuel reading');
    }
    
    insights['recommendations'] = recommendations;
    
    return insights;
  }
}
