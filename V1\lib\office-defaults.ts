// Default values for office locations

// Function to get default duty time based on office location
export function getDefaultDutyTime(officeId: string): string {
  // Check if it's a Brane office
  if (officeId.includes("brane")) {
    return "9:15 - 18:45"
  }
  // For SRSR offices
  return "9:00 - 18:00"
}

// Function to get default check-in time based on office location
export function getDefaultCheckInTime(officeId: string): string {
  if (officeId.includes("brane")) {
    return "09:15"
  }
  return "09:00"
}

// Function to get default check-out time based on office location
export function getDefaultCheckOutTime(officeId: string): string {
  if (officeId.includes("brane")) {
    return "18:45"
  }
  return "18:00"
}

// Function to format duty time from check-in and check-out times
export function formatDutyTime(checkIn: string, checkOut: string): string {
  return `${checkIn} - ${checkOut}`
}

// Default working hours
export const DEFAULT_WORKING_HOURS = 9.3

// Available roles for office members
export const OFFICE_ROLES = [
  "Employee (Full Time)",
  "Employee (Contract)",
  "Intern",
  "Consultant",
  "Support Staff",
  "Housekeeping",
  "Security",
]
