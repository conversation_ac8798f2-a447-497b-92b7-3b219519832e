import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { validateRequest, createPropertySchema } from '@/lib/validation';
import { createApiResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';

// Property type specific default services
function getDefaultServicesForPropertyType(propertyType: string): string[] {
  const type = propertyType.toLowerCase();

  const commonServices = ['electricity', 'water', 'internet', 'security', 'maintenance'];

  switch (type) {
    case 'residential':
      return [...commonServices, 'ott'];
    case 'office':
      return [...commonServices, 'ott'];
    case 'construction_site':
      return ['electricity', 'water', 'security']; // Basic services for construction sites
    default:
      return commonServices;
  }
}

async function getPropertiesHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const params = getQueryParams(request);
    const { type, status } = params;

    // Build where clause
    const where: any = {};

    if (type && ['residential', 'office', 'construction_site'].includes(type)) {
      where.type = type.toUpperCase();
    }

    if (status) {
      where.isActive = status === 'active';
    }

    // Get properties with their services
    const properties = await prisma.property.findMany({
      where,
      include: {
        services: {
          select: {
            id: true,
            serviceType: true,
            status: true,
            lastChecked: true,
            notes: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transform data to match API response format
    const transformedProperties = properties.map(property => ({
      id: property.id,
      name: property.name,
      type: property.type.toLowerCase(),
      address: property.address,
      description: property.description,
      image_url: property.imageUrl,
      is_active: property.isActive,
      services: property.services.map(service => ({
        id: service.id,
        service_type: service.serviceType.toLowerCase(),
        status: service.status.toLowerCase(),
        last_checked: service.lastChecked,
        notes: service.notes,
      })),
      created_at: property.createdAt,
    }));

    return Response.json(
      createApiResponse(transformedProperties),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch properties');
  }
}

async function createPropertyHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(createPropertySchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const {
      name,
      type,
      parent_property_id,
      address,
      description,
      capacity,
      department,
      project_type,
      start_date,
      expected_end_date,
      hourly_rate_standard,
      location
    } = validation.data;

    // Validate parent property if specified
    if (parent_property_id) {
      const parentProperty = await prisma.property.findUnique({
        where: { id: parent_property_id },
      });

      if (!parentProperty) {
        return Response.json(
          createApiResponse(null, 'Parent property not found', 'PARENT_NOT_FOUND'),
          { status: 404 }
        );
      }

      // Business rule: construction sites should have office parent
      if (type === 'construction_site' && parentProperty.type !== 'OFFICE') {
        return Response.json(
          createApiResponse(null, 'Construction sites must have an office as parent property', 'INVALID_PARENT_TYPE'),
          { status: 400 }
        );
      }
    }

    // Create property with type-specific fields
    const property = await prisma.property.create({
      data: {
        name,
        type: type.toUpperCase(),
        parentPropertyId: parent_property_id,
        address,
        description,
        location,

        // Office-specific fields
        ...(type === 'office' && {
          capacity,
          department,
        }),

        // Site-specific fields
        ...(type === 'construction_site' && {
          projectType: project_type,
          startDate: start_date ? new Date(start_date) : null,
          expectedEndDate: expected_end_date ? new Date(expected_end_date) : null,
          hourlyRateStandard: hourly_rate_standard,
        }),

        isActive: true,
      },
    });

    // Create default services based on property type
    const defaultServices = getDefaultServicesForPropertyType(type);

    if (defaultServices.length > 0) {
      await prisma.propertyService.createMany({
        data: defaultServices.map(serviceType => ({
          propertyId: property.id,
          serviceType: serviceType.toUpperCase(),
          status: 'OPERATIONAL',
          lastChecked: new Date(),
          notes: `Default ${serviceType} service for ${type} property`,
        })),
      });
    }

    // Fetch the created property with services
    const propertyWithServices = await prisma.property.findUnique({
      where: { id: property.id },
      include: {
        services: {
          select: {
            id: true,
            serviceType: true,
            status: true,
            lastChecked: true,
            notes: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Property created successfully with default services',
        property: {
          id: propertyWithServices!.id,
          name: propertyWithServices!.name,
          type: propertyWithServices!.type.toLowerCase(),
          address: propertyWithServices!.address,
          description: propertyWithServices!.description,
          is_active: propertyWithServices!.isActive,
          services: propertyWithServices!.services.map(service => ({
            id: service.id,
            service_type: service.serviceType.toLowerCase(),
            status: service.status.toLowerCase(),
            last_checked: service.lastChecked,
            notes: service.notes,
          })),
          created_at: propertyWithServices!.createdAt,
        },
      }),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create property');
  }
}

export const GET = requireAuth(getPropertiesHandler);
export const POST = requireRole(['admin', 'property_manager'])(createPropertyHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
