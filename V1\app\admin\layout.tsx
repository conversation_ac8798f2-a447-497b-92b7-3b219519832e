import type React from "react"
import { requirePermission } from "@/lib/auth"
import { <PERSON><PERSON><PERSON>, <PERSON>, Shield, Key, <PERSON><PERSON><PERSON>riangle, UserPlus } from "lucide-react"
import Link from "next/link"

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Explicitly check admin permissions
  await requirePermission("/admin")

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <aside className="w-64 bg-white shadow-sm">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900">Admin Panel</h2>
          </div>
          <nav className="mt-6">
            <div className="px-3">
              <div className="space-y-1">
                <Link
                  href="/admin"
                  className="bg-gray-100 text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                >
                  <Settings className="text-gray-500 mr-3 h-5 w-5" />
                  Dashboard
                </Link>
                <Link
                  href="/admin/users"
                  className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                >
                  <Users className="text-gray-400 mr-3 h-5 w-5" />
                  Users
                </Link>
                <Link
                  href="/admin/roles"
                  className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                >
                  <Shield className="text-gray-400 mr-3 h-5 w-5" />
                  Roles
                </Link>
                <Link
                  href="/admin/permissions"
                  className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                >
                  <Key className="text-gray-400 mr-3 h-5 w-5" />
                  Permissions
                </Link>
                <Link
                  href="/admin/thresholds"
                  className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                >
                  <AlertTriangle className="text-gray-400 mr-3 h-5 w-5" />
                  Thresholds
                </Link>
                <Link
                  href="/admin/setup-househelp"
                  className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
                >
                  <UserPlus className="text-gray-400 mr-3 h-5 w-5" />
                  Setup Househelp
                </Link>
              </div>
            </div>
          </nav>
        </aside>
        <main className="flex-1 p-8">{children}</main>
      </div>
    </div>
  )
}
