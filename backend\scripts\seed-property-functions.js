// Import Prisma client from the lib directory
const { prisma } = require('../lib/prisma.ts');

async function getDefaultFunctionsForPropertyType(propertyType) {
  const baseFunctions = [
    { function_name: 'maintenance', is_enabled: true, configuration: { priority_levels: ['low', 'medium', 'high', 'critical'] }, display_order: 1 },
    { function_name: 'security', is_enabled: true, configuration: { monitoring_enabled: true }, display_order: 2 },
    { function_name: 'uptime', is_enabled: true, configuration: { check_interval: 300 }, display_order: 3 },
  ];

  switch (propertyType.toLowerCase()) {
    case 'office':
      return [
        ...baseFunctions,
        { function_name: 'attendance', is_enabled: true, configuration: { 
          working_hours: { start: '09:00', end: '18:00' },
          break_duration: 60,
          overtime_threshold: 8
        }, display_order: 4 },
        { function_name: 'meeting_rooms', is_enabled: true, configuration: { 
          booking_advance_days: 30,
          max_duration_hours: 8
        }, display_order: 5 },
        { function_name: 'visitor_management', is_enabled: true, configuration: { 
          pre_approval_required: true,
          max_visitors_per_day: 50
        }, display_order: 6 },
        { function_name: 'ott', is_enabled: false, configuration: { note: 'Not typically required for office properties' }, display_order: 7 },
        { function_name: 'fuel', is_enabled: false, configuration: { note: 'Not typically required for office properties' }, display_order: 8 },
        { function_name: 'diesel', is_enabled: false, configuration: { note: 'Not typically required for office properties' }, display_order: 9 },
      ];
    case 'construction_site':
      return [
        ...baseFunctions,
        { function_name: 'attendance', is_enabled: true, configuration: { 
          working_hours: { start: '07:00', end: '17:00' },
          break_duration: 60,
          overtime_threshold: 8,
          safety_briefing_required: true
        }, display_order: 4 },
        { function_name: 'equipment', is_enabled: true, configuration: { 
          maintenance_schedule: 'weekly',
          usage_tracking: true
        }, display_order: 5 },
        { function_name: 'safety', is_enabled: true, configuration: { 
          incident_reporting: true,
          safety_checks: 'daily'
        }, display_order: 6 },
        { function_name: 'materials', is_enabled: true, configuration: { 
          inventory_tracking: true,
          reorder_threshold: 20
        }, display_order: 7 },
        { function_name: 'progress', is_enabled: true, configuration: { 
          milestone_tracking: true,
          photo_documentation: true
        }, display_order: 8 },
        { function_name: 'fuel', is_enabled: true, configuration: { 
          generator_monitoring: true,
          fuel_threshold: 25
        }, display_order: 9 },
        { function_name: 'diesel', is_enabled: true, configuration: { 
          consumption_tracking: true
        }, display_order: 10 },
        { function_name: 'ott', is_enabled: false, configuration: { note: 'Not typically required for construction sites' }, display_order: 11 },
      ];
    case 'residential':
    default:
      return [
        ...baseFunctions,
        { function_name: 'ott', is_enabled: true, configuration: { 
          shared_subscriptions: true,
          cost_splitting: true
        }, display_order: 4 },
        { function_name: 'fuel', is_enabled: true, configuration: { 
          generator_monitoring: true,
          fuel_threshold: 30
        }, display_order: 5 },
        { function_name: 'diesel', is_enabled: true, configuration: { 
          consumption_tracking: true
        }, display_order: 6 },
        { function_name: 'attendance', is_enabled: false, configuration: { 
          note: 'Typically not required for residential properties unless staff is employed'
        }, display_order: 7 },
      ];
  }
}

async function seedPropertyFunctions() {
  try {
    console.log('🌱 Starting property functions seeding...');

    // Get all properties
    const properties = await prisma.property.findMany({
      select: {
        id: true,
        name: true,
        type: true,
      }
    });

    // Filter out properties that already have functions configured
    const propertiesWithoutFunctions = [];
    for (const property of properties) {
      const existingFunctions = await prisma.propertyFunction.findMany({
        where: { propertyId: property.id }
      });
      if (existingFunctions.length === 0) {
        propertiesWithoutFunctions.push(property);
      }
    }

    console.log(`📋 Found ${propertiesWithoutFunctions.length} properties without function configuration`);

    let seededCount = 0;

    for (const property of propertiesWithoutFunctions) {
      console.log(`⚙️  Configuring functions for ${property.name} (${property.type})`);
      
      const defaultFunctions = await getDefaultFunctionsForPropertyType(property.type);
      
      // Create property functions
      const createData = defaultFunctions.map(func => ({
        propertyId: property.id,
        functionName: func.function_name,
        isEnabled: func.is_enabled,
        configuration: func.configuration || {},
        displayOrder: func.display_order || 0,
      }));

      await prisma.propertyFunction.createMany({
        data: createData,
        skipDuplicates: true,
      });

      seededCount++;
      console.log(`✅ Configured ${defaultFunctions.length} functions for ${property.name}`);
    }

    console.log(`🎉 Successfully seeded functions for ${seededCount} properties`);

    // Show summary of what was configured
    const summary = await prisma.property.findMany({
      include: {
        propertyFunctions: {
          where: { isEnabled: true },
          select: { functionName: true }
        }
      }
    });

    console.log('\n📊 Property Functions Summary:');
    summary.forEach(property => {
      const enabledFunctions = property.propertyFunctions.map(f => f.functionName).join(', ');
      console.log(`  ${property.name} (${property.type}): ${enabledFunctions || 'No enabled functions'}`);
    });

  } catch (error) {
    console.error('❌ Error seeding property functions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedPropertyFunctions()
    .then(() => {
      console.log('✨ Property functions seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Property functions seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedPropertyFunctions, getDefaultFunctionsForPropertyType };
