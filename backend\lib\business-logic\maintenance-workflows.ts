/**
 * Maintenance issue workflow management
 * Enhanced version of V1's maintenance logic with improvements
 */

import { prisma } from '@/lib/prisma';
import { MaintenanceStatus, Priority } from '@prisma/client';

export interface RecurringIssueConfig {
  isRecurring: boolean;
  recurrencePeriod?: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  nextDueDate?: Date;
}

export interface EscalationConfig {
  level: number;
  daysToEscalate: number;
  escalateTo: string;
  notificationMethod: 'email' | 'sms' | 'both';
}

/**
 * Create a new maintenance issue with enhanced workflow
 */
export async function createMaintenanceIssueWithWorkflow(
  issueData: {
    propertyId: string;
    title: string;
    description: string;
    priority: Priority;
    serviceType?: string;
    department?: string;
    reportedBy: string;
    dueDate?: Date;
    estimatedCost?: number;
  },
  recurringConfig?: RecurringIssueConfig
): Promise<{
  success: boolean;
  issue?: any;
  error?: string;
  warnings?: string[];
}> {
  const warnings: string[] = [];

  try {
    // Create the main issue
    const issue = await prisma.maintenanceIssue.create({
      data: {
        propertyId: issueData.propertyId,
        title: issueData.title,
        description: issueData.description,
        priority: issueData.priority,
        serviceType: issueData.serviceType,
        department: issueData.department,
        reportedBy: issueData.reportedBy,
        dueDate: issueData.dueDate,
        estimatedCost: issueData.estimatedCost,
        status: MaintenanceStatus.OPEN,
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        reporter: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    // Set up escalation if priority is high or critical
    if (issueData.priority === Priority.HIGH || issueData.priority === Priority.CRITICAL) {
      try {
        await setupEscalationWorkflow(issue.id, issueData.priority);
      } catch (escalationError) {
        console.error('Failed to setup escalation:', escalationError);
        warnings.push('Issue created but escalation setup failed');
      }
    }

    // Set up recurring issue if configured
    if (recurringConfig?.isRecurring && recurringConfig.recurrencePeriod) {
      try {
        await setupRecurringIssue(issue.id, recurringConfig);
      } catch (recurringError) {
        console.error('Failed to setup recurring issue:', recurringError);
        warnings.push('Issue created but recurring setup failed');
      }
    }

    return {
      success: true,
      issue,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  } catch (error) {
    console.error('Failed to create maintenance issue:', error);
    return {
      success: false,
      error: 'Failed to create maintenance issue',
    };
  }
}

/**
 * Update issue status with workflow logic
 */
export async function updateIssueStatusWithWorkflow(
  issueId: string,
  newStatus: MaintenanceStatus,
  updatedBy: string,
  resolutionNotes?: string
): Promise<{
  success: boolean;
  issue?: any;
  nextRecurringIssue?: any;
  error?: string;
}> {
  try {
    // Get current issue with full details
    const currentIssue = await prisma.maintenanceIssue.findUnique({
      where: { id: issueId },
      include: {
        property: true,
      },
    });

    if (!currentIssue) {
      return {
        success: false,
        error: 'Maintenance issue not found',
      };
    }

    // Prepare update data
    const updateData: any = {
      status: newStatus,
    };

    if (newStatus === MaintenanceStatus.RESOLVED) {
      updateData.resolvedAt = new Date();
      updateData.resolvedBy = updatedBy;
    }

    // Update the issue
    const updatedIssue = await prisma.maintenanceIssue.update({
      where: { id: issueId },
      data: updateData,
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    let nextRecurringIssue = null;

    // Handle recurring issue creation if status is CLOSED
    if (newStatus === MaintenanceStatus.CLOSED) {
      // Check if this was a recurring issue (we'd need to add this field to schema)
      // For now, we'll check if there's a pattern in the title or description
      const isRecurring = currentIssue.title.toLowerCase().includes('recurring') ||
                         currentIssue.description.toLowerCase().includes('recurring');

      if (isRecurring) {
        try {
          nextRecurringIssue = await createNextRecurringIssue(currentIssue);
        } catch (recurringError) {
          console.error('Failed to create next recurring issue:', recurringError);
          // Don't fail the main operation
        }
      }
    }

    return {
      success: true,
      issue: updatedIssue,
      nextRecurringIssue,
    };
  } catch (error) {
    console.error('Failed to update issue status:', error);
    return {
      success: false,
      error: 'Failed to update issue status',
    };
  }
}

/**
 * Create next recurring issue
 * Enhanced version of V1's createNextRecurringIssue
 */
async function createNextRecurringIssue(currentIssue: any): Promise<any> {
  // Calculate next due date (simplified logic - should be enhanced)
  const nextDueDate = new Date();
  nextDueDate.setMonth(nextDueDate.getMonth() + 1); // Default to monthly

  // Extract recurrence period from description or title
  let recurrencePeriod = 'monthly';
  const description = currentIssue.description.toLowerCase();
  
  if (description.includes('weekly')) recurrencePeriod = 'weekly';
  else if (description.includes('quarterly')) recurrencePeriod = 'quarterly';
  else if (description.includes('yearly')) recurrencePeriod = 'yearly';

  // Adjust next due date based on recurrence period
  switch (recurrencePeriod) {
    case 'weekly':
      nextDueDate.setDate(nextDueDate.getDate() + 7);
      break;
    case 'quarterly':
      nextDueDate.setMonth(nextDueDate.getMonth() + 3);
      break;
    case 'yearly':
      nextDueDate.setFullYear(nextDueDate.getFullYear() + 1);
      break;
    default: // monthly
      nextDueDate.setMonth(nextDueDate.getMonth() + 1);
  }

  // Create new recurring issue
  const newIssue = await prisma.maintenanceIssue.create({
    data: {
      propertyId: currentIssue.propertyId,
      title: currentIssue.title,
      description: `Recurring issue from ${currentIssue.id}. ${currentIssue.description}`,
      priority: currentIssue.priority,
      serviceType: currentIssue.serviceType,
      department: currentIssue.department,
      reportedBy: currentIssue.reportedBy,
      dueDate: nextDueDate,
      status: MaintenanceStatus.OPEN,
    },
    include: {
      property: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  return newIssue;
}

/**
 * Setup escalation workflow
 * Enhanced version of V1's setupEscalation
 */
async function setupEscalationWorkflow(
  issueId: string,
  priority: Priority
): Promise<void> {
  // Define escalation rules based on priority
  const escalationRules: EscalationConfig[] = [];

  switch (priority) {
    case Priority.CRITICAL:
      escalationRules.push(
        { level: 1, daysToEscalate: 1, escalateTo: 'supervisor', notificationMethod: 'both' },
        { level: 2, daysToEscalate: 2, escalateTo: 'manager', notificationMethod: 'both' },
        { level: 3, daysToEscalate: 3, escalateTo: 'director', notificationMethod: 'both' }
      );
      break;
    case Priority.HIGH:
      escalationRules.push(
        { level: 1, daysToEscalate: 2, escalateTo: 'supervisor', notificationMethod: 'email' },
        { level: 2, daysToEscalate: 5, escalateTo: 'manager', notificationMethod: 'both' }
      );
      break;
    case Priority.MEDIUM:
      escalationRules.push(
        { level: 1, daysToEscalate: 7, escalateTo: 'supervisor', notificationMethod: 'email' }
      );
      break;
    default:
      // No escalation for LOW priority
      return;
  }

  // Store escalation rules (we'd need an escalation_rules table)
  // For now, we'll create escalation logs with future dates
  for (const rule of escalationRules) {
    const escalationDate = new Date();
    escalationDate.setDate(escalationDate.getDate() + rule.daysToEscalate);

    await prisma.escalationLog.create({
      data: {
        issueId,
        escalationLevel: rule.level,
        escalationReason: `Automatic escalation after ${rule.daysToEscalate} days for ${priority} priority issue`,
        escalatedAt: escalationDate,
        // escalatedTo would need to be resolved from role to user ID
      },
    });
  }
}

/**
 * Setup recurring issue configuration
 */
async function setupRecurringIssue(
  issueId: string,
  config: RecurringIssueConfig
): Promise<void> {
  // This would require adding a recurring_issues table to track recurrence
  // For now, we'll add a note to the issue description
  await prisma.maintenanceIssue.update({
    where: { id: issueId },
    data: {
      description: (await prisma.maintenanceIssue.findUnique({
        where: { id: issueId },
        select: { description: true }
      }))!.description + `\n\nRecurring: ${config.recurrencePeriod}, Next due: ${config.nextDueDate?.toISOString().split('T')[0]}`
    },
  });
}

/**
 * Check and process escalations
 * Enhanced version of V1's checkEscalations
 */
export async function processEscalations(): Promise<{
  processed: number;
  errors: string[];
}> {
  const errors: string[] = [];
  let processed = 0;

  try {
    // Get all unresolved escalations that are due
    const dueEscalations = await prisma.escalationLog.findMany({
      where: {
        resolvedAt: null,
        escalatedAt: {
          lte: new Date(),
        },
      },
      include: {
        issue: {
          include: {
            property: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    for (const escalation of dueEscalations) {
      try {
        // Process the escalation (send notifications, etc.)
        // This would integrate with notification service
        
        // Mark as processed
        await prisma.escalationLog.update({
          where: { id: escalation.id },
          data: {
            resolvedAt: new Date(),
            resolutionNotes: 'Escalation processed automatically',
          },
        });

        processed++;
      } catch (error) {
        console.error(`Failed to process escalation ${escalation.id}:`, error);
        errors.push(`Failed to process escalation ${escalation.id}`);
      }
    }

    return { processed, errors };
  } catch (error) {
    console.error('Failed to process escalations:', error);
    return { processed, errors: ['Failed to process escalations'] };
  }
}
