import 'package:flutter/material.dart';
import '../../core/business_logic/status_calculator.dart';
import 'property.dart' as property_model;
import 'user.dart';

class MaintenanceIssue {
  final String id;
  final String propertyId;
  final String title;
  final String description;
  final String priority; // low, medium, high, critical
  final String status; // open, in_progress, resolved, closed
  final String? serviceType;
  final String? department;
  final String reportedBy;
  final String? assignedTo;
  final String? approvalStatus;
  final DateTime? dueDate;
  final DateTime createdAt;
  final property_model.Property property;
  final User reporter;
  final User? assignee;

  const MaintenanceIssue({
    required this.id,
    required this.propertyId,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    this.serviceType,
    this.department,
    required this.reportedBy,
    this.assignedTo,
    this.approvalStatus,
    this.dueDate,
    required this.createdAt,
    required this.property,
    required this.reporter,
    this.assignee,
  });

  factory MaintenanceIssue.fromJson(Map<String, dynamic> json) {
    // Log the incoming JSON for debugging
    debugPrint('MaintenanceIssue.fromJson: $json');

    // Handle property field manually to avoid import alias issues in generated code
    final propertyData = json['property'] as Map<String, dynamic>?;
    final property = propertyData != null
        ? property_model.Property.fromJson(propertyData)
        : property_model.Property(id: 'unknown', name: 'Unknown Property', type: 'unknown', isActive: true, createdAt: DateTime.now());

    // Handle reporter field manually
    final reporterData = json['reporter'] as Map<String, dynamic>?;
    final reporter = reporterData != null
        ? User.fromJson(reporterData)
        : User(id: 'unknown', email: '<EMAIL>', fullName: 'Unknown Reporter', roles: const ['viewer'], isActive: true, createdAt: DateTime.now());

    // Handle assignee field manually
    final assigneeData = json['assignee'] as Map<String, dynamic>?;
    final assignee = assigneeData != null ? User.fromJson(assigneeData) : null;

    return MaintenanceIssue(
      id: json['id'] as String? ?? 'unknown',
      title: json['title'] as String? ?? 'Untitled Issue',
      description: json['description'] as String? ?? 'No description provided',
      propertyId: json['property_id'] as String? ?? 'unknown',
      property: property,
      reportedBy: json['reported_by'] as String? ?? 'unknown',
      reporter: reporter,
      assignedTo: json['assigned_to'] as String?,
      assignee: assignee,
      status: json['status'] as String? ?? 'unknown',
      priority: json['priority'] as String? ?? 'low',
      serviceType: json['service_type'] as String?,
      department: json['department'] as String?,
      approvalStatus: json['approval_status'] as String?,
      dueDate: json['due_date'] != null
          ? DateTime.parse(json['due_date'] as String)
          : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
    );
  }
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'id': id,
      'title': title,
      'description': description,
      'property_id': propertyId,
      'property': property.toJson(),
      'reported_by': reportedBy,
      'reporter': reporter.toJson(),
      'assigned_to': assignedTo,
      'assignee': assignee?.toJson(),
      'status': status,
      'priority': priority,
      'service_type': serviceType,
      'department': department,
      'approval_status': approvalStatus,
      'due_date': dueDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
    return json;
  }

  MaintenanceIssue copyWith({
    String? id,
    String? propertyId,
    String? title,
    String? description,
    String? priority,
    String? status,
    String? serviceType,
    String? department,
    String? reportedBy,
    String? assignedTo,
    String? approvalStatus,
    DateTime? dueDate,
    DateTime? createdAt,
    property_model.Property? property,
    User? reporter,
    User? assignee,
  }) {
    return MaintenanceIssue(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      serviceType: serviceType ?? this.serviceType,
      department: department ?? this.department,
      reportedBy: reportedBy ?? this.reportedBy,
      assignedTo: assignedTo ?? this.assignedTo,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      property: property ?? this.property,
      reporter: reporter ?? this.reporter,
      assignee: assignee ?? this.assignee,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MaintenanceIssue && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MaintenanceIssue(id: $id, propertyId: $propertyId, title: $title, description: $description, priority: $priority, status: $status, serviceType: $serviceType, department: $department, reportedBy: $reportedBy, assignedTo: $assignedTo, dueDate: $dueDate, createdAt: $createdAt)';
  }

  // Business logic methods
  bool get isOpen => status.toLowerCase() == 'open';
  bool get isInProgress => status.toLowerCase() == 'in_progress';
  bool get isResolved => status.toLowerCase() == 'resolved';
  bool get isClosed => status.toLowerCase() == 'closed';

  bool get isLowPriority => priority.toLowerCase() == 'low';
  bool get isMediumPriority => priority.toLowerCase() == 'medium';
  bool get isHighPriority => priority.toLowerCase() == 'high';
  bool get isCriticalPriority => priority.toLowerCase() == 'critical';

  StatusLevel get priorityLevel {
    switch (priority.toLowerCase()) {
      case 'low':
        return StatusLevel.green;
      case 'medium':
        return StatusLevel.green;
      case 'high':
        return StatusLevel.orange;
      case 'critical':
        return StatusLevel.red;
      default:
        return StatusLevel.orange;
    }
  }

  Color get priorityColor {
    switch (priorityLevel) {
      case StatusLevel.green:
        return Colors.green;
      case StatusLevel.orange:
        return Colors.orange;
      case StatusLevel.red:
        return Colors.red;
    }
  }

  Color get statusColor {
    switch (status.toLowerCase()) {
      case 'open':
        return Colors.red;
      case 'in_progress':
        return Colors.orange;
      case 'resolved':
        return Colors.blue;
      case 'closed':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData get priorityIcon {
    switch (priority.toLowerCase()) {
      case 'low':
        return Icons.low_priority;
      case 'medium':
        return Icons.remove;
      case 'high':
        return Icons.priority_high;
      case 'critical':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  IconData get statusIcon {
    switch (status.toLowerCase()) {
      case 'open':
        return Icons.new_releases;
      case 'in_progress':
        return Icons.engineering;
      case 'resolved':
        return Icons.check_circle_outline;
      case 'closed':
        return Icons.check_circle;
      default:
        return Icons.help;
    }
  }

  String get displayPriority {
    switch (priority.toLowerCase()) {
      case 'low':
        return 'Low';
      case 'medium':
        return 'Medium';
      case 'high':
        return 'High';
      case 'critical':
        return 'Critical';
      default:
        return priority.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  String get displayStatus {
    switch (status.toLowerCase()) {
      case 'open':
        return 'Open';
      case 'in_progress':
        return 'In Progress';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      default:
        return status.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  String get displayServiceType {
    if (serviceType == null) return 'General';

    switch (serviceType!.toLowerCase()) {
      case 'electricity':
        return 'Electricity';
      case 'water':
        return 'Water Supply';
      case 'internet':
        return 'Internet';
      case 'security':
        return 'Security';
      case 'ott':
        return 'OTT Services';
      case 'generator':
        return 'Generator';
      case 'maintenance':
        return 'General Maintenance';
      default:
        return serviceType!.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!) && !isClosed && !isResolved;
  }

  bool get isDueSoon {
    if (dueDate == null) return false;
    final now = DateTime.now();
    final daysDifference = dueDate!.difference(now).inDays;
    return daysDifference <= 3 && daysDifference >= 0 && !isClosed && !isResolved;
  }

  Duration get age => DateTime.now().difference(createdAt);

  String get ageDescription {
    final days = age.inDays;
    final hours = age.inHours;
    final minutes = age.inMinutes;

    if (days > 0) {
      return '$days days ago';
    } else if (hours > 0) {
      return '$hours hours ago';
    } else if (minutes > 0) {
      return '$minutes minutes ago';
    } else {
      return 'Just now';
    }
  }

  String? get dueDateDescription {
    if (dueDate == null) return null;

    final now = DateTime.now();
    final difference = dueDate!.difference(now);

    if (difference.isNegative) {
      final days = difference.inDays.abs();
      return 'Overdue by $days days';
    } else {
      final days = difference.inDays;
      if (days == 0) {
        return 'Due today';
      } else if (days == 1) {
        return 'Due tomorrow';
      } else {
        return 'Due in $days days';
      }
    }
  }

  bool get isAssigned => assignedTo != null && assignedTo!.isNotEmpty;

  bool get requiresEscalation {
    if (isCriticalPriority && isOpen && age.inHours > 2) return true;
    if (isHighPriority && isOpen && age.inHours > 8) return true;
    if (isMediumPriority && isOpen && age.inDays > 2) return true;
    if (isOverdue) return true;
    return false;
  }

  int get escalationLevel {
    if (!requiresEscalation) return 0;

    if (isCriticalPriority) {
      if (age.inHours > 24) return 3;
      if (age.inHours > 8) return 2;
      return 1;
    }

    if (isHighPriority) {
      if (age.inDays > 3) return 3;
      if (age.inDays > 1) return 2;
      return 1;
    }

    if (isOverdue) {
      final overdueDays = DateTime.now().difference(dueDate!).inDays;
      if (overdueDays > 7) return 3;
      if (overdueDays > 3) return 2;
      return 1;
    }

    return 1;
  }

  // Validation methods
  bool get isValid => title.isNotEmpty && description.isNotEmpty &&
                     priority.isNotEmpty && status.isNotEmpty;

  List<String> get validationErrors {
    final errors = <String>[];
    if (title.isEmpty) errors.add('Title is required');
    if (description.isEmpty) errors.add('Description is required');
    if (priority.isEmpty) errors.add('Priority is required');
    if (status.isEmpty) errors.add('Status is required');
    if (propertyId.isEmpty) errors.add('Property is required');
    if (reportedBy.isEmpty) errors.add('Reporter is required');

    if (title.length < 5) errors.add('Title must be at least 5 characters');
    if (title.length > 200) errors.add('Title cannot exceed 200 characters');
    if (description.length < 10) errors.add('Description must be at least 10 characters');
    if (description.length > 1000) errors.add('Description cannot exceed 1000 characters');

    const validPriorities = ['low', 'medium', 'high', 'critical'];
    if (!validPriorities.contains(priority.toLowerCase())) {
      errors.add('Invalid priority level');
    }

    const validStatuses = ['open', 'in_progress', 'resolved', 'closed'];
    if (!validStatuses.contains(status.toLowerCase())) {
      errors.add('Invalid status');
    }

    if (dueDate != null && dueDate!.isBefore(createdAt)) {
      errors.add('Due date cannot be before creation date');
    }

    return errors;
  }

  // Helper methods for creating issues
  static MaintenanceIssue create({
    required String propertyId,
    required String title,
    required String description,
    required String priority,
    required String reportedBy,
    String? serviceType,
    String? department,
    DateTime? dueDate,
  }) {
    return MaintenanceIssue(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      propertyId: propertyId,
      title: title,
      description: description,
      priority: priority,
      status: 'open',
      serviceType: serviceType,
      department: department,
      reportedBy: reportedBy,
      dueDate: dueDate,
      createdAt: DateTime.now(),
      property: property_model.Property(id: propertyId, name: '', type: 'unknown', isActive: true, createdAt: DateTime.now()), // Placeholder, name should be set appropriately
      reporter: User(id: '', email: '<EMAIL>', fullName: '', roles: const ['viewer'], isActive: true, createdAt: DateTime.now()), // Placeholder, fullName should be set appropriately
    );
  }

  // Status transition methods
  MaintenanceIssue assignTo(String userId) {
    return copyWith(
      assignedTo: userId,
      status: isOpen ? 'in_progress' : status,
    );
  }

  MaintenanceIssue markInProgress() {
    return copyWith(status: 'in_progress');
  }

  MaintenanceIssue markResolved() {
    return copyWith(status: 'resolved');
  }

  MaintenanceIssue markClosed() {
    return copyWith(status: 'closed');
  }

  MaintenanceIssue reopen() {
    return copyWith(status: 'open');
  }

  MaintenanceIssue updatePriority(String newPriority) {
    return copyWith(priority: newPriority);
  }

  MaintenanceIssue updateDueDate(DateTime newDueDate) {
    return copyWith(dueDate: newDueDate);
  }
}
