const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create all roles from database.sql
  const roles = [
    { name: 'admin', description: 'System Administrator with full access', isSystemRole: true },
    { name: 'property_manager', description: 'Property Manager with property management access', isSystemRole: false },
    { name: 'maintenance_staff', description: 'Maintenance Staff with limited access', isSystemRole: false },
    { name: 'security_guard', description: 'Security Guard with security-related access', isSystemRole: false },
    { name: 'househelp', description: 'House Help with basic property access', isSystemRole: false },
    { name: 'office_manager', description: 'Office Manager with office management access', isSystemRole: false },
    { name: 'site_supervisor', description: 'Site Supervisor with site management access', isSystemRole: false },
  ];

  const createdRoles = {};
  for (const roleData of roles) {
    const role = await prisma.role.upsert({
      where: { name: roleData.name },
      update: {},
      create: roleData,
    });
    createdRoles[roleData.name] = role;
  }

  console.log('✅ All roles created');

  // Create all permissions from database.sql
  const permissions = [
    { name: 'view_dashboard', description: 'View main dashboard', resource: 'dashboard', action: 'read' },
    { name: 'manage_users', description: 'Manage system users', resource: 'users', action: 'write' },
    { name: 'manage_roles', description: 'Manage user roles', resource: 'roles', action: 'write' },
    { name: 'manage_permissions', description: 'Manage permissions', resource: 'permissions', action: 'write' },
    { name: 'view_properties', description: 'View properties', resource: 'properties', action: 'read' },
    { name: 'manage_properties', description: 'Manage properties', resource: 'properties', action: 'write' },
    { name: 'view_maintenance', description: 'View maintenance issues', resource: 'maintenance', action: 'read' },
    { name: 'manage_maintenance', description: 'Manage maintenance issues', resource: 'maintenance', action: 'write' },
    { name: 'view_attendance', description: 'View attendance records', resource: 'attendance', action: 'read' },
    { name: 'manage_attendance', description: 'Manage attendance records', resource: 'attendance', action: 'write' },
    { name: 'view_security', description: 'View security logs', resource: 'security', action: 'read' },
    { name: 'manage_security', description: 'Manage security logs', resource: 'security', action: 'write' },
    { name: 'view_reports', description: 'View system reports', resource: 'reports', action: 'read' },
    { name: 'manage_thresholds', description: 'Manage system thresholds', resource: 'thresholds', action: 'write' },
    { name: 'view_function_processes', description: 'View function processes', resource: 'function_processes', action: 'read' },
    { name: 'manage_function_processes', description: 'Manage function processes', resource: 'function_processes', action: 'write' },
  ];

  const createdPermissions = {};
  for (const permData of permissions) {
    const permission = await prisma.permission.upsert({
      where: { name: permData.name },
      update: {},
      create: permData,
    });
    createdPermissions[permData.name] = permission;
  }

  console.log('✅ All permissions created');

  // Create role-permission mappings
  // Admin gets all permissions
  for (const permission of Object.values(createdPermissions)) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.admin.id,
          permissionId: permission.id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.admin.id,
        permissionId: permission.id,
      },
    });
  }

  // Property Manager permissions
  const propertyManagerPerms = ['view_dashboard', 'view_properties', 'manage_properties', 'view_maintenance', 'manage_maintenance', 'view_reports'];
  for (const permName of propertyManagerPerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.property_manager.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.property_manager.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  // Maintenance Staff permissions
  const maintenancePerms = ['view_dashboard', 'view_properties', 'view_maintenance', 'manage_maintenance'];
  for (const permName of maintenancePerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.maintenance_staff.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.maintenance_staff.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  // Security Guard permissions
  const securityPerms = ['view_dashboard', 'view_properties', 'view_security', 'manage_security'];
  for (const permName of securityPerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.security_guard.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.security_guard.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  // Househelp permissions
  const househelpPerms = ['view_dashboard', 'view_properties', 'view_maintenance'];
  for (const permName of househelpPerms) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: createdRoles.househelp.id,
          permissionId: createdPermissions[permName].id,
        },
      },
      update: {},
      create: {
        roleId: createdRoles.househelp.id,
        permissionId: createdPermissions[permName].id,
      },
    });
  }

  console.log('✅ Role-permission mappings created');

  // Create all users from database.sql
  const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123', 12);

  const adminUser = await prisma.user.upsert({
    where: { email: process.env.ADMIN_EMAIL || '<EMAIL>' },
    update: {},
    create: {
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      passwordHash: hashedPassword,
      fullName: process.env.ADMIN_NAME || 'System Administrator',
      phone: '+1234567890',
      isActive: true,
    },
  });

  // Assign roles to admin user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: createdRoles.admin.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: createdRoles.admin.id,
      assignedBy: adminUser.id,
    },
  });

  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: createdRoles.property_manager.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: createdRoles.property_manager.id,
      assignedBy: adminUser.id,
    },
  });

  console.log('✅ Admin user created:', adminUser.email);

  // Create sample properties
  const property1 = await prisma.property.create({
    data: {
      name: 'Sunrise Residential Complex',
      type: 'RESIDENTIAL',
      address: '123 Main Street, Downtown',
      description: 'Modern residential complex with 50 units',
      isActive: true,
    },
  });

  const property2 = await prisma.property.create({
    data: {
      name: 'Corporate Office Tower',
      type: 'OFFICE',
      address: '456 Business Ave, Financial District',
      description: 'Premium office space with modern amenities',
      isActive: true,
    },
  });

  console.log('✅ Sample properties created');

  // Create additional properties (construction sites)
  const property3 = await prisma.property.create({
    data: {
      name: 'Construction Site Alpha',
      type: 'CONSTRUCTION_SITE',
      address: 'North Wing Development Area',
      description: 'Residential construction project with modern amenities',
      projectType: 'Residential Construction',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      expectedEndDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 180 days from now
      capacity: 100,
      isActive: true,
    },
  });

  const property4 = await prisma.property.create({
    data: {
      name: 'Renovation Project Beta',
      type: 'CONSTRUCTION_SITE',
      address: 'South District Commercial Area',
      description: 'Office renovation and modernization project',
      projectType: 'Office Renovation',
      startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
      expectedEndDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      capacity: 50,
      isActive: true,
    },
  });

  console.log('✅ Additional properties created');

  // Create property services
  const services = [
    { propertyId: property1.id, serviceType: 'ELECTRICITY', status: 'OPERATIONAL' },
    { propertyId: property1.id, serviceType: 'WATER', status: 'OPERATIONAL' },
    { propertyId: property1.id, serviceType: 'INTERNET', status: 'WARNING' },
    { propertyId: property1.id, serviceType: 'SECURITY', status: 'OPERATIONAL' },
    { propertyId: property2.id, serviceType: 'ELECTRICITY', status: 'OPERATIONAL' },
    { propertyId: property2.id, serviceType: 'WATER', status: 'OPERATIONAL' },
    { propertyId: property2.id, serviceType: 'INTERNET', status: 'OPERATIONAL' },
    { propertyId: property2.id, serviceType: 'SECURITY', status: 'CRITICAL' },
  ];

  for (const service of services) {
    await prisma.propertyService.create({
      data: {
        ...service,
        lastChecked: new Date(),
      },
    });
  }

  console.log('✅ Property services created');

  // Create sample maintenance issues
  await prisma.maintenanceIssue.create({
    data: {
      propertyId: property1.id,
      title: 'Elevator maintenance required',
      description: 'Elevator making unusual noises and needs inspection',
      priority: 'HIGH',
      status: 'OPEN',
      serviceType: 'Mechanical',
      department: 'Maintenance',
      reportedBy: adminUser.id,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    },
  });

  await prisma.maintenanceIssue.create({
    data: {
      propertyId: property2.id,
      title: 'HVAC system not cooling properly',
      description: 'Air conditioning system on 5th floor not maintaining temperature',
      priority: 'MEDIUM',
      status: 'IN_PROGRESS',
      serviceType: 'HVAC',
      department: 'Facilities',
      reportedBy: adminUser.id,
      assignedTo: adminUser.id,
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
    },
  });

  console.log('✅ Sample maintenance issues created');

  // Create property members for the properties
  await prisma.propertyMember.create({
    data: {
      propertyId: property1.id,
      userId: adminUser.id,
      role: 'Manager',
      position: 'Property Manager',
      department: 'Administration',
      isActive: true,
    },
  });

  await prisma.propertyMember.create({
    data: {
      propertyId: property3.id,
      userId: adminUser.id,
      role: 'Site Supervisor',
      position: 'Construction Supervisor',
      department: 'Construction',
      isActive: true,
    },
  });

  console.log('✅ Property members created');

  // Create sample generator fuel logs
  await prisma.generatorFuelLog.create({
    data: {
      propertyId: property1.id,
      fuelLevelLiters: 850.5,
      consumptionRate: 12.5,
      runtimeHours: 24.0,
      efficiencyPercentage: 85.2,
      lastMaintenance: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      nextMaintenance: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
      notes: 'Generator running smoothly, fuel consumption within normal range',
    },
  });

  console.log('✅ Sample generator fuel logs created');

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
