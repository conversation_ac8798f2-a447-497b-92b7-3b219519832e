import type React from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { getCurrentUser, logout } from "@/lib/auth"
import { redirect } from "next/navigation"
import { LogOut, User, Settings, Home, Building2, Shield } from "lucide-react"

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const user = await getCurrentUser()

  if (!user) {
    redirect("/login")
  }

  const isAdmin = user.role === "admin" || user.username === "admin1"

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="border-b bg-white">
        <div className="container mx-auto flex items-center justify-between p-4">
          <h1 className="text-xl font-bold">SRSR Property Management</h1>
          <div className="flex items-center space-x-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>{user.full_name || user.username}</span>
            </div>
            {isAdmin && (
              <Link href="/admin">
                <Button variant="outline" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  Admin
                </Button>
              </Link>
            )}
            <form action={logout}>
              <Button variant="outline" size="sm" type="submit">
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </form>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-4">
        <div className="mb-6 flex items-center justify-between">
          <nav className="flex space-x-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <Home className="mr-2 h-4 w-4" />
                Dashboard
              </Button>
            </Link>
            <Link href="/dashboard/home">
              <Button variant="ghost" size="sm">
                <Home className="mr-2 h-4 w-4" />
                Residential
              </Button>
            </Link>
            <Link href="/dashboard/office">
              <Button variant="ghost" size="sm">
                <Building2 className="mr-2 h-4 w-4" />
                Office
              </Button>
            </Link>
            {/* Always show Security link for admin users */}
            {(isAdmin || user.role === "security_manager") && (
              <Link href="/dashboard/security">
                <Button variant="ghost" size="sm">
                  <Shield className="mr-2 h-4 w-4" />
                  Security
                </Button>
              </Link>
            )}
          </nav>
        </div>
        {children}
      </div>
    </div>
  )
}
