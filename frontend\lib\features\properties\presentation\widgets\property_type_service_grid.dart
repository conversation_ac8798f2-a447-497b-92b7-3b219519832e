import 'package:flutter/material.dart';
import '../../../../shared/models/property.dart';
import '../../../../shared/models/property_service.dart';

class PropertyTypeServiceGrid extends StatelessWidget {
  final Property property;
  final Function(PropertyService)? onServiceTap;
  final bool showOnlyAvailable;

  const PropertyTypeServiceGrid({
    super.key,
    required this.property,
    this.onServiceTap,
    this.showOnlyAvailable = false,
  });

  @override
  Widget build(BuildContext context) {
    final services = _getServicesToShow();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: services.length,
      itemBuilder: (context, index) {
        final serviceInfo = services[index];
        return _buildServiceCard(context, serviceInfo);
      },
    );
  }

  List<Map<String, dynamic>> _getServicesToShow() {
    final defaultServices = property.defaultServices;
    final currentServices = property.services ?? [];
    final currentServiceMap = {
      for (var service in currentServices)
        service.serviceType.toLowerCase(): service
    };

    List<Map<String, dynamic>> services = [];

    // Add all default services for this property type
    for (final serviceType in defaultServices) {
      final existingService = currentServiceMap[serviceType];
      final priority = property.getServicePriority(serviceType);

      services.add({
        'type': serviceType,
        'service': existingService,
        'priority': priority,
        'isDefault': true,
        'isAvailable': property.hasService(serviceType),
      });
    }

    // Add any additional services that exist but aren't default
    for (final service in currentServices) {
      final serviceType = service.serviceType.toLowerCase();
      if (!defaultServices.contains(serviceType)) {
        services.add({
          'type': serviceType,
          'service': service,
          'priority': 999,
          'isDefault': false,
          'isAvailable': true,
        });
      }
    }

    // Sort by priority
    services.sort((a, b) => a['priority'].compareTo(b['priority']));

    // Filter if needed
    if (showOnlyAvailable) {
      services = services.where((s) => s['isAvailable'] == true).toList();
    }

    return services;
  }

  Widget _buildServiceCard(BuildContext context, Map<String, dynamic> serviceInfo) {
    final String serviceType = serviceInfo['type'];
    final PropertyService? service = serviceInfo['service'];
    final bool isDefault = serviceInfo['isDefault'];

    final hasService = service != null;
    final status = service?.status.toLowerCase() ?? 'not_configured';

    Color cardColor;
    Color textColor;
    IconData statusIcon;

    if (!hasService) {
      cardColor = Colors.grey.shade100;
      textColor = Colors.grey.shade600;
      statusIcon = Icons.add_circle_outline;
    } else {
      switch (status) {
        case 'operational':
          cardColor = Colors.green.shade50;
          textColor = Colors.green.shade700;
          statusIcon = Icons.check_circle;
          break;
        case 'warning':
          cardColor = Colors.orange.shade50;
          textColor = Colors.orange.shade700;
          statusIcon = Icons.warning;
          break;
        case 'critical':
          cardColor = Colors.red.shade50;
          textColor = Colors.red.shade700;
          statusIcon = Icons.error;
          break;
        case 'maintenance':
          cardColor = Colors.blue.shade50;
          textColor = Colors.blue.shade700;
          statusIcon = Icons.build;
          break;
        default:
          cardColor = Colors.grey.shade100;
          textColor = Colors.grey.shade600;
          statusIcon = Icons.help_outline;
      }
    }

    return Card(
      elevation: hasService ? 3 : 1,
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: hasService ? textColor.withValues(alpha: 0.3) : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: hasService && onServiceTap != null ? () => onServiceTap!(service) : null,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(
                    _getServiceIcon(serviceType),
                    color: textColor,
                    size: 24,
                  ),
                  if (isDefault)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: property.primaryColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'DEFAULT',
                        style: TextStyle(
                          color: property.primaryColor,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                _formatServiceName(serviceType),
                style: TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    statusIcon,
                    color: textColor,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatStatus(status),
                    style: TextStyle(
                      color: textColor,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              if (service?.lastChecked != null) ...[
                const SizedBox(height: 4),
                Text(
                  'Last: ${_formatLastChecked(service!.lastChecked!)}',
                  style: TextStyle(
                    color: textColor.withValues(alpha: 0.7),
                    fontSize: 8,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getServiceIcon(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'water':
        return Icons.water_drop;
      case 'internet':
        return Icons.wifi;
      case 'security':
        return Icons.security;
      case 'ott':
        return Icons.tv;
      case 'generator':
        return Icons.power;
      case 'maintenance':
        return Icons.handyman;
      case 'hvac':
        return Icons.ac_unit;
      case 'fire_safety':
        return Icons.local_fire_department;
      case 'access_control':
        return Icons.key;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String _formatServiceName(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'ott':
        return 'OTT Services';
      case 'hvac':
        return 'HVAC';
      case 'fire_safety':
        return 'Fire Safety';
      case 'access_control':
        return 'Access Control';
      default:
        return serviceType
            .split('_')
            .map((word) => word[0].toUpperCase() + word.substring(1))
            .join(' ');
    }
  }

  String _formatStatus(String status) {
    switch (status.toLowerCase()) {
      case 'operational':
        return 'Operational';
      case 'warning':
        return 'Warning';
      case 'critical':
        return 'Critical';
      case 'maintenance':
        return 'Maintenance';
      case 'not_configured':
        return 'Not Setup';
      default:
        return 'Unknown';
    }
  }

  String _formatLastChecked(DateTime lastChecked) {
    final now = DateTime.now();
    final difference = now.difference(lastChecked);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
