import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Cameras tab - Live feeds, recording, and camera controls
class CamerasTab extends StatefulWidget {
  const CamerasTab({super.key});

  @override
  State<CamerasTab> createState() => _CamerasTabState();
}

class _CamerasTabState extends State<CamerasTab> {
  String _selectedView = 'grid';
  bool _isRecording = false;

  final List<CameraDevice> _cameras = [
    CameraDevice(
      id: 'cam_001',
      name: 'Main Entrance',
      location: 'Building A - Front Gate',
      status: CameraStatus.online,
      isRecording: true,
      hasMotionDetection: true,
      hasNightVision: true,
      resolution: '4K',
      lastMotion: DateTime.now().subtract(const Duration(minutes: 5)),
    ),
    CameraDevice(
      id: 'cam_002',
      name: 'Parking Area',
      location: 'Building A - Parking Lot',
      status: CameraStatus.online,
      isRecording: false,
      hasMotionDetection: true,
      hasNightVision: true,
      resolution: '1080p',
      lastMotion: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    CameraDevice(
      id: 'cam_003',
      name: 'Generator Room',
      location: 'Building B - Basement',
      status: CameraStatus.offline,
      isRecording: false,
      hasMotionDetection: false,
      hasNightVision: true,
      resolution: '720p',
      lastMotion: DateTime.now().subtract(const Duration(days: 1)),
    ),
    CameraDevice(
      id: 'cam_004',
      name: 'Rooftop',
      location: 'Building A - Roof Access',
      status: CameraStatus.online,
      isRecording: true,
      hasMotionDetection: true,
      hasNightVision: false,
      resolution: '4K',
      lastMotion: DateTime.now().subtract(const Duration(minutes: 15)),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildControlBar(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _selectedView == 'grid'
                ? _buildGridView(context)
                : _buildListView(context),
          ),
        ],
      ),
    );
  }

  Widget _buildControlBar(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              _buildViewToggle(),
              const SizedBox(width: AppConstants.smallPadding),
              _buildRecordingToggle(),
            ],
          ),
        ),
        _buildQuickActions(),
      ],
    );
  }

  Widget _buildViewToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleButton(
            'Grid',
            Icons.grid_view,
            _selectedView == 'grid',
            () => setState(() => _selectedView = 'grid'),
          ),
          _buildToggleButton(
            'List',
            Icons.list,
            _selectedView == 'list',
            () => setState(() => _selectedView = 'list'),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(
    String label,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.purple : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingToggle() {
    return GestureDetector(
      onTap: () => setState(() => _isRecording = !_isRecording),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _isRecording ? Colors.red.shade100 : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _isRecording ? Colors.red.shade300 : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: _isRecording ? Colors.red : Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              _isRecording ? 'Recording All' : 'Record All',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: _isRecording ? Colors.red.shade700 : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.fullscreen),
          onPressed: _enterFullscreenMode,
          tooltip: 'Fullscreen View',
        ),
        IconButton(
          icon: const Icon(Icons.picture_in_picture),
          onPressed: _enablePictureInPicture,
          tooltip: 'Picture in Picture',
        ),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: _showCameraSettings,
          tooltip: 'Camera Settings',
        ),
      ],
    );
  }

  Widget _buildGridView(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConstants.smallPadding,
        mainAxisSpacing: AppConstants.smallPadding,
        childAspectRatio: 1.2,
      ),
      itemCount: _cameras.length,
      itemBuilder: (context, index) {
        return _buildCameraCard(_cameras[index]);
      },
    );
  }

  Widget _buildListView(BuildContext context) {
    return ListView.separated(
      itemCount: _cameras.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConstants.smallPadding),
      itemBuilder: (context, index) {
        return _buildCameraListItem(_cameras[index]);
      },
    );
  }

  Widget _buildCameraCard(CameraDevice camera) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: InkWell(
        onTap: () => _openCameraDetails(camera),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Column(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppConstants.borderRadius),
                    topRight: Radius.circular(AppConstants.borderRadius),
                  ),
                ),
                child: Stack(
                  children: [
                    // Live feed placeholder
                    Center(
                      child: camera.status == CameraStatus.online
                          ? _buildLiveFeedPlaceholder()
                          : _buildOfflineIndicator(),
                    ),
                    // Status indicators
                    Positioned(
                      top: 8,
                      left: 8,
                      child: _buildStatusIndicator(camera.status),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (camera.isRecording)
                            _buildRecordingIndicator(),
                          if (camera.hasMotionDetection)
                            const SizedBox(width: 4),
                          if (camera.hasMotionDetection)
                            _buildMotionIndicator(),
                        ],
                      ),
                    ),
                    // Controls overlay
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: _buildCameraControls(camera),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppConstants.smallPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    camera.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    camera.location,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraListItem(CameraDevice camera) {
    return Card(
      child: ListTile(
        leading: Container(
          width: 60,
          height: 45,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(4),
          ),
          child: camera.status == CameraStatus.online
              ? _buildLiveFeedPlaceholder()
              : _buildOfflineIndicator(),
        ),
        title: Text(camera.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(camera.location),
            const SizedBox(height: 2),
            Row(
              children: [
                _buildStatusIndicator(camera.status),
                const SizedBox(width: 8),
                Text(
                  camera.resolution,
                  style: const TextStyle(fontSize: 12),
                ),
                if (camera.isRecording) ...[
                  const SizedBox(width: 8),
                  _buildRecordingIndicator(),
                ],
              ],
            ),
          ],
        ),
        trailing: _buildCameraControls(camera),
        onTap: () => _openCameraDetails(camera),
      ),
    );
  }

  Widget _buildLiveFeedPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey.shade800,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.videocam,
              color: Colors.white54,
              size: 32,
            ),
            SizedBox(height: 4),
            Text(
              'LIVE',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey.shade600,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.videocam_off,
              color: Colors.white54,
              size: 32,
            ),
            SizedBox(height: 4),
            Text(
              'OFFLINE',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(CameraStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case CameraStatus.online:
        color = Colors.green;
        text = 'ONLINE';
        break;
      case CameraStatus.offline:
        color = Colors.red;
        text = 'OFFLINE';
        break;
      case CameraStatus.maintenance:
        color = Colors.orange;
        text = 'MAINTENANCE';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRecordingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.fiber_manual_record, color: Colors.white, size: 8),
          SizedBox(width: 2),
          Text(
            'REC',
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotionIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.motion_photos_on, color: Colors.white, size: 8),
          SizedBox(width: 2),
          Text(
            'MD',
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCameraControls(CameraDevice camera) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(
            camera.isRecording ? Icons.stop : Icons.fiber_manual_record,
            color: camera.isRecording ? Colors.red : Colors.white,
            size: 16,
          ),
          onPressed: () => _toggleRecording(camera),
          tooltip: camera.isRecording ? 'Stop Recording' : 'Start Recording',
        ),
        IconButton(
          icon: const Icon(Icons.fullscreen, color: Colors.white, size: 16),
          onPressed: () => _openFullscreen(camera),
          tooltip: 'Fullscreen',
        ),
      ],
    );
  }

  void _enterFullscreenMode() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Entering fullscreen mode...')),
    );
  }

  void _enablePictureInPicture() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Picture-in-picture mode enabled')),
    );
  }

  void _showCameraSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Camera Settings'),
        content: const Text('Camera configuration options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _openCameraDetails(CameraDevice camera) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(camera.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Location: ${camera.location}'),
            Text('Status: ${camera.status.name}'),
            Text('Resolution: ${camera.resolution}'),
            Text('Recording: ${camera.isRecording ? 'Yes' : 'No'}'),
            Text('Motion Detection: ${camera.hasMotionDetection ? 'Enabled' : 'Disabled'}'),
            Text('Night Vision: ${camera.hasNightVision ? 'Available' : 'Not Available'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _toggleRecording(CameraDevice camera) {
    setState(() {
      camera.isRecording = !camera.isRecording;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${camera.name} ${camera.isRecording ? 'started' : 'stopped'} recording',
        ),
      ),
    );
  }

  void _openFullscreen(CameraDevice camera) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening ${camera.name} in fullscreen...')),
    );
  }
}

/// Camera device data model
class CameraDevice {
  final String id;
  final String name;
  final String location;
  final CameraStatus status;
  bool isRecording;
  final bool hasMotionDetection;
  final bool hasNightVision;
  final String resolution;
  final DateTime lastMotion;

  CameraDevice({
    required this.id,
    required this.name,
    required this.location,
    required this.status,
    required this.isRecording,
    required this.hasMotionDetection,
    required this.hasNightVision,
    required this.resolution,
    required this.lastMotion,
  });
}

/// Camera status enum
enum CameraStatus {
  online,
  offline,
  maintenance,
}
