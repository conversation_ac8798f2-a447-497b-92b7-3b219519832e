import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../core/storage/storage_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../data/dashboard_api_service.dart';
import '../../domain/dashboard_models.dart';

// Debug API Service Provider with detailed logging
final debugDashboardApiServiceProvider = Provider<DashboardApiService>((ref) {
  return DashboardApiService(DioClient.instance.dio);
});

// Debug Dashboard Provider with detailed error handling
final debugDashboardDataProvider = FutureProvider<DashboardStatus>((ref) async {
  print('🔍 DEBUG: Starting dashboard data fetch...');

  try {
    final apiService = ref.read(debugDashboardApiServiceProvider);
    print('🔍 DEBUG: API service created successfully');

    print('🔍 DEBUG: Making API call to dashboard/status...');
    final result = await apiService.getDashboardStatus();
    print('🔍 DEBUG: API call successful!');
    print('🔍 DEBUG: Result success: ${result.success}');
    print('🔍 DEBUG: Result data: ${result.data}');
    print('🔍 DEBUG: Properties total: ${result.data.properties.total}');
    print('🔍 DEBUG: Alerts count: ${result.data.recentAlerts.length}');

    return result;
  } catch (e, stackTrace) {
    print('🔍 DEBUG: Error occurred!');
    print('🔍 DEBUG: Error type: ${e.runtimeType}');
    print('🔍 DEBUG: Error message: $e');

    if (e is DioException) {
      print('🔍 DEBUG: DioException details:');
      print('🔍 DEBUG: - Type: ${e.type}');
      print('🔍 DEBUG: - Message: ${e.message}');
      print('🔍 DEBUG: - Response status: ${e.response?.statusCode}');
      print('🔍 DEBUG: - Response data: ${e.response?.data}');
      print('🔍 DEBUG: - Request URL: ${e.requestOptions.uri}');
      print('🔍 DEBUG: - Request headers: ${e.requestOptions.headers}');
    }

    print('🔍 DEBUG: Stack trace: $stackTrace');
    rethrow;
  }
});

// Raw API test provider - bypasses models to test raw response
final debugRawApiProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  print('🔍 RAW DEBUG: Starting raw API test...');

  try {
    final dio = DioClient.instance.dio;
    print('🔍 RAW DEBUG: Dio client created');

    final response = await dio.get('/api/dashboard/status');
    print('🔍 RAW DEBUG: Raw response received!');
    print('🔍 RAW DEBUG: Status code: ${response.statusCode}');
    print('🔍 RAW DEBUG: Response data type: ${response.data.runtimeType}');
    print('🔍 RAW DEBUG: Response data: ${response.data}');

    if (response.data is Map<String, dynamic>) {
      final data = response.data as Map<String, dynamic>;
      print('🔍 RAW DEBUG: Parsed as Map successfully');
      print('🔍 RAW DEBUG: Keys: ${data.keys.toList()}');

      if (data.containsKey('success')) {
        print('🔍 RAW DEBUG: Success field: ${data['success']}');
      }

      if (data.containsKey('data')) {
        print('🔍 RAW DEBUG: Data field type: ${data['data'].runtimeType}');
        if (data['data'] is Map) {
          final dataMap = data['data'] as Map;
          print('🔍 RAW DEBUG: Data keys: ${dataMap.keys.toList()}');
        }
      }

      return data;
    } else {
      print('🔍 RAW DEBUG: Response is not a Map!');
      return {'error': 'Response is not a Map', 'actual_type': response.data.runtimeType.toString()};
    }
  } catch (e, stackTrace) {
    print('🔍 RAW DEBUG: Raw API error!');
    print('🔍 RAW DEBUG: Error: $e');
    print('🔍 RAW DEBUG: Stack trace: $stackTrace');
    rethrow;
  }
});

// Model parsing test provider - tests if the issue is in JSON parsing
final debugModelParsingProvider = FutureProvider<String>((ref) async {
  print('🔍 MODEL DEBUG: Testing model parsing...');

  try {
    // Get raw data first
    final rawData = await ref.read(debugRawApiProvider.future);
    print('🔍 MODEL DEBUG: Raw data obtained');

    // Try to parse it
    final dashboardStatus = DashboardStatus.fromJson(rawData);
    print('🔍 MODEL DEBUG: Model parsing successful!');
    print('🔍 MODEL DEBUG: Parsed success: ${dashboardStatus.success}');
    print('🔍 MODEL DEBUG: Parsed properties total: ${dashboardStatus.data.properties.total}');

    return 'Model parsing successful';
  } catch (e, stackTrace) {
    print('🔍 MODEL DEBUG: Model parsing failed!');
    print('🔍 MODEL DEBUG: Error: $e');
    print('🔍 MODEL DEBUG: Stack trace: $stackTrace');
    return 'Model parsing failed: $e';
  }
});

// Network connectivity test
final debugNetworkTestProvider = FutureProvider<String>((ref) async {
  print('🔍 NETWORK DEBUG: Testing network connectivity...');

  try {
    final dio = DioClient.instance.dio;

    // Test the actual dashboard endpoint instead of health
    final response = await dio.get('/api/dashboard/status');
    print('🔍 NETWORK DEBUG: Dashboard endpoint response: ${response.statusCode}');

    return 'Network connectivity OK - Dashboard endpoint reachable';
  } catch (e) {
    print('🔍 NETWORK DEBUG: Dashboard endpoint test failed: $e');

    // Try root endpoint as fallback
    try {
      final dio = DioClient.instance.dio;
      final response = await dio.get('/');
      print('🔍 NETWORK DEBUG: Root endpoint response: ${response.statusCode}');
      return 'Network OK (via root endpoint) - Dashboard endpoint failed: $e';
    } catch (e2) {
      print('🔍 NETWORK DEBUG: All network tests failed: $e2');
      return 'Network connectivity failed: $e2';
    }
  }
});

// Authentication check provider
final debugAuthCheckProvider = FutureProvider<String>((ref) async {
  print('🔍 AUTH DEBUG: Checking authentication...');

  try {
    final token = StorageService.getToken();
    print('🔍 AUTH DEBUG: Token exists: ${token != null}');

    if (token == null) {
      return 'No token found - User not logged in';
    }

    print('🔍 AUTH DEBUG: Token length: ${token.length}');
    print('🔍 AUTH DEBUG: Token preview: ${token.substring(0, token.length > 20 ? 20 : token.length)}...');

    // Test an authenticated endpoint
    final dio = DioClient.instance.dio;
    final response = await dio.get('/api/auth/me');
    print('🔍 AUTH DEBUG: Auth check response: ${response.statusCode}');
    print('🔍 AUTH DEBUG: User data: ${response.data}');

    return 'Authentication OK - User data retrieved';
  } catch (e) {
    print('🔍 AUTH DEBUG: Authentication check failed: $e');

    if (e is DioException) {
      if (e.response?.statusCode == 401) {
        final token = StorageService.getToken();
        return 'Authentication failed - 401 Unauthorized (Token: ${token != null ? "exists but invalid" : "missing"})';
      } else if (e.response?.statusCode == 403) {
        return 'Authentication failed - 403 Forbidden';
      } else {
        return 'Authentication check failed - ${e.response?.statusCode}: $e';
      }
    }

    return 'Authentication check failed: $e';
  }
});

// Permission check provider
final debugPermissionCheckProvider = FutureProvider<String>((ref) async {
  print('🔍 PERMISSION DEBUG: Checking user permissions...');

  try {
    final authState = ref.read(authStateProvider);
    print('🔍 PERMISSION DEBUG: Auth state - isAuthenticated: ${authState.isAuthenticated}');

    if (!authState.isAuthenticated || authState.user == null) {
      return 'Not authenticated - no user data';
    }

    final user = authState.user!;
    print('🔍 PERMISSION DEBUG: User ID: ${user.id}');
    print('🔍 PERMISSION DEBUG: User roles: ${user.roles}');
    print('🔍 PERMISSION DEBUG: User permissions: ${user.permissions}');

    // Check specific dashboard permissions
    final hasPropertiesRead = user.hasPermission('properties.read') || user.hasPermission('view_properties');
    final hasDashboardRead = user.hasPermission('dashboard.read') || user.hasPermission('view_dashboard');
    final hasAlertsRead = user.hasPermission('alerts.read') || user.hasPermission('view_alerts');

    print('🔍 PERMISSION DEBUG: Has properties.read/view_properties: $hasPropertiesRead');
    print('🔍 PERMISSION DEBUG: Has dashboard.read/view_dashboard: $hasDashboardRead');
    print('🔍 PERMISSION DEBUG: Has alerts.read/view_alerts: $hasAlertsRead');

    final permissionSummary = [
      'Properties: $hasPropertiesRead',
      'Dashboard: $hasDashboardRead',
      'Alerts: $hasAlertsRead',
      'Roles: ${user.roles.join(", ")}',
      'Permissions: ${user.permissions.length} total'
    ].join(' | ');

    return permissionSummary;
  } catch (e) {
    print('🔍 PERMISSION DEBUG: Permission check failed: $e');
    return 'Permission check failed: $e';
  }
});

// Combined debug info provider
final debugInfoProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  print('🔍 COMBINED DEBUG: Running all debug tests...');

  final results = <String, dynamic>{};

  // Test authentication
  try {
    results['authentication'] = await ref.read(debugAuthCheckProvider.future);
  } catch (e) {
    results['authentication'] = 'Failed: $e';
  }

  // Test permissions
  try {
    results['permissions'] = await ref.read(debugPermissionCheckProvider.future);
  } catch (e) {
    results['permissions'] = 'Failed: $e';
  }

  // Test network
  try {
    results['network'] = await ref.read(debugNetworkTestProvider.future);
  } catch (e) {
    results['network'] = 'Failed: $e';
  }

  // Test raw API
  try {
    results['raw_api'] = await ref.read(debugRawApiProvider.future);
  } catch (e) {
    results['raw_api'] = 'Failed: $e';
  }

  // Test model parsing
  try {
    results['model_parsing'] = await ref.read(debugModelParsingProvider.future);
  } catch (e) {
    results['model_parsing'] = 'Failed: $e';
  }

  // Test full dashboard
  try {
    final dashboard = await ref.read(debugDashboardDataProvider.future);
    results['full_dashboard'] = 'Success: ${dashboard.data.properties.total} properties';
  } catch (e) {
    results['full_dashboard'] = 'Failed: $e';
  }

  print('🔍 COMBINED DEBUG: All tests completed');
  print('🔍 COMBINED DEBUG: Results: $results');

  return results;
});
