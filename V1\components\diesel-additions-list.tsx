"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Droplet, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import type { DieselAddition } from "@/app/actions/diesel-additions"
import { deleteDieselAddition } from "@/app/actions/diesel-additions"

interface DieselAdditionsListProps {
  additions: DieselAddition[]
  onRefresh: () => void
}

export function DieselAdditionsList({ additions, onRefresh }: DieselAdditionsListProps) {
  const { toast } = useToast()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const confirmDelete = (id: number | undefined) => {
    if (!id) return
    setDeleteId(id)
    setDeleteDialogOpen(true)
  }

  const handleDelete = async () => {
    if (!deleteId) return

    setIsDeleting(true)

    try {
      const result = await deleteDieselAddition(deleteId)

      if (!result.success) {
        throw new Error(result.error || "Failed to delete diesel addition")
      }

      // Success - close dialog
      setDeleteDialogOpen(false)
      setDeleteId(null)

      // Show success toast
      toast({
        title: "Success",
        description: "Diesel addition entry has been deleted.",
      })

      // Refresh the data
      onRefresh()
    } catch (err) {
      console.error("Error deleting diesel addition:", err)

      // Show error toast
      toast({
        variant: "destructive",
        title: "Error",
        description: err instanceof Error ? err.message : "An unknown error occurred",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Droplet className="h-5 w-5" />
            Diesel Addition Updates
          </CardTitle>
          <CardDescription>Recent diesel additions to the generator</CardDescription>
        </CardHeader>
        <CardContent>
          {!additions || additions.length === 0 ? (
            <div className="rounded-md bg-slate-50 p-4 text-center">
              <p className="text-slate-500">No diesel additions found</p>
            </div>
          ) : (
            <div className="rounded-md border overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Diesel Added (in Ltrs)</TableHead>
                    <TableHead>Added By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {additions.map((addition) => {
                    if (!addition) return null

                    return (
                      <TableRow key={addition.id || Math.random().toString()}>
                        <TableCell>{formatDate(addition.date || "")}</TableCell>
                        <TableCell>{(addition.diesel_added || 0).toFixed(1)} L</TableCell>
                        <TableCell>{addition.added_by}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => confirmDelete(addition.id)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            aria-label="Delete diesel addition"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this diesel addition entry? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
