import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/attendance.dart';
import '../providers/attendance_providers.dart';

class AddAttendanceDialog extends ConsumerStatefulWidget {
  final DateTime selectedDate;

  const AddAttendanceDialog({
    super.key,
    required this.selectedDate,
  });

  @override
  ConsumerState<AddAttendanceDialog> createState() => _AddAttendanceDialogState();
}

class _AddAttendanceDialogState extends ConsumerState<AddAttendanceDialog> {
  late FormGroup form;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    form = FormGroup({
      'workerName': FormControl<String>(validators: [Validators.required]),
      'workerRole': FormControl<String>(),
      'attendanceType': FormControl<String>(
        value: 'site',
        validators: [Validators.required],
      ),
      'status': FormControl<String>(
        value: 'present',
        validators: [Validators.required],
      ),
      'checkInTime': FormControl<DateTime>(
        value: DateTime.now(),
      ),
      'checkOutTime': FormControl<DateTime>(),
      'notes': FormControl<String>(),
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.add, color: Colors.blue),
          const SizedBox(width: 8),
          const Text('Add Attendance Record'),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: ReactiveForm(
          formGroup: form,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Worker Name
                ReactiveTextField<String>(
                  formControlName: 'workerName',
                  decoration: const InputDecoration(
                    labelText: 'Worker Name *',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Worker name is required',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Worker Role
                ReactiveTextField<String>(
                  formControlName: 'workerRole',
                  decoration: const InputDecoration(
                    labelText: 'Worker Role',
                    prefixIcon: Icon(Icons.work),
                    border: OutlineInputBorder(),
                    hintText: 'e.g., Security Guard, Maintenance',
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Attendance Type
                ReactiveDropdownField<String>(
                  formControlName: 'attendanceType',
                  decoration: const InputDecoration(
                    labelText: 'Attendance Type *',
                    prefixIcon: Icon(Icons.location_on),
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'site', child: Text('Site Attendance')),
                    DropdownMenuItem(value: 'office', child: Text('Office Attendance')),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Status
                ReactiveDropdownField<String>(
                  formControlName: 'status',
                  decoration: const InputDecoration(
                    labelText: 'Status *',
                    prefixIcon: Icon(Icons.check_circle),
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'present', child: Text('Present')),
                    DropdownMenuItem(value: 'absent', child: Text('Absent')),
                    DropdownMenuItem(value: 'late', child: Text('Late')),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Check-in Time
                ReactiveTextField<DateTime>(
                  formControlName: 'checkInTime',
                  decoration: const InputDecoration(
                    labelText: 'Check-in Time',
                    prefixIcon: Icon(Icons.login),
                    border: OutlineInputBorder(),
                  ),
                  readOnly: true,
                  onTap: (control) => _selectTime(context, control),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Check-out Time
                ReactiveTextField<DateTime>(
                  formControlName: 'checkOutTime',
                  decoration: const InputDecoration(
                    labelText: 'Check-out Time (Optional)',
                    prefixIcon: Icon(Icons.logout),
                    border: OutlineInputBorder(),
                  ),
                  readOnly: true,
                  onTap: (control) => _selectTime(context, control),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Notes
                ReactiveTextField<String>(
                  formControlName: 'notes',
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _handleSubmit,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Add Record'),
        ),
      ],
    );
  }

  Future<void> _selectTime(BuildContext context, FormControl<DateTime> control) async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(control.value ?? DateTime.now()),
    );

    if (time != null) {
      final selectedDateTime = DateTime(
        widget.selectedDate.year,
        widget.selectedDate.month,
        widget.selectedDate.day,
        time.hour,
        time.minute,
      );
      control.value = selectedDateTime;
    }
  }

  Future<void> _handleSubmit() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;

      final record = AttendanceRecord.create(
        userId: 'current_user', // TODO: Get actual user ID
        workerName: formValue['workerName'] as String,
        workerRole: formValue['workerRole'] as String?,
        date: widget.selectedDate,
        status: formValue['status'] as String,
        checkInTime: _formatTime(formValue['checkInTime'] as DateTime?),
        checkOutTime: _formatTime(formValue['checkOutTime'] as DateTime?),
        notes: formValue['notes'] as String?,
      );

      await ref.read(attendanceRecordsProvider.notifier).addRecord(record);

      if (mounted) {
        Navigator.of(context).pop();
        AppUtils.showSuccessSnackBar(context, 'Attendance record added successfully');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to add attendance record: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  String? _formatTime(DateTime? dateTime) {
    if (dateTime == null) return null;
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
