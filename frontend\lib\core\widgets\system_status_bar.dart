import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';

/// System status indicator colors
enum SystemStatus {
  operational(Colors.green, 'Operational'),
  warning(Colors.orange, 'Warning'),
  critical(Colors.red, 'Critical'),
  unknown(Colors.grey, 'Unknown');

  const SystemStatus(this.color, this.label);
  final Color color;
  final String label;
}

/// Individual system status data
class SystemStatusData {
  final String name;
  final SystemStatus status;
  final String? details;
  final IconData icon;

  const SystemStatusData({
    required this.name,
    required this.status,
    this.details,
    required this.icon,
  });
}

/// System Status Bar Widget
class SystemStatusBar extends ConsumerWidget {
  final List<SystemStatusData> systems;
  final VoidCallback? onTap;
  final bool showDetails;

  const SystemStatusBar({
    super.key,
    required this.systems,
    this.onTap,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final overallStatus = _calculateOverallStatus();

    return Card(
      margin: const EdgeInsets.all(AppConstants.smallPadding),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    Icons.dashboard,
                    color: overallStatus.color,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'System Status',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: overallStatus.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: overallStatus.color.withOpacity(0.3)),
                    ),
                    child: Text(
                      overallStatus.label,
                      style: TextStyle(
                        color: overallStatus.color,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Status Indicators
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: systems.map((system) => _buildStatusIndicator(context, system)).toList(),
              ),
              
              if (showDetails) ...[
                const SizedBox(height: AppConstants.defaultPadding),
                const Divider(),
                const SizedBox(height: AppConstants.smallPadding),
                _buildDetailsList(context),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(BuildContext context, SystemStatusData system) {
    return Tooltip(
      message: system.details ?? '${system.name}: ${system.status.label}',
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: system.status.color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: system.status.color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              system.icon,
              size: 16,
              color: system.status.color,
            ),
            const SizedBox(width: 4),
            Text(
              system.name,
              style: TextStyle(
                color: system.status.color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: system.status.color,
                shape: BoxShape.circle,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'System Details',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        ...systems.map((system) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              Icon(
                system.icon,
                size: 14,
                color: system.status.color,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  system.name,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
              Text(
                system.status.label,
                style: TextStyle(
                  color: system.status.color,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  SystemStatus _calculateOverallStatus() {
    if (systems.isEmpty) return SystemStatus.unknown;
    
    bool hasCritical = systems.any((s) => s.status == SystemStatus.critical);
    bool hasWarning = systems.any((s) => s.status == SystemStatus.warning);
    
    if (hasCritical) return SystemStatus.critical;
    if (hasWarning) return SystemStatus.warning;
    return SystemStatus.operational;
  }
}

/// Property System Status Provider
final propertySystemStatusProvider = Provider.family<List<SystemStatusData>, String>((ref, propertyId) {
  // This would typically fetch real data from APIs
  // For now, returning mock data
  return [
    const SystemStatusData(
      name: 'Electricity',
      status: SystemStatus.operational,
      details: 'Power supply stable',
      icon: Icons.electrical_services,
    ),
    const SystemStatusData(
      name: 'Water',
      status: SystemStatus.warning,
      details: 'Low pressure detected',
      icon: Icons.water_drop,
    ),
    const SystemStatusData(
      name: 'Internet',
      status: SystemStatus.operational,
      details: 'Connection stable',
      icon: Icons.wifi,
    ),
    const SystemStatusData(
      name: 'Security',
      status: SystemStatus.operational,
      details: 'All systems active',
      icon: Icons.security,
    ),
    const SystemStatusData(
      name: 'Generator',
      status: SystemStatus.warning,
      details: 'Fuel level at 30%',
      icon: Icons.power,
    ),
  ];
});
