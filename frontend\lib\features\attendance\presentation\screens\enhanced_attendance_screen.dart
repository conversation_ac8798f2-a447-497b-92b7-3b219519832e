import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/attendance.dart';
import '../providers/attendance_providers.dart';
import '../widgets/attendance_calendar_widget.dart';
import '../widgets/attendance_stats_widget.dart';
import '../widgets/check_in_out_widget.dart';
import '../widgets/attendance_filter_chips.dart';
import '../widgets/bulk_attendance_dialog.dart';

class EnhancedAttendanceScreen extends ConsumerStatefulWidget {
  const EnhancedAttendanceScreen({super.key});

  @override
  ConsumerState<EnhancedAttendanceScreen> createState() => _EnhancedAttendanceScreenState();
}

class _EnhancedAttendanceScreenState extends ConsumerState<EnhancedAttendanceScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _selectedDate = DateTime.now();
  String _selectedFilter = 'all';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final attendanceAsync = ref.watch(attendanceProvider(_selectedDate));
    final attendanceStatsAsync = ref.watch(attendanceStatsProvider(_selectedDate));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Attendance'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['attendance.create'],
            child: IconButton(
              icon: const Icon(Icons.add_circle),
              onPressed: _showBulkAttendanceDialog,
              tooltip: 'Bulk Attendance',
            ),
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _showDatePicker,
            tooltip: 'Select Date',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(attendanceProvider(_selectedDate));
              ref.invalidate(attendanceStatsProvider(_selectedDate));
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Today', icon: Icon(Icons.today)),
            Tab(text: 'Calendar', icon: Icon(Icons.calendar_month)),
            Tab(text: 'Reports', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Date and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Selected Date Display
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: Theme.of(context).primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        DateFormat('EEEE, MMM dd, yyyy').format(_selectedDate),
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search staff members...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() => _searchQuery = '');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      AttendanceFilterChip(
                        label: 'All',
                        isSelected: _selectedFilter == 'all',
                        onTap: () => setState(() => _selectedFilter = 'all'),
                      ),
                      const SizedBox(width: 8),
                      AttendanceFilterChip(
                        label: 'Present',
                        isSelected: _selectedFilter == 'present',
                        onTap: () => setState(() => _selectedFilter = 'present'),
                      ),
                      const SizedBox(width: 8),
                      AttendanceFilterChip(
                        label: 'Absent',
                        isSelected: _selectedFilter == 'absent',
                        onTap: () => setState(() => _selectedFilter = 'absent'),
                      ),
                      const SizedBox(width: 8),
                      AttendanceFilterChip(
                        label: 'Late',
                        isSelected: _selectedFilter == 'late',
                        onTap: () => setState(() => _selectedFilter = 'late'),
                      ),
                      const SizedBox(width: 8),
                      AttendanceFilterChip(
                        label: 'Half Day',
                        isSelected: _selectedFilter == 'half_day',
                        onTap: () => setState(() => _selectedFilter = 'half_day'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTodayTab(attendanceAsync, attendanceStatsAsync),
                _buildCalendarTab(),
                _buildReportsTab(attendanceStatsAsync),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: RoleBasedFAB(
        requiredPermissions: const ['attendance.create'],
        onPressed: _showCheckInOutDialog,
        tooltip: 'Check In/Out',
        child: const Icon(Icons.access_time),
      ),
    );
  }

  Widget _buildTodayTab(
    AsyncValue<List<AttendanceRecord>> attendanceAsync,
    AsyncValue<AttendanceStats> statsAsync,
  ) {
    return Column(
      children: [
        // Stats Section
        attendanceAsync.when(
          data: (attendanceRecords) => AttendanceStatsWidget(
            attendanceRecords: attendanceRecords,
            startDate: _selectedDate,
            endDate: _selectedDate,
          ),
          loading: () => const Padding(
            padding: EdgeInsets.all(AppConstants.defaultPadding),
            child: LinearProgressIndicator(),
          ),
          error: (_, __) => const SizedBox.shrink(),
        ),
        // Attendance List
        Expanded(
          child: attendanceAsync.when(
            data: (attendanceList) {
              final filteredAttendance = _filterAttendance(attendanceList);

              if (filteredAttendance.isEmpty) {
                return _buildEmptyState();
              }

              return RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(attendanceProvider(_selectedDate));
                  ref.invalidate(attendanceStatsProvider(_selectedDate));
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  itemCount: filteredAttendance.length,
                  itemBuilder: (context, index) {
                    final attendance = filteredAttendance[index];
                    return _buildAttendanceCard(attendance);
                  },
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => _buildErrorState(error),
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarTab() {
    return AttendanceCalendarWidget(
      attendanceRecords: [], // TODO: Pass actual records
    );
  }

  Widget _buildReportsTab(AsyncValue<AttendanceStats> statsAsync) {
    return statsAsync.when(
      data: (stats) => SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attendance Reports',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildReportCard('Daily Summary', stats),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildTrendChart(stats),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildAttendanceCard(AttendanceRecord attendance) {
    final statusColor = attendance.statusColor;
    final statusIcon = attendance.statusIcon;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: statusColor.withValues(alpha: 0.1),
          child: Icon(statusIcon, color: statusColor),
        ),
        title: Text(
          attendance.workerName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${attendance.attendanceType}'),
            if (attendance.hasCheckInTime)
              Text('Check In: ${attendance.checkInTime}'),
            if (attendance.hasCheckOutTime)
              Text('Check Out: ${attendance.checkOutTime}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor),
              ),
              child: Text(
                attendance.displayStatus.toUpperCase(),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Text(
              attendance.hoursWorkedDisplay,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        onTap: () => _showAttendanceDetails(attendance),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No attendance records',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Attendance records for ${DateFormat('MMM dd, yyyy').format(_selectedDate)} will appear here',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('Failed to load attendance',
               style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(error.toString(), style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.invalidate(attendanceProvider(_selectedDate)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(String title, AttendanceStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem('Present', stats.presentCount, Colors.green),
                ),
                Expanded(
                  child: _buildStatItem('Absent', stats.absentCount, Colors.red),
                ),
                Expanded(
                  child: _buildStatItem('Late', stats.lateCount, Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildTrendChart(AttendanceStats stats) {
    // Placeholder for attendance trend chart
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attendance Trend',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('Trend chart coming soon'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<AttendanceRecord> _filterAttendance(List<AttendanceRecord> attendance) {
    var filtered = attendance;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((a) {
        return a.workerName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (a.propertyId?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) ||
               (a.officeId?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply status filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((a) => a.status == _selectedFilter).toList();
    }

    return filtered;
  }



  void _showDatePicker() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  void _showCheckInOutDialog() {
    showDialog(
      context: context,
      builder: (context) => CheckInOutWidget(
        propertyId: 'default', // TODO: Get actual property ID
        onCheckInOut: () {
          // TODO: Implement attendance provider methods
          AppUtils.showSuccessSnackBar(context, 'Attendance recorded');
        },
      ),
    );
  }

  void _showBulkAttendanceDialog() {
    showDialog(
      context: context,
      builder: (context) => BulkAttendanceDialog(
        propertyId: 'default', // TODO: Get actual property ID
        selectedDate: _selectedDate,
      ),
    );
  }

  void _showAttendanceDetails(AttendanceRecord attendance) {
    // Show detailed attendance information
    AppUtils.showInfoSnackBar(context, 'Attendance details coming soon');
  }
}
