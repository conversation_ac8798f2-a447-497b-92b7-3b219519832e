import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/validation/validation_rules.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/maintenance_issue.dart';
import '../../../../features/properties/presentation/providers/properties_providers.dart';
import '../providers/maintenance_providers.dart';

class AddMaintenanceIssueDialog extends ConsumerStatefulWidget {
  const AddMaintenanceIssueDialog({super.key});

  @override
  ConsumerState<AddMaintenanceIssueDialog> createState() => _AddMaintenanceIssueDialogState();
}

class _AddMaintenanceIssueDialogState extends ConsumerState<AddMaintenanceIssueDialog> {
  late FormGroup form;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    form = FormGroup(ValidationRules.createMaintenanceIssueForm().cast<String, AbstractControl<dynamic>>());
  }

  @override
  Widget build(BuildContext context) {
    final propertiesAsync = ref.watch(propertiesProvider);

    return AlertDialog(
      title: const Text('Create Maintenance Issue'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: propertiesAsync.when(
          data: (properties) => _buildForm(properties),
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(AppConstants.largePadding),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.largePadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 48),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Failed to load properties',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    'Please try again later',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _submitForm,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create Issue'),
        ),
      ],
    );
  }

  Widget _buildForm(List<dynamic> properties) {
    return ReactiveForm(
      formGroup: form,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Selection
            ReactiveDropdownField<String>(
              formControlName: 'propertyId',
              decoration: const InputDecoration(
                labelText: 'Property *',
                border: OutlineInputBorder(),
              ),
              items: properties.map((property) => DropdownMenuItem<String>(
                value: property.id,
                child: Text(property.name),
              )).toList(),
              validationMessages: {
                ValidationMessage.required: (_) => 'Property is required',
              },
            ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Title
                ReactiveTextField<String>(
                  formControlName: 'title',
                  decoration: const InputDecoration(
                    labelText: 'Issue Title *',
                    border: OutlineInputBorder(),
                    hintText: 'Brief description of the issue',
                  ),
                  maxLines: 1,
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Title is required',
                    ValidationMessage.minLength: (_) => 'Title must be at least 5 characters',
                    ValidationMessage.maxLength: (_) => 'Title cannot exceed 200 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Description
                ReactiveTextField<String>(
                  formControlName: 'description',
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    border: OutlineInputBorder(),
                    hintText: 'Detailed description of the issue',
                  ),
                  maxLines: 4,
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Description is required',
                    ValidationMessage.minLength: (_) => 'Description must be at least 10 characters',
                    ValidationMessage.maxLength: (_) => 'Description cannot exceed 1000 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Priority
                ReactiveDropdownField<String>(
                  formControlName: 'priority',
                  decoration: const InputDecoration(
                    labelText: 'Priority *',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'low', child: Text('Low')),
                    DropdownMenuItem(value: 'medium', child: Text('Medium')),
                    DropdownMenuItem(value: 'high', child: Text('High')),
                    DropdownMenuItem(value: 'critical', child: Text('Critical')),
                  ],
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Priority is required',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Service Type
                ReactiveDropdownField<String>(
                  formControlName: 'serviceType',
                  decoration: const InputDecoration(
                    labelText: 'Service Type *',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'electricity', child: Text('Electricity')),
                    DropdownMenuItem(value: 'water', child: Text('Water Supply')),
                    DropdownMenuItem(value: 'internet', child: Text('Internet')),
                    DropdownMenuItem(value: 'security', child: Text('Security')),
                    DropdownMenuItem(value: 'generator', child: Text('Generator')),
                    DropdownMenuItem(value: 'maintenance', child: Text('General Maintenance')),
                  ],
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Service type is required',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Department (Optional)
                ReactiveTextField<String>(
                  formControlName: 'department',
                  decoration: const InputDecoration(
                    labelText: 'Department',
                    border: OutlineInputBorder(),
                    hintText: 'e.g., Facilities, IT, Security',
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Due Date (Optional)
                ReactiveTextField<DateTime>(
                  formControlName: 'dueDate',
                  decoration: const InputDecoration(
                    labelText: 'Due Date',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: (control) => _selectDueDate(context, control),
                  valueAccessor: DateTimeValueAccessor(
                    dateTimeFormat: DateFormat('MMM dd, yyyy'),
                  ),
                ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDueDate(BuildContext context, FormControl<DateTime> control) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate != null) {
      control.value = selectedDate;
    }
  }

  Future<void> _submitForm() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;

      final issue = MaintenanceIssue.create(
        propertyId: formValue['propertyId'] as String,
        title: formValue['title'] as String,
        description: formValue['description'] as String,
        priority: formValue['priority'] as String,
        reportedBy: 'current_user', // TODO: Get from auth provider
        serviceType: formValue['serviceType'] as String?,
        department: formValue['department'] as String?,
        dueDate: formValue['dueDate'] as DateTime?,
      );

      await ref.read(maintenanceIssuesNotifierProvider.notifier).addIssue(issue);

      if (mounted) {
        Navigator.of(context).pop();
        AppUtils.showSuccessSnackBar(context, 'Maintenance issue created successfully');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to create issue: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}

// Custom value accessor for DateTime formatting
class DateTimeValueAccessor extends ControlValueAccessor<DateTime, String> {
  final DateFormat dateTimeFormat;

  DateTimeValueAccessor({required this.dateTimeFormat});

  @override
  String? modelToViewValue(DateTime? modelValue) {
    return modelValue != null ? dateTimeFormat.format(modelValue) : null;
  }

  @override
  DateTime? viewToModelValue(String? viewValue) {
    return viewValue != null && viewValue.isNotEmpty
        ? dateTimeFormat.parse(viewValue)
        : null;
  }
}


