-- CreateTable
CREATE TABLE "property_functions" (
    "id" TEXT NOT NULL,
    "property_id" TEXT NOT NULL,
    "function_name" TEXT NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "configuration" JSONB NOT NULL DEFAULT '{}',
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "property_functions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "property_functions_property_id_function_name_key" ON "property_functions"("property_id", "function_name");

-- AddForeignKey
ALTER TABLE "property_functions" ADD CONSTRAINT "property_functions_property_id_fkey" FOREIGN KEY ("property_id") REFERENCES "properties"("id") ON DELETE CASCADE ON UPDATE CASCADE;
