// Jest setup configuration for SRSR API tests
require('dotenv').config();

// Set test timeout
jest.setTimeout(30000);

// Global test configuration
global.testConfig = {
  baseURL: process.env.API_BASE_URL || 'http://localhost:3000',
  timeout: 30000,
  retries: 3
};

// Setup global test helpers
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: console.warn,
  error: console.error,
};

// Global beforeAll setup
beforeAll(async () => {
  console.info('🚀 Starting SRSR API tests...');
  console.info(`📡 API Base URL: ${global.testConfig.baseURL}`);
});

// Global afterAll cleanup
afterAll(async () => {
  console.info('✅ SRSR API tests completed');
});

// Global error handler
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
