import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';

enum StatusLevel { green, orange, red }

class ThresholdConfig {
  final String id;
  final String functionalArea;
  final String metricName;
  final double? greenMin;
  final double? greenMax;
  final double? orangeMin;
  final double? orangeMax;
  final double? redMin;
  final double? redMax;
  final String unit;
  final String description;
  final String propertyType;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ThresholdConfig({
    required this.id,
    required this.functionalArea,
    required this.metricName,
    this.greenMin,
    this.greenMax,
    this.orangeMin,
    this.orangeMax,
    this.redMin,
    this.redMax,
    required this.unit,
    required this.description,
    required this.propertyType,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ThresholdConfig.fromJson(Map<String, dynamic> json) {
    return ThresholdConfig(
      id: json['id'] as String,
      functionalArea: json['functional_area'] as String,
      metricName: json['metric_name'] as String,
      greenMin: json['green_min']?.toDouble(),
      greenMax: json['green_max']?.toDouble(),
      orangeMin: json['orange_min']?.toDouble(),
      orangeMax: json['orange_max']?.toDouble(),
      redMin: json['red_min']?.toDouble(),
      redMax: json['red_max']?.toDouble(),
      unit: json['unit'] as String,
      description: json['description'] as String,
      propertyType: json['property_type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'functional_area': functionalArea,
      'metric_name': metricName,
      'green_min': greenMin,
      'green_max': greenMax,
      'orange_min': orangeMin,
      'orange_max': orangeMax,
      'red_min': redMin,
      'red_max': redMax,
      'unit': unit,
      'description': description,
      'property_type': propertyType,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ThresholdConfig copyWith({
    String? id,
    String? functionalArea,
    String? metricName,
    double? greenMin,
    double? greenMax,
    double? orangeMin,
    double? orangeMax,
    double? redMin,
    double? redMax,
    String? unit,
    String? description,
    String? propertyType,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ThresholdConfig(
      id: id ?? this.id,
      functionalArea: functionalArea ?? this.functionalArea,
      metricName: metricName ?? this.metricName,
      greenMin: greenMin ?? this.greenMin,
      greenMax: greenMax ?? this.greenMax,
      orangeMin: orangeMin ?? this.orangeMin,
      orangeMax: orangeMax ?? this.orangeMax,
      redMin: redMin ?? this.redMin,
      redMax: redMax ?? this.redMax,
      unit: unit ?? this.unit,
      description: description ?? this.description,
      propertyType: propertyType ?? this.propertyType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class ThresholdManager {
  static const String _storageKey = 'threshold_configs';
  static ThresholdManager? _instance;

  ThresholdManager._();

  static ThresholdManager get instance {
    _instance ??= ThresholdManager._();
    return _instance!;
  }

  List<ThresholdConfig> _thresholds = [];
  bool _isInitialized = false;

  /// Initialize with default thresholds
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadThresholds();

    if (_thresholds.isEmpty) {
      _thresholds = _getDefaultThresholds();
      await _saveThresholds();
    }

    _isInitialized = true;
  }

  /// Get all threshold configurations
  List<ThresholdConfig> get thresholds => List.unmodifiable(_thresholds);

  /// Get thresholds by functional area
  List<ThresholdConfig> getByFunctionalArea(String functionalArea, [String? propertyType]) {
    return _thresholds.where((threshold) {
      final areaMatch = threshold.functionalArea == functionalArea;
      final typeMatch = propertyType == null ||
                       threshold.propertyType == propertyType ||
                       threshold.propertyType == 'all';
      return areaMatch && typeMatch;
    }).toList();
  }

  /// Get specific threshold configuration
  ThresholdConfig? getThreshold(String functionalArea, String metricName, [String? propertyType]) {
    return _thresholds.where((threshold) {
      final areaMatch = threshold.functionalArea == functionalArea;
      final metricMatch = threshold.metricName == metricName;
      final typeMatch = propertyType == null ||
                       threshold.propertyType == propertyType ||
                       threshold.propertyType == 'all';
      return areaMatch && metricMatch && typeMatch;
    }).firstOrNull;
  }

  /// Calculate status based on value and thresholds
  StatusLevel calculateStatus(String functionalArea, String metricName, double value, [String? propertyType]) {
    final threshold = getThreshold(functionalArea, metricName, propertyType);

    if (threshold == null) {
      debugPrint('No threshold found for $functionalArea.$metricName');
      return StatusLevel.red; // Default to red if no threshold found
    }

    // Check green range
    if (threshold.greenMin != null && threshold.greenMax != null) {
      if (value >= threshold.greenMin! && value <= threshold.greenMax!) {
        return StatusLevel.green;
      }
    }

    // Check orange range
    if (threshold.orangeMin != null && threshold.orangeMax != null) {
      if (value >= threshold.orangeMin! && value <= threshold.orangeMax!) {
        return StatusLevel.orange;
      }
    }

    // Default to red
    return StatusLevel.red;
  }

  /// Add or update threshold configuration
  Future<void> setThreshold(ThresholdConfig threshold) async {
    final index = _thresholds.indexWhere((t) =>
      t.functionalArea == threshold.functionalArea &&
      t.metricName == threshold.metricName &&
      t.propertyType == threshold.propertyType
    );

    if (index >= 0) {
      _thresholds[index] = threshold.copyWith(updatedAt: DateTime.now());
    } else {
      _thresholds.add(threshold);
    }

    await _saveThresholds();
  }

  /// Remove threshold configuration
  Future<void> removeThreshold(String functionalArea, String metricName, String propertyType) async {
    _thresholds.removeWhere((t) =>
      t.functionalArea == functionalArea &&
      t.metricName == metricName &&
      t.propertyType == propertyType
    );
    await _saveThresholds();
  }

  /// Load thresholds from storage
  Future<void> _loadThresholds() async {
    try {
      final data = StorageService.getString(_storageKey);
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _thresholds = jsonList.map((json) => ThresholdConfig.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error loading thresholds: $e');
      _thresholds = [];
    }
  }

  /// Save thresholds to storage
  Future<void> _saveThresholds() async {
    try {
      final jsonList = _thresholds.map((t) => t.toJson()).toList();
      await StorageService.setString(_storageKey, jsonEncode(jsonList));
    } catch (e) {
      debugPrint('Error saving thresholds: $e');
    }
  }

  /// Get default threshold configurations
  List<ThresholdConfig> _getDefaultThresholds() {
    final now = DateTime.now();

    return [
      // Fuel thresholds
      ThresholdConfig(
        id: 'fuel_level_residential',
        functionalArea: 'fuel',
        metricName: 'fuel_level',
        greenMin: 70,
        greenMax: 100,
        orangeMin: 30,
        orangeMax: 69.99,
        redMin: 0,
        redMax: 29.99,
        unit: '%',
        description: 'Generator fuel level percentage',
        propertyType: 'residential',
        createdAt: now,
        updatedAt: now,
      ),
      ThresholdConfig(
        id: 'backup_hours_residential',
        functionalArea: 'fuel',
        metricName: 'backup_hours',
        greenMin: 24,
        greenMax: 999,
        orangeMin: 8,
        orangeMax: 23.99,
        redMin: 0,
        redMax: 7.99,
        unit: 'hrs',
        description: 'Available backup hours',
        propertyType: 'residential',
        createdAt: now,
        updatedAt: now,
      ),
      // Maintenance thresholds
      ThresholdConfig(
        id: 'open_issues_all',
        functionalArea: 'maintenance',
        metricName: 'open_issues',
        greenMin: 0,
        greenMax: 3,
        orangeMin: 4,
        orangeMax: 7,
        redMin: 8,
        redMax: 999,
        unit: 'issues',
        description: 'Number of open maintenance issues',
        propertyType: 'all',
        createdAt: now,
        updatedAt: now,
      ),
      // Attendance thresholds
      ThresholdConfig(
        id: 'attendance_percentage_all',
        functionalArea: 'attendance',
        metricName: 'attendance_percentage',
        greenMin: 90,
        greenMax: 100,
        orangeMin: 70,
        orangeMax: 89.99,
        redMin: 0,
        redMax: 69.99,
        unit: '%',
        description: 'Staff attendance percentage',
        propertyType: 'all',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}
