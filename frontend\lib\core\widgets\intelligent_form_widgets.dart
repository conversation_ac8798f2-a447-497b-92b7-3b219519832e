import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../auth/providers/permission_providers.dart';

/// Intelligent form that adapts fields based on user permissions
class IntelligentForm extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final List<IntelligentFormField> fields;
  final Widget? submitButton;
  final EdgeInsets? padding;
  final double spacing;

  const IntelligentForm({
    super.key,
    required this.formKey,
    required this.fields,
    this.submitButton,
    this.padding,
    this.spacing = 16.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<Widget>>(
      future: _buildAuthorizedFields(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final authorizedFields = snapshot.data ?? [];

        return Form(
          key: formKey,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ...authorizedFields.map((field) => Padding(
                  padding: EdgeInsets.only(bottom: spacing),
                  child: field,
                )),
                if (submitButton != null) ...[
                  SizedBox(height: spacing),
                  submitButton!,
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Future<List<Widget>> _buildAuthorizedFields(WidgetRef ref) async {
    final authorizedFields = <Widget>[];

    for (final field in fields) {
      bool hasPermission = true;

      if (field.requiredPermissions.isNotEmpty) {
        final permissionChecker = ref.read(permissionCheckerProvider);

        if (field.requireAll) {
          hasPermission = await permissionChecker.hasAllPermissions(field.requiredPermissions);
        } else {
          hasPermission = await permissionChecker.hasAnyPermission(field.requiredPermissions);
        }
      }

      if (hasPermission) {
        authorizedFields.add(field.widget);
      } else if (field.showReadOnlyWhenUnauthorized && field.readOnlyWidget != null) {
        authorizedFields.add(field.readOnlyWidget!);
      }
    }

    return authorizedFields;
  }
}

/// Form field with permission requirements
class IntelligentFormField {
  final Widget widget;
  final Widget? readOnlyWidget;
  final List<String> requiredPermissions;
  final bool requireAll;
  final bool showReadOnlyWhenUnauthorized;

  const IntelligentFormField({
    required this.widget,
    this.readOnlyWidget,
    this.requiredPermissions = const [],
    this.requireAll = true,
    this.showReadOnlyWhenUnauthorized = false,
  });

  /// Create a text field with permission requirements
  factory IntelligentFormField.text({
    required String label,
    required TextEditingController controller,
    String? initialValue,
    String? hintText,
    String? Function(String?)? validator,
    List<String> requiredPermissions = const [],
    bool requireAll = true,
    bool showReadOnlyWhenUnauthorized = true,
    TextInputType? keyboardType,
    int? maxLines,
    bool obscureText = false,
  }) {
    final textField = TextFormField(
      controller: controller,
      initialValue: initialValue,
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        border: const OutlineInputBorder(),
      ),
      validator: validator,
      keyboardType: keyboardType,
      maxLines: maxLines,
      obscureText: obscureText,
    );

    final readOnlyField = showReadOnlyWhenUnauthorized
        ? TextFormField(
            controller: controller,
            initialValue: initialValue,
            decoration: InputDecoration(
              labelText: label,
              hintText: hintText,
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.lock, color: Colors.grey),
            ),
            readOnly: true,
            enabled: false,
          )
        : null;

    return IntelligentFormField(
      widget: textField,
      readOnlyWidget: readOnlyField,
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      showReadOnlyWhenUnauthorized: showReadOnlyWhenUnauthorized,
    );
  }

  /// Create a dropdown field with permission requirements
  static IntelligentFormField dropdown<T>({
    required String label,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
    String? Function(T?)? validator,
    List<String> requiredPermissions = const [],
    bool requireAll = true,
    bool showReadOnlyWhenUnauthorized = true,
  }) {
    final dropdownField = DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      validator: validator,
    );

    final readOnlyField = showReadOnlyWhenUnauthorized
        ? DropdownButtonFormField<T>(
            value: value,
            items: items,
            onChanged: null, // Disabled
            decoration: InputDecoration(
              labelText: label,
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.lock, color: Colors.grey),
            ),
            validator: validator,
          )
        : null;

    return IntelligentFormField(
      widget: dropdownField,
      readOnlyWidget: readOnlyField,
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      showReadOnlyWhenUnauthorized: showReadOnlyWhenUnauthorized,
    );
  }

  /// Create a checkbox field with permission requirements
  factory IntelligentFormField.checkbox({
    required String title,
    required bool value,
    required ValueChanged<bool?> onChanged,
    String? subtitle,
    List<String> requiredPermissions = const [],
    bool requireAll = true,
    bool showReadOnlyWhenUnauthorized = true,
  }) {
    final checkboxField = CheckboxListTile(
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      value: value,
      onChanged: onChanged,
      controlAffinity: ListTileControlAffinity.leading,
    );

    final readOnlyField = showReadOnlyWhenUnauthorized
        ? CheckboxListTile(
            title: Text(title),
            subtitle: subtitle != null ? Text(subtitle) : null,
            value: value,
            onChanged: null, // Disabled
            controlAffinity: ListTileControlAffinity.leading,
            secondary: const Icon(Icons.lock, color: Colors.grey),
          )
        : null;

    return IntelligentFormField(
      widget: checkboxField,
      readOnlyWidget: readOnlyField,
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      showReadOnlyWhenUnauthorized: showReadOnlyWhenUnauthorized,
    );
  }

  /// Create a date picker field with permission requirements
  factory IntelligentFormField.datePicker({
    required String label,
    required DateTime? value,
    required ValueChanged<DateTime?> onChanged,
    String? Function(DateTime?)? validator,
    DateTime? firstDate,
    DateTime? lastDate,
    List<String> requiredPermissions = const [],
    bool requireAll = true,
    bool showReadOnlyWhenUnauthorized = true,
  }) {
    return IntelligentFormField(
      widget: IntelligentDatePickerField(
        label: label,
        value: value,
        onChanged: onChanged,
        validator: validator,
        firstDate: firstDate,
        lastDate: lastDate,
      ),
      readOnlyWidget: showReadOnlyWhenUnauthorized
          ? IntelligentDatePickerField(
              label: label,
              value: value,
              onChanged: onChanged,
              validator: validator,
              firstDate: firstDate,
              lastDate: lastDate,
              readOnly: true,
            )
          : null,
      requiredPermissions: requiredPermissions,
      requireAll: requireAll,
      showReadOnlyWhenUnauthorized: showReadOnlyWhenUnauthorized,
    );
  }
}

/// Date picker field widget that can be used with IntelligentFormField
class IntelligentDatePickerField extends StatefulWidget {
  final String label;
  final DateTime? value;
  final ValueChanged<DateTime?> onChanged;
  final String? Function(DateTime?)? validator;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final bool readOnly;

  const IntelligentDatePickerField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.validator,
    this.firstDate,
    this.lastDate,
    this.readOnly = false,
  });

  @override
  State<IntelligentDatePickerField> createState() => _IntelligentDatePickerFieldState();
}

class _IntelligentDatePickerFieldState extends State<IntelligentDatePickerField> {
  late TextEditingController controller;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController(
      text: widget.value != null ? '${widget.value!.day}/${widget.value!.month}/${widget.value!.year}' : '',
    );
  }

  @override
  void didUpdateWidget(IntelligentDatePickerField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      controller.text = widget.value != null ? '${widget.value!.day}/${widget.value!.month}/${widget.value!.year}' : '';
    }
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: widget.label,
        border: const OutlineInputBorder(),
        suffixIcon: Icon(
          widget.readOnly ? Icons.lock : Icons.calendar_today,
          color: widget.readOnly ? Colors.grey : null,
        ),
      ),
      readOnly: true,
      enabled: !widget.readOnly,
      onTap: widget.readOnly ? null : () async {
        final date = await showDatePicker(
          context: context,
          initialDate: widget.value ?? DateTime.now(),
          firstDate: widget.firstDate ?? DateTime(1900),
          lastDate: widget.lastDate ?? DateTime(2100),
        );
        if (date != null) {
          widget.onChanged(date);
          controller.text = '${date.day}/${date.month}/${date.year}';
        }
      },
      validator: (text) {
        if (widget.validator != null) {
          return widget.validator!(widget.value);
        }
        return null;
      },
    );
  }
}

/// Intelligent submit button that checks permissions before submission
class IntelligentSubmitButton extends ConsumerWidget {
  final VoidCallback? onPressed;
  final String text;
  final List<String> requiredPermissions;
  final bool requireAll;
  final ButtonStyle? style;
  final bool isLoading;

  const IntelligentSubmitButton({
    super.key,
    this.onPressed,
    required this.text,
    this.requiredPermissions = const [],
    this.requireAll = true,
    this.style,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return FutureBuilder<bool>(
      future: _checkPermissions(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return ElevatedButton(
            onPressed: null,
            style: style,
            child: const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        final hasPermission = snapshot.data ?? false;

        return ElevatedButton(
          onPressed: hasPermission ? onPressed : null,
          style: style,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(text),
              if (!hasPermission) ...[
                const SizedBox(width: 8),
                const Icon(Icons.lock, size: 16),
              ],
            ],
          ),
        );
      },
    );
  }

  Future<bool> _checkPermissions(WidgetRef ref) async {
    if (requiredPermissions.isEmpty) {
      return true;
    }

    final permissionChecker = ref.read(permissionCheckerProvider);

    if (requireAll) {
      return await permissionChecker.hasAllPermissions(requiredPermissions);
    } else {
      return await permissionChecker.hasAnyPermission(requiredPermissions);
    }
  }
}

/// Intelligent section that shows/hides based on permissions
class IntelligentFormSection extends ConsumerWidget {
  final String title;
  final List<Widget> children;
  final List<String> requiredPermissions;
  final bool requireAll;
  final Widget? fallback;

  const IntelligentFormSection({
    super.key,
    required this.title,
    required this.children,
    this.requiredPermissions = const [],
    this.requireAll = true,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<bool>(
      future: _checkPermissions(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return fallback ?? const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            ...children,
          ],
        );
      },
    );
  }

  Future<bool> _checkPermissions(WidgetRef ref) async {
    if (requiredPermissions.isEmpty) {
      return true;
    }

    final permissionChecker = ref.read(permissionCheckerProvider);

    if (requireAll) {
      return await permissionChecker.hasAllPermissions(requiredPermissions);
    } else {
      return await permissionChecker.hasAnyPermission(requiredPermissions);
    }
  }
}
