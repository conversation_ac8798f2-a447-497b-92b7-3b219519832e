import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class StorageService {
  static late Box _authBox;
  static late Box _settingsBox;
  static late Box _cacheBox;
  static late SharedPreferences _prefs;

  static Future<void> init() async {
    await Hive.initFlutter();

    _authBox = await Hive.openBox(AppConstants.authBox);
    _settingsBox = await Hive.openBox(AppConstants.settingsBox);
    _cacheBox = await Hive.openBox(AppConstants.cacheBox);

    _prefs = await SharedPreferences.getInstance();
  }

  // Auth Token Management
  static Future<void> saveToken(String token) async {
    print('🔐 Storage: Saving token: ${token.substring(0, 20)}...');
    await _authBox.put(AppConstants.tokenKey, token);
    print('🔐 Storage: Token saved successfully');
  }

  static String? getToken() {
    final token = _authBox.get(AppConstants.tokenKey);
    print('🔐 Storage: Getting token: ${token != null ? '${token.substring(0, 20)}...' : 'null'}');
    return token;
  }

  static Future<void> clearToken() async {
    print('🔐 Storage: Clearing token');
    await _authBox.delete(AppConstants.tokenKey);
  }

  // User Data Management
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    await _authBox.put(AppConstants.userKey, userData);
  }

  static Map<String, dynamic>? getUserData() {
    final data = _authBox.get(AppConstants.userKey);
    return data != null ? Map<String, dynamic>.from(data) : null;
  }

  static Future<void> clearUserData() async {
    await _authBox.delete(AppConstants.userKey);
  }

  // Settings Management
  static Future<void> saveThemeMode(String themeMode) async {
    await _settingsBox.put(AppConstants.themeKey, themeMode);
  }

  static String? getThemeMode() {
    return _settingsBox.get(AppConstants.themeKey);
  }

  static Future<void> saveLanguage(String language) async {
    await _settingsBox.put(AppConstants.languageKey, language);
  }

  static String? getLanguage() {
    return _settingsBox.get(AppConstants.languageKey);
  }

  // Cache Management
  static Future<void> cacheData(String key, dynamic data) async {
    await _cacheBox.put(key, data);
  }

  static T? getCachedData<T>(String key) {
    return _cacheBox.get(key);
  }

  static Future<void> clearCache() async {
    await _cacheBox.clear();
  }

  static Future<void> removeCachedData(String key) async {
    await _cacheBox.delete(key);
  }

  // SharedPreferences helpers
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs.getString(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  static Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  // Clear all data
  static Future<void> clearAll() async {
    await _authBox.clear();
    await _settingsBox.clear();
    await _cacheBox.clear();
    await _prefs.clear();
  }

  // Check if user is logged in
  static bool get isLoggedIn {
    final token = getToken();
    return token != null && token.isNotEmpty;
  }
}
