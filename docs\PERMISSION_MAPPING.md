# 🛡️ SRSR Property Management - Permission Mapping

## 📋 Overview

This document provides a comprehensive mapping of all screens and widgets in the Flutter frontend to their corresponding permission requirements and role access levels. The permissions are based on the actual Flutter frontend implementation and existing database roles.

**✅ VERIFICATION STATUS: All permissions are correctly mapped to existing database roles and permissions!**

## 🎭 Existing Roles

Based on the database.sql file, the following roles exist in the system:

1. **admin** - System Administrator with full access (14 screens, 58 widgets)
2. **property_manager** - Property Manager with property management access (6 screens, 35 widgets)
3. **maintenance_staff** - Maintenance Staff with limited access (4 screens, 17 widgets)
4. **security_guard** - Security Guard with security-related access (4 screens, 16 widgets)
5. **househelp** - House Help with basic property access (4 screens, 12 widgets)
6. **office_manager** - Office Manager with office management access (1 screen, 2 widgets)
7. **site_supervisor** - Site Supervisor with site management access (1 screen, 2 widgets)

## 🔑 Permission Categories

### Core Permissions (from database.sql):
- `view_dashboard` - View main dashboard
- `manage_users` - Manage system users
- `manage_roles` - Manage user roles
- `manage_permissions` - Manage permissions
- `view_properties` - View properties
- `manage_properties` - Manage properties
- `view_maintenance` - View maintenance issues
- `manage_maintenance` - Manage maintenance issues
- `view_attendance` - View attendance records
- `manage_attendance` - Manage attendance records
- `view_security` - View security logs
- `manage_security` - Manage security logs
- `view_reports` - View system reports
- `manage_thresholds` - Manage system thresholds

## 📊 Current Statistics

- **Total Screens**: 16 (2 public + 7 main app + 7 admin)
- **Total Widgets**: 58 across all screens
- **Total Roles**: 7 distinct roles
- **Total Permissions Used**: 14 out of 16 available permissions
- **✅ All roles properly mapped**
- **✅ All permissions exist in database**

## 📱 Screen Permissions

### Public Screens
| Screen | Required Permissions | Allowed Roles | Description |
|--------|---------------------|---------------|-------------|
| `login` | None | Public | Login screen |
| `register` | None | Public | Registration screen |

### Main Application Screens
| Screen | Required Permissions | Allowed Roles | Description |
|--------|---------------------|---------------|-------------|
| `dashboard` | `view_dashboard` | All authenticated users | Main dashboard |
| `properties` | `view_properties` | admin, property_manager, office_manager, site_supervisor | Properties management |
| `maintenance` | `view_maintenance` | admin, property_manager, maintenance_staff | Maintenance issues |
| `attendance` | `view_attendance` | admin, property_manager, office_manager, site_supervisor | Attendance tracking |
| `fuel_monitoring` | `view_properties` | admin, property_manager, maintenance_staff | Fuel monitoring |

### Admin Screens
| Screen | Required Permissions | Allowed Roles | Description |
|--------|---------------------|---------------|-------------|
| `admin_dashboard` | `manage_users` | admin | Admin dashboard |
| `user_management` | `manage_users` | admin | User management |
| `threshold_config` | `manage_thresholds` | admin, property_manager | Threshold configuration |
| `role_management` | `manage_roles` | admin | Role management |
| `screen_management` | `manage_permissions` | admin | Screen permission management |
| `widget_management` | `manage_permissions` | admin | Widget permission management |
| `permission_config` | `manage_permissions` | admin | Permission configuration |

## 🧩 Widget Permissions

### Dashboard Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `property_stats` | dashboard | `view_properties` | admin, property_manager, office_manager, site_supervisor |
| `recent_activities` | dashboard | `view_dashboard` | All authenticated users |
| `maintenance_summary` | dashboard | `view_maintenance` | admin, property_manager, maintenance_staff |
| `attendance_summary` | dashboard | `view_attendance` | admin, property_manager, office_manager, site_supervisor |
| `fuel_summary` | dashboard | `view_properties` | admin, property_manager, maintenance_staff |
| `alerts_summary` | dashboard | `view_dashboard` | admin, property_manager, maintenance_staff, security_guard |

### Properties Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `property_list` | properties | `view_properties` | admin, property_manager, office_manager, site_supervisor |
| `property_details` | properties | `view_properties` | admin, property_manager, office_manager, site_supervisor |
| `add_property` | properties | `manage_properties` | admin, property_manager |
| `edit_property` | properties | `manage_properties` | admin, property_manager |
| `property_services` | properties | `view_properties` | admin, property_manager, maintenance_staff |

### Maintenance Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `issue_list` | maintenance | `view_maintenance` | admin, property_manager, maintenance_staff |
| `create_issue` | maintenance | `manage_maintenance` | admin, property_manager, maintenance_staff |
| `assign_issue` | maintenance | `manage_maintenance` | admin, property_manager |
| `issue_details` | maintenance | `view_maintenance` | admin, property_manager, maintenance_staff |
| `escalation_controls` | maintenance | `manage_maintenance` | admin, property_manager |

### Attendance Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `attendance_list` | attendance | `view_attendance` | admin, property_manager, office_manager, site_supervisor |
| `mark_attendance` | attendance | `manage_attendance` | admin, property_manager, office_manager, site_supervisor |
| `attendance_reports` | attendance | `view_attendance` | admin, property_manager, office_manager, site_supervisor |
| `attendance_summary` | attendance | `view_attendance` | admin, property_manager, office_manager, site_supervisor |

### Fuel Monitoring Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `fuel_logs` | fuel_monitoring | `view_properties` | admin, property_manager, maintenance_staff |
| `add_fuel_log` | fuel_monitoring | `manage_properties` | admin, property_manager, maintenance_staff |
| `fuel_analytics` | fuel_monitoring | `view_properties` | admin, property_manager, maintenance_staff |
| `diesel_additions` | fuel_monitoring | `manage_properties` | admin, property_manager, maintenance_staff |

### Admin Dashboard Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `user_stats` | admin_dashboard | `manage_users` | admin |
| `system_health` | admin_dashboard | `manage_users` | admin |
| `threshold_alerts` | admin_dashboard | `manage_thresholds` | admin |
| `quick_actions` | admin_dashboard | `manage_users` | admin |

### User Management Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `user_list` | user_management | `manage_users` | admin |
| `create_user` | user_management | `manage_users` | admin |
| `user_roles` | user_management | `manage_users` | admin |
| `user_permissions` | user_management | `manage_users` | admin |

### Navigation Widgets
| Widget | Screen | Required Permissions | Allowed Roles |
|--------|--------|---------------------|---------------|
| `dashboard_nav` | navigation | `view_dashboard` | All authenticated users |
| `properties_nav` | navigation | `view_properties` | admin, property_manager, office_manager, site_supervisor |
| `maintenance_nav` | navigation | `view_maintenance` | admin, property_manager, maintenance_staff |
| `attendance_nav` | navigation | `view_attendance` | admin, property_manager, office_manager, site_supervisor |
| `fuel_nav` | navigation | `view_properties` | admin, property_manager, maintenance_staff |
| `admin_nav` | navigation | `manage_users` | admin |
| `security_nav` | navigation | `view_security` | admin, property_manager, security_guard |
| `reports_nav` | navigation | `view_reports` | admin, property_manager |

## 📊 Statistics

- **Total Screens**: 14 (2 public + 5 main app + 7 admin)
- **Total Widgets**: 53 across all screens
- **Total Roles**: 7 distinct roles
- **Permission Categories**: 14 core permissions

## 🔄 Usage in Flutter

The Flutter frontend uses these permissions through:

1. **ConfigurablePermissionSection** - For dashboard widgets
2. **RoleBasedWidget** - For navigation and conditional UI
3. **PermissionWidget** - For specific permission checks
4. **DynamicRoleBasedWidget** - For runtime permission evaluation

## 🚀 Backend Integration

The backend provides these permissions through:

- `GET /api/permissions/screens` - Get screen permissions
- `GET /api/permissions/widgets` - Get widget permissions
- `PUT /api/permissions/screens/{screenName}` - Update screen permissions
- `PUT /api/permissions/widgets/{screenName}/{widgetName}` - Update widget permissions

## 🔧 Configuration

All permissions are seeded automatically when running:
```bash
node scripts/seed-permissions.js
```

This creates the complete permission matrix based on the actual Flutter frontend implementation and existing database roles.
