import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/validation/validation_rules.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/property.dart';
import '../providers/properties_providers.dart';

class PropertyFormDialog extends ConsumerStatefulWidget {
  final Property? property; // null for create, non-null for edit
  final VoidCallback? onSuccess;

  const PropertyFormDialog({
    super.key,
    this.property,
    this.onSuccess,
  });

  @override
  ConsumerState<PropertyFormDialog> createState() => _PropertyFormDialogState();
}

class _PropertyFormDialogState extends ConsumerState<PropertyFormDialog> {
  late FormGroup form;
  bool isLoading = false;

  bool get isEditing => widget.property != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    form = FormGroup(ValidationRules.createPropertyForm().cast<String, AbstractControl<dynamic>>());
    
    // If editing, populate form with existing data
    if (isEditing) {
      final property = widget.property!;
      form.control('name').value = property.name;
      form.control('type').value = property.type;
      form.control('address').value = property.address ?? '';
      form.control('description').value = property.description ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(isEditing ? 'Edit Property' : 'Create Property'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: ReactiveForm(
          formGroup: form,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Property Name
                ReactiveTextField<String>(
                  formControlName: 'name',
                  decoration: const InputDecoration(
                    labelText: 'Property Name *',
                    border: OutlineInputBorder(),
                    hintText: 'Enter property name',
                    prefixIcon: Icon(Icons.business),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Property name is required',
                    ValidationMessage.minLength: (_) => 'Name must be at least 2 characters',
                    ValidationMessage.maxLength: (_) => 'Name cannot exceed 100 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Property Type
                ReactiveDropdownField<String>(
                  formControlName: 'type',
                  decoration: const InputDecoration(
                    labelText: 'Property Type *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'office', child: Text('Office')),
                    DropdownMenuItem(value: 'site', child: Text('Construction Site')),
                    DropdownMenuItem(value: 'residential', child: Text('Residential')),
                    DropdownMenuItem(value: 'commercial', child: Text('Commercial')),
                    DropdownMenuItem(value: 'industrial', child: Text('Industrial')),
                    DropdownMenuItem(value: 'warehouse', child: Text('Warehouse')),
                  ],
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Property type is required',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Address
                ReactiveTextField<String>(
                  formControlName: 'address',
                  decoration: const InputDecoration(
                    labelText: 'Address',
                    border: OutlineInputBorder(),
                    hintText: 'Enter property address',
                    prefixIcon: Icon(Icons.location_on),
                  ),
                  maxLines: 2,
                  validationMessages: {
                    ValidationMessage.maxLength: (_) => 'Address cannot exceed 500 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Description
                ReactiveTextField<String>(
                  formControlName: 'description',
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                    hintText: 'Enter property description',
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                  validationMessages: {
                    ValidationMessage.maxLength: (_) => 'Description cannot exceed 1000 characters',
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _submitForm,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Create'),
        ),
      ],
    );
  }

  Future<void> _submitForm() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;
      
      if (isEditing) {
        // Update existing property
        final updatedProperty = widget.property!.copyWith(
          name: formValue['name'] as String,
          type: formValue['type'] as String,
          address: formValue['address'] as String?,
          description: formValue['description'] as String?,
        );

        await ref.read(propertiesNotifierProvider.notifier).updateProperty(updatedProperty);
        
        if (mounted) {
          Navigator.of(context).pop();
          AppUtils.showSuccessSnackBar(context, 'Property updated successfully');
        }
      } else {
        // Create new property
        final newProperty = Property(
          id: DateTime.now().millisecondsSinceEpoch.toString(), // Temporary ID
          name: formValue['name'] as String,
          type: formValue['type'] as String,
          address: formValue['address'] as String?,
          description: formValue['description'] as String?,
          isActive: true,
          createdAt: DateTime.now(),
        );

        await ref.read(propertiesNotifierProvider.notifier).addProperty(newProperty);
        
        if (mounted) {
          Navigator.of(context).pop();
          AppUtils.showSuccessSnackBar(context, 'Property created successfully');
        }
      }

      // Call success callback if provided
      if (widget.onSuccess != null) {
        widget.onSuccess!();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(
          context, 
          isEditing 
            ? 'Failed to update property: $e' 
            : 'Failed to create property: $e'
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
