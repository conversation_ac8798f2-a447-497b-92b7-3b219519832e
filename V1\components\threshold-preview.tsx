"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { calculateStatus, type ThresholdConfig } from "@/app/actions/threshold-config"

interface ThresholdPreviewProps {
  thresholds: ThresholdConfig[]
}

export function ThresholdPreview({ thresholds }: ThresholdPreviewProps) {
  const [testValue, setTestValue] = useState("")
  const [selectedArea, setSelectedArea] = useState("")
  const [selectedMetric, setSelectedMetric] = useState("")

  const functionalAreas = [...new Set(thresholds.map((t) => t.functional_area))]
  const metrics = selectedArea
    ? thresholds.filter((t) => t.functional_area === selectedArea).map((t) => t.metric_name)
    : []

  const getStatusForValue = () => {
    if (!testValue || !selectedArea || !selectedMetric) return null

    const relevantThresholds = thresholds.filter(
      (t) => t.functional_area === selectedArea && t.metric_name === selectedMetric,
    )

    return calculateStatus(Number.parseFloat(testValue), relevantThresholds)
  }

  const status = getStatusForValue()

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label>Functional Area</Label>
          <select
            className="w-full p-2 border rounded"
            value={selectedArea}
            onChange={(e) => {
              setSelectedArea(e.target.value)
              setSelectedMetric("")
            }}
          >
            <option value="">Select area</option>
            {functionalAreas.map((area) => (
              <option key={area} value={area}>
                {area}
              </option>
            ))}
          </select>
        </div>

        <div>
          <Label>Metric</Label>
          <select
            className="w-full p-2 border rounded"
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value)}
            disabled={!selectedArea}
          >
            <option value="">Select metric</option>
            {metrics.map((metric) => (
              <option key={metric} value={metric}>
                {metric}
              </option>
            ))}
          </select>
        </div>

        <div>
          <Label>Test Value</Label>
          <Input
            type="number"
            value={testValue}
            onChange={(e) => setTestValue(e.target.value)}
            placeholder="Enter value to test"
          />
        </div>
      </div>

      {status && (
        <div className="p-4 border rounded-lg">
          <div className="flex items-center gap-2">
            <span>Status for value {testValue}:</span>
            <Badge
              className={
                status === "green"
                  ? "bg-green-100 text-green-800"
                  : status === "orange"
                    ? "bg-orange-100 text-orange-800"
                    : "bg-red-100 text-red-800"
              }
            >
              {status.toUpperCase()}
            </Badge>
          </div>
        </div>
      )}
    </div>
  )
}
