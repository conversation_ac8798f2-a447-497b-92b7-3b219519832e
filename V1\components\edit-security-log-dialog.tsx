"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Sun, Moon, LogOut, LogIn } from "lucide-react"
import { updateSecurityGuardLog, type SecurityGuardLog } from "@/app/actions/security-guard-logs"
import { useToast } from "@/hooks/use-toast"

interface EditSecurityLogDialogProps {
  log: SecurityGuardLog | null
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  guards: string[]
}

export function EditSecurityLogDialog({ log, isOpen, onClose, onSuccess, guards }: EditSecurityLogDialogProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Form state
  const [guardName, setGuardName] = useState("")
  const [shift, setShift] = useState("")
  const [logDate, setLogDate] = useState("")
  const [leftTime, setLeftTime] = useState("")
  const [returnTime, setReturnTime] = useState("")
  const [replacement, setReplacement] = useState("")
  const [reason, setReason] = useState("")

  const reasons = ["Washroom", "Gardening", "Pool cleaning", "Other reason"]
  const shifts = ["Morning", "Night"]

  // Populate form when log changes
  useEffect(() => {
    if (log) {
      setGuardName(log.guard_name)
      setShift(log.shift)
      setLogDate(log.log_date)
      setLeftTime(log.out_time || "")
      setReturnTime(log.in_time || "")
      setReplacement(log.replacement || "")
      setReason(log.reason || "")
    }
  }, [log])

  const formatTime = (timeString: string) => {
    if (!timeString) return ""
    // Convert from HH:MM:SS to HH:MM for input
    return timeString.substring(0, 5)
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!log) return

    setIsSubmitting(true)

    try {
      const formData = new FormData()
      formData.append("log_id", log.id)
      formData.append("guard_name", guardName)
      formData.append("shift", shift)
      formData.append("log_date", logDate)
      formData.append("left_time", leftTime)
      formData.append("return_time", returnTime)
      formData.append("replacement", replacement)
      formData.append("reason", reason)

      const result = await updateSecurityGuardLog(formData)

      if (result.success) {
        toast({
          title: "Success",
          description: "Security guard log updated successfully",
        })
        onSuccess()
        onClose()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update log entry",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Update error:", error)
      toast({
        title: "Error",
        description: "Failed to update log entry",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!log) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Security Guard Log</DialogTitle>
          <DialogDescription>Update the security guard log entry details</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit_guard_name">Security Guard Name</Label>
              <Select value={guardName} onValueChange={setGuardName} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select guard" />
                </SelectTrigger>
                <SelectContent>
                  {guards.map((guard) => (
                    <SelectItem key={guard} value={guard}>
                      {guard}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_shift">Shift</Label>
              <Select value={shift} onValueChange={setShift} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select shift" />
                </SelectTrigger>
                <SelectContent>
                  {shifts.map((shiftOption) => (
                    <SelectItem key={shiftOption} value={shiftOption}>
                      <div className="flex items-center gap-2">
                        {shiftOption === "Morning" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                        {shiftOption} ({shiftOption === "Morning" ? "8:00 AM - 8:00 PM" : "8:00 PM - 8:00 AM"})
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_log_date">Date</Label>
              <Input
                id="edit_log_date"
                type="date"
                value={logDate}
                onChange={(e) => setLogDate(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_reason">Reason for Leaving Post</Label>
              <Select value={reason} onValueChange={setReason}>
                <SelectTrigger>
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  {reasons.map((reasonOption) => (
                    <SelectItem key={reasonOption} value={reasonOption}>
                      {reasonOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_left_time" className="flex items-center gap-2">
                <LogOut className="h-4 w-4 text-red-500" />
                Left Post Time
              </Label>
              <Input
                id="edit_left_time"
                type="time"
                value={formatTime(leftTime)}
                onChange={(e) => setLeftTime(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_return_time" className="flex items-center gap-2">
                <LogIn className="h-4 w-4 text-green-500" />
                Returned to Post Time
              </Label>
              <Input
                id="edit_return_time"
                type="time"
                value={formatTime(returnTime)}
                onChange={(e) => setReturnTime(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit_replacement">Replacement Details</Label>
            <Textarea
              id="edit_replacement"
              value={replacement}
              onChange={(e) => setReplacement(e.target.value)}
              placeholder="Who covered the post while away..."
              className="min-h-[80px]"
            />
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Log"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
