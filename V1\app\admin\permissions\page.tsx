import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"
import { PermissionManagement } from "@/components/permission-management"

export default async function PermissionsPage() {
  // Simple, direct admin check
  const user = await getCurrentUser()

  if (!user || (user.username !== "admin1" && user.role !== "admin")) {
    redirect("/unauthorized")
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Permission Management</h1>
        <p className="text-gray-600">Configure access permissions for different roles</p>
      </div>
      <PermissionManagement />
    </div>
  )
}
