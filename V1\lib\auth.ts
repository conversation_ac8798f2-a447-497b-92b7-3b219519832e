import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import * as bcrypt from "bcryptjs"
import { v4 as uuidv4 } from "uuid"
import { createClient } from "@/lib/supabase/server"

// Types
export type User = {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  is_active: boolean
}

export type Role = {
  id: number
  name: string
  description: string
}

export type Permission = {
  id: number
  url_pattern: string
  role_id: number
}

// Session duration in seconds (24 hours)
const SESSION_DURATION = 24 * 60 * 60

// Hash password
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10)
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// Create session
export async function createSession(userId: string): Promise<string> {
  const supabase = createClient()
  const token = uuidv4()
  const expiresAt = new Date()
  expiresAt.setSeconds(expiresAt.getSeconds() + SESSION_DURATION)

  await supabase.from("sessions").insert({
    user_id: userId,
    token,
    expires_at: expiresAt.toISOString(),
  })

  // Set cookie
  cookies().set("session_token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires: expiresAt,
    path: "/",
  })

  return token
}

// Get current session
export async function getCurrentSession() {
  const supabase = createClient()
  const token = cookies().get("session_token")?.value

  if (!token) {
    return null
  }

  const { data, error } = await supabase
    .from("sessions")
    .select("*")
    .eq("token", token)
    .gt("expires_at", new Date().toISOString())
    .single()

  if (error || !data) {
    cookies().delete("session_token")
    return null
  }

  return data
}

// Get current user
export async function getCurrentUser(): Promise<User | null> {
  const session = await getCurrentSession()
  if (!session) {
    return null
  }

  const supabase = createClient()
  const { data, error } = await supabase
    .from("users")
    .select("*")
    .eq("id", session.user_id)
    .eq("is_active", true)
    .single()

  if (error || !data) {
    return null
  }

  return data as User
}

// Get user roles
export async function getUserRoles(userId: string): Promise<Role[]> {
  const supabase = createClient()
  const { data, error } = await supabase.from("user_roles").select("role_id").eq("user_id", userId)

  if (error || !data || data.length === 0) {
    return []
  }

  const roleIds = data.map((ur) => ur.role_id)
  const { data: roles, error: rolesError } = await supabase.from("roles").select("*").in("id", roleIds)

  if (rolesError || !roles) {
    return []
  }

  return roles as Role[]
}

// Check if user has permission to access URL
export async function hasPermission(userId: string, url: string): Promise<boolean> {
  const supabase = createClient()

  console.log(`Checking permission for user ${userId} to access ${url}`)

  // First check if user is admin
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("role, username")
    .eq("id", userId)
    .single()

  if (userError || !userData) {
    console.log(`User not found or error: ${userError?.message}`)
    return false
  }

  console.log(`User role: ${userData.role}, username: ${userData.username}`)

  // admin1 username has access to EVERYTHING
  if (userData.username === "admin1") {
    console.log(`User is admin1, granting full access`)
    return true
  }

  // Users with admin role have access to admin routes
  if (url.startsWith("/admin") && userData.role === "admin") {
    console.log(`User has admin role, granting admin access`)
    return true
  }

  // Deny admin access to non-admin users
  if (url.startsWith("/admin")) {
    console.log(`Denying admin access to non-admin user`)
    return false
  }

  // For dashboard routes, allow users with specific roles
  if (url.startsWith("/dashboard")) {
    // Admin users can access everything
    if (userData.role === "admin") {
      return true
    }

    // Allow specific roles to access dashboard
    const allowedRoles = ["househelp", "maintenance", "security", "manager"]
    const hasAccess = allowedRoles.includes(userData.role)
    console.log(`Dashboard access for role ${userData.role}: ${hasAccess}`)
    return hasAccess
  }

  return false
}

// Logout
export async function logout() {
  const token = cookies().get("session_token")?.value

  if (token) {
    const supabase = createClient()
    await supabase.from("sessions").delete().eq("token", token)
    cookies().delete("session_token")
  }
}

// Auth middleware
export async function requireAuth() {
  const user = await getCurrentUser()

  if (!user) {
    redirect("/login")
  }

  return user
}

// Role middleware
export async function requireRole(roles: string[]) {
  const user = await requireAuth()

  // Admin users and admin1 bypass role checks
  if (user.role === "admin" || user.username === "admin1") {
    return user
  }

  // Check if user's role property is in the required roles
  if (roles.includes(user.role)) {
    return user
  }

  // Fallback to checking user_roles table
  const userRoles = await getUserRoles(user.id)
  const hasRole = userRoles.some((role) => roles.includes(role.name))

  if (!hasRole) {
    redirect("/unauthorized")
  }

  return user
}

// URL permission middleware - THIS IS THE KEY FUNCTION
export async function requirePermission(url: string) {
  const user = await requireAuth()

  console.log(`requirePermission called for user ${user.username} (${user.role}) accessing ${url}`)

  // Check if user has permission
  const hasAccess = await hasPermission(user.id, url)

  if (!hasAccess) {
    console.log(`Access denied for user ${user.username} to ${url}, redirecting to unauthorized`)
    redirect("/unauthorized")
  }

  console.log(`Access granted for user ${user.username} to ${url}`)
  return user
}
