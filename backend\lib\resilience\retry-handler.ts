/**
 * Enhanced retry and resilience patterns from V1
 * Implements exponential backoff, rate limiting detection, and graceful degradation
 */

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
  jitter?: boolean;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

export interface RateLimitInfo {
  isRateLimited: boolean;
  retryAfter?: number;
  errorType: 'rate_limit' | 'json_parse' | 'network' | 'unknown';
}

/**
 * Enhanced retry mechanism with exponential backoff and jitter
 * Based on V1's proven resilience patterns
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    backoffMultiplier = 2,
    jitter = true,
    retryCondition = defaultRetryCondition,
    onRetry,
  } = options;

  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Check if we should retry this error
      if (!retryCondition(error)) {
        break;
      }
      
      // Calculate delay with exponential backoff and optional jitter
      let delay = Math.min(baseDelay * Math.pow(backoffMultiplier, attempt), maxDelay);
      
      if (jitter) {
        // Add random jitter (±25% of delay)
        const jitterAmount = delay * 0.25;
        delay += (Math.random() - 0.5) * 2 * jitterAmount;
      }
      
      console.warn(
        `Operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${Math.round(delay)}ms:`,
        getErrorMessage(error)
      );
      
      if (onRetry) {
        onRetry(attempt + 1, error);
      }
      
      await sleep(delay);
    }
  }
  
  throw lastError;
}

/**
 * Default retry condition based on V1's patterns
 */
function defaultRetryCondition(error: any): boolean {
  const rateLimitInfo = detectRateLimit(error);
  
  // Always retry rate limits and network errors
  if (rateLimitInfo.isRateLimited) {
    return true;
  }
  
  // Retry on specific HTTP status codes
  if (error.response?.status) {
    const status = error.response.status;
    return status >= 500 || status === 429 || status === 408 || status === 503;
  }
  
  // Retry on network errors
  if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.code === 'ENOTFOUND') {
    return true;
  }
  
  return false;
}

/**
 * Detect rate limiting and parsing errors like V1
 */
export function detectRateLimit(error: any): RateLimitInfo {
  const errorMessage = getErrorMessage(error);
  const lowerMessage = errorMessage.toLowerCase();
  
  // Rate limiting detection patterns from V1
  if (
    lowerMessage.includes('too many requests') ||
    lowerMessage.includes('rate limit') ||
    lowerMessage.includes('429') ||
    error.response?.status === 429
  ) {
    const retryAfter = error.response?.headers?.['retry-after'];
    return {
      isRateLimited: true,
      retryAfter: retryAfter ? parseInt(retryAfter) * 1000 : undefined,
      errorType: 'rate_limit',
    };
  }
  
  // JSON parsing errors (common with rate limiting)
  if (
    error instanceof SyntaxError ||
    lowerMessage.includes('unexpected token') ||
    lowerMessage.includes('json') ||
    lowerMessage.includes('syntaxerror')
  ) {
    return {
      isRateLimited: true, // Treat as rate limit for retry purposes
      errorType: 'json_parse',
    };
  }
  
  // Network errors
  if (
    lowerMessage.includes('network') ||
    lowerMessage.includes('fetch') ||
    lowerMessage.includes('connection')
  ) {
    return {
      isRateLimited: true, // Treat as retryable
      errorType: 'network',
    };
  }
  
  return {
    isRateLimited: false,
    errorType: 'unknown',
  };
}

/**
 * Enhanced error handler with V1's resilience patterns
 */
export function createResilientHandler<T>(
  operation: () => Promise<T>,
  fallbackValue: T,
  options: RetryOptions & { 
    gracefulDegradation?: boolean;
    logErrors?: boolean;
  } = {}
): () => Promise<T> {
  const { gracefulDegradation = true, logErrors = true, ...retryOptions } = options;
  
  return async () => {
    try {
      return await withRetry(operation, retryOptions);
    } catch (error) {
      if (logErrors) {
        console.error('Operation failed after all retries:', getErrorMessage(error));
      }
      
      if (gracefulDegradation) {
        console.warn('Falling back to default value due to persistent errors');
        return fallbackValue;
      }
      
      throw error;
    }
  };
}

/**
 * Batch operation with individual error handling
 * Based on V1's batch processing patterns
 */
export async function withBatchResilience<T, R>(
  items: T[],
  operation: (item: T) => Promise<R>,
  options: {
    batchSize?: number;
    continueOnError?: boolean;
    retryOptions?: RetryOptions;
  } = {}
): Promise<Array<{ item: T; result?: R; error?: any }>> {
  const { batchSize = 10, continueOnError = true, retryOptions = {} } = options;
  const results: Array<{ item: T; result?: R; error?: any }> = [];
  
  // Process in batches to avoid overwhelming the system
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (item) => {
      try {
        const result = await withRetry(() => operation(item), retryOptions);
        return { item, result };
      } catch (error) {
        if (continueOnError) {
          console.warn(`Batch operation failed for item:`, item, getErrorMessage(error));
          return { item, error };
        }
        throw error;
      }
    });
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Add small delay between batches to be respectful
    if (i + batchSize < items.length) {
      await sleep(100);
    }
  }
  
  return results;
}

/**
 * Database operation wrapper with V1's resilience patterns
 */
export async function withDatabaseResilience<T>(
  operation: () => Promise<T>,
  fallbackValue?: T
): Promise<T> {
  const retryOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    backoffMultiplier: 2,
    retryCondition: (error) => {
      // Retry on database connection issues
      if (error.code === 'P1001' || error.code === 'P1008' || error.code === 'P1017') {
        return true;
      }
      
      // Retry on timeout errors
      if (error.code === 'P2024') {
        return true;
      }
      
      // Use default retry condition for other errors
      return defaultRetryCondition(error);
    },
    onRetry: (attempt, error) => {
      console.warn(`Database operation retry ${attempt}:`, error.code || error.message);
    },
  };
  
  if (fallbackValue !== undefined) {
    return createResilientHandler(operation, fallbackValue, retryOptions)();
  }
  
  return withRetry(operation, retryOptions);
}

/**
 * API call wrapper with rate limiting awareness
 */
export async function withApiResilience<T>(
  apiCall: () => Promise<T>,
  options: {
    endpoint?: string;
    fallbackValue?: T;
    respectRateLimit?: boolean;
  } = {}
): Promise<T> {
  const { endpoint, fallbackValue, respectRateLimit = true } = options;
  
  const retryOptions: RetryOptions = {
    maxRetries: 5, // More retries for API calls
    baseDelay: 1000,
    maxDelay: 60000, // Up to 1 minute for API rate limits
    retryCondition: (error) => {
      const rateLimitInfo = detectRateLimit(error);
      return rateLimitInfo.isRateLimited || defaultRetryCondition(error);
    },
    onRetry: (attempt, error) => {
      const rateLimitInfo = detectRateLimit(error);
      console.warn(
        `API call retry ${attempt} for ${endpoint || 'unknown endpoint'}:`,
        `Type: ${rateLimitInfo.errorType}, Message: ${getErrorMessage(error)}`
      );
    },
  };
  
  // Add initial delay to prevent rate limiting (like V1)
  if (respectRateLimit) {
    await sleep(Math.random() * 500 + 200); // 200-700ms random delay
  }
  
  if (fallbackValue !== undefined) {
    return createResilientHandler(apiCall, fallbackValue, retryOptions)();
  }
  
  return withRetry(apiCall, retryOptions);
}

/**
 * Utility functions
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function getErrorMessage(error: any): string {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.response?.data?.message) return error.response.data.message;
  if (error?.response?.statusText) return error.response.statusText;
  return 'Unknown error';
}

/**
 * Pre-configured resilient handlers for common operations
 */
export const resilientHandlers = {
  /**
   * For database queries that should return empty arrays on failure
   */
  queryWithEmptyFallback: <T>(query: () => Promise<T[]>) =>
    createResilientHandler(query, [] as T[], {
      maxRetries: 3,
      gracefulDegradation: true,
    }),
  
  /**
   * For operations that should return null on failure
   */
  queryWithNullFallback: <T>(query: () => Promise<T | null>) =>
    createResilientHandler(query, null, {
      maxRetries: 3,
      gracefulDegradation: true,
    }),
  
  /**
   * For critical operations that should not fail silently
   */
  criticalOperation: <T>(operation: () => Promise<T>) =>
    withRetry(operation, {
      maxRetries: 5,
      baseDelay: 2000,
      gracefulDegradation: false,
    }),
};
