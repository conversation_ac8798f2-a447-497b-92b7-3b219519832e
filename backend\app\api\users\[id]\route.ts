import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const updateUserSchema = Joi.object({
  email: Joi.string().email().optional(),
  full_name: Joi.string().min(2).optional(),
  phone: Joi.string().optional(),
  is_active: Joi.boolean().optional(),
  roles: Joi.array().items(Joi.string()).optional(),
});

async function getUserHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        fullName: true,
        phone: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const transformedUser = {
      id: user.id,
      email: user.email,
      full_name: user.fullName,
      phone: user.phone,
      is_active: user.isActive,
      roles: user.userRoles.map(ur => ({
        id: ur.role.id,
        name: ur.role.name,
        description: ur.role.description,
      })),
      created_at: user.createdAt,
      updated_at: user.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedUser),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch user');
  }
}

async function updateUserHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateUserSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const { email, full_name, phone, is_active, roles } = validation.data;

    // Check if email is being changed and if it already exists
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email },
      });

      if (emailExists) {
        return Response.json(
          createApiResponse(null, 'Email already exists', 'DUPLICATE_ENTRY'),
          { status: 409 }
        );
      }
    }

    // Update user basic info
    const updateData: any = {};
    if (email) updateData.email = email;
    if (full_name) updateData.fullName = full_name;
    if (phone !== undefined) updateData.phone = phone;
    if (is_active !== undefined) updateData.isActive = is_active;

    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
    });

    // Update roles if provided
    if (roles !== undefined) {
      // Remove existing roles
      await prisma.userRole.deleteMany({
        where: { userId: id },
      });

      if (roles.length > 0) {
        // Verify roles exist
        const existingRoles = await prisma.role.findMany({
          where: {
            name: { in: roles },
          },
        });

        if (existingRoles.length !== roles.length) {
          console.warn('Some roles not found:', roles.filter(r => !existingRoles.find(er => er.name === r)));
        }

        // Create new role assignments
        const userRoleData = existingRoles.map(role => ({
          userId: id,
          roleId: role.id,
          assignedBy: currentUser.id,
        }));

        await prisma.userRole.createMany({
          data: userRoleData,
        });
      }
    }

    // Get updated user with roles
    const userWithRoles = await prisma.user.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'User updated successfully',
        user: {
          id: userWithRoles!.id,
          email: userWithRoles!.email,
          full_name: userWithRoles!.fullName,
          phone: userWithRoles!.phone,
          is_active: userWithRoles!.isActive,
          roles: userWithRoles!.userRoles.map(ur => ur.role.name),
          updated_at: userWithRoles!.updatedAt,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update user');
  }
}

async function deleteUserHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Prevent self-deletion
    if (id === currentUser.id) {
      return Response.json(
        createApiResponse(null, 'Cannot delete your own account', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    // Verify user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Delete user (this will cascade delete user roles due to foreign key constraints)
    await prisma.user.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'User deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete user');
  }
}

export const GET = requireAuth(getUserHandler);
export const PUT = requireRole(['admin'])(updateUserHandler);
export const DELETE = requireRole(['admin'])(deleteUserHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
