import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/generator_fuel.dart';
import '../../../../shared/models/property.dart';
import '../../data/fuel_api_service.dart';
import '../../data/fuel_repository_impl.dart';
import '../../domain/fuel_repository.dart';

// API Service Provider
final fuelApiServiceProvider = Provider<FuelApiService>((ref) {
  return FuelApiService(DioClient.instance.dio);
});

// Repository Provider
final fuelRepositoryProvider = Provider<FuelRepository>((ref) {
  return FuelRepositoryImpl(ref.watch(fuelApiServiceProvider));
});

// Fuel Logs Provider
final fuelLogsProvider = StateNotifierProvider<FuelLogsNotifier, AsyncValue<List<GeneratorFuelLog>>>((ref) {
  return FuelLogsNotifier(ref.watch(fuelRepositoryProvider));
});

// All fuel logs
final allFuelLogsProvider = Provider<AsyncValue<List<GeneratorFuelLog>>>((ref) {
  return ref.watch(fuelLogsProvider);
});

// Fuel logs by property
final fuelLogsByPropertyProvider = Provider.family<AsyncValue<List<GeneratorFuelLog>>, String>((ref, propertyId) {
  final logsAsync = ref.watch(fuelLogsProvider);
  return logsAsync.when(
    data: (logs) => AsyncValue.data(logs.where((log) => log.propertyId == propertyId).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Fuel trends by time range
final fuelLogsTrendProvider = FutureProvider.family<List<GeneratorFuelLog>, String>((ref, timeRange) async {
  final repository = ref.watch(fuelRepositoryProvider);
  final days = _getTimeRangeDays(timeRange);
  final startDate = DateTime.now().subtract(Duration(days: days));
  return await repository.getFuelLogsByDateRange(startDate, DateTime.now());
});

// Fuel trends by property and time range
final fuelLogsTrendByPropertyProvider = FutureProvider.family<List<GeneratorFuelLog>, (String, String)>((ref, params) async {
  final (propertyId, timeRange) = params;
  final repository = ref.watch(fuelRepositoryProvider);
  final days = _getTimeRangeDays(timeRange);
  final startDate = DateTime.now().subtract(Duration(days: days));
  return await repository.getFuelLogsByPropertyAndDateRange(propertyId, startDate, DateTime.now());
});

// Critical fuel alerts
final criticalFuelAlertsProvider = Provider<AsyncValue<List<GeneratorFuelLog>>>((ref) {
  final logsAsync = ref.watch(fuelLogsProvider);
  return logsAsync.when(
    data: (logs) {
      // Get latest log for each property
      final latestLogs = <String, GeneratorFuelLog>{};
      for (final log in logs) {
        if (!latestLogs.containsKey(log.propertyId) ||
            log.recordedAt.isAfter(latestLogs[log.propertyId]!.recordedAt)) {
          latestLogs[log.propertyId] = log;
        }
      }

      // Filter critical logs
      final criticalLogs = latestLogs.values.where((log) => log.isCritical).toList();
      return AsyncValue.data(criticalLogs);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Fuel consumption analytics
final fuelConsumptionAnalyticsProvider = Provider.family<AsyncValue<Map<String, dynamic>>, String>((ref, timeRange) {
  final logsAsync = ref.watch(fuelLogsTrendProvider(timeRange));
  return logsAsync.when(
    data: (logs) {
      final analytics = _calculateFuelAnalytics(logs);
      return AsyncValue.data(analytics);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Properties provider (for fuel monitoring)
final propertiesProvider = Provider<AsyncValue<List<Property>>>((ref) {
  // This should be implemented to fetch properties
  // For now, return empty list
  return const AsyncValue.data([]);
});

class FuelLogsNotifier extends StateNotifier<AsyncValue<List<GeneratorFuelLog>>> {
  final FuelRepository _repository;

  FuelLogsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadLogs();
  }

  Future<void> loadLogs() async {
    try {
      state = const AsyncValue.loading();
      final logs = await _repository.getAllFuelLogs();
      state = AsyncValue.data(logs);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> addFuelLog(GeneratorFuelLog log) async {
    try {
      await _repository.createFuelLog(log);
      await loadLogs();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> createFuelLog(GeneratorFuelLog log) async {
    try {
      await _repository.createFuelLog(log);
      await loadLogs();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> updateFuelLog(GeneratorFuelLog log) async {
    try {
      await _repository.updateFuelLog(log);
      await loadLogs();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> deleteFuelLog(String logId) async {
    try {
      await _repository.deleteFuelLog(logId);
      await loadLogs();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> recordFuelReading(String propertyId, double fuelLevel, {
    double? consumptionRate,
    String? notes,
    DateTime? recordedAt,
  }) async {
    try {
      await _repository.recordFuelReading(
        propertyId,
        fuelLevel,
        consumptionRate: consumptionRate,
        notes: notes,
        recordedAt: recordedAt,
      );
      await loadLogs();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}

// Helper functions
int _getTimeRangeDays(String timeRange) {
  switch (timeRange) {
    case '7d':
      return 7;
    case '30d':
      return 30;
    case '90d':
      return 90;
    default:
      return 7;
  }
}

Map<String, dynamic> _calculateFuelAnalytics(List<GeneratorFuelLog> logs) {
  if (logs.isEmpty) {
    return {
      'totalConsumption': 0.0,
      'averageConsumptionRate': 0.0,
      'totalRefills': 0,
      'averageFuelLevel': 0.0,
      'efficiency': 0.0,
      'trends': <String, dynamic>{},
    };
  }

  // Sort logs by date
  final sortedLogs = List<GeneratorFuelLog>.from(logs)
    ..sort((a, b) => a.recordedAt.compareTo(b.recordedAt));

  double totalConsumption = 0.0;
  double totalConsumptionRate = 0.0;
  int consumptionRateCount = 0;
  int totalRefills = 0;
  double totalFuelLevel = 0.0;

  for (int i = 0; i < sortedLogs.length; i++) {
    final log = sortedLogs[i];

    // Calculate consumption
    if (i > 0) {
      final prevLog = sortedLogs[i - 1];
      final consumption = prevLog.fuelLevelLiters - log.fuelLevelLiters;
      if (consumption > 0) {
        totalConsumption += consumption;
      } else if (consumption < -50) {
        // Likely a refill (significant increase)
        totalRefills++;
      }
    }

    // Average consumption rate
    if (log.consumptionRate != null) {
      totalConsumptionRate += log.consumptionRate!;
      consumptionRateCount++;
    }

    totalFuelLevel += log.fuelLevelLiters;
  }

  final averageConsumptionRate = consumptionRateCount > 0
      ? totalConsumptionRate / consumptionRateCount
      : 0.0;

  final averageFuelLevel = totalFuelLevel / logs.length;

  // Calculate efficiency (fuel level vs consumption rate)
  final efficiency = averageConsumptionRate > 0
      ? (averageFuelLevel / averageConsumptionRate) * 100
      : 0.0;

  return {
    'totalConsumption': totalConsumption,
    'averageConsumptionRate': averageConsumptionRate,
    'totalRefills': totalRefills,
    'averageFuelLevel': averageFuelLevel,
    'efficiency': efficiency.clamp(0.0, 100.0),
    'trends': _calculateTrends(sortedLogs),
  };
}

Map<String, dynamic> _calculateTrends(List<GeneratorFuelLog> logs) {
  if (logs.length < 2) return {};

  final firstLog = logs.first;
  final lastLog = logs.last;

  final fuelLevelTrend = lastLog.fuelLevelLiters - firstLog.fuelLevelLiters;
  final timeDiff = lastLog.recordedAt.difference(firstLog.recordedAt).inHours;

  return {
    'fuelLevelChange': fuelLevelTrend,
    'timeSpanHours': timeDiff,
    'averageChangePerHour': timeDiff > 0 ? fuelLevelTrend / timeDiff : 0.0,
  };
}
