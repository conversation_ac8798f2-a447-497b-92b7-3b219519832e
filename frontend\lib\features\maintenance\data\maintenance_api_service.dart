import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../../core/constants/api_constants.dart';
import '../../../shared/models/maintenance_issue.dart';
import '../../../shared/models/api_response.dart';

part 'maintenance_api_service.g.dart';

@RestApi()
abstract class MaintenanceApiService {
  factory MaintenanceApiService(Dio dio) = _MaintenanceApiService;

  @GET(ApiConstants.maintenance)
  Future<ApiResponse<List<MaintenanceIssue>>> getMaintenanceIssues({
    @Query('property_id') String? propertyId,
    @Query('status') String? status,
    @Query('priority') String? priority,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @GET('${ApiConstants.maintenance}/{id}')
  Future<ApiResponse<MaintenanceIssue>> getMaintenanceIssueById(@Path('id') String id);

  @POST(ApiConstants.maintenance)
  Future<HttpResponse<dynamic>> createMaintenanceIssue(@Body() CreateMaintenanceIssueRequest request);

  @PUT('${ApiConstants.maintenance}/{id}')
  Future<ApiResponse<MaintenanceIssue>> updateMaintenanceIssue(
    @Path('id') String id,
    @Body() UpdateMaintenanceIssueRequest request,
  );

  @DELETE('${ApiConstants.maintenance}/{id}')
  Future<ApiResponse<VoidResponse>> deleteMaintenanceIssue(@Path('id') String id);

  @GET('${ApiConstants.maintenance}/property/{propertyId}')
  Future<ApiResponse<List<MaintenanceIssue>>> getMaintenanceIssuesByProperty(@Path('propertyId') String propertyId);

  @POST('${ApiConstants.maintenance}/{id}/assign')
  Future<ApiResponse<MaintenanceIssue>> assignMaintenanceIssue(
    @Path('id') String id,
    @Body() AssignMaintenanceIssueRequest request,
  );

  @POST('${ApiConstants.maintenance}/{id}/escalate')
  Future<ApiResponse<MaintenanceIssue>> escalateMaintenanceIssue(@Path('id') String id);
}

// Request models
class CreateMaintenanceIssueRequest {
  @JsonKey(name: 'property_id')
  final String propertyId;
  final String title;
  final String description;
  final String priority;
  final String status;
  @JsonKey(name: 'service_type')
  final String? serviceType;
  final String? department;
  @JsonKey(name: 'reported_by')
  final String reportedBy;
  @JsonKey(name: 'due_date')
  final DateTime? dueDate;

  const CreateMaintenanceIssueRequest({
    required this.propertyId,
    required this.title,
    required this.description,
    required this.priority,
    this.status = 'open',
    this.serviceType,
    this.department,
    required this.reportedBy,
    this.dueDate,
  });

  Map<String, dynamic> toJson() => {
        'property_id': propertyId,
        'title': title,
        'description': description,
        'priority': priority,
        // Don't send status - backend sets it automatically
        if (serviceType != null) 'service_type': serviceType,
        if (department != null) 'department': department,
        // Don't send reported_by - backend uses current user
        if (dueDate != null) 'due_date': dueDate!.toIso8601String(),
      };
}

class UpdateMaintenanceIssueRequest {
  final String? title;
  final String? description;
  final String? priority;
  final String? status;
  @JsonKey(name: 'service_type')
  final String? serviceType;
  final String? department;
  @JsonKey(name: 'assigned_to')
  final String? assignedTo;
  @JsonKey(name: 'due_date')
  final DateTime? dueDate;

  const UpdateMaintenanceIssueRequest({
    this.title,
    this.description,
    this.priority,
    this.status,
    this.serviceType,
    this.department,
    this.assignedTo,
    this.dueDate,
  });

  Map<String, dynamic> toJson() {
    final json = {
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (priority != null) 'priority': priority,
      if (status != null) 'status': status,
      if (serviceType != null) 'service_type': serviceType,
      if (department != null) 'department': department,
      if (assignedTo != null) 'assigned_to': assignedTo,
      if (dueDate != null) 'due_date': dueDate!.toIso8601String(),
    };
    print('🔧 MAINTENANCE API: UpdateMaintenanceIssueRequest.toJson() = $json');
    return json;
  }
}

class AssignMaintenanceIssueRequest {
  @JsonKey(name: 'assigned_to')
  final String assignedTo;
  final String? notes;

  const AssignMaintenanceIssueRequest({
    required this.assignedTo,
    this.notes,
  });

  Map<String, dynamic> toJson() => {
        'assigned_to': assignedTo,
        'notes': notes,
      };
}
