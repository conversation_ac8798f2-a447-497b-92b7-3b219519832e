import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, handleError, corsHeaders } from '@/lib/utils';
import {
  generateSystemDashboardMetrics,
  generatePropertyDashboardStatus
} from '@/lib/business-logic/dashboard-metrics';
import { withApiResilience } from '@/lib/resilience/retry-handler';

async function getDashboardStatusHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const url = new URL(request.url);
    const propertyId = url.searchParams.get('property_id');
    const includeDetails = url.searchParams.get('include_details') === 'true';
    const dashboardVersion = url.searchParams.get('version') || 'v1'; // Support v1/v2

    // If property_id is specified, return property-specific dashboard (V1 style)
    if (propertyId) {
      const propertyDashboard = await withApiResilience(
        () => generatePropertyDashboardStatus(propertyId),
        {
          endpoint: `dashboard/property/${propertyId}`,
          fallbackValue: {
            id: propertyId,
            name: 'Unknown Property',
            type: 'residential' as const,
            overallStatus: 'red' as const,
            functionalAreas: {
              electricity: {
                status: 'red' as const,
                metrics: [],
                issueCount: 1,
                lastUpdated: new Date(),
              },
              internet: {
                status: 'green' as const,
                metrics: [],
                issueCount: 0,
                lastUpdated: new Date(),
              },
              maintenance: {
                status: 'green' as const,
                metrics: [],
                issueCount: 0,
                lastUpdated: new Date(),
              },
              ott_services: {
                status: 'green' as const,
                metrics: [],
                issueCount: 0,
                lastUpdated: new Date(),
              },
            },
            lastUpdated: new Date(),
          }
        }
      );

      return Response.json(
        createApiResponse(propertyDashboard),
        {
          status: 200,
          headers: corsHeaders(),
        }
      );
    }

    // System-wide dashboard with V1's rich metrics
    const systemMetrics = await withApiResilience(
      () => generateSystemDashboardMetrics(),
      {
        endpoint: 'dashboard/system',
        fallbackValue: {
          properties: { total: 0, operational: 0, warning: 0, critical: 0 },
          maintenance_issues: { total: 0, open: 0, in_progress: 0, critical: 0 },
          recent_alerts: [],
          system_health: { overall_score: 0, uptime_percentage: 0, active_services: 0, total_services: 0 },
        }
      }
    );

    // Enhanced data for Dashboard V2
    let enhancedData = {};
    if (dashboardVersion === 'v2') {
      enhancedData = await generateV2DashboardData(currentUser);
    }

    // Enhanced response with V1-style rich data
    const responseData = {
      ...systemMetrics,
      ...enhancedData,
      // Add V1-style summary metrics
      summary: {
        overall_health: systemMetrics.system_health.overall_score,
        critical_alerts: systemMetrics.recent_alerts.filter(alert => alert.severity === 'critical').length,
        properties_needing_attention: systemMetrics.properties.warning + systemMetrics.properties.critical,
        system_status: systemMetrics.system_health.overall_score >= 90 ? 'excellent' :
                      systemMetrics.system_health.overall_score >= 75 ? 'good' :
                      systemMetrics.system_health.overall_score >= 50 ? 'fair' : 'poor',
      },
      // Add timestamp for cache management
      generated_at: new Date(),
      cache_duration: 300, // 5 minutes cache suggestion
    };

    // Include detailed property breakdowns if requested
    if (includeDetails) {
      const activeProperties = await withApiResilience(
        () => prisma.property.findMany({
          where: { isActive: true },
          select: { id: true, name: true, type: true },
          take: 10, // Limit for performance
        }),
        {
          endpoint: 'dashboard/properties',
          fallbackValue: []
        }
      );

      const propertyDetails = await Promise.allSettled(
        activeProperties.map(property =>
          withApiResilience(
            () => generatePropertyDashboardStatus(property.id),
            {
              endpoint: `dashboard/property/${property.id}`,
              fallbackValue: null
            }
          )
        )
      );

      responseData.property_details = propertyDetails
        .filter((result): result is PromiseFulfilledResult<any> =>
          result.status === 'fulfilled' && result.value !== null
        )
        .map(result => result.value);
    }

    return Response.json(
      createApiResponse(responseData),
      {
        status: 200,
        headers: {
          ...corsHeaders(),
          'Cache-Control': 'public, max-age=300', // 5 minutes cache
        },
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch dashboard status', 'dashboard-status');
  }
}

async function generateV2DashboardData(currentUser: any) {
  try {
    // Get user's accessible properties
    const userProperties = await prisma.property.findMany({
      where: { isActive: true },
      include: {
        propertyFunctions: true,
        services: true,
        maintenanceIssues: {
          where: { status: { in: ['OPEN', 'IN_PROGRESS'] } },
          take: 5,
        },
        alerts: {
          where: { isResolved: false },
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });

    // Calculate V2-specific metrics
    const v2Metrics = {
      insights: {
        trending_up: ['fuel_efficiency', 'attendance_rate'],
        trending_down: ['maintenance_costs', 'downtime'],
        critical_attention: userProperties
          .filter(p => p.alerts.some(a => a.severity === 'critical'))
          .map(p => ({ property_id: p.id, property_name: p.name, alert_count: p.alerts.length })),
      },
      real_time: {
        active_properties: userProperties.filter(p => p.services.some(s => s.status === 'OPERATIONAL')).length,
        total_alerts: userProperties.reduce((sum, p) => sum + p.alerts.length, 0),
        maintenance_pending: userProperties.reduce((sum, p) => sum + p.maintenanceIssues.length, 0),
        last_updated: new Date(),
      },
      property_functions: userProperties.map(property => ({
        property_id: property.id,
        property_name: property.name,
        property_type: property.type,
        enabled_functions: property.propertyFunctions
          .filter(pf => pf.isEnabled)
          .map(pf => pf.functionName),
        function_count: property.propertyFunctions.filter(pf => pf.isEnabled).length,
      })),
      quick_actions: [
        { action: 'add_maintenance', label: 'Report Issue', icon: 'build', enabled: true },
        { action: 'check_attendance', label: 'Mark Attendance', icon: 'people', enabled: true },
        { action: 'fuel_reading', label: 'Fuel Reading', icon: 'local_gas_station', enabled: true },
        { action: 'emergency_alert', label: 'Emergency', icon: 'warning', enabled: true },
      ],
    };

    return { v2_data: v2Metrics };
  } catch (error) {
    console.error('Error generating V2 dashboard data:', error);
    return { v2_data: null };
  }
}

export const GET = requireAuth(getDashboardStatusHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
