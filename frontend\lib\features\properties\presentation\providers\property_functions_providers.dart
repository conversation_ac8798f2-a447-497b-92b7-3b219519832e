import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../data/property_functions_api_service.dart';

// API Service Provider
final propertyFunctionsApiServiceProvider = Provider<PropertyFunctionsApiService>((ref) {
  return PropertyFunctionsApiService(DioClient.instance.dio);
});

// Property Functions Provider
final propertyFunctionsProvider = FutureProvider.family<PropertyFunctionsData, String>((ref, propertyId) async {
  try {
    final apiService = ref.read(propertyFunctionsApiServiceProvider);
    final response = await apiService.getPropertyFunctions(propertyId);

    return response.data;
  } catch (error) {
    rethrow;
  }
});

// Enabled Functions Provider - Returns only enabled functions for a property
final enabledPropertyFunctionsProvider = Provider.family<AsyncValue<List<PropertyFunction>>, String>((ref, propertyId) {
  final functionsAsync = ref.watch(propertyFunctionsProvider(propertyId));

  return functionsAsync.when(
    data: (data) => AsyncValue.data(
      data.functions.where((f) => f.isEnabled).toList()
        ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder)),
    ),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Function Availability Checker
final isFunctionEnabledProvider = Provider.family<AsyncValue<bool>, ({String propertyId, String functionName})>((ref, params) {
  final functionsAsync = ref.watch(propertyFunctionsProvider(params.propertyId));

  return functionsAsync.when(
    data: (data) {
      final function = data.functions.firstWhere(
        (f) => f.functionName == params.functionName,
        orElse: () => PropertyFunction(
          functionName: params.functionName,
          isEnabled: false,
          configuration: {},
          displayOrder: 999,
        ),
      );
      return AsyncValue.data(function.isEnabled);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Property Functions Update Provider
final propertyFunctionsUpdateProvider = StateNotifierProvider.family<PropertyFunctionsUpdateNotifier, AsyncValue<PropertyFunctionsData?>, String>(
  (ref, propertyId) => PropertyFunctionsUpdateNotifier(ref, propertyId),
);

class PropertyFunctionsUpdateNotifier extends StateNotifier<AsyncValue<PropertyFunctionsData?>> {
  final Ref ref;
  final String propertyId;

  PropertyFunctionsUpdateNotifier(this.ref, this.propertyId) : super(const AsyncValue.data(null));

  Future<void> updatePropertyFunctions(List<PropertyFunctionUpdate> functions) async {
    state = const AsyncValue.loading();

    try {
      final apiService = ref.read(propertyFunctionsApiServiceProvider);
      final request = UpdatePropertyFunctionsRequest(functions: functions);

      final response = await apiService.updatePropertyFunctions(propertyId, request);

      // Invalidate the property functions provider to refresh data
      ref.invalidate(propertyFunctionsProvider(propertyId));

      state = AsyncValue.data(response.data);
    } catch (error, stack) {
      state = AsyncValue.error(error, stack);
    }
  }

  Future<void> toggleFunction(String functionName, bool enabled) async {
    final currentFunctionsAsync = ref.read(propertyFunctionsProvider(propertyId));

    await currentFunctionsAsync.when(
      data: (data) async {
        final updatedFunctions = data.functions.map((f) {
          if (f.functionName == functionName) {
            return PropertyFunctionUpdate(
              functionName: f.functionName,
              isEnabled: enabled,
              configuration: f.configuration,
              displayOrder: f.displayOrder,
            );
          }
          return PropertyFunctionUpdate(
            functionName: f.functionName,
            isEnabled: f.isEnabled,
            configuration: f.configuration,
            displayOrder: f.displayOrder,
          );
        }).toList();

        await updatePropertyFunctions(updatedFunctions);
      },
      loading: () async {
        throw Exception('Cannot update functions while loading');
      },
      error: (error, stack) async {
        throw Exception('Cannot update functions due to error: $error');
      },
    );
  }

  Future<void> reorderFunctions(List<String> orderedFunctionNames) async {
    final currentFunctionsAsync = ref.read(propertyFunctionsProvider(propertyId));

    await currentFunctionsAsync.when(
      data: (data) async {
        final updatedFunctions = <PropertyFunctionUpdate>[];

        // Add functions in the new order
        for (int i = 0; i < orderedFunctionNames.length; i++) {
          final functionName = orderedFunctionNames[i];
          final existingFunction = data.functions.firstWhere(
            (f) => f.functionName == functionName,
            orElse: () => PropertyFunction(
              functionName: functionName,
              isEnabled: true,
              configuration: {},
              displayOrder: i,
            ),
          );

          updatedFunctions.add(PropertyFunctionUpdate(
            functionName: existingFunction.functionName,
            isEnabled: existingFunction.isEnabled,
            configuration: existingFunction.configuration,
            displayOrder: i,
          ));
        }

        // Add any remaining functions that weren't in the ordered list
        for (final function in data.functions) {
          if (!orderedFunctionNames.contains(function.functionName)) {
            updatedFunctions.add(PropertyFunctionUpdate(
              functionName: function.functionName,
              isEnabled: function.isEnabled,
              configuration: function.configuration,
              displayOrder: function.displayOrder,
            ));
          }
        }

        await updatePropertyFunctions(updatedFunctions);
      },
      loading: () async {
        throw Exception('Cannot reorder functions while loading');
      },
      error: (error, stack) async {
        throw Exception('Cannot reorder functions due to error: $error');
      },
    );
  }
}

// Helper provider for property function navigation
final propertyFunctionNavigationProvider = Provider.family<String?, ({String propertyId, String functionName})>((ref, params) {
  final isEnabledAsync = ref.watch(isFunctionEnabledProvider(params));

  return isEnabledAsync.when(
    data: (isEnabled) {
      if (!isEnabled) return null;

      // Return the navigation route for the function
      switch (params.functionName) {
        case 'attendance':
          return '/property/${params.propertyId}/hub/attendance';
        case 'maintenance':
          return '/property/${params.propertyId}/hub/maintenance';
        case 'fuel':
          return '/property/${params.propertyId}/hub/fuel';
        case 'ott':
          return '/property/${params.propertyId}/hub/ott';
        case 'security':
          return '/property/${params.propertyId}/hub/security';
        case 'uptime':
          return '/property/${params.propertyId}/hub/uptime';
        case 'diesel':
          return '/property/${params.propertyId}/hub/diesel';
        default:
          return null;
      }
    },
    loading: () => null,
    error: (error, stack) => null,
  );
});
