"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"
import type { Permission } from "@/lib/auth"

// Get all permissions
export async function getPermissions(): Promise<Permission[]> {
  const supabase = createServerClient()

  const { data, error } = await supabase.from("permissions").select("*").order("url_pattern")

  if (error) {
    console.error("Error fetching permissions:", error)
    throw new Error("Failed to fetch permissions")
  }

  return data as Permission[]
}

// Create permission
export async function createPermission({
  urlPattern,
  roleId,
}: {
  urlPattern: string
  roleId: number
}) {
  const supabase = createServerClient()

  // Check if permission already exists
  const { data: existingPermission } = await supabase
    .from("permissions")
    .select("id")
    .eq("url_pattern", urlPattern)
    .eq("role_id", roleId)
    .single()

  if (existingPermission) {
    return { success: false, error: "Permission already exists for this role and URL pattern" }
  }

  const { error } = await supabase.from("permissions").insert({
    url_pattern: urlPattern,
    role_id: roleId,
  })

  if (error) {
    console.error("Error creating permission:", error)
    return { success: false, error: "Failed to create permission" }
  }

  revalidatePath("/admin/permissions")
  return { success: true }
}

// Delete permission
export async function deletePermission(id: number) {
  const supabase = createServerClient()

  const { error } = await supabase.from("permissions").delete().eq("id", id)

  if (error) {
    console.error("Error deleting permission:", error)
    return { success: false, error: "Failed to delete permission" }
  }

  revalidatePath("/admin/permissions")
  return { success: true }
}
