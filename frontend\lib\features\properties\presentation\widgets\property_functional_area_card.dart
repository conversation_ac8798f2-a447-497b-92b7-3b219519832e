import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

class FunctionalAreaStat {
  final String label;
  final String value;
  final Color color;

  const FunctionalAreaStat(this.label, this.value, this.color);
}

class PropertyFunctionalAreaCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final bool hasChanges;
  final List<FunctionalAreaStat> stats;
  final VoidCallback onTap;

  const PropertyFunctionalAreaCard({
    super.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.hasChanges,
    required this.stats,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: hasChanges ? 4 : 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: hasChanges 
              ? Border.all(color: color.withValues(alpha: 0.3), width: 2)
              : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with icon and title
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        icon,
                        color: color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding / 2),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (hasChanges)
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.orange.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.orange.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                'Has Changes',
                                style: TextStyle(
                                  color: Colors.orange[700],
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.defaultPadding),
                
                // Stats Grid
                _buildStatsGrid(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatsGrid(BuildContext context) {
    return Column(
      children: [
        // First row - up to 2 stats
        if (stats.isNotEmpty)
          Row(
            children: [
              Expanded(child: _buildStatItem(context, stats[0])),
              if (stats.length > 1) ...[
                const SizedBox(width: AppConstants.defaultPadding / 2),
                Expanded(child: _buildStatItem(context, stats[1])),
              ],
            ],
          ),
        
        // Second row - remaining stats
        if (stats.length > 2) ...[
          const SizedBox(height: AppConstants.defaultPadding / 2),
          Row(
            children: [
              if (stats.length > 2)
                Expanded(child: _buildStatItem(context, stats[2])),
              if (stats.length > 3) ...[
                const SizedBox(width: AppConstants.defaultPadding / 2),
                Expanded(child: _buildStatItem(context, stats[3])),
              ],
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildStatItem(BuildContext context, FunctionalAreaStat stat) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: stat.color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: stat.color.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            stat.label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
              fontSize: 11,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            stat.value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: stat.color,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
