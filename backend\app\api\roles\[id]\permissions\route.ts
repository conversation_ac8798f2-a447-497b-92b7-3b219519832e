import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireRole } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const addPermissionSchema = Joi.object({
  permission_id: Joi.string().uuid().required(),
});

async function getRolePermissionsHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id: roleId } = context.params;

    // Verify role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId },
      include: {
        permissions: {
          include: {
            permission: {
              select: {
                id: true,
                name: true,
                description: true,
                resource: true,
                action: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (!role) {
      return Response.json(
        createApiResponse(null, 'Role not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    const permissions = role.permissions.map(rp => ({
      id: rp.permission.id,
      name: rp.permission.name,
      description: rp.permission.description,
      resource: rp.permission.resource,
      action: rp.permission.action,
      assigned_at: rp.createdAt,
      created_at: rp.permission.createdAt,
    }));

    return Response.json(
      createApiResponse({
        role: {
          id: role.id,
          name: role.name,
          description: role.description,
        },
        permissions,
        total: permissions.length,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch role permissions');
  }
}

async function addRolePermissionHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id: roleId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(addPermissionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400, headers: corsHeaders() }
      );
    }

    const { permission_id } = validation.data;

    // Verify role exists
    const role = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      return Response.json(
        createApiResponse(null, 'Role not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    // Verify permission exists
    const permission = await prisma.permission.findUnique({
      where: { id: permission_id },
    });

    if (!permission) {
      return Response.json(
        createApiResponse(null, 'Permission not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    // Check if permission is already assigned to role
    const existingRolePermission = await prisma.rolePermission.findUnique({
      where: {
        roleId_permissionId: {
          roleId: roleId,
          permissionId: permission_id,
        },
      },
    });

    if (existingRolePermission) {
      return Response.json(
        createApiResponse(null, 'Permission already assigned to role', 'CONFLICT'),
        { status: 409, headers: corsHeaders() }
      );
    }

    // Add permission to role
    const rolePermission = await prisma.rolePermission.create({
      data: {
        roleId: roleId,
        permissionId: permission_id,
        createdBy: currentUser.id,
      },
      include: {
        permission: {
          select: {
            id: true,
            name: true,
            description: true,
            resource: true,
            action: true,
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Permission added to role successfully',
        role_permission: {
          role_id: roleId,
          permission: {
            id: rolePermission.permission.id,
            name: rolePermission.permission.name,
            description: rolePermission.permission.description,
            resource: rolePermission.permission.resource,
            action: rolePermission.permission.action,
          },
          assigned_at: rolePermission.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to add permission to role');
  }
}

async function removeRolePermissionHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id: roleId } = context.params;
    const url = new URL(request.url);
    const permissionId = url.searchParams.get('permission_id');

    if (!permissionId) {
      return Response.json(
        createApiResponse(null, 'Permission ID is required', 'VALIDATION_ERROR'),
        { status: 400, headers: corsHeaders() }
      );
    }

    // Verify role permission exists
    const rolePermission = await prisma.rolePermission.findUnique({
      where: {
        roleId_permissionId: {
          roleId: roleId,
          permissionId: permissionId,
        },
      },
    });

    if (!rolePermission) {
      return Response.json(
        createApiResponse(null, 'Role permission not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    // Remove permission from role
    await prisma.rolePermission.delete({
      where: {
        roleId_permissionId: {
          roleId: roleId,
          permissionId: permissionId,
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Permission removed from role successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to remove permission from role');
  }
}

// GET /api/roles/{id}/permissions - Get role permissions
export const GET = requireRole(['admin'])(getRolePermissionsHandler);

// POST /api/roles/{id}/permissions - Add permission to role
export const POST = requireRole(['admin'])(addRolePermissionHandler);

// DELETE /api/roles/{id}/permissions?permission_id={id} - Remove permission from role
export const DELETE = requireRole(['admin'])(removeRolePermissionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
