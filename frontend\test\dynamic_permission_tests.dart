import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

// Import core auth models and providers
import 'package:srsr_property_management/core/auth/models/user_role.dart' as core_models;
import 'package:srsr_property_management/features/auth/presentation/providers/auth_providers.dart';
import 'package:srsr_property_management/core/auth/providers/permission_providers.dart';
import 'package:srsr_property_management/core/auth/widgets/dynamic_role_based_widget.dart';

// Import API services
import 'package:srsr_property_management/features/auth/data/auth_api_service.dart';
import 'package:srsr_property_management/features/admin/data/role_management_api_service.dart' as role_api;
import 'package:srsr_property_management/features/admin/data/user_management_api_service.dart';

// Import models
import 'package:srsr_property_management/shared/models/user.dart';
import 'package:srsr_property_management/shared/models/api_response.dart';
import 'package:srsr_property_management/core/constants/api_constants.dart';

void main() {
  group('Dynamic Permission Tests', () {
    late Dio dio;
    late AuthApiService authService;
    late role_api.RoleManagementApiService roleService;
    late UserManagementApiService userService;
    late ProviderContainer container;

    // Test data
    String? adminToken;
    User? adminUser;
    String? testRoleId;
    String? testUserId;
    String? testPermissionId;

    setUpAll(() async {
      // Configure Dio for real API calls
      dio = Dio(BaseOptions(
        baseUrl: 'http://192.168.1.3:3000',
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {'Content-Type': 'application/json'},
      ));

      // Add logging interceptor
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('[PERMISSION_TEST] $obj'),
      ));

      // Initialize services
      authService = AuthApiService(dio);
      roleService = role_api.RoleManagementApiService(dio);
      userService = UserManagementApiService(dio);
    });

    setUp(() {
      // Create fresh provider container for each test
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Setup and Authentication', () {
      test('authenticate or create admin user for permission testing', () async {
        try {
          // First try to authenticate with existing admin
          final loginRequest = LoginRequest(
            email: '<EMAIL>',
            password: 'admin123',
          );

          final result = await authService.login(loginRequest);
          adminToken = result.token;
          adminUser = result.user;

          print('✅ Admin authenticated for permission testing: ${adminUser!.fullName}');
        } catch (e) {
          print('⚠️ Admin login failed, will create test admin: $e');

          // If admin doesn't exist, create one for testing
          try {
            final createRequest = CreateUserRequest(
              email: '<EMAIL>',
              fullName: 'Test Admin for Permissions',
              password: 'testadmin123',
              roles: ['admin'],
            );

            await userService.createUser(createRequest);

            // Now authenticate with the created admin
            final loginRequest = LoginRequest(
              email: '<EMAIL>',
              password: 'testadmin123',
            );

            final result = await authService.login(loginRequest);
            adminToken = result.token;
            adminUser = result.user;

            print('✅ Test admin created and authenticated: ${adminUser!.fullName}');
          } catch (createError) {
            fail('❌ Failed to create test admin: $createError');
          }
        }

        // Set auth header for subsequent requests
        dio.options.headers['Authorization'] = 'Bearer $adminToken';
      });
    });

    group('Dynamic Role Creation and Permission Tests', () {
      test('create new custom role with specific permissions', () async {
        if (adminToken == null) {
          fail('❌ Admin authentication required');
        }

        try {
          // Create a unique role name
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final roleName = 'Test_Role_$timestamp';

          final request = role_api.CreateRoleRequest(
            name: roleName,
            description: 'Test role created for permission testing',
            permissions: [
              'properties.read',
              'maintenance.read',
              'attendance.read',
            ],
          );

          final result = await roleService.createRole(request);
          expect(result.success, true);
          expect(result.data, isNotNull);

          testRoleId = result.data!.id;

          print('✅ Custom role created: $roleName (ID: $testRoleId)');
          print('   Permissions: ${request.permissions}');
        } catch (e) {
          fail('❌ Role creation failed: $e');
        }
      });

      test('create test user for role assignment', () async {
        if (adminToken == null) {
          fail('❌ Admin authentication required');
        }

        try {
          // Create a unique test user
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          final email = 'testuser_$<EMAIL>';

          final request = CreateUserRequest(
            email: email,
            fullName: 'Test User $timestamp',
            password: 'testpass123',
            roles: [], // No roles initially
          );

          final result = await userService.createUser(request);
          expect(result.response.statusCode, 201);

          // Extract user ID from response
          final responseData = result.data as Map<String, dynamic>;
          final userData = responseData['data']['user'] as Map<String, dynamic>;
          testUserId = userData['id'];

          print('✅ Test user created: $email (ID: $testUserId)');
        } catch (e) {
          fail('❌ Test user creation failed: $e');
        }
      });

      test('assign custom role to test user', () async {
        if (adminToken == null || testRoleId == null || testUserId == null) {
          fail('❌ Required test data not available');
        }

        try {
          final request = role_api.AssignRolesRequest(
            roleIds: [testRoleId!],
            replaceExisting: true,
          );

          final result = await roleService.assignUserRoles(testUserId!, request);
          expect(result.success, true);

          print('✅ Custom role assigned to test user');
        } catch (e) {
          fail('❌ Role assignment failed: $e');
        }
      });

      test('verify user has correct permissions after role assignment', () async {
        if (adminToken == null || testUserId == null) {
          fail('❌ Required test data not available');
        }

        try {
          // Get user roles and permissions
          final result = await roleService.getUserRoles(testUserId!);
          expect(result.success, true);
          expect(result.data, isNotNull);

          final userRoles = result.data!.roles;
          expect(userRoles, isNotEmpty);

          // Find our test role
          final testRole = userRoles.firstWhere(
            (role) => role.id == testRoleId,
            orElse: () => throw Exception('Test role not found in user roles'),
          );

          expect(testRole.name, contains('Test_Role_'));

          print('✅ User has correct role assignment');
          print('   Role: ${testRole.name}');
        } catch (e) {
          fail('❌ Permission verification failed: $e');
        }
      });
    });

    group('Dynamic Permission Widget Tests', () {
      testWidgets('DynamicRoleBasedWidget responds to new permissions', (WidgetTester tester) async {
        if (testUserId == null) {
          markTestSkipped('Test user not available');
          return;
        }

        // Create a mock user with our test role
        final testUser = User(
          id: testUserId!,
          email: '<EMAIL>',
          fullName: 'Test User',
          roles: ['Test_Role'],
          isActive: true,
          createdAt: DateTime.now(),
        );

        // Override auth state with test user
        final authStateOverride = AuthState(
          isAuthenticated: true,
          user: testUser,
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => AuthNotifier(ref.read(authRepositoryProvider))..state = authStateOverride),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: Column(
                  children: [
                    // Test permissions that should be granted
                    DynamicRoleBasedWidget(
                      requiredPermissions: const ['properties.read'],
                      child: const Text('Properties Read Access'),
                      fallback: const Text('No Properties Access'),
                      loadingWidget: const CircularProgressIndicator(),
                    ),
                    // Test permissions that should be denied
                    DynamicRoleBasedWidget(
                      requiredPermissions: const ['users.create'],
                      child: const Text('Create User Access'),
                      fallback: const Text('No User Creation Access'),
                      loadingWidget: const CircularProgressIndicator(),
                    ),
                    // Test multiple permissions (should be granted)
                    DynamicRoleBasedWidget(
                      requiredPermissions: const ['properties.read', 'maintenance.read'],
                      requireAll: true,
                      child: const Text('Multiple Permissions Access'),
                      fallback: const Text('Missing Some Permissions'),
                      loadingWidget: const CircularProgressIndicator(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should see granted permissions
        expect(find.text('Properties Read Access'), findsOneWidget);
        expect(find.text('Multiple Permissions Access'), findsOneWidget);

        // Should see denied permissions fallback
        expect(find.text('No User Creation Access'), findsOneWidget);

        // Should not see fallbacks for granted permissions
        expect(find.text('No Properties Access'), findsNothing);
        expect(find.text('Missing Some Permissions'), findsNothing);

        print('✅ Dynamic permission widget test passed');
      });
    });

    group('Permission Modification Tests', () {
      test('modify role permissions and verify changes', () async {
        if (adminToken == null || testRoleId == null) {
          fail('❌ Required test data not available');
        }

        try {
          // Update role with additional permissions
          final updateRequest = role_api.UpdateRoleRequest(
            name: 'Updated_Test_Role',
            description: 'Updated test role with more permissions',
            permissions: [
              'properties.read',
              'properties.create', // New permission
              'maintenance.read',
              'maintenance.update', // New permission
              'attendance.read',
            ],
          );

          final result = await roleService.updateRole(testRoleId!, updateRequest);
          expect(result.success, true);

          print('✅ Role permissions updated successfully');
          print('   New permissions: ${updateRequest.permissions}');
        } catch (e) {
          fail('❌ Role permission update failed: $e');
        }
      });

      test('verify user inherits updated permissions', () async {
        if (adminToken == null || testUserId == null) {
          fail('❌ Required test data not available');
        }

        try {
          // Get updated user permissions
          final result = await roleService.getUserRoles(testUserId!);
          expect(result.success, true);

          // Verify the user now has the updated permissions
          final userRoles = result.data!.roles;
          final testRole = userRoles.firstWhere(
            (role) => role.id == testRoleId,
            orElse: () => throw Exception('Test role not found'),
          );

          expect(testRole.name, 'Updated_Test_Role');

          print('✅ User inherited updated permissions');
          print('   Updated role: ${testRole.name}');
        } catch (e) {
          fail('❌ Permission inheritance verification failed: $e');
        }
      });

      test('create additional permission and assign to role', () async {
        if (adminToken == null || testRoleId == null) {
          fail('❌ Required test data not available');
        }

        try {
          // Create a new custom permission
          final permissionRequest = role_api.CreatePermissionRequest(
            name: 'custom.test.action',
            description: 'Custom test permission for dynamic testing',
            resource: 'custom',
            action: 'test',
          );

          final result = await roleService.createPermission(permissionRequest);
          expect(result.success, true);

          testPermissionId = result.data!.id;

          print('✅ Custom permission created: ${permissionRequest.name}');
        } catch (e) {
          fail('❌ Custom permission creation failed: $e');
        }
      });

      test('remove role from user and verify access is revoked', () async {
        if (adminToken == null || testUserId == null) {
          fail('❌ Required test data not available');
        }

        try {
          // Remove all roles from test user
          final request = role_api.AssignRolesRequest(
            roleIds: [], // Empty array removes all roles
            replaceExisting: true,
          );

          final result = await roleService.assignUserRoles(testUserId!, request);
          expect(result.success, true);

          // Verify user has no roles
          final rolesResult = await roleService.getUserRoles(testUserId!);
          expect(rolesResult.success, true);
          expect(rolesResult.data!.roles, isEmpty);

          print('✅ User roles removed successfully');
        } catch (e) {
          fail('❌ Role removal failed: $e');
        }
      });
    });

    group('Real-time Permission Enforcement Tests', () {
      test('permission checker responds to role changes', () async {
        if (adminToken == null || testUserId == null) {
          fail('❌ Required test data not available');
        }

        // Create permission checker using provider
        final permissionChecker = container.read(permissionCheckerProvider);

        try {
          // Test with no roles (should deny all permissions)
          final hasPropertiesRead = await permissionChecker.hasPermission('properties.read');
          expect(hasPropertiesRead, false);

          // Re-assign role to user
          final assignRequest = role_api.AssignRolesRequest(
            roleIds: [testRoleId!],
            replaceExisting: true,
          );

          await roleService.assignUserRoles(testUserId!, assignRequest);

          // Wait for permission cache to update
          await Future.delayed(const Duration(milliseconds: 500));

          // Test with role assigned (should grant permissions)
          final hasPropertiesReadAfter = await permissionChecker.hasPermission('properties.read');
          expect(hasPropertiesReadAfter, true);

          print('✅ Permission checker responds to role changes');
        } catch (e) {
          fail('❌ Real-time permission test failed: $e');
        }
      });

      testWidgets('UI updates when permissions change', (WidgetTester tester) async {
        if (testUserId == null) {
          markTestSkipped('Test user not available');
          return;
        }

        // Create test user with no roles initially
        final testUserNoRoles = User(
          id: testUserId!,
          email: '<EMAIL>',
          fullName: 'Test User',
          roles: [], // No roles
          isActive: true,
          createdAt: DateTime.now(),
        );

        // Create test user with roles
        final testUserWithRoles = User(
          id: testUserId!,
          email: '<EMAIL>',
          fullName: 'Test User',
          roles: ['Updated_Test_Role'],
          isActive: true,
          createdAt: DateTime.now(),
        );

        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => AuthNotifier(ref.read(authRepositoryProvider))..state = AuthState(
                isAuthenticated: true,
                user: testUserNoRoles,
              )),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: DynamicRoleBasedWidget(
                  requiredPermissions: const ['properties.read'],
                  child: const Text('Properties Access Granted'),
                  fallback: const Text('Properties Access Denied'),
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show denied access initially
        expect(find.text('Properties Access Denied'), findsOneWidget);
        expect(find.text('Properties Access Granted'), findsNothing);

        // Update to user with roles
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              authStateProvider.overrideWith((ref) => AuthNotifier(ref.read(authRepositoryProvider))..state = AuthState(
                isAuthenticated: true,
                user: testUserWithRoles,
              )),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: DynamicRoleBasedWidget(
                  requiredPermissions: const ['properties.read'],
                  child: const Text('Properties Access Granted'),
                  fallback: const Text('Properties Access Denied'),
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show granted access after role assignment
        expect(find.text('Properties Access Granted'), findsOneWidget);
        expect(find.text('Properties Access Denied'), findsNothing);

        print('✅ UI updates correctly when permissions change');
      });
    });

    group('Cleanup', () {
      test('cleanup test data', () async {
        if (adminToken == null) {
          return; // Skip cleanup if no admin token
        }

        try {
          // Delete test user if created
          if (testUserId != null) {
            try {
              await userService.deleteUser(testUserId!);
              print('✅ Test user deleted');
            } catch (e) {
              print('⚠️ Test user deletion failed: $e');
            }
          }

          // Delete test role if created
          if (testRoleId != null) {
            try {
              await roleService.deleteRole(testRoleId!);
              print('✅ Test role deleted');
            } catch (e) {
              print('⚠️ Test role deletion failed: $e');
            }
          }

          // Delete test permission if created
          if (testPermissionId != null) {
            try {
              await roleService.deletePermission(testPermissionId!);
              print('✅ Test permission deleted');
            } catch (e) {
              print('⚠️ Test permission deletion failed: $e');
            }
          }

          // Delete test admin if we created one
          try {
            final users = await userService.getUsers();
            final testAdmin = users.data?.firstWhere(
              (u) => u.email == '<EMAIL>',
              orElse: () => throw Exception('User not found'),
            );

            if (testAdmin != null) {
              await userService.deleteUser(testAdmin.id);
              print('✅ Test admin deleted');
            }
          } catch (e) {
            print('⚠️ Test admin deletion failed: $e');
          }

          print('✅ Test cleanup completed');
        } catch (e) {
          print('⚠️ Cleanup failed: $e');
        }
      });
    });
  });
}
