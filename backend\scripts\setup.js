#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up SRSR Property Management Backend...\n');

// Check if .env exists
const envPath = path.join(__dirname, '..', '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from .env.example...');
  const envExamplePath = path.join(__dirname, '..', '.env.example');
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created');
  } else {
    console.log('❌ .env.example not found');
  }
}

try {
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
  console.log('✅ Dependencies installed\n');

  console.log('🗄️  Setting up database...');
  execSync('npx prisma generate', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
  console.log('✅ Prisma client generated');

  console.log('📊 Pushing database schema...');
  execSync('npx prisma db push', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
  console.log('✅ Database schema pushed');

  console.log('🌱 Seeding database...');
  execSync('npm run db:seed', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
  console.log('✅ Database seeded\n');

  console.log('🎉 Setup completed successfully!\n');
  console.log('Next steps:');
  console.log('1. Update DATABASE_URL in .env with your PostgreSQL credentials');
  console.log('2. Run: npm run dev');
  console.log('3. Visit: http://localhost:3000\n');
  console.log('Default admin credentials:');
  console.log('Email: <EMAIL>');
  console.log('Password: admin123');

} catch (error) {
  console.error('❌ Setup failed:', error.message);
  console.log('\nManual setup steps:');
  console.log('1. npm install');
  console.log('2. Update .env with your database URL');
  console.log('3. npx prisma generate');
  console.log('4. npx prisma db push');
  console.log('5. npm run db:seed');
  console.log('6. npm run dev');
}
