import '../../../shared/models/generator_fuel.dart';
import '../domain/fuel_repository.dart';
import 'fuel_api_service.dart';

class FuelRepositoryImpl implements FuelRepository {
  final FuelApiService _apiService;

  FuelRepositoryImpl(this._apiService);

  @override
  Future<List<GeneratorFuelLog>> getFuelLogsByProperty(String propertyId) async {
    try {
      final response = await _apiService.getFuelLogs(propertyId);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch fuel logs');
      }
    } catch (e) {
      throw Exception('Failed to fetch fuel logs: $e');
    }
  }

  @override
  Future<GeneratorFuelLog> getFuelLogById(String id) async {
    try {
      final response = await _apiService.getFuelLogById(id);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to fetch fuel log');
      }
    } catch (e) {
      throw Exception('Failed to fetch fuel log: $e');
    }
  }

  @override
  Future<GeneratorFuelLog> createFuelLog(GeneratorFuelLog log) async {
    try {
      final request = CreateFuelLogRequest(
        fuelLevelLiters: log.fuelLevelLiters,
        consumptionRate: log.consumptionRate,
        runtimeHours: log.runtimeHours,
        efficiencyPercentage: log.efficiencyPercentage,
        notes: log.notes,
      );

      final response = await _apiService.createFuelLog(log.propertyId, request);

      if (response.response.statusCode == 201 && response.data != null) {
        // Handle nested response structure from backend
        final responseData = response.data as Map<String, dynamic>;

        // Check if it's a successful API response
        if (responseData['success'] == true && responseData['data'] != null) {
          final data = responseData['data'] as Map<String, dynamic>;

          if (data.containsKey('log')) {
            // Extract fuel log data from nested structure
            final logData = data['log'] as Map<String, dynamic>;
            return GeneratorFuelLog.fromJson(logData);
          } else {
            // Fallback: try to parse the entire data as GeneratorFuelLog
            return GeneratorFuelLog.fromJson(data);
          }
        } else {
          throw Exception(responseData['error'] ?? 'Failed to create fuel log');
        }
      } else {
        throw Exception('Failed to create fuel log: HTTP ${response.response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to create fuel log: $e');
    }
  }

  @override
  Future<GeneratorFuelLog> updateFuelLog(GeneratorFuelLog log) async {
    try {
      final request = UpdateFuelLogRequest(
        fuelLevelLiters: log.fuelLevelLiters,
        consumptionRate: log.consumptionRate,
        runtimeHours: log.runtimeHours,
        efficiencyPercentage: log.efficiencyPercentage,
        notes: log.notes,
      );

      final response = await _apiService.updateFuelLog(log.id, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.error ?? 'Failed to update fuel log');
      }
    } catch (e) {
      throw Exception('Failed to update fuel log: $e');
    }
  }

  @override
  Future<void> deleteFuelLog(String logId) async {
    try {
      final response = await _apiService.deleteFuelLog(logId);
      if (!response.success) {
        throw Exception(response.error ?? 'Failed to delete fuel log');
      }
    } catch (e) {
      throw Exception('Failed to delete fuel log: $e');
    }
  }

  @override
  Future<List<GeneratorFuelLog>> getAllFuelLogs() async {
    // Since we don't have a direct API endpoint for all fuel logs,
    // we'll need to implement this based on available properties
    // For now, return empty list - this should be implemented with proper property fetching
    try {
      // This is a placeholder implementation
      // In a real scenario, you'd fetch all properties and then get fuel logs for each
      return <GeneratorFuelLog>[];
    } catch (e) {
      throw Exception('Failed to fetch all fuel logs: $e');
    }
  }

  @override
  Future<void> recordFuelReading(
    String propertyId,
    double fuelLevel, {
    double? consumptionRate,
    String? notes,
    DateTime? recordedAt,
  }) async {
    try {
      final log = GeneratorFuelLog.create(
        propertyId: propertyId,
        fuelLevelLiters: fuelLevel,
        consumptionRate: consumptionRate,
        notes: notes,
      );

      await createFuelLog(log);
    } catch (e) {
      throw Exception('Failed to record fuel reading: $e');
    }
  }

  @override
  Future<List<GeneratorFuelLog>> getFuelLogsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      // Since we don't have a direct API endpoint for date range across all properties,
      // we'll need to implement this based on available properties
      // For now, return empty list - this should be implemented with proper property fetching
      return <GeneratorFuelLog>[];
    } catch (e) {
      throw Exception('Failed to fetch fuel logs by date range: $e');
    }
  }

  @override
  Future<List<GeneratorFuelLog>> getFuelLogsByPropertyAndDateRange(String propertyId, DateTime startDate, DateTime endDate) async {
    try {
      // Get all fuel logs for the property and filter by date range
      final allLogs = await getFuelLogsByProperty(propertyId);

      final filteredLogs = allLogs.where((log) {
        return log.recordedAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
               log.recordedAt.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();

      return filteredLogs;
    } catch (e) {
      throw Exception('Failed to fetch fuel logs by property and date range: $e');
    }
  }
}
