"use client"

import { StatusBar } from "./status-bar"

export default function StatusBarDemo() {
  return (
    <div className="min-h-screen bg-slate-50 p-8">
      <div className="container mx-auto max-w-4xl">
        <h1 className="text-3xl font-bold mb-8">Status Bar Component</h1>

        <div className="space-y-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">Interactive Status Bar</h2>
            <StatusBar />
          </div>

          <div className="grid gap-4">
            <h2 className="text-xl font-semibold">Usage Examples</h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Compact Version</h3>
                <StatusBar className="max-w-md" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
