import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';
import 'screen_permission_config.dart';

class PermissionConfigService {
  static const String _storageKey = 'screen_permission_configs';
  static final PermissionConfigService _instance = PermissionConfigService._internal();
  factory PermissionConfigService() => _instance;
  PermissionConfigService._internal();

  final Map<String, ScreenPermissionConfig> _cachedConfigs = {};
  bool _isInitialized = false;

  /// Initialize the service and load configurations
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load from local storage first
      await _loadFromStorage();

      // Try to sync with server
      await _syncWithServer();

      _isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing PermissionConfigService: $e');
      // Fall back to default configurations
      _loadDefaultConfigs();
      _isInitialized = true;
    }
  }

  /// Load configurations from local storage
  Future<void> _loadFromStorage() async {
    try {
      final configsJson = await StorageService.getString(_storageKey);
      if (configsJson != null) {
        final Map<String, dynamic> configsMap = json.decode(configsJson);
        _cachedConfigs.clear();

        for (final entry in configsMap.entries) {
          _cachedConfigs[entry.key] = ScreenPermissionConfig.fromJson(
            entry.value as Map<String, dynamic>,
          );
        }
      } else {
        _loadDefaultConfigs();
      }
    } catch (e) {
      debugPrint('Error loading configs from storage: $e');
      _loadDefaultConfigs();
    }
  }

  /// Load default configurations
  void _loadDefaultConfigs() {
    _cachedConfigs.clear();
    _cachedConfigs.addAll(DefaultScreenPermissions.getAllDefaultConfigs());
  }

  /// Save configurations to local storage
  Future<void> _saveToStorage() async {
    try {
      final configsMap = _cachedConfigs.map(
        (key, value) => MapEntry(key, value.toJson()),
      );
      await StorageService.setString(_storageKey, json.encode(configsMap));
    } catch (e) {
      debugPrint('Error saving configs to storage: $e');
    }
  }

  /// Sync configurations with server
  Future<void> _syncWithServer() async {
    try {
      // TODO: Implement server sync when backend endpoint is available
      // For now, we'll use local storage and defaults
    } catch (e) {
      debugPrint('Error syncing with server: $e');
    }
  }

  /// Get configuration for a specific screen
  ScreenPermissionConfig? getScreenConfig(String screenName) {
    if (!_isInitialized) {
      debugPrint('PermissionConfigService not initialized');
      return DefaultScreenPermissions.getDefaultConfig(screenName);
    }

    return _cachedConfigs[screenName] ??
           DefaultScreenPermissions.getDefaultConfig(screenName);
  }

  /// Get widget configuration for a specific screen and widget
  WidgetPermissionConfig? getWidgetConfig(String screenName, String widgetName) {
    final screenConfig = getScreenConfig(screenName);
    return screenConfig?.widgetPermissions[widgetName];
  }

  /// Update screen configuration
  Future<bool> updateScreenConfig(ScreenPermissionConfig config) async {
    try {
      _cachedConfigs[config.screenName] = config;
      await _saveToStorage();

      // TODO: Sync with server

      return true;
    } catch (e) {
      debugPrint('Error updating screen config: $e');
      return false;
    }
  }

  /// Update widget configuration
  Future<bool> updateWidgetConfig(
    String screenName,
    WidgetPermissionConfig widgetConfig,
  ) async {
    try {
      final screenConfig = getScreenConfig(screenName);
      if (screenConfig == null) return false;

      final updatedWidgetPermissions = Map<String, WidgetPermissionConfig>.from(
        screenConfig.widgetPermissions,
      );
      updatedWidgetPermissions[widgetConfig.widgetName] = widgetConfig;

      final updatedScreenConfig = screenConfig.copyWith(
        widgetPermissions: updatedWidgetPermissions,
      );

      return await updateScreenConfig(updatedScreenConfig);
    } catch (e) {
      debugPrint('Error updating widget config: $e');
      return false;
    }
  }

  /// Check if user has permission to access a screen
  bool hasScreenPermission(
    String screenName,
    List<String> userPermissions,
    List<String> userRoles,
  ) {
    final config = getScreenConfig(screenName);
    if (config == null || !config.isEnabled) return false;

    // Check if user has any of the required permissions
    if (config.requiredPermissions.isNotEmpty) {
      final hasPermission = config.requiredPermissions.any(
        (permission) => userPermissions.contains(permission),
      );
      if (!hasPermission) return false;
    }

    // Check if user has any of the allowed roles
    if (config.allowedRoles.isNotEmpty) {
      final hasRole = config.allowedRoles.any(
        (role) => userRoles.contains(role),
      );
      if (!hasRole) return false;
    }

    return true;
  }

  /// Check if user has permission to access a widget
  bool hasWidgetPermission(
    String screenName,
    String widgetName,
    List<String> userPermissions,
    List<String> userRoles,
  ) {
    // First check screen permission
    if (!hasScreenPermission(screenName, userPermissions, userRoles)) {
      return false;
    }

    final widgetConfig = getWidgetConfig(screenName, widgetName);
    if (widgetConfig == null || !widgetConfig.isEnabled || !widgetConfig.isVisible) {
      return false;
    }

    // Check if user has any of the required permissions
    if (widgetConfig.requiredPermissions.isNotEmpty) {
      final hasPermission = widgetConfig.requiredPermissions.any(
        (permission) => userPermissions.contains(permission),
      );
      if (!hasPermission) return false;
    }

    // Check if user has any of the allowed roles
    if (widgetConfig.allowedRoles.isNotEmpty) {
      final hasRole = widgetConfig.allowedRoles.any(
        (role) => userRoles.contains(role),
      );
      if (!hasRole) return false;
    }

    return true;
  }

  /// Get all screen configurations
  Map<String, ScreenPermissionConfig> getAllConfigs() {
    return Map.unmodifiable(_cachedConfigs);
  }

  /// Reset to default configurations
  Future<void> resetToDefaults() async {
    _loadDefaultConfigs();
    await _saveToStorage();
  }

  /// Add a new screen configuration
  Future<bool> addScreenConfig(ScreenPermissionConfig config) async {
    return await updateScreenConfig(config);
  }

  /// Remove a screen configuration
  Future<bool> removeScreenConfig(String screenName) async {
    try {
      _cachedConfigs.remove(screenName);
      await _saveToStorage();
      return true;
    } catch (e) {
      debugPrint('Error removing screen config: $e');
      return false;
    }
  }

  /// Get screens accessible by user
  List<String> getAccessibleScreens(
    List<String> userPermissions,
    List<String> userRoles,
  ) {
    return _cachedConfigs.keys
        .where((screenName) => hasScreenPermission(screenName, userPermissions, userRoles))
        .toList();
  }

  /// Get widgets accessible by user for a specific screen
  List<String> getAccessibleWidgets(
    String screenName,
    List<String> userPermissions,
    List<String> userRoles,
  ) {
    final screenConfig = getScreenConfig(screenName);
    if (screenConfig == null) return [];

    return screenConfig.widgetPermissions.keys
        .where((widgetName) => hasWidgetPermission(screenName, widgetName, userPermissions, userRoles))
        .toList();
  }

  /// Export configurations as JSON
  String exportConfigs() {
    final configsMap = _cachedConfigs.map(
      (key, value) => MapEntry(key, value.toJson()),
    );
    return json.encode(configsMap);
  }

  /// Import configurations from JSON
  Future<bool> importConfigs(String configsJson) async {
    try {
      final Map<String, dynamic> configsMap = json.decode(configsJson);
      _cachedConfigs.clear();

      for (final entry in configsMap.entries) {
        _cachedConfigs[entry.key] = ScreenPermissionConfig.fromJson(
          entry.value as Map<String, dynamic>,
        );
      }

      await _saveToStorage();
      return true;
    } catch (e) {
      debugPrint('Error importing configs: $e');
      return false;
    }
  }
}
