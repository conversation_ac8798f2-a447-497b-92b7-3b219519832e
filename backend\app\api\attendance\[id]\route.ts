import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const updateAttendanceSchema = Joi.object({
  status: Joi.string().valid('present', 'absent', 'late', 'half_day', 'sick_leave', 'vacation').optional(),
  check_in_time: Joi.string().optional(),
  check_out_time: Joi.string().optional(),
  hours_worked: Joi.number().min(0).max(24).optional(),
  notes: Joi.string().optional(),
});

async function getAttendanceHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    const attendance = await prisma.attendance.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
        recorder: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    if (!attendance) {
      return Response.json(
        createApiResponse(null, 'Attendance record not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    const transformedAttendance = {
      id: attendance.id,
      user_id: attendance.userId,
      property_id: attendance.propertyId,
      date: attendance.date,
      status: attendance.status,
      check_in_time: attendance.checkInTime,
      check_out_time: attendance.checkOutTime,
      hours_worked: attendance.hoursWorked,
      notes: attendance.notes,
      user: {
        id: attendance.user.id,
        full_name: attendance.user.fullName,
        email: attendance.user.email,
      },
      property: {
        id: attendance.property.id,
        name: attendance.property.name,
      },
      recorder: attendance.recorder ? {
        id: attendance.recorder.id,
        full_name: attendance.recorder.fullName,
        email: attendance.recorder.email,
      } : null,
      created_at: attendance.createdAt,
      updated_at: attendance.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedAttendance),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch attendance record');
  }
}

async function updateAttendanceHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateAttendanceSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400, headers: corsHeaders() }
      );
    }

    // Verify attendance record exists
    const existingAttendance = await prisma.attendance.findUnique({
      where: { id },
    });

    if (!existingAttendance) {
      return Response.json(
        createApiResponse(null, 'Attendance record not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    const updateData: any = {};
    const { 
      status, 
      check_in_time, 
      check_out_time, 
      hours_worked, 
      notes 
    } = validation.data;

    if (status !== undefined) updateData.status = status;
    if (check_in_time !== undefined) updateData.checkInTime = check_in_time ? new Date(check_in_time) : null;
    if (check_out_time !== undefined) updateData.checkOutTime = check_out_time ? new Date(check_out_time) : null;
    if (hours_worked !== undefined) updateData.hoursWorked = hours_worked;
    if (notes !== undefined) updateData.notes = notes;

    // Update attendance record
    const updatedAttendance = await prisma.attendance.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const transformedAttendance = {
      id: updatedAttendance.id,
      user_id: updatedAttendance.userId,
      property_id: updatedAttendance.propertyId,
      date: updatedAttendance.date,
      status: updatedAttendance.status,
      check_in_time: updatedAttendance.checkInTime,
      check_out_time: updatedAttendance.checkOutTime,
      hours_worked: updatedAttendance.hoursWorked,
      notes: updatedAttendance.notes,
      user: {
        id: updatedAttendance.user.id,
        full_name: updatedAttendance.user.fullName,
        email: updatedAttendance.user.email,
      },
      property: {
        id: updatedAttendance.property.id,
        name: updatedAttendance.property.name,
      },
      updated_at: updatedAttendance.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'Attendance record updated successfully',
        attendance: transformedAttendance,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update attendance record');
  }
}

async function deleteAttendanceHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Verify attendance record exists
    const existingAttendance = await prisma.attendance.findUnique({
      where: { id },
    });

    if (!existingAttendance) {
      return Response.json(
        createApiResponse(null, 'Attendance record not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    // Delete attendance record
    await prisma.attendance.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'Attendance record deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete attendance record');
  }
}

export const GET = requireAuth(getAttendanceHandler);
export const PUT = requireAuth(updateAttendanceHandler);
export const DELETE = requireAuth(deleteAttendanceHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
