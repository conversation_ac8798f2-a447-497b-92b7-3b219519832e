import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Lighting tab - Smart lighting controls and scenes
class LightingTab extends StatefulWidget {
  const LightingTab({super.key});

  @override
  State<LightingTab> createState() => _LightingTabState();
}

class _LightingTabState extends State<LightingTab> {
  final List<LightingZone> _zones = [
    LightingZone(
      id: 'zone_001',
      name: 'Main Lobby',
      isOn: true,
      brightness: 85,
      color: Colors.white,
      lightCount: 12,
      energyUsage: 240,
    ),
    LightingZone(
      id: 'zone_002',
      name: 'Parking Area',
      isOn: true,
      brightness: 60,
      color: Colors.white,
      lightCount: 8,
      energyUsage: 160,
    ),
    LightingZone(
      id: 'zone_003',
      name: 'Emergency Exits',
      isOn: true,
      brightness: 100,
      color: Colors.red,
      lightCount: 6,
      energyUsage: 60,
    ),
    LightingZone(
      id: 'zone_004',
      name: 'Garden Lights',
      isOn: false,
      brightness: 0,
      color: Colors.green,
      lightCount: 15,
      energyUsage: 0,
    ),
  ];

  final List<LightingScene> _scenes = [
    LightingScene(
      id: 'scene_001',
      name: 'Normal Operation',
      description: 'Standard lighting for business hours',
      icon: Icons.wb_sunny,
      isActive: true,
    ),
    LightingScene(
      id: 'scene_002',
      name: 'Night Mode',
      description: 'Reduced lighting for after hours',
      icon: Icons.nights_stay,
      isActive: false,
    ),
    LightingScene(
      id: 'scene_003',
      name: 'Emergency',
      description: 'Full brightness emergency lighting',
      icon: Icons.emergency,
      isActive: false,
    ),
    LightingScene(
      id: 'scene_004',
      name: 'Energy Saver',
      description: 'Minimal lighting to save energy',
      icon: Icons.eco,
      isActive: false,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildSceneSelector(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildEnergyOverview(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildZonesList(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSceneSelector(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Lighting Scenes',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: AppConstants.smallPadding,
                mainAxisSpacing: AppConstants.smallPadding,
                childAspectRatio: 2.5,
              ),
              itemCount: _scenes.length,
              itemBuilder: (context, index) {
                return _buildSceneCard(_scenes[index]);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSceneCard(LightingScene scene) {
    return Card(
      elevation: scene.isActive ? 4 : 1,
      color: scene.isActive ? Colors.amber.shade50 : null,
      child: InkWell(
        onTap: () => _activateScene(scene),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: scene.isActive 
                      ? Colors.amber.shade100 
                      : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  scene.icon,
                  color: scene.isActive 
                      ? Colors.amber.shade700 
                      : Colors.grey.shade600,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      scene.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: scene.isActive 
                            ? Colors.amber.shade800 
                            : null,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      scene.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (scene.isActive)
                Icon(
                  Icons.check_circle,
                  color: Colors.amber.shade700,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnergyOverview(BuildContext context) {
    final totalLights = _zones.fold(0, (sum, zone) => sum + zone.lightCount);
    final activeLights = _zones.where((z) => z.isOn).fold(0, (sum, zone) => sum + zone.lightCount);
    final totalEnergy = _zones.fold(0.0, (sum, zone) => sum + zone.energyUsage);

    return Row(
      children: [
        Expanded(
          child: _buildEnergyCard(
            'Total Lights',
            totalLights.toString(),
            Icons.lightbulb,
            Colors.amber,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildEnergyCard(
            'Active',
            activeLights.toString(),
            Icons.lightbulb_outline,
            Colors.green,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildEnergyCard(
            'Energy Usage',
            '${totalEnergy.toInt()}W',
            Icons.electrical_services,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildEnergyCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildZonesList(BuildContext context) {
    return ListView.separated(
      itemCount: _zones.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConstants.smallPadding),
      itemBuilder: (context, index) {
        return _buildZoneCard(_zones[index]);
      },
    );
  }

  Widget _buildZoneCard(LightingZone zone) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: zone.isOn 
                        ? Colors.amber.shade100 
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    zone.isOn ? Icons.lightbulb : Icons.lightbulb_outline,
                    color: zone.isOn 
                        ? Colors.amber.shade700 
                        : Colors.grey.shade600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        zone.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${zone.lightCount} lights • ${zone.energyUsage}W',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: zone.isOn,
                  onChanged: (value) => _toggleZone(zone),
                  activeColor: Colors.amber.shade700,
                ),
              ],
            ),
            if (zone.isOn) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.brightness_6,
                        color: Colors.grey.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Brightness',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const Spacer(),
                      Text(
                        '${zone.brightness}%',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Slider(
                    value: zone.brightness.toDouble(),
                    min: 0,
                    max: 100,
                    divisions: 10,
                    activeColor: Colors.amber.shade700,
                    onChanged: (value) => _adjustBrightness(zone, value.toInt()),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showColorPicker(zone),
                      icon: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: zone.color,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.grey.shade400),
                        ),
                      ),
                      label: const Text('Color'),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _scheduleZone(zone),
                      icon: const Icon(Icons.schedule, size: 16),
                      label: const Text('Schedule'),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _configureZone(zone),
                      icon: const Icon(Icons.settings, size: 16),
                      label: const Text('Settings'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _activateScene(LightingScene scene) {
    setState(() {
      for (var s in _scenes) {
        s.isActive = s.id == scene.id;
      }
      
      // Apply scene settings to zones
      switch (scene.id) {
        case 'scene_001': // Normal Operation
          for (var zone in _zones) {
            zone.isOn = true;
            zone.brightness = 85;
            zone.color = Colors.white;
          }
          break;
        case 'scene_002': // Night Mode
          for (var zone in _zones) {
            if (zone.name == 'Emergency Exits') {
              zone.isOn = true;
              zone.brightness = 100;
            } else {
              zone.isOn = true;
              zone.brightness = 30;
            }
            zone.color = Colors.white;
          }
          break;
        case 'scene_003': // Emergency
          for (var zone in _zones) {
            zone.isOn = true;
            zone.brightness = 100;
            zone.color = Colors.white;
          }
          break;
        case 'scene_004': // Energy Saver
          for (var zone in _zones) {
            if (zone.name == 'Emergency Exits') {
              zone.isOn = true;
              zone.brightness = 100;
            } else {
              zone.isOn = false;
              zone.brightness = 0;
            }
          }
          break;
      }
      
      _updateEnergyUsage();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${scene.name} scene activated')),
    );
  }

  void _toggleZone(LightingZone zone) {
    setState(() {
      zone.isOn = !zone.isOn;
      if (!zone.isOn) {
        zone.brightness = 0;
      } else {
        zone.brightness = 85;
      }
      _updateEnergyUsage();
    });
  }

  void _adjustBrightness(LightingZone zone, int brightness) {
    setState(() {
      zone.brightness = brightness;
      _updateEnergyUsage();
    });
  }

  void _updateEnergyUsage() {
    for (var zone in _zones) {
      if (zone.isOn) {
        zone.energyUsage = (zone.lightCount * 20 * (zone.brightness / 100)).toInt();
      } else {
        zone.energyUsage = 0;
      }
    }
  }

  void _showColorPicker(LightingZone zone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${zone.name} Color'),
        content: const Text('Color picker coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _scheduleZone(LightingZone zone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Schedule ${zone.name}'),
        content: const Text('Lighting schedule configuration coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _configureZone(LightingZone zone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${zone.name} Settings'),
        content: const Text('Zone configuration options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Lighting zone data model
class LightingZone {
  final String id;
  final String name;
  bool isOn;
  int brightness;
  Color color;
  final int lightCount;
  int energyUsage;

  LightingZone({
    required this.id,
    required this.name,
    required this.isOn,
    required this.brightness,
    required this.color,
    required this.lightCount,
    required this.energyUsage,
  });
}

/// Lighting scene data model
class LightingScene {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  bool isActive;

  LightingScene({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.isActive,
  });
}
