import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle } from "lucide-react"

export default function RegistrationSuccessPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-slate-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-center text-2xl font-bold">Registration Successful</CardTitle>
          <CardDescription className="text-center">
            Your account has been created successfully. An administrator will review and activate your account.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <p>Once your account is activated, you will be able to log in to the SRSR management system.</p>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/login">
            <Button>Return to Login</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  )
}
