import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';

/// Simple test to verify backend connectivity and basic API functionality
/// This test creates its own test data and cleans up afterward
void main() {
  group('Backend Connectivity Tests', () {
    late Dio dio;

    setUpAll(() {
      // Configure Dio for real API calls
      dio = Dio(BaseOptions(
        baseUrl: 'http://192.168.1.3:3000',
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {'Content-Type': 'application/json'},
      ));

      // Add logging interceptor
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('[BACKEND_TEST] $obj'),
      ));
    });

    test('backend server is running and accessible', () async {
      try {
        final response = await dio.get('/api');
        expect(response.statusCode, 200);
        print('✅ Backend server is accessible');
        print('   Response: ${response.data}');
      } catch (e) {
        fail('❌ Backend server is not accessible: $e');
      }
    });

    test('can create and authenticate test user', () async {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final testEmail = 'connectivity_test_$<EMAIL>';
      final testPassword = 'test123';

      String? userId;
      String? authToken;

      try {
        // Step 1: Create test user
        final createUserResponse = await dio.post('/api/users', data: {
          'email': testEmail,
          'fullName': 'Connectivity Test User',
          'password': testPassword,
          'roles': ['viewer'], // Basic role
        });

        expect(createUserResponse.statusCode, 201);
        
        // Extract user ID from response
        final userData = createUserResponse.data['data']['user'];
        userId = userData['id'];
        
        print('✅ Test user created successfully');
        print('   Email: $testEmail');
        print('   ID: $userId');

        // Step 2: Authenticate with created user
        final loginResponse = await dio.post('/api/auth/login', data: {
          'email': testEmail,
          'password': testPassword,
        });

        expect(loginResponse.statusCode, 200);
        
        final loginData = loginResponse.data;
        authToken = loginData['token'];
        final user = loginData['user'];
        
        expect(authToken, isNotNull);
        expect(user['email'], testEmail);
        
        print('✅ Test user authenticated successfully');
        print('   Token: ${authToken!.substring(0, 20)}...');

        // Step 3: Test authenticated request
        dio.options.headers['Authorization'] = 'Bearer $authToken';
        
        final profileResponse = await dio.get('/api/auth/me');
        expect(profileResponse.statusCode, 200);
        
        final profile = profileResponse.data['user'];
        expect(profile['email'], testEmail);
        
        print('✅ Authenticated request successful');

      } catch (e) {
        fail('❌ User creation/authentication failed: $e');
      } finally {
        // Cleanup: Delete test user
        if (userId != null && authToken != null) {
          try {
            dio.options.headers['Authorization'] = 'Bearer $authToken';
            await dio.delete('/api/users/$userId');
            print('✅ Test user cleaned up');
          } catch (e) {
            print('⚠️ Cleanup failed: $e');
          }
        }
      }
    });

    test('can create and manage test role', () async {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final testRoleName = 'ConnectivityTestRole_$timestamp';
      
      String? roleId;
      String? adminToken;

      try {
        // Step 1: Get admin token (assuming admin exists or create one)
        try {
          final loginResponse = await dio.post('/api/auth/login', data: {
            'email': '<EMAIL>',
            'password': 'admin123',
          });
          adminToken = loginResponse.data['token'];
          print('✅ Admin authenticated');
        } catch (e) {
          // If admin doesn't exist, create one
          await dio.post('/api/users', data: {
            'email': '<EMAIL>',
            'fullName': 'System Admin',
            'password': 'admin123',
            'roles': ['admin'],
          });
          
          final loginResponse = await dio.post('/api/auth/login', data: {
            'email': '<EMAIL>',
            'password': 'admin123',
          });
          adminToken = loginResponse.data['token'];
          print('✅ Admin created and authenticated');
        }

        dio.options.headers['Authorization'] = 'Bearer $adminToken';

        // Step 2: Create test role
        final createRoleResponse = await dio.post('/api/roles', data: {
          'name': testRoleName,
          'description': 'Test role for connectivity testing',
          'permissions': ['properties.read', 'maintenance.read'],
        });

        expect(createRoleResponse.statusCode, 201);
        
        final roleData = createRoleResponse.data['data'];
        roleId = roleData['id'];
        
        print('✅ Test role created successfully');
        print('   Name: $testRoleName');
        print('   ID: $roleId');

        // Step 3: Verify role exists
        final getRoleResponse = await dio.get('/api/roles/$roleId');
        expect(getRoleResponse.statusCode, 200);
        
        final role = getRoleResponse.data['data'];
        expect(role['name'], testRoleName);
        
        print('✅ Test role verified');

        // Step 4: Update role permissions
        final updateRoleResponse = await dio.put('/api/roles/$roleId', data: {
          'name': testRoleName,
          'description': 'Updated test role for connectivity testing',
          'permissions': ['properties.read', 'maintenance.read', 'attendance.read'],
        });

        expect(updateRoleResponse.statusCode, 200);
        print('✅ Test role updated successfully');

      } catch (e) {
        fail('❌ Role management failed: $e');
      } finally {
        // Cleanup: Delete test role
        if (roleId != null && adminToken != null) {
          try {
            dio.options.headers['Authorization'] = 'Bearer $adminToken';
            await dio.delete('/api/roles/$roleId');
            print('✅ Test role cleaned up');
          } catch (e) {
            print('⚠️ Role cleanup failed: $e');
          }
        }
      }
    });

    test('verify API endpoints are available', () async {
      final endpoints = [
        '/api',
        '/api/auth/login',
        '/api/users',
        '/api/roles',
        '/api/properties',
        '/api/maintenance',
      ];

      for (final endpoint in endpoints) {
        try {
          // Use HEAD request to check if endpoint exists without authentication
          final response = await dio.head(endpoint);
          
          // Accept various status codes that indicate the endpoint exists
          final validCodes = [200, 401, 403, 405]; // 401/403 = auth required, 405 = method not allowed
          
          if (validCodes.contains(response.statusCode)) {
            print('✅ Endpoint available: $endpoint (${response.statusCode})');
          } else {
            print('⚠️ Endpoint unexpected status: $endpoint (${response.statusCode})');
          }
        } catch (e) {
          if (e is DioException) {
            final statusCode = e.response?.statusCode;
            if ([401, 403, 405].contains(statusCode)) {
              print('✅ Endpoint available: $endpoint ($statusCode - auth/method restriction)');
            } else {
              print('❌ Endpoint not available: $endpoint ($statusCode)');
            }
          } else {
            print('❌ Endpoint error: $endpoint - $e');
          }
        }
      }
    });
  });
}
