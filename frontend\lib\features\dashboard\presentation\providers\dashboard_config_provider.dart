import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Dashboard configuration state
class DashboardConfig {
  final bool useV2Dashboard;
  final DashboardLayout layout;
  final bool showInsights;
  final bool enableRealTimeUpdates;
  final TimeRange defaultTimeRange;
  final List<String> favoriteMetrics;
  final Map<String, bool> widgetVisibility;

  const DashboardConfig({
    this.useV2Dashboard = false, // Default to legacy for gradual rollout
    this.layout = DashboardLayout.adaptive,
    this.showInsights = true,
    this.enableRealTimeUpdates = true,
    this.defaultTimeRange = TimeRange.week,
    this.favoriteMetrics = const [],
    this.widgetVisibility = const {},
  });

  DashboardConfig copyWith({
    bool? useV2Dashboard,
    DashboardLayout? layout,
    bool? showInsights,
    bool? enableRealTimeUpdates,
    TimeRange? defaultTimeRange,
    List<String>? favoriteMetrics,
    Map<String, bool>? widgetVisibility,
  }) {
    return DashboardConfig(
      useV2Dashboard: useV2Dashboard ?? this.useV2Dashboard,
      layout: layout ?? this.layout,
      showInsights: showInsights ?? this.showInsights,
      enableRealTimeUpdates: enableRealTimeUpdates ?? this.enableRealTimeUpdates,
      defaultTimeRange: defaultTimeRange ?? this.defaultTimeRange,
      favoriteMetrics: favoriteMetrics ?? this.favoriteMetrics,
      widgetVisibility: widgetVisibility ?? this.widgetVisibility,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'useV2Dashboard': useV2Dashboard,
      'layout': layout.name,
      'showInsights': showInsights,
      'enableRealTimeUpdates': enableRealTimeUpdates,
      'defaultTimeRange': defaultTimeRange.name,
      'favoriteMetrics': favoriteMetrics,
      'widgetVisibility': widgetVisibility,
    };
  }

  factory DashboardConfig.fromJson(Map<String, dynamic> json) {
    return DashboardConfig(
      useV2Dashboard: json['useV2Dashboard'] ?? false,
      layout: DashboardLayout.values.firstWhere(
        (e) => e.name == json['layout'],
        orElse: () => DashboardLayout.adaptive,
      ),
      showInsights: json['showInsights'] ?? true,
      enableRealTimeUpdates: json['enableRealTimeUpdates'] ?? true,
      defaultTimeRange: TimeRange.values.firstWhere(
        (e) => e.name == json['defaultTimeRange'],
        orElse: () => TimeRange.week,
      ),
      favoriteMetrics: List<String>.from(json['favoriteMetrics'] ?? []),
      widgetVisibility: Map<String, bool>.from(json['widgetVisibility'] ?? {}),
    );
  }
}

/// Dashboard layout options
enum DashboardLayout {
  adaptive,
  compact,
  detailed,
  grid,
}

/// Time range options for dashboard data
enum TimeRange {
  day,
  week,
  month,
  quarter,
}

/// Dashboard configuration notifier
class DashboardConfigNotifier extends StateNotifier<DashboardConfig> {
  DashboardConfigNotifier() : super(const DashboardConfig()) {
    _loadConfig();
  }

  static const String _configKey = 'dashboard_config';

  /// Load configuration from shared preferences
  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_configKey);
      
      if (configJson != null) {
        final config = DashboardConfig.fromJson(
          Map<String, dynamic>.from(
            // In a real app, you'd use json.decode here
            // For now, we'll use a simple approach
            {},
          ),
        );
        state = config;
      }
    } catch (e) {
      print('Error loading dashboard config: $e');
    }
  }

  /// Save configuration to shared preferences
  Future<void> _saveConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // In a real app, you'd use json.encode here
      await prefs.setString(_configKey, state.toJson().toString());
    } catch (e) {
      print('Error saving dashboard config: $e');
    }
  }

  /// Toggle between v1 and v2 dashboard
  Future<void> toggleDashboardVersion() async {
    state = state.copyWith(useV2Dashboard: !state.useV2Dashboard);
    await _saveConfig();
  }

  /// Update dashboard layout
  Future<void> updateLayout(DashboardLayout layout) async {
    state = state.copyWith(layout: layout);
    await _saveConfig();
  }

  /// Toggle insights visibility
  Future<void> toggleInsights() async {
    state = state.copyWith(showInsights: !state.showInsights);
    await _saveConfig();
  }

  /// Toggle real-time updates
  Future<void> toggleRealTimeUpdates() async {
    state = state.copyWith(enableRealTimeUpdates: !state.enableRealTimeUpdates);
    await _saveConfig();
  }

  /// Update default time range
  Future<void> updateTimeRange(TimeRange timeRange) async {
    state = state.copyWith(defaultTimeRange: timeRange);
    await _saveConfig();
  }

  /// Add favorite metric
  Future<void> addFavoriteMetric(String metric) async {
    final favorites = List<String>.from(state.favoriteMetrics);
    if (!favorites.contains(metric)) {
      favorites.add(metric);
      state = state.copyWith(favoriteMetrics: favorites);
      await _saveConfig();
    }
  }

  /// Remove favorite metric
  Future<void> removeFavoriteMetric(String metric) async {
    final favorites = List<String>.from(state.favoriteMetrics);
    favorites.remove(metric);
    state = state.copyWith(favoriteMetrics: favorites);
    await _saveConfig();
  }

  /// Update widget visibility
  Future<void> updateWidgetVisibility(String widgetId, bool visible) async {
    final visibility = Map<String, bool>.from(state.widgetVisibility);
    visibility[widgetId] = visible;
    state = state.copyWith(widgetVisibility: visibility);
    await _saveConfig();
  }

  /// Reset to default configuration
  Future<void> resetToDefaults() async {
    state = const DashboardConfig();
    await _saveConfig();
  }
}

/// Provider for dashboard configuration
final dashboardConfigProvider = StateNotifierProvider<DashboardConfigNotifier, DashboardConfig>((ref) {
  return DashboardConfigNotifier();
});

/// Convenience provider for checking if v2 dashboard is enabled
final isV2DashboardEnabledProvider = Provider<bool>((ref) {
  return ref.watch(dashboardConfigProvider).useV2Dashboard;
});
