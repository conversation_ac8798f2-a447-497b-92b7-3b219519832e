// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

export interface PaginationResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// User Types
export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  is_active: boolean;
  roles: string[];
  created_at: Date;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  phone?: string;
}

// Property Types
export interface Property {
  id: string;
  name: string;
  type: 'residential' | 'office';
  address?: string;
  description?: string;
  image_url?: string;
  is_active: boolean;
  services?: PropertyService[];
  created_at: Date;
}

export interface PropertyService {
  id: string;
  service_type: 'electricity' | 'water' | 'internet' | 'security' | 'ott';
  status: 'operational' | 'warning' | 'critical' | 'maintenance';
  last_checked?: Date;
  notes?: string;
}

// Maintenance Types
export interface MaintenanceIssue {
  id: string;
  property_id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  service_type?: string;
  department?: string;
  reported_by: string;
  assigned_to?: string;
  due_date?: Date;
  created_at: Date;
}

// Attendance Types
export interface AttendanceRecord {
  id: string;
  user_id: string;
  date: Date;
  check_in_time?: string;
  check_out_time?: string;
  hours_worked?: number;
  status: 'present' | 'absent' | 'late' | 'half_day';
  notes?: string;
}

export interface Site {
  id: string;
  name: string;
  location: string;
  project_type: string;
  is_active: boolean;
}

// Generator Fuel Types
export interface GeneratorFuelLog {
  id: string;
  property_id: string;
  fuel_level_liters: number;
  consumption_rate?: number;
  runtime_hours?: number;
  efficiency_percentage?: number;
  last_maintenance?: Date;
  next_maintenance?: Date;
  notes?: string;
  recorded_at: Date;
}

// Dashboard Types
export interface DashboardStatus {
  properties: {
    total: number;
    operational: number;
    warning: number;
    critical: number;
  };
  maintenance_issues: {
    total: number;
    open: number;
    in_progress: number;
    critical: number;
  };
  recent_alerts: Array<{
    type: string;
    message: string;
    timestamp: Date;
  }>;
}
