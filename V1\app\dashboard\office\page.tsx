"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Building2, MapPin, ArrowRight } from "lucide-react"
import Link from "next/link"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"

export default function OfficeMainPage() {
  const sections = [
    {
      id: "office",
      name: "Office",
      description: "Manage office locations and operations",
      icon: <Building2 className="h-8 w-8 text-slate-700" />,
    },
    {
      id: "sites",
      name: "Sites",
      description: "Manage construction and project sites",
      icon: <MapPin className="h-8 w-8 text-slate-700" />,
    },
  ]

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/office">Office</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <h1 className="mb-8 text-3xl font-bold">Office Management</h1>

        <div className="grid gap-6 md:grid-cols-2">
          {sections.map((section) => (
            <Link key={section.id} href={`/dashboard/office/${section.id}`} className="block">
              <Card className="h-full transition-all hover:shadow-md">
                <CardHeader className="flex flex-row items-center gap-4">
                  {section.icon}
                  <div>
                    <CardTitle>{section.name}</CardTitle>
                    <CardDescription>{section.description}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="flex justify-end">
                  <ArrowRight className="h-5 w-5 text-slate-400" />
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
