"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"

export type DieselAddition = {
  id?: number
  property_id: string
  service_id: string
  date: string
  diesel_added: number
  added_by: string
}

export async function createDieselAddition(formData: FormData) {
  try {
    const supabase = createServerClient()

    // Extract and validate form data
    const propertyId = formData.get("property_id")
    const date = formData.get("date")
    const dieselAdded = formData.get("diesel_added")
    const addedBy = formData.get("added_by")

    // Validate required fields
    if (!propertyId || !date || !dieselAdded || !addedBy) {
      return {
        success: false,
        error: "Missing required fields",
      }
    }

    // Parse numeric value
    const dieselAddedValue = Number.parseFloat(dieselAdded.toString())
    if (isNaN(dieselAddedValue) || dieselAddedValue <= 0) {
      return {
        success: false,
        error: "Invalid diesel amount",
      }
    }

    const addition: DieselAddition = {
      property_id: propertyId.toString(),
      service_id: "electricity", // Hardcoded since this is specific to electricity service
      date: date.toString(),
      diesel_added: dieselAddedValue,
      added_by: addedBy.toString(),
    }

    const { data, error } = await supabase.from("diesel_additions").insert(addition).select()

    if (error) {
      console.error("Supabase error:", error)
      return {
        success: false,
        error: error.message,
      }
    }

    revalidatePath(`/dashboard/home/<USER>/electricity`)
    return {
      success: true,
      data,
    }
  } catch (error) {
    console.error("Server action error:", error)
    return {
      success: false,
      error: "An unexpected error occurred",
    }
  }
}

export async function getDieselAdditions(propertyId: string, limit = 10) {
  try {
    const supabase = createServerClient()

    const { data, error } = await supabase
      .from("diesel_additions")
      .select("*")
      .eq("property_id", propertyId)
      .eq("service_id", "electricity")
      .order("date", { ascending: false })
      .limit(limit)

    if (error) {
      console.error("Error fetching diesel additions:", error)
      return []
    }

    return data || []
  } catch (error) {
    console.error("Error in getDieselAdditions:", error)
    return []
  }
}

export async function deleteDieselAddition(id: number) {
  try {
    const supabase = createServerClient()

    // First get the property_id for path revalidation
    const { data: addition, error: fetchError } = await supabase
      .from("diesel_additions")
      .select("property_id")
      .eq("id", id)
      .single()

    if (fetchError) {
      return { success: false, error: fetchError.message }
    }

    const { error } = await supabase.from("diesel_additions").delete().eq("id", id)

    if (error) {
      return { success: false, error: error.message }
    }

    if (addition?.property_id) {
      revalidatePath(`/dashboard/home/<USER>/electricity`)
    }

    return { success: true }
  } catch (error) {
    console.error("Error in deleteDieselAddition:", error)
    return { success: false, error: "An unexpected error occurred" }
  }
}
