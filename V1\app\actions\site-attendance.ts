"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"

export type AttendanceRecord = {
  id?: string
  site_id: string
  worker_id: string
  worker_name: string
  worker_role: string
  date: string
  status: string
  hours_worked: number
  notes?: string
  created_at?: string
}

export async function submitSiteAttendance(formData: FormData) {
  const supabase = createClient()
  const siteId = formData.get("site_id") as string
  const date = formData.get("attendance_date") as string
  const workerIds = formData.getAll("worker_id") as string[]
  const workerNames = formData.getAll("worker_name") as string[]
  const workerRoles = formData.getAll("worker_role") as string[]
  const statuses = formData.getAll("status") as string[]
  const hoursWorked = formData.getAll("hours_worked") as string[]
  const notes = formData.getAll("notes") as string[]

  const records: AttendanceRecord[] = []

  // Create attendance records for each worker
  for (let i = 0; i < workerIds.length; i++) {
    records.push({
      site_id: siteId,
      worker_id: workerIds[i],
      worker_name: workerNames[i],
      worker_role: workerRoles[i],
      date: date,
      status: statuses[i],
      hours_worked: Number.parseFloat(hoursWorked[i]) || 0,
      notes: notes[i] || "",
      created_at: new Date().toISOString(),
    })
  }

  // Insert all records
  const { data, error } = await supabase.from("site_attendance").insert(records).select()

  if (error) {
    console.error("Error submitting attendance:", error)
    return { success: false, error: error.message }
  }

  revalidatePath(`/dashboard/office/sites/${siteId}`)
  return { success: true, data }
}

export async function getSiteAttendance(siteId: string, startDate?: string, endDate?: string) {
  const supabase = createClient()

  let query = supabase.from("site_attendance").select("*").eq("site_id", siteId).order("date", { ascending: false })

  if (startDate) {
    query = query.gte("date", startDate)
  }

  if (endDate) {
    query = query.lte("date", endDate)
  }

  const { data, error } = await query

  if (error) {
    console.error("Error fetching site attendance:", error)
    return []
  }

  return data as AttendanceRecord[]
}

export async function getWorkerAttendance(siteId: string, workerId: string) {
  const supabase = createClient()

  const { data, error } = await supabase
    .from("site_attendance")
    .select("*")
    .eq("site_id", siteId)
    .eq("worker_id", workerId)
    .order("date", { ascending: false })

  if (error) {
    console.error("Error fetching worker attendance:", error)
    return []
  }

  return data as AttendanceRecord[]
}
