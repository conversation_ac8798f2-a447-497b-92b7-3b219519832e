#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// <PERSON>ript to run integration tests with backend server
/// This script will:
/// 1. Check if backend server is running
/// 2. Start backend server if needed
/// 3. Wait for server to be ready
/// 4. Run integration tests
/// 5. Optionally stop the server

Future<void> main(List<String> args) async {
  print('🚀 Starting SRSR Property Management Integration Tests');
  print('=' * 60);

  final bool autoStartBackend = args.contains('--start-backend');
  final bool keepBackendRunning = args.contains('--keep-backend');
  
  Process? backendProcess;
  
  try {
    // Check if backend is already running
    final isBackendRunning = await checkBackendHealth();
    
    if (!isBackendRunning) {
      if (autoStartBackend) {
        print('📡 Backend not running. Starting backend server...');
        backendProcess = await startBackendServer();
        
        // Wait for backend to be ready
        await waitForBackendReady();
      } else {
        print('❌ Backend server is not running!');
        print('');
        print('Please start the backend server first:');
        print('  cd backend');
        print('  npm run dev');
        print('');
        print('Or run this script with --start-backend flag:');
        print('  dart test/run_integration_tests.dart --start-backend');
        exit(1);
      }
    } else {
      print('✅ Backend server is already running');
    }

    // Run the integration tests
    print('🧪 Running integration tests...');
    print('-' * 40);
    
    final testResult = await Process.run(
      'flutter',
      ['test', 'test/api_compatibility_test.dart', '--reporter=expanded'],
      workingDirectory: '.',
    );

    print(testResult.stdout);
    if (testResult.stderr.isNotEmpty) {
      print('STDERR: ${testResult.stderr}');
    }

    if (testResult.exitCode == 0) {
      print('');
      print('🎉 All integration tests passed!');
    } else {
      print('');
      print('❌ Some integration tests failed. Exit code: ${testResult.exitCode}');
    }

    exit(testResult.exitCode);

  } catch (e) {
    print('❌ Error running integration tests: $e');
    exit(1);
  } finally {
    // Clean up backend process if we started it
    if (backendProcess != null && !keepBackendRunning) {
      print('🛑 Stopping backend server...');
      backendProcess.kill();
      await backendProcess.exitCode;
      print('✅ Backend server stopped');
    }
  }
}

/// Check if backend server is healthy
Future<bool> checkBackendHealth() async {
  try {
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('http://192.168.1.3:3000/api'));
    request.headers.set('Content-Type', 'application/json');
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    client.close();
    
    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      return data['status'] == 'operational';
    }
    return false;
  } catch (e) {
    return false;
  }
}

/// Start the backend server
Future<Process> startBackendServer() async {
  final backendDir = Directory('../backend');
  if (!await backendDir.exists()) {
    throw Exception('Backend directory not found. Please ensure you are running this from the frontend directory.');
  }

  // Start the backend server
  final process = await Process.start(
    'npm',
    ['run', 'dev'],
    workingDirectory: backendDir.path,
    mode: ProcessStartMode.normal,
  );

  // Listen to output for debugging
  process.stdout.transform(utf8.decoder).listen((data) {
    print('[BACKEND] $data');
  });
  
  process.stderr.transform(utf8.decoder).listen((data) {
    print('[BACKEND ERROR] $data');
  });

  return process;
}

/// Wait for backend to be ready
Future<void> waitForBackendReady() async {
  print('⏳ Waiting for backend server to be ready...');
  
  for (int i = 0; i < 30; i++) {
    await Future.delayed(Duration(seconds: 2));
    
    if (await checkBackendHealth()) {
      print('✅ Backend server is ready!');
      return;
    }
    
    print('   Attempt ${i + 1}/30...');
  }
  
  throw Exception('Backend server did not become ready within 60 seconds');
}
