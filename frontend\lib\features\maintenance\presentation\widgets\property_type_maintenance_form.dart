import 'package:flutter/material.dart';
import '../../../../shared/models/property.dart';

class PropertyTypeMaintenanceForm extends StatefulWidget {
  final Property property;
  final Function(Map<String, dynamic>) onSubmit;

  const PropertyTypeMaintenanceForm({
    super.key,
    required this.property,
    required this.onSubmit,
  });

  @override
  State<PropertyTypeMaintenanceForm> createState() => _PropertyTypeMaintenanceFormState();
}

class _PropertyTypeMaintenanceFormState extends State<PropertyTypeMaintenanceForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedCategory = '';
  String _selectedPriority = 'medium';
  String _selectedServiceType = '';
  String _selectedDepartment = 'maintenance';

  @override
  void initState() {
    super.initState();
    final categories = widget.property.maintenanceCategories;
    if (categories.isNotEmpty) {
      _selectedCategory = categories.first;
    }

    final services = widget.property.defaultServices;
    if (services.isNotEmpty) {
      _selectedServiceType = services.first;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 20),
              _buildPropertyInfo(),
              const SizedBox(height: 20),
              _buildFormFields(),
              const SizedBox(height: 24),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          widget.property.typeIcon,
          color: widget.property.primaryColor,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Maintenance Request',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: widget.property.primaryColor,
                ),
              ),
              Text(
                '${widget.property.displayType} Property',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPropertyInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.property.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: widget.property.primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.business,
            color: widget.property.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.property.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: widget.property.primaryColor,
                  ),
                ),
                if (widget.property.address != null)
                  Text(
                    widget.property.address!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        TextFormField(
          controller: _titleController,
          decoration: InputDecoration(
            labelText: 'Issue Title *',
            hintText: 'Brief description of the issue',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: const Icon(Icons.title),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter an issue title';
            }
            if (value.trim().length < 5) {
              return 'Title must be at least 5 characters';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // Category and Service Type Row
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedCategory.isNotEmpty ? _selectedCategory : null,
                decoration: InputDecoration(
                  labelText: 'Category *',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.category),
                ),
                items: widget.property.maintenanceCategories.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(_formatCategoryName(category)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value ?? '';
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a category';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedServiceType.isNotEmpty ? _selectedServiceType : null,
                decoration: InputDecoration(
                  labelText: 'Service Type *',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.miscellaneous_services),
                ),
                items: widget.property.defaultServices.map((service) {
                  return DropdownMenuItem(
                    value: service,
                    child: Text(_formatServiceName(service)),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedServiceType = value ?? '';
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a service type';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Priority and Department Row
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedPriority,
                decoration: InputDecoration(
                  labelText: 'Priority *',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.priority_high),
                ),
                items: const [
                  DropdownMenuItem(value: 'low', child: Text('Low')),
                  DropdownMenuItem(value: 'medium', child: Text('Medium')),
                  DropdownMenuItem(value: 'high', child: Text('High')),
                  DropdownMenuItem(value: 'critical', child: Text('Critical')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedPriority = value ?? 'medium';
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedDepartment,
                decoration: InputDecoration(
                  labelText: 'Department',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  prefixIcon: const Icon(Icons.business_center),
                ),
                items: _getDepartmentOptions(),
                onChanged: (value) {
                  setState(() {
                    _selectedDepartment = value ?? 'maintenance';
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Description
        TextFormField(
          controller: _descriptionController,
          maxLines: 4,
          decoration: InputDecoration(
            labelText: 'Detailed Description *',
            hintText: 'Provide detailed information about the issue...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            prefixIcon: const Icon(Icons.description),
            alignLabelWithHint: true,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please provide a detailed description';
            }
            if (value.trim().length < 20) {
              return 'Description must be at least 20 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  List<DropdownMenuItem<String>> _getDepartmentOptions() {
    final departments = widget.property.isOffice
        ? ['maintenance', 'facilities', 'it', 'security', 'admin']
        : ['maintenance', 'security', 'utilities'];

    return departments.map((dept) {
      return DropdownMenuItem(
        value: dept,
        child: Text(_formatDepartmentName(dept)),
      );
    }).toList();
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.property.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Submit Maintenance Request',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final formData = {
        'property_id': widget.property.id,
        'title': _titleController.text.trim(),
        'description': _descriptionController.text.trim(),
        'category': _selectedCategory,
        'service_type': _selectedServiceType,
        'priority': _selectedPriority,
        'department': _selectedDepartment,
        'property_type': widget.property.type,
      };

      widget.onSubmit(formData);
    }
  }

  String _formatCategoryName(String category) {
    return category
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _formatServiceName(String service) {
    switch (service.toLowerCase()) {
      case 'ott':
        return 'OTT Services';
      case 'hvac':
        return 'HVAC';
      default:
        return _formatCategoryName(service);
    }
  }

  String _formatDepartmentName(String department) {
    switch (department.toLowerCase()) {
      case 'it':
        return 'IT Department';
      default:
        return _formatCategoryName(department);
    }
  }
}
