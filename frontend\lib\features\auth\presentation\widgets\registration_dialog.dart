import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';

import '../providers/auth_providers.dart';

class RegistrationDialog extends ConsumerStatefulWidget {
  const RegistrationDialog({super.key});

  @override
  ConsumerState<RegistrationDialog> createState() => _RegistrationDialogState();
}

class _RegistrationDialogState extends ConsumerState<RegistrationDialog> {
  late FormGroup form;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    form = FormGroup({
      'fullName': FormControl<String>(validators: [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
      ]),
      'email': FormControl<String>(validators: [
        Validators.required,
        Validators.email,
      ]),
      'username': FormControl<String>(validators: [
        Validators.minLength(3),
        Validators.maxLength(20),
      ]),
      'mobileNumber': FormControl<String>(validators: [
        Validators.pattern(r'^[\+]?[1-9][\d]{0,15}$'),
      ]),
      'phone': FormControl<String>(),
      'requestedRole': FormControl<String>(
        value: 'viewer',
        validators: [Validators.required],
      ),
      'password': FormControl<String>(validators: [
        Validators.required,
        Validators.minLength(8),
      ]),
      'confirmPassword': FormControl<String>(validators: [
        Validators.required,
      ]),
    });
  }



  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.person_add, color: Colors.blue),
          const SizedBox(width: 8),
          const Text('Create Account'),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: ReactiveForm(
          formGroup: form,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Info Text
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue[700], size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Your account will need admin approval before you can login.',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Full Name
                ReactiveTextField<String>(
                  formControlName: 'fullName',
                  decoration: const InputDecoration(
                    labelText: 'Full Name *',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Full name is required',
                    ValidationMessage.minLength: (_) => 'Name must be at least 2 characters',
                    ValidationMessage.maxLength: (_) => 'Name cannot exceed 50 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Email
                ReactiveTextField<String>(
                  formControlName: 'email',
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'Email Address *',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder(),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Email is required',
                    ValidationMessage.email: (_) => 'Please enter a valid email',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Username (Optional)
                ReactiveTextField<String>(
                  formControlName: 'username',
                  decoration: const InputDecoration(
                    labelText: 'Username (Optional)',
                    prefixIcon: Icon(Icons.alternate_email),
                    border: OutlineInputBorder(),
                    hintText: 'Choose a unique username',
                  ),
                  validationMessages: {
                    ValidationMessage.minLength: (_) => 'Username must be at least 3 characters',
                    ValidationMessage.maxLength: (_) => 'Username cannot exceed 20 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Mobile Number (Optional)
                ReactiveTextField<String>(
                  formControlName: 'mobileNumber',
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(
                    labelText: 'Mobile Number (Optional)',
                    prefixIcon: Icon(Icons.phone),
                    border: OutlineInputBorder(),
                    hintText: '+1234567890',
                  ),
                  validationMessages: {
                    ValidationMessage.pattern: (_) => 'Please enter a valid mobile number',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Requested Role
                ReactiveDropdownField<String>(
                  formControlName: 'requestedRole',
                  decoration: const InputDecoration(
                    labelText: 'Requested Role *',
                    prefixIcon: Icon(Icons.work),
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'viewer', child: Text('Viewer - Read-only access')),
                    DropdownMenuItem(value: 'maintenance', child: Text('Maintenance - Maintenance management')),
                    DropdownMenuItem(value: 'security', child: Text('Security - Security monitoring')),
                    DropdownMenuItem(value: 'manager', child: Text('Manager - Property management')),
                  ],
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Please select a role',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Password
                ReactiveTextField<String>(
                  formControlName: 'password',
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'Password *',
                    prefixIcon: Icon(Icons.lock),
                    border: OutlineInputBorder(),
                    hintText: 'At least 8 characters',
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Password is required',
                    ValidationMessage.minLength: (_) => 'Password must be at least 8 characters',
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Confirm Password
                ReactiveTextField<String>(
                  formControlName: 'confirmPassword',
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'Confirm Password *',
                    prefixIcon: Icon(Icons.lock_outline),
                    border: OutlineInputBorder(),
                  ),
                  validationMessages: {
                    ValidationMessage.required: (_) => 'Please confirm your password',
                  },
                ),


              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _handleRegistration,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Register'),
        ),
      ],
    );
  }

  Future<void> _handleRegistration() async {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;

      await ref.read(authStateProvider.notifier).register(
        formValue['email'] as String,
        formValue['password'] as String,
        formValue['fullName'] as String,
        formValue['phone'] as String?,
      );

      if (mounted) {
        Navigator.of(context).pop();
        AppUtils.showSuccessSnackBar(
          context,
          'Registration successful! Please wait for admin approval.',
        );
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Registration failed: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
