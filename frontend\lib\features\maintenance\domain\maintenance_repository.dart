import '../../../shared/models/maintenance_issue.dart';

abstract class MaintenanceRepository {
  Future<List<MaintenanceIssue>> getMaintenanceIssues();
  Future<MaintenanceIssue> getMaintenanceIssueById(String id);
  Future<MaintenanceIssue> createMaintenanceIssue(MaintenanceIssue issue);
  Future<MaintenanceIssue> updateMaintenanceIssue(MaintenanceIssue issue);
  Future<void> deleteMaintenanceIssue(String id);
  Future<List<MaintenanceIssue>> getMaintenanceIssuesByProperty(String propertyId);
  Future<MaintenanceIssue> assignMaintenanceIssue(String issueId, String assignedTo, {String? notes});
  Future<MaintenanceIssue> escalateMaintenanceIssue(String issueId);
  Future<MaintenanceIssue> getIssueById(String id);
}
