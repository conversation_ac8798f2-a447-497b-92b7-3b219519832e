import 'package:flutter/material.dart';

enum UserRole {
  admin,
  manager,
  security,
  maintenance,
  viewer,
}

extension UserRoleExtension on UserRole {
  String get name {
    switch (this) {
      case UserRole.admin:
        return 'Admin';
      case UserRole.manager:
        return 'Manager';
      case UserRole.security:
        return 'Security';
      case UserRole.maintenance:
        return 'Maintenance';
      case UserRole.viewer:
        return 'Viewer';
    }
  }

  String get description {
    switch (this) {
      case UserRole.admin:
        return 'Full system access and user management';
      case UserRole.manager:
        return 'Property and team management';
      case UserRole.security:
        return 'Security monitoring and incident management';
      case UserRole.maintenance:
        return 'Maintenance and repair management';
      case UserRole.viewer:
        return 'Read-only access to dashboards';
    }
  }

  Color get primaryColor {
    switch (this) {
      case UserRole.admin:
        return Colors.red;
      case UserRole.manager:
        return Colors.blue;
      case UserRole.security:
        return Colors.purple;
      case UserRole.maintenance:
        return Colors.green;
      case UserRole.viewer:
        return Colors.grey;
    }
  }

  Color get accentColor {
    switch (this) {
      case UserRole.admin:
        return Colors.orange;
      case UserRole.manager:
        return Colors.lightBlue;
      case UserRole.security:
        return Colors.deepPurple;
      case UserRole.maintenance:
        return Colors.lightGreen;
      case UserRole.viewer:
        return Colors.blueGrey;
    }
  }

  IconData get icon {
    switch (this) {
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.manager:
        return Icons.business_center;
      case UserRole.security:
        return Icons.security;
      case UserRole.maintenance:
        return Icons.build;
      case UserRole.viewer:
        return Icons.visibility;
    }
  }

  List<String> get permissions {
    switch (this) {
      case UserRole.admin:
        return [
          'users.create',
          'users.read',
          'users.update',
          'users.delete',
          'users.approve',
          'roles.manage',
          'permissions.manage',
          'properties.create',
          'properties.read',
          'properties.update',
          'properties.delete',
          'maintenance.create',
          'maintenance.read',
          'maintenance.update',
          'maintenance.delete',
          'maintenance.assign',
          'maintenance.escalate',
          'attendance.create',
          'attendance.read',
          'attendance.update',
          'attendance.delete',
          'fuel.create',
          'fuel.read',
          'fuel.update',
          'fuel.delete',
          'settings.configure',
          'thresholds.configure',
          'reports.generate',
          'reports.export',
        ];
      case UserRole.manager:
        return [
          'properties.create',
          'properties.read',
          'properties.update',
          'maintenance.create',
          'maintenance.read',
          'maintenance.update',
          'maintenance.assign',
          'attendance.create',
          'attendance.read',
          'attendance.update',
          'fuel.create',
          'fuel.read',
          'fuel.update',
          'reports.generate',
          'users.read', // Can view team members
        ];
      case UserRole.security:
        return [
          'properties.read',
          'maintenance.create',
          'maintenance.read',
          'maintenance.update',
          'attendance.read',
          'fuel.read',
          'security.manage',
          'incidents.create',
          'incidents.read',
          'incidents.update',
        ];
      case UserRole.maintenance:
        return [
          'properties.read',
          'maintenance.create',
          'maintenance.read',
          'maintenance.update',
          'fuel.create',
          'fuel.read',
          'fuel.update',
          'attendance.read',
        ];
      case UserRole.viewer:
        return [
          'properties.read',
          'maintenance.read',
          'attendance.read',
          'fuel.read',
        ];
    }
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  bool canAccessScreen(String screenPath) {
    switch (screenPath) {
      case '/admin':
      case '/users':
      case '/roles':
      case '/settings':
        return this == UserRole.admin;
      case '/properties':
        return hasPermission('properties.read');
      case '/maintenance':
        return hasPermission('maintenance.read');
      case '/attendance':
        return hasPermission('attendance.read');
      case '/fuel':
        return hasPermission('fuel.read');
      case '/reports':
        return hasPermission('reports.generate');
      default:
        return true; // Allow access to dashboard and other common screens
    }
  }

  List<BottomNavigationBarItem> getBottomNavItems() {
    final items = <BottomNavigationBarItem>[
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
    ];

    if (hasPermission('properties.read')) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.business),
        label: 'Properties',
      ));
    }

    if (hasPermission('maintenance.read')) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.build),
        label: 'Maintenance',
      ));
    }

    if (hasPermission('attendance.read')) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.people),
        label: 'Attendance',
      ));
    }

    // Admin gets additional navigation items
    if (this == UserRole.admin) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.admin_panel_settings),
        label: 'Admin',
      ));
    }

    // Ensure minimum 2 items for BottomNavigationBar requirement
    if (items.length < 2) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.person),
        label: 'Profile',
      ));
    }

    return items;
  }

  ThemeData getThemeData(BuildContext context) {
    final baseTheme = Theme.of(context);

    return baseTheme.copyWith(
      primaryColor: primaryColor,
      colorScheme: baseTheme.colorScheme.copyWith(
        primary: primaryColor,
        secondary: accentColor,
      ),
      appBarTheme: baseTheme.appBarTheme.copyWith(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
      ),
      floatingActionButtonTheme: baseTheme.floatingActionButtonTheme.copyWith(
        backgroundColor: primaryColor,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}

class Permission {
  static const String usersCreate = 'users.create';
  static const String usersRead = 'users.read';
  static const String usersUpdate = 'users.update';
  static const String usersDelete = 'users.delete';
  static const String usersApprove = 'users.approve';

  static const String rolesManage = 'roles.manage';
  static const String permissionsManage = 'permissions.manage';

  static const String propertiesCreate = 'properties.create';
  static const String propertiesRead = 'properties.read';
  static const String propertiesUpdate = 'properties.update';
  static const String propertiesDelete = 'properties.delete';

  static const String maintenanceCreate = 'maintenance.create';
  static const String maintenanceRead = 'maintenance.read';
  static const String maintenanceUpdate = 'maintenance.update';
  static const String maintenanceDelete = 'maintenance.delete';
  static const String maintenanceAssign = 'maintenance.assign';
  static const String maintenanceEscalate = 'maintenance.escalate';

  static const String attendanceCreate = 'attendance.create';
  static const String attendanceRead = 'attendance.read';
  static const String attendanceUpdate = 'attendance.update';
  static const String attendanceDelete = 'attendance.delete';

  static const String fuelCreate = 'fuel.create';
  static const String fuelRead = 'fuel.read';
  static const String fuelUpdate = 'fuel.update';
  static const String fuelDelete = 'fuel.delete';

  static const String settingsConfigure = 'settings.configure';
  static const String thresholdsConfigure = 'thresholds.configure';

  static const String reportsGenerate = 'reports.generate';
  static const String reportsExport = 'reports.export';

  static const String securityManage = 'security.manage';
  static const String incidentsCreate = 'incidents.create';
  static const String incidentsRead = 'incidents.read';
  static const String incidentsUpdate = 'incidents.update';
}
