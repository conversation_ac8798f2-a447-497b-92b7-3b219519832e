import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../core/auth/models/user_role.dart';

import '../providers/auth_providers.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);
    final user = authState.user;

    if (user == null) {
      return const Scaffold(
        body: Center(
          child: Text('User not found'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.push('/settings'),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh user data - for now just invalidate the provider
          ref.invalidate(authStateProvider);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Header
              _buildProfileHeader(user),
              const SizedBox(height: AppConstants.largePadding),

              // User Information
              _buildUserInformation(user),
              const SizedBox(height: AppConstants.largePadding),

              // Role & Permissions
              _buildRoleSection(user),
              const SizedBox(height: AppConstants.largePadding),

              // Properties Access
              _buildPropertiesSection(user),
              const SizedBox(height: AppConstants.largePadding),

              // Account Actions
              _buildAccountActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader(dynamic user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 40,
              backgroundColor: user.getThemeData(context).primaryColor,
              child: Text(
                user.initials,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),

            // User Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.fullName,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: user.getThemeData(context).primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      () {
                        print('🔍 DEBUG: user.role type: ${user.role.runtimeType}');
                        print('🔍 DEBUG: user.role value: ${user.role}');
                        print('🔍 DEBUG: user.role toString: ${user.role.toString()}');

                        // Try to access the name property safely
                        try {
                          final roleName = user.role.name;
                          print('🔍 DEBUG: Successfully got role name: $roleName');
                          return roleName;
                        } catch (e) {
                          print('🔍 DEBUG: Error accessing role.name: $e');

                          // Try alternative approaches
                          try {
                            final roleString = user.role.toString();
                            print('🔍 DEBUG: Role toString: $roleString');

                            // Extract role name from toString if it's like "UserRole.admin"
                            if (roleString.startsWith('UserRole.')) {
                              final roleName = roleString.split('.').last;
                              final capitalizedName = roleName[0].toUpperCase() + roleName.substring(1);
                              print('🔍 DEBUG: Extracted role name: $capitalizedName');
                              return capitalizedName;
                            }

                            return roleString;
                          } catch (e2) {
                            print('🔍 DEBUG: Error with toString approach: $e2');
                            return 'Unknown Role';
                          }
                        }
                      }(),
                      style: TextStyle(
                        color: user.getThemeData(context).primaryColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInformation(dynamic user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'User Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            _buildInfoRow('Username', user.username ?? 'Not set'),
            _buildInfoRow('Email', user.email),
            _buildInfoRow('Full Name', user.fullName),
            if (user.mobileNumber != null) _buildInfoRow('Mobile', user.mobileNumber!),
            if (user.phone != null) _buildInfoRow('Phone', user.phone!),
            _buildInfoRow('Status', user.isActive ? 'Active' : 'Inactive'),
            _buildInfoRow('Member Since', AppUtils.formatDate(user.createdAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleSection(dynamic user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Role & Permissions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Role Badge
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: user.getThemeData(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: user.getThemeData(context).primaryColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    () {
                      try {
                        return user.role.icon;
                      } catch (e) {
                        print('🔍 DEBUG: Error accessing role.icon: $e');
                        return Icons.person;
                      }
                    }(),
                    color: user.getThemeData(context).primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        () {
                          try {
                            return user.role.name;
                          } catch (e) {
                            print('🔍 DEBUG: Error accessing role.name in role section: $e');
                            final roleString = user.role.toString();
                            if (roleString.startsWith('UserRole.')) {
                              final roleName = roleString.split('.').last;
                              return roleName[0].toUpperCase() + roleName.substring(1);
                            }
                            return 'Unknown Role';
                          }
                        }(),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: user.getThemeData(context).primaryColor,
                        ),
                      ),
                      Text(
                        () {
                          try {
                            return user.role.description;
                          } catch (e) {
                            print('🔍 DEBUG: Error accessing role.description: $e');
                            return 'Role description not available';
                          }
                        }(),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertiesSection(dynamic user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Property Access',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // TODO: Implement property assignments when backend supports it
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue[700]),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Property assignments will be displayed here once implemented.',
                      style: TextStyle(color: Colors.blue[700]),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => context.push('/settings'),
            ),

            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('Security'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => context.push('/security'),
            ),

            const Divider(),

            ListTile(
              leading: Icon(Icons.logout, color: Colors.red[600]),
              title: Text(
                'Logout',
                style: TextStyle(color: Colors.red[600]),
              ),
              onTap: _showLogoutConfirmation,
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              setState(() => _isLoading = true);

              try {
                await ref.read(authStateProvider.notifier).logout();
                if (mounted) {
                  context.go('/login');
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showErrorSnackBar(context, 'Failed to logout: $e');
                }
              } finally {
                if (mounted) {
                  setState(() => _isLoading = false);
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
