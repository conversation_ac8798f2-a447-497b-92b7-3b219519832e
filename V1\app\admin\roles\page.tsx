import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"
import { RoleManagement } from "@/components/role-management"

export default async function RolesPage() {
  // Simple, direct admin check
  const user = await getCurrentUser()

  if (!user || (user.username !== "admin1" && user.role !== "admin")) {
    redirect("/unauthorized")
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Role Management</h1>
        <p className="text-gray-600">Configure user roles and access levels</p>
      </div>
      <RoleManagement />
    </div>
  )
}
