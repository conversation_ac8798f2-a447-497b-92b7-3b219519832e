import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/widgets/stat_card.dart';

class ReportsScreen extends ConsumerStatefulWidget {
  const ReportsScreen({super.key});

  @override
  ConsumerState<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends ConsumerState<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _selectedDateRange = DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 30)),
      end: DateTime.now(),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _selectDateRange,
          ),
          RoleBasedWidget(
            requiredPermissions: const ['reports.export'],
            child: IconButton(
              icon: const Icon(Icons.download),
              onPressed: _exportReports,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Properties', icon: Icon(Icons.business)),
            Tab(text: 'Maintenance', icon: Icon(Icons.build)),
            Tab(text: 'Attendance', icon: Icon(Icons.people)),
            Tab(text: 'Fuel', icon: Icon(Icons.local_gas_station)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildPropertiesTab(),
          _buildMaintenanceTab(),
          _buildAttendanceTab(),
          _buildFuelTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Display
          _buildDateRangeCard(),
          const SizedBox(height: AppConstants.defaultPadding),

          // Key Metrics
          Text(
            'Key Metrics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: AppConstants.smallPadding,
            mainAxisSpacing: AppConstants.smallPadding,
            childAspectRatio: 1.2,
            children: [
              StatCard(
                title: 'Total Properties',
                value: '12',
                icon: Icons.business,
                color: Colors.blue,
                onTap: () => _tabController.animateTo(1),
              ),
              StatCard(
                title: 'Active Issues',
                value: '8',
                icon: Icons.warning,
                color: Colors.orange,
                onTap: () => _tabController.animateTo(2),
              ),
              StatCard(
                title: 'Staff Present',
                value: '45/52',
                icon: Icons.people,
                color: Colors.green,
                onTap: () => _tabController.animateTo(3),
              ),
              StatCard(
                title: 'Fuel Efficiency',
                value: '85%',
                icon: Icons.local_gas_station,
                color: Colors.purple,
                onTap: () => _tabController.animateTo(4),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Quick Actions
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          _buildQuickActionsCard(),
        ],
      ),
    );
  }

  Widget _buildPropertiesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeCard(),
          const SizedBox(height: AppConstants.defaultPadding),

          // Property Performance
          _buildReportCard(
            'Property Performance',
            'Operational status and efficiency metrics',
            Icons.business,
            Colors.blue,
            () => _showPropertyPerformanceReport(),
          ),

          _buildReportCard(
            'Occupancy Report',
            'Property occupancy rates and trends',
            Icons.home,
            Colors.green,
            () => _showOccupancyReport(),
          ),

          _buildReportCard(
            'Revenue Analysis',
            'Property revenue and cost analysis',
            Icons.attach_money,
            Colors.purple,
            () => _showRevenueReport(),
          ),

          _buildReportCard(
            'Utility Usage',
            'Electricity, water, and other utilities',
            Icons.electrical_services,
            Colors.orange,
            () => _showUtilityReport(),
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeCard(),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildReportCard(
            'Issue Summary',
            'Overview of maintenance issues by status',
            Icons.build,
            Colors.orange,
            () => _showMaintenanceIssueReport(),
          ),

          _buildReportCard(
            'Response Time Analysis',
            'Average response and resolution times',
            Icons.timer,
            Colors.blue,
            () => _showResponseTimeReport(),
          ),

          _buildReportCard(
            'Cost Analysis',
            'Maintenance costs and budget tracking',
            Icons.attach_money,
            Colors.green,
            () => _showMaintenanceCostReport(),
          ),

          _buildReportCard(
            'Preventive Maintenance',
            'Scheduled maintenance completion rates',
            Icons.schedule,
            Colors.purple,
            () => _showPreventiveMaintenanceReport(),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeCard(),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildReportCard(
            'Daily Attendance',
            'Daily attendance rates by property',
            Icons.today,
            Colors.blue,
            () => _showDailyAttendanceReport(),
          ),

          _buildReportCard(
            'Staff Performance',
            'Individual staff attendance patterns',
            Icons.person,
            Colors.green,
            () => _showStaffPerformanceReport(),
          ),

          _buildReportCard(
            'Overtime Analysis',
            'Overtime hours and cost analysis',
            Icons.access_time,
            Colors.orange,
            () => _showOvertimeReport(),
          ),

          _buildReportCard(
            'Leave Management',
            'Leave requests and approval status',
            Icons.event_busy,
            Colors.purple,
            () => _showLeaveReport(),
          ),
        ],
      ),
    );
  }

  Widget _buildFuelTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeCard(),
          const SizedBox(height: AppConstants.defaultPadding),

          _buildReportCard(
            'Fuel Consumption',
            'Generator fuel usage by property',
            Icons.local_gas_station,
            Colors.red,
            () => _showFuelConsumptionReport(),
          ),

          _buildReportCard(
            'Efficiency Analysis',
            'Fuel efficiency and optimization',
            Icons.trending_up,
            Colors.green,
            () => _showFuelEfficiencyReport(),
          ),

          _buildReportCard(
            'Cost Tracking',
            'Fuel costs and budget analysis',
            Icons.attach_money,
            Colors.blue,
            () => _showFuelCostReport(),
          ),

          _buildReportCard(
            'Inventory Management',
            'Fuel stock levels and refill schedules',
            Icons.inventory,
            Colors.orange,
            () => _showFuelInventoryReport(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            const Icon(Icons.date_range),
            const SizedBox(width: AppConstants.smallPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Report Period'),
                  Text(
                    _selectedDateRange != null
                        ? '${AppUtils.formatDate(_selectedDateRange!.start)} - ${AppUtils.formatDate(_selectedDateRange!.end)}'
                        : 'Select date range',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            TextButton(
              onPressed: _selectDateRange,
              child: const Text('Change'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('Export All Reports'),
              subtitle: const Text('Download comprehensive report'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _exportReports,
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('Schedule Reports'),
              subtitle: const Text('Set up automated reporting'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _scheduleReports,
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('Share Reports'),
              subtitle: const Text('Share with team members'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _shareReports,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null && picked != _selectedDateRange) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _exportReports() {
    // TODO: Implement report export functionality
    AppUtils.showInfoSnackBar(context, 'Export functionality coming soon');
  }

  void _scheduleReports() {
    // TODO: Implement report scheduling
    AppUtils.showInfoSnackBar(context, 'Report scheduling coming soon');
  }

  void _shareReports() {
    // TODO: Implement report sharing
    AppUtils.showInfoSnackBar(context, 'Report sharing coming soon');
  }

  // Report detail methods
  void _showPropertyPerformanceReport() {
    AppUtils.showInfoSnackBar(context, 'Property performance report coming soon');
  }

  void _showOccupancyReport() {
    AppUtils.showInfoSnackBar(context, 'Occupancy report coming soon');
  }

  void _showRevenueReport() {
    AppUtils.showInfoSnackBar(context, 'Revenue report coming soon');
  }

  void _showUtilityReport() {
    AppUtils.showInfoSnackBar(context, 'Utility report coming soon');
  }

  void _showMaintenanceIssueReport() {
    AppUtils.showInfoSnackBar(context, 'Maintenance issue report coming soon');
  }

  void _showResponseTimeReport() {
    AppUtils.showInfoSnackBar(context, 'Response time report coming soon');
  }

  void _showMaintenanceCostReport() {
    AppUtils.showInfoSnackBar(context, 'Maintenance cost report coming soon');
  }

  void _showPreventiveMaintenanceReport() {
    AppUtils.showInfoSnackBar(context, 'Preventive maintenance report coming soon');
  }

  void _showDailyAttendanceReport() {
    AppUtils.showInfoSnackBar(context, 'Daily attendance report coming soon');
  }

  void _showStaffPerformanceReport() {
    AppUtils.showInfoSnackBar(context, 'Staff performance report coming soon');
  }

  void _showOvertimeReport() {
    AppUtils.showInfoSnackBar(context, 'Overtime report coming soon');
  }

  void _showLeaveReport() {
    AppUtils.showInfoSnackBar(context, 'Leave report coming soon');
  }

  void _showFuelConsumptionReport() {
    AppUtils.showInfoSnackBar(context, 'Fuel consumption report coming soon');
  }

  void _showFuelEfficiencyReport() {
    AppUtils.showInfoSnackBar(context, 'Fuel efficiency report coming soon');
  }

  void _showFuelCostReport() {
    AppUtils.showInfoSnackBar(context, 'Fuel cost report coming soon');
  }

  void _showFuelInventoryReport() {
    AppUtils.showInfoSnackBar(context, 'Fuel inventory report coming soon');
  }
}
