import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Sensors tab - Environmental monitoring and sensor data
class SensorsTab extends StatefulWidget {
  const SensorsTab({super.key});

  @override
  State<SensorsTab> createState() => _SensorsTabState();
}

class _SensorsTabState extends State<SensorsTab> {
  final List<SensorDevice> _sensors = [
    SensorDevice(
      id: 'sensor_001',
      name: 'Water Level Sensor',
      type: SensorType.waterLevel,
      location: 'Building A - Water Tank',
      status: SensorStatus.active,
      currentValue: 85.0,
      unit: '%',
      minThreshold: 20.0,
      maxThreshold: 95.0,
      lastUpdate: DateTime.now().subtract(const Duration(minutes: 2)),
      batteryLevel: 92.0,
    ),
    SensorDevice(
      id: 'sensor_002',
      name: 'Temperature Sensor',
      type: SensorType.temperature,
      location: 'Building A - Server Room',
      status: SensorStatus.active,
      currentValue: 22.5,
      unit: '°C',
      minThreshold: 18.0,
      maxThreshold: 25.0,
      lastUpdate: DateTime.now().subtract(const Duration(minutes: 1)),
      batteryLevel: 78.0,
    ),
    SensorDevice(
      id: 'sensor_003',
      name: 'Humidity Sensor',
      type: SensorType.humidity,
      location: 'Building A - Server Room',
      status: SensorStatus.active,
      currentValue: 45.0,
      unit: '%',
      minThreshold: 30.0,
      maxThreshold: 60.0,
      lastUpdate: DateTime.now().subtract(const Duration(minutes: 1)),
      batteryLevel: 65.0,
    ),
    SensorDevice(
      id: 'sensor_004',
      name: 'Motion Detector',
      type: SensorType.motion,
      location: 'Building B - Main Entrance',
      status: SensorStatus.active,
      currentValue: 0.0,
      unit: '',
      lastUpdate: DateTime.now().subtract(const Duration(minutes: 15)),
      batteryLevel: 88.0,
    ),
    SensorDevice(
      id: 'sensor_005',
      name: 'Smoke Detector',
      type: SensorType.smoke,
      location: 'Building A - Kitchen',
      status: SensorStatus.warning,
      currentValue: 15.0,
      unit: 'ppm',
      maxThreshold: 50.0,
      lastUpdate: DateTime.now().subtract(const Duration(minutes: 3)),
      batteryLevel: 45.0,
    ),
    SensorDevice(
      id: 'sensor_006',
      name: 'Pressure Sensor',
      type: SensorType.pressure,
      location: 'Building A - Water System',
      status: SensorStatus.active,
      currentValue: 42.0,
      unit: 'PSI',
      minThreshold: 30.0,
      maxThreshold: 60.0,
      lastUpdate: DateTime.now().subtract(const Duration(seconds: 30)),
      batteryLevel: 95.0,
    ),
    SensorDevice(
      id: 'sensor_007',
      name: 'Air Quality Sensor',
      type: SensorType.airQuality,
      location: 'Building A - Lobby',
      status: SensorStatus.offline,
      currentValue: 0.0,
      unit: 'AQI',
      maxThreshold: 100.0,
      lastUpdate: DateTime.now().subtract(const Duration(hours: 2)),
      batteryLevel: 12.0,
    ),
    SensorDevice(
      id: 'sensor_008',
      name: 'Vibration Sensor',
      type: SensorType.vibration,
      location: 'Building B - Generator',
      status: SensorStatus.active,
      currentValue: 2.3,
      unit: 'mm/s',
      maxThreshold: 10.0,
      lastUpdate: DateTime.now().subtract(const Duration(seconds: 45)),
      batteryLevel: 72.0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildSensorOverview(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildQuickActions(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildSensorsList(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSensorOverview(BuildContext context) {
    final activeSensors = _sensors.where((s) => s.status == SensorStatus.active).length;
    final warningSensors = _sensors.where((s) => s.status == SensorStatus.warning).length;
    final offlineSensors = _sensors.where((s) => s.status == SensorStatus.offline).length;
    final lowBatterySensors = _sensors.where((s) => s.batteryLevel < 20).length;

    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Active',
            activeSensors.toString(),
            Icons.sensors,
            Colors.green,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildOverviewCard(
            'Warnings',
            warningSensors.toString(),
            Icons.warning,
            Colors.orange,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildOverviewCard(
            'Offline',
            offlineSensors.toString(),
            Icons.sensors_off,
            Colors.red,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildOverviewCard(
            'Low Battery',
            lowBatterySensors.toString(),
            Icons.battery_alert,
            Colors.amber,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _calibrateAllSensors,
            icon: const Icon(Icons.tune),
            label: const Text('Calibrate All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _refreshAllSensors,
            icon: const Icon(Icons.refresh),
            label: const Text('Refresh All'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _showSensorMap,
            icon: const Icon(Icons.map),
            label: const Text('Sensor Map'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSensorsList(BuildContext context) {
    return ListView.separated(
      itemCount: _sensors.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConstants.smallPadding),
      itemBuilder: (context, index) {
        return _buildSensorCard(_sensors[index]);
      },
    );
  }

  Widget _buildSensorCard(SensorDevice sensor) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getStatusColor(sensor.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getSensorIcon(sensor.type),
                    color: _getStatusColor(sensor.status),
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        sensor.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        sensor.location,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildStatusChip(sensor.status),
                          const SizedBox(width: 8),
                          _buildBatteryIndicator(sensor.batteryLevel),
                        ],
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${sensor.currentValue.toStringAsFixed(1)}${sensor.unit}',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getValueColor(sensor),
                      ),
                    ),
                    Text(
                      _getLastUpdateText(sensor.lastUpdate),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (sensor.minThreshold != null || sensor.maxThreshold != null) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              _buildThresholdIndicator(sensor),
            ],
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _calibrateSensor(sensor),
                    icon: const Icon(Icons.tune, size: 16),
                    label: const Text('Calibrate'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showSensorHistory(sensor),
                    icon: const Icon(Icons.history, size: 16),
                    label: const Text('History'),
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _configureSensor(sensor),
                    icon: const Icon(Icons.settings, size: 16),
                    label: const Text('Settings'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(SensorStatus status) {
    Color color = _getStatusColor(status);
    String text = status.name.toUpperCase();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  Widget _buildBatteryIndicator(double batteryLevel) {
    Color color;
    IconData icon;

    if (batteryLevel > 50) {
      color = Colors.green;
      icon = Icons.battery_full;
    } else if (batteryLevel > 20) {
      color = Colors.orange;
      icon = Icons.battery_3_bar;
    } else {
      color = Colors.red;
      icon = Icons.battery_alert;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 2),
        Text(
          '${batteryLevel.toInt()}%',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildThresholdIndicator(SensorDevice sensor) {
    final progress = sensor.maxThreshold != null
        ? (sensor.currentValue / sensor.maxThreshold!).clamp(0.0, 1.0)
        : 0.5;

    Color progressColor;
    if (sensor.minThreshold != null && sensor.currentValue < sensor.minThreshold!) {
      progressColor = Colors.red;
    } else if (sensor.maxThreshold != null && sensor.currentValue > sensor.maxThreshold!) {
      progressColor = Colors.red;
    } else {
      progressColor = Colors.green;
    }

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Min: ${sensor.minThreshold?.toStringAsFixed(1) ?? 'N/A'}${sensor.unit}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              'Max: ${sensor.maxThreshold?.toStringAsFixed(1) ?? 'N/A'}${sensor.unit}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
        ),
      ],
    );
  }

  Color _getStatusColor(SensorStatus status) {
    switch (status) {
      case SensorStatus.active:
        return Colors.green;
      case SensorStatus.warning:
        return Colors.orange;
      case SensorStatus.offline:
        return Colors.red;
      case SensorStatus.maintenance:
        return Colors.blue;
    }
  }

  Color _getValueColor(SensorDevice sensor) {
    if (sensor.minThreshold != null && sensor.currentValue < sensor.minThreshold!) {
      return Colors.red;
    } else if (sensor.maxThreshold != null && sensor.currentValue > sensor.maxThreshold!) {
      return Colors.red;
    } else {
      return Colors.green;
    }
  }

  IconData _getSensorIcon(SensorType type) {
    switch (type) {
      case SensorType.temperature:
        return Icons.thermostat;
      case SensorType.humidity:
        return Icons.water_drop;
      case SensorType.pressure:
        return Icons.compress;
      case SensorType.motion:
        return Icons.motion_photos_on;
      case SensorType.smoke:
        return Icons.smoke_free;
      case SensorType.waterLevel:
        return Icons.water;
      case SensorType.airQuality:
        return Icons.air;
      case SensorType.vibration:
        return Icons.vibration;
    }
  }

  String _getLastUpdateText(DateTime lastUpdate) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdate);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _calibrateAllSensors() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Calibrating all sensors...')),
    );
  }

  void _refreshAllSensors() {
    setState(() {
      for (var sensor in _sensors) {
        sensor.lastUpdate = DateTime.now();
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All sensors refreshed')),
    );
  }

  void _showSensorMap() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sensor Map'),
        content: const Text('Interactive sensor map coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _calibrateSensor(SensorDevice sensor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Calibrating ${sensor.name}...')),
    );
  }

  void _showSensorHistory(SensorDevice sensor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${sensor.name} History'),
        content: const Text('Sensor data history coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _configureSensor(SensorDevice sensor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${sensor.name} Settings'),
        content: const Text('Sensor configuration options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// Sensor device data model
class SensorDevice {
  final String id;
  final String name;
  final SensorType type;
  final String location;
  final SensorStatus status;
  final double currentValue;
  final String unit;
  final double? minThreshold;
  final double? maxThreshold;
  DateTime lastUpdate;
  final double batteryLevel;

  SensorDevice({
    required this.id,
    required this.name,
    required this.type,
    required this.location,
    required this.status,
    required this.currentValue,
    required this.unit,
    this.minThreshold,
    this.maxThreshold,
    required this.lastUpdate,
    required this.batteryLevel,
  });
}

/// Sensor type enum
enum SensorType {
  temperature,
  humidity,
  pressure,
  motion,
  smoke,
  waterLevel,
  airQuality,
  vibration,
}

/// Sensor status enum
enum SensorStatus {
  active,
  warning,
  offline,
  maintenance,
}
