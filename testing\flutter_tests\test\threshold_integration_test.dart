import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

// Import necessary files
import 'package:srsr_property_management/features/admin/data/threshold_api_service.dart';
import 'package:srsr_property_management/shared/models/api_response.dart';

void main() {
  group('Threshold Integration Tests', () {
    late Dio dio;
    late ThresholdApiService thresholdService;
    late ProviderContainer container;

    setUpAll(() async {
      print('🧪 Setting up Threshold Integration Tests...');

      // Initialize Dio with test configuration
      dio = Dio(BaseOptions(
        baseUrl: 'http://192.168.1.3:3000',
        connectTimeout: const Duration(milliseconds: 10000),
        receiveTimeout: const Duration(milliseconds: 10000),
        headers: {
          'Content-Type': 'application/json',
        },
      ));

      // Initialize services
      thresholdService = ThresholdApiService(dio);

      // Create provider container
      container = ProviderContainer(
        overrides: [
          // Override auth state for testing - no overrides needed for basic API testing
        ],
      );
    });

    tearDownAll(() {
      container.dispose();
      dio.close();
    });

    test('Should parse real API response correctly', () async {
      print('🧪 Testing real API response parsing...');

      try {
        // Test with actual API response structure from logs
        final sampleApiResponse = {
          'success': true,
          'data': [
            {
              'id': '10dc8353-8920-4367-bb76-5746da900d89',
              'service_type': 'electricity',
              'metric_name': 'generator_fuel_level',
              'warning_threshold': '30', // String as returned by API
              'critical_threshold': '15', // String as returned by API
              'unit': 'percentage',
              'description': 'Generator fuel level thresholds',
              'is_active': true,
              'created_at': '2025-05-30T19:12:24.448Z',
              'updated_at': '2025-05-30T19:12:24.448Z',
            }
          ]
        };

        final apiResponse = ApiResponse<List<ThresholdConfig>>.fromJson(
          sampleApiResponse,
          (json) => (json as List).map((item) => ThresholdConfig.fromJson(item)).toList(),
        );

        expect(apiResponse.success, isTrue);
        expect(apiResponse.data, isNotNull);
        expect(apiResponse.data!.length, equals(1));

        final threshold = apiResponse.data!.first;
        expect(threshold.id, equals('10dc8353-8920-4367-bb76-5746da900d89'));
        expect(threshold.serviceType, equals('electricity'));
        expect(threshold.metricName, equals('generator_fuel_level'));
        expect(threshold.warningThreshold, equals(30.0)); // Should be parsed as double
        expect(threshold.criticalThreshold, equals(15.0)); // Should be parsed as double
        expect(threshold.unit, equals('percentage'));
        expect(threshold.description, equals('Generator fuel level thresholds'));
        expect(threshold.isActive, isTrue);

        print('✅ Real API response parsing test passed');
      } catch (e, stackTrace) {
        print('❌ Real API response parsing failed: $e');
        print('Stack trace: $stackTrace');
        fail('Failed to parse real API response: $e');
      }
    });

    test('Should fetch thresholds from API successfully', () async {
      print('🧪 Testing threshold API fetch...');

      try {
        final response = await thresholdService.getThresholds();

        print('📊 API Response received');
        print('Success: ${response.success}');
        print('Data count: ${response.data?.length ?? 0}');

        expect(response.success, isTrue);
        expect(response.data, isNotNull);
        expect(response.data, isA<List<ThresholdConfig>>());

        if (response.data!.isNotEmpty) {
          final firstThreshold = response.data!.first;
          print('📋 First threshold details:');
          print('  ID: ${firstThreshold.id}');
          print('  Service Type: ${firstThreshold.serviceType}');
          print('  Metric Name: ${firstThreshold.metricName}');
          print('  Warning Threshold: ${firstThreshold.warningThreshold}');
          print('  Critical Threshold: ${firstThreshold.criticalThreshold}');
          print('  Unit: ${firstThreshold.unit}');
          print('  Description: ${firstThreshold.description}');
          print('  Is Active: ${firstThreshold.isActive}');

          // Verify threshold structure
          expect(firstThreshold.id, isNotEmpty);
          expect(firstThreshold.serviceType, isNotEmpty);
          expect(firstThreshold.metricName, isNotEmpty);
          expect(firstThreshold.warningThreshold, isA<double>());
          expect(firstThreshold.criticalThreshold, isA<double>());
          expect(firstThreshold.unit, isNotEmpty);
          expect(firstThreshold.description, isNotEmpty);
          expect(firstThreshold.isActive, isA<bool>());
          expect(firstThreshold.createdAt, isA<DateTime>());
          expect(firstThreshold.updatedAt, isA<DateTime>());
        }

        print('✅ Threshold API fetch test passed');
      } catch (e, stackTrace) {
        print('❌ Threshold API fetch failed: $e');
        print('Stack trace: $stackTrace');
        fail('Failed to fetch thresholds: $e');
      }
    });

    test('Should handle threshold data parsing correctly', () async {
      print('🧪 Testing threshold data parsing...');

      try {
        // Test with sample data that matches API response structure
        final sampleJson = {
          'id': 'test-threshold-id',
          'service_type': 'electricity',
          'metric_name': 'generator_fuel_level',
          'warning_threshold': 30.0,
          'critical_threshold': 15.0,
          'unit': 'percentage',
          'description': 'Generator fuel level thresholds',
          'is_active': true,
          'created_at': '2025-05-30T19:12:24.448Z',
          'updated_at': '2025-05-30T19:12:24.448Z',
        };

        final threshold = ThresholdConfig.fromJson(sampleJson);

        expect(threshold.id, equals('test-threshold-id'));
        expect(threshold.serviceType, equals('electricity'));
        expect(threshold.metricName, equals('generator_fuel_level'));
        expect(threshold.warningThreshold, equals(30.0));
        expect(threshold.criticalThreshold, equals(15.0));
        expect(threshold.unit, equals('percentage'));
        expect(threshold.description, equals('Generator fuel level thresholds'));
        expect(threshold.isActive, isTrue);
        expect(threshold.createdAt, isA<DateTime>());
        expect(threshold.updatedAt, isA<DateTime>());

        print('✅ Threshold data parsing test passed');
      } catch (e, stackTrace) {
        print('❌ Threshold data parsing failed: $e');
        print('Stack trace: $stackTrace');
        fail('Failed to parse threshold data: $e');
      }
    });

    test('Should handle API response with string numbers correctly', () async {
      print('🧪 Testing threshold parsing with string numbers...');

      try {
        // Test with string numbers as they come from API
        final sampleJson = {
          'id': 'test-threshold-id-2',
          'service_type': 'internet',
          'metric_name': 'speed',
          'warning_threshold': '50', // String number
          'critical_threshold': '25', // String number
          'unit': 'mbps',
          'description': 'Internet speed thresholds',
          'is_active': true,
          'created_at': '2025-05-30T19:12:24.454Z',
          'updated_at': '2025-05-30T19:12:24.454Z',
        };

        final threshold = ThresholdConfig.fromJson(sampleJson);

        expect(threshold.warningThreshold, equals(50.0));
        expect(threshold.criticalThreshold, equals(25.0));
        expect(threshold.serviceType, equals('internet'));
        expect(threshold.metricName, equals('speed'));

        print('✅ String number parsing test passed');
      } catch (e, stackTrace) {
        print('❌ String number parsing failed: $e');
        print('Stack trace: $stackTrace');
        fail('Failed to parse string numbers: $e');
      }
    });

    test('Should handle mixed number types correctly', () async {
      print('🧪 Testing mixed number types parsing...');

      try {
        // Test with mixed number types (some strings, some numbers)
        final mixedJson = {
          'id': 'test-mixed-id',
          'service_type': 'internet',
          'metric_name': 'speed',
          'warning_threshold': 50, // Number
          'critical_threshold': '25', // String
          'unit': 'mbps',
          'description': 'Mixed number types test',
          'is_active': true,
          'created_at': '2025-05-30T19:12:24.454Z',
          'updated_at': '2025-05-30T19:12:24.454Z',
        };

        final threshold = ThresholdConfig.fromJson(mixedJson);

        expect(threshold.warningThreshold, equals(50.0));
        expect(threshold.criticalThreshold, equals(25.0));
        expect(threshold.serviceType, equals('internet'));
        expect(threshold.metricName, equals('speed'));

        print('✅ Mixed number types parsing test passed');
      } catch (e, stackTrace) {
        print('❌ Mixed number types parsing failed: $e');
        print('Stack trace: $stackTrace');
        fail('Failed to parse mixed number types: $e');
      }
    });

    print('🎯 All threshold integration tests completed');
  });
}
