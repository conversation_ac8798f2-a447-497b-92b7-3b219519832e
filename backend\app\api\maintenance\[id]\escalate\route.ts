import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const escalateMaintenanceIssueSchema = Joi.object({
  escalation_reason: Joi.string().min(5).optional(),
  escalation_level: Joi.number().integer().min(1).max(5).optional(),
});

async function escalateMaintenanceIssueHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(escalateMaintenanceIssueSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { escalation_reason, escalation_level } = validation.data;

    // Verify issue exists
    const existingIssue = await prisma.maintenanceIssue.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            name: true,
          },
        },
        escalationLogs: {
          orderBy: {
            escalatedAt: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!existingIssue) {
      return Response.json(
        createApiResponse(null, 'Maintenance issue not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Determine escalation level
    const currentEscalationLevel = existingIssue.escalationLogs.length > 0 
      ? existingIssue.escalationLogs[0].escalationLevel 
      : 0;
    
    const newEscalationLevel = escalation_level || (currentEscalationLevel + 1);

    // Auto-escalation logic based on priority and time
    let autoEscalationReason = escalation_reason;
    if (!autoEscalationReason) {
      const hoursSinceCreated = Math.floor(
        (new Date().getTime() - existingIssue.createdAt.getTime()) / (1000 * 60 * 60)
      );
      
      if (existingIssue.priority === 'CRITICAL' && hoursSinceCreated > 2) {
        autoEscalationReason = `Critical issue auto-escalated after ${hoursSinceCreated} hours`;
      } else if (existingIssue.priority === 'HIGH' && hoursSinceCreated > 8) {
        autoEscalationReason = `High priority issue auto-escalated after ${hoursSinceCreated} hours`;
      } else if (hoursSinceCreated > 24) {
        autoEscalationReason = `Issue auto-escalated after ${hoursSinceCreated} hours without resolution`;
      } else {
        autoEscalationReason = 'Manual escalation requested';
      }
    }

    // Update issue priority if escalating
    let newPriority = existingIssue.priority;
    if (newEscalationLevel > currentEscalationLevel) {
      if (existingIssue.priority === 'LOW') newPriority = 'MEDIUM';
      else if (existingIssue.priority === 'MEDIUM') newPriority = 'HIGH';
      else if (existingIssue.priority === 'HIGH') newPriority = 'CRITICAL';
    }

    // Update issue
    const updatedIssue = await prisma.maintenanceIssue.update({
      where: { id },
      data: {
        priority: newPriority,
        status: 'ESCALATED',
        updatedAt: new Date(),
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        assignee: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
      },
    });

    // Create escalation log entry
    const escalationLog = await prisma.maintenanceEscalationLog.create({
      data: {
        maintenanceIssueId: id,
        escalationLevel: newEscalationLevel,
        escalationReason: autoEscalationReason,
        escalatedBy: currentUser.id,
        escalatedAt: new Date(),
      },
    });

    const transformedIssue = {
      id: updatedIssue.id,
      property: {
        id: updatedIssue.property.id,
        name: updatedIssue.property.name,
        type: updatedIssue.property.type.toLowerCase(),
      },
      title: updatedIssue.title,
      description: updatedIssue.description,
      priority: updatedIssue.priority.toLowerCase(),
      status: updatedIssue.status.toLowerCase(),
      service_type: updatedIssue.serviceType,
      department: updatedIssue.department,
      assignee: updatedIssue.assignee,
      due_date: updatedIssue.dueDate,
      created_at: updatedIssue.createdAt,
      updated_at: updatedIssue.updatedAt,
      escalation_info: {
        level: newEscalationLevel,
        reason: autoEscalationReason,
        escalated_at: escalationLog.escalatedAt,
      },
    };

    return Response.json(
      createApiResponse({
        message: 'Maintenance issue escalated successfully',
        issue: transformedIssue,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to escalate maintenance issue');
  }
}

export const POST = requireAuth(escalateMaintenanceIssueHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
