import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/widgets/stat_card.dart';

class SecurityScreen extends ConsumerStatefulWidget {
  const SecurityScreen({super.key});

  @override
  ConsumerState<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends ConsumerState<SecurityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Security'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['security.manage'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: _addSecurityIncident,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Incidents', icon: Icon(Icons.warning)),
            Tab(text: 'Access Logs', icon: Icon(Icons.lock)),
            Tab(text: 'Guards', icon: Icon(Icons.security)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildIncidentsTab(),
          _buildAccessLogsTab(),
          _buildGuardsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Security Status Cards
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: AppConstants.smallPadding,
            mainAxisSpacing: AppConstants.smallPadding,
            childAspectRatio: 1.2,
            children: [
              StatCard(
                title: 'Active Guards',
                value: '8/10',
                icon: Icons.security,
                color: Colors.green,
                onTap: () => _tabController.animateTo(3),
              ),
              StatCard(
                title: 'Open Incidents',
                value: '3',
                icon: Icons.warning,
                color: Colors.orange,
                onTap: () => _tabController.animateTo(1),
              ),
              StatCard(
                title: 'Access Violations',
                value: '2',
                icon: Icons.lock,
                color: Colors.red,
                onTap: () => _tabController.animateTo(2),
              ),
              StatCard(
                title: 'System Status',
                value: 'Online',
                icon: Icons.check_circle,
                color: Colors.blue,
                onTap: () => _showSystemStatus(),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Recent Incidents
          Text(
            'Recent Incidents',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          _buildRecentIncidentsCard(),

          const SizedBox(height: AppConstants.largePadding),

          // Quick Actions
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),

          _buildQuickActionsCard(),
        ],
      ),
    );
  }

  Widget _buildIncidentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Filter Row
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items: ['All', 'Open', 'In Progress', 'Resolved', 'Closed']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) {
                    // TODO: Filter incidents by status
                  },
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Priority',
                    border: OutlineInputBorder(),
                  ),
                  items: ['All', 'Low', 'Medium', 'High', 'Critical']
                      .map((priority) => DropdownMenuItem(
                            value: priority,
                            child: Text(priority),
                          ))
                      .toList(),
                  onChanged: (value) {
                    // TODO: Filter incidents by priority
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Incidents List
          _buildIncidentsList(),
        ],
      ),
    );
  }

  Widget _buildAccessLogsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Date Filter
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Row(
                children: [
                  const Icon(Icons.date_range),
                  const SizedBox(width: AppConstants.smallPadding),
                  const Expanded(
                    child: Text('Today'),
                  ),
                  TextButton(
                    onPressed: _selectDateRange,
                    child: const Text('Change'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Access Logs List
          _buildAccessLogsList(),
        ],
      ),
    );
  }

  Widget _buildGuardsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Guards Status
          _buildGuardsStatusCard(),

          const SizedBox(height: AppConstants.defaultPadding),

          // Guards List
          _buildGuardsList(),
        ],
      ),
    );
  }

  Widget _buildRecentIncidentsCard() {
    return Card(
      child: Column(
        children: [
          _buildIncidentTile(
            'Unauthorized Access Attempt',
            'Property A - Main Gate',
            'High',
            Colors.red,
            '2 hours ago',
          ),
          const Divider(),
          _buildIncidentTile(
            'Suspicious Activity',
            'Property B - Parking Area',
            'Medium',
            Colors.orange,
            '4 hours ago',
          ),
          const Divider(),
          _buildIncidentTile(
            'Equipment Malfunction',
            'Property C - Security Camera',
            'Low',
            Colors.blue,
            '1 day ago',
          ),
          Padding(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            child: TextButton(
              onPressed: () => _tabController.animateTo(1),
              child: const Text('View All Incidents'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIncidentTile(
    String title,
    String location,
    String priority,
    Color priorityColor,
    String time,
  ) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: priorityColor.withValues(alpha: 0.1),
        child: Icon(Icons.warning, color: priorityColor),
      ),
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(location),
          Text(
            time,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: priorityColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          priority,
          style: TextStyle(
            color: priorityColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      onTap: () => _showIncidentDetails(title),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.add_alert),
            title: const Text('Report Incident'),
            subtitle: const Text('Create new security incident'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _addSecurityIncident,
          ),
          ListTile(
            leading: const Icon(Icons.emergency),
            title: const Text('Emergency Alert'),
            subtitle: const Text('Send emergency notification'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _sendEmergencyAlert,
          ),
          ListTile(
            leading: const Icon(Icons.camera_alt),
            title: const Text('Camera System'),
            subtitle: const Text('View live camera feeds'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _viewCameraSystem,
          ),
          ListTile(
            leading: const Icon(Icons.lock),
            title: const Text('Access Control'),
            subtitle: const Text('Manage access permissions'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _manageAccessControl,
          ),
        ],
      ),
    );
  }

  Widget _buildIncidentsList() {
    // Mock data for demonstration
    final incidents = [
      {
        'title': 'Unauthorized Access Attempt',
        'location': 'Property A - Main Gate',
        'priority': 'High',
        'status': 'Open',
        'time': '2 hours ago',
      },
      {
        'title': 'Suspicious Activity',
        'location': 'Property B - Parking Area',
        'priority': 'Medium',
        'status': 'In Progress',
        'time': '4 hours ago',
      },
      {
        'title': 'Equipment Malfunction',
        'location': 'Property C - Security Camera',
        'priority': 'Low',
        'status': 'Resolved',
        'time': '1 day ago',
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: incidents.length,
      itemBuilder: (context, index) {
        final incident = incidents[index];
        final priorityColor = _getPriorityColor(incident['priority']!);

        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: priorityColor.withValues(alpha: 0.1),
              child: Icon(Icons.warning, color: priorityColor),
            ),
            title: Text(incident['title']!),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(incident['location']!),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: priorityColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        incident['priority']!,
                        style: TextStyle(
                          color: priorityColor,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      incident['time']!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: Text(incident['status']!),
            onTap: () => _showIncidentDetails(incident['title']!),
          ),
        );
      },
    );
  }

  Widget _buildAccessLogsList() {
    // Mock data for demonstration
    final logs = [
      {
        'user': 'John Doe',
        'action': 'Entry',
        'location': 'Main Gate',
        'time': '09:15 AM',
        'status': 'Authorized',
      },
      {
        'user': 'Jane Smith',
        'action': 'Exit',
        'location': 'Side Gate',
        'time': '08:45 AM',
        'status': 'Authorized',
      },
      {
        'user': 'Unknown',
        'action': 'Entry Attempt',
        'location': 'Main Gate',
        'time': '07:30 AM',
        'status': 'Denied',
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: logs.length,
      itemBuilder: (context, index) {
        final log = logs[index];
        final isAuthorized = log['status'] == 'Authorized';

        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: isAuthorized
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              child: Icon(
                isAuthorized ? Icons.check : Icons.close,
                color: isAuthorized ? Colors.green : Colors.red,
              ),
            ),
            title: Text('${log['user']} - ${log['action']}'),
            subtitle: Text('${log['location']} • ${log['time']}'),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isAuthorized
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                log['status']!,
                style: TextStyle(
                  color: isAuthorized ? Colors.green : Colors.red,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGuardsStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Guards Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusIndicator('On Duty', '8', Colors.green),
                _buildStatusIndicator('Off Duty', '2', Colors.grey),
                _buildStatusIndicator('On Break', '1', Colors.orange),
                _buildStatusIndicator('Emergency', '0', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(String label, String count, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Text(
            count,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildGuardsList() {
    // Mock data for demonstration
    final guards = [
      {
        'name': 'Rajesh Kumar',
        'shift': 'Day Shift',
        'location': 'Main Gate',
        'status': 'On Duty',
        'contact': '+91 98765 43210',
      },
      {
        'name': 'Suresh Patel',
        'shift': 'Night Shift',
        'location': 'Side Gate',
        'status': 'Off Duty',
        'contact': '+91 98765 43211',
      },
      {
        'name': 'Mahesh Singh',
        'shift': 'Day Shift',
        'location': 'Parking Area',
        'status': 'On Break',
        'contact': '+91 98765 43212',
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: guards.length,
      itemBuilder: (context, index) {
        final guard = guards[index];
        final statusColor = _getStatusColor(guard['status']!);

        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: statusColor.withValues(alpha: 0.1),
              child: Icon(Icons.security, color: statusColor),
            ),
            title: Text(guard['name']!),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${guard['shift']} • ${guard['location']}'),
                Text(guard['contact']!),
              ],
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                guard['status']!,
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            onTap: () => _showGuardDetails(guard['name']!),
          ),
        );
      },
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
      case 'critical':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'on duty':
        return Colors.green;
      case 'off duty':
        return Colors.grey;
      case 'on break':
        return Colors.orange;
      case 'emergency':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _addSecurityIncident() {
    // TODO: Implement add security incident dialog
    AppUtils.showInfoSnackBar(context, 'Add security incident coming soon');
  }

  void _refreshData() {
    // TODO: Implement data refresh
    AppUtils.showInfoSnackBar(context, 'Data refreshed');
  }

  void _showSystemStatus() {
    // TODO: Implement system status dialog
    AppUtils.showInfoSnackBar(context, 'System status details coming soon');
  }

  void _selectDateRange() {
    // TODO: Implement date range picker
    AppUtils.showInfoSnackBar(context, 'Date range picker coming soon');
  }

  void _showIncidentDetails(String title) {
    // TODO: Implement incident details screen
    AppUtils.showInfoSnackBar(context, 'Incident details: $title');
  }

  void _sendEmergencyAlert() {
    // TODO: Implement emergency alert
    AppUtils.showInfoSnackBar(context, 'Emergency alert feature coming soon');
  }

  void _viewCameraSystem() {
    // TODO: Implement camera system view
    AppUtils.showInfoSnackBar(context, 'Camera system coming soon');
  }

  void _manageAccessControl() {
    // TODO: Implement access control management
    AppUtils.showInfoSnackBar(context, 'Access control management coming soon');
  }

  void _showGuardDetails(String name) {
    // TODO: Implement guard details screen
    AppUtils.showInfoSnackBar(context, 'Guard details: $name');
  }
}
