"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  createThresholdConfiguration,
  updateThresholdConfiguration,
  type ThresholdConfig,
} from "@/app/actions/threshold-config"

interface ThresholdFormProps {
  threshold?: ThresholdConfig | null
  onClose: () => void
}

const FUNCTIONAL_AREAS = ["electricity", "water", "security", "internet", "ott", "maintenance", "attendance"]

const PROPERTY_TYPES = [
  { value: "all", label: "All Properties" },
  { value: "home", label: "Home Properties" },
  { value: "office", label: "Office Properties" },
]

const UNITS = ["%", "count", "hours", "days", "boolean", "liters", "kWh"]

export function ThresholdForm({ threshold, onClose }: ThresholdFormProps) {
  const [formData, setFormData] = useState({
    functional_area: threshold?.functional_area || "",
    metric_name: threshold?.metric_name || "",
    green_min: threshold?.green_min?.toString() || "",
    green_max: threshold?.green_max?.toString() || "",
    orange_min: threshold?.orange_min?.toString() || "",
    orange_max: threshold?.orange_max?.toString() || "",
    red_min: threshold?.red_min?.toString() || "",
    red_max: threshold?.red_max?.toString() || "",
    unit: threshold?.unit || "",
    description: threshold?.description || "",
    property_type: threshold?.property_type || "all",
  })

  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const data = {
        functional_area: formData.functional_area,
        metric_name: formData.metric_name,
        green_min: formData.green_min ? Number.parseFloat(formData.green_min) : null,
        green_max: formData.green_max ? Number.parseFloat(formData.green_max) : null,
        orange_min: formData.orange_min ? Number.parseFloat(formData.orange_min) : null,
        orange_max: formData.orange_max ? Number.parseFloat(formData.orange_max) : null,
        red_min: formData.red_min ? Number.parseFloat(formData.red_min) : null,
        red_max: formData.red_max ? Number.parseFloat(formData.red_max) : null,
        unit: formData.unit || null,
        description: formData.description || null,
        property_type: formData.property_type,
      }

      if (threshold?.id) {
        await updateThresholdConfiguration(threshold.id, data)
      } else {
        await createThresholdConfiguration(data)
      }

      onClose()
    } catch (error) {
      console.error("Failed to save threshold:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="functional_area">Functional Area</Label>
          <Select value={formData.functional_area} onValueChange={(value) => handleChange("functional_area", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select functional area" />
            </SelectTrigger>
            <SelectContent>
              {FUNCTIONAL_AREAS.map((area) => (
                <SelectItem key={area} value={area}>
                  {area.charAt(0).toUpperCase() + area.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="property_type">Property Type</Label>
          <Select value={formData.property_type} onValueChange={(value) => handleChange("property_type", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select property type" />
            </SelectTrigger>
            <SelectContent>
              {PROPERTY_TYPES.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="metric_name">Metric Name</Label>
        <Input
          id="metric_name"
          value={formData.metric_name}
          onChange={(e) => handleChange("metric_name", e.target.value)}
          placeholder="e.g., fuel_percentage, open_issues_count"
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleChange("description", e.target.value)}
          placeholder="Describe what this threshold measures"
        />
      </div>

      <div>
        <Label htmlFor="unit">Unit</Label>
        <Select value={formData.unit} onValueChange={(value) => handleChange("unit", value)}>
          <SelectTrigger>
            <SelectValue placeholder="Select unit" />
          </SelectTrigger>
          <SelectContent>
            {UNITS.map((unit) => (
              <SelectItem key={unit} value={unit}>
                {unit}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Threshold Ranges</h4>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-green-600">Green Range (Healthy)</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Min"
                value={formData.green_min}
                onChange={(e) => handleChange("green_min", e.target.value)}
                type="number"
                step="any"
              />
              <Input
                placeholder="Max"
                value={formData.green_max}
                onChange={(e) => handleChange("green_max", e.target.value)}
                type="number"
                step="any"
              />
            </div>
          </div>

          <div>
            <Label className="text-orange-600">Orange Range (Warning)</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Min"
                value={formData.orange_min}
                onChange={(e) => handleChange("orange_min", e.target.value)}
                type="number"
                step="any"
              />
              <Input
                placeholder="Max"
                value={formData.orange_max}
                onChange={(e) => handleChange("orange_max", e.target.value)}
                type="number"
                step="any"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-red-600">Red Range (Critical)</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Min"
                value={formData.red_min}
                onChange={(e) => handleChange("red_min", e.target.value)}
                type="number"
                step="any"
              />
              <Input
                placeholder="Max"
                value={formData.red_max}
                onChange={(e) => handleChange("red_max", e.target.value)}
                type="number"
                step="any"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Saving..." : threshold ? "Update" : "Create"}
        </Button>
      </div>
    </form>
  )
}
