// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://localhost:3001';

async function testPropertyFunctionsSeeding() {
  try {
    console.log('🧪 Testing Property Functions Seeding...');

    // First, let's check if the server is running
    console.log('🔍 Checking server health...');
    try {
      const healthResponse = await fetch(`${BASE_URL}/api`);
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        console.log('✅ Server is running:', healthData);
      } else {
        console.log('⚠️ Server responded with status:', healthResponse.status);
      }
    } catch (error) {
      console.log('❌ Server health check failed:', error.message);
      return;
    }

    // Let's try calling the seeding endpoint directly first
    console.log('🌱 Attempting direct seeding call...');
    try {
      const directSeedResponse = await fetch(`${BASE_URL}/api/admin/seed-property-functions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (directSeedResponse.ok) {
        const seedData = await directSeedResponse.json();
        console.log('✅ Direct seeding successful!');
        console.log('📊 Results:', JSON.stringify(seedData, null, 2));
        return;
      } else {
        console.log('❌ Direct seeding failed with status:', directSeedResponse.status);
        const errorText = await directSeedResponse.text();
        console.log('Error response:', errorText.substring(0, 500));
      }
    } catch (error) {
      console.log('❌ Direct seeding failed:', error.message);
    }

    // First, let's try to login as admin to get a token
    console.log('🔐 Attempting admin login...');
    
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: 'admin',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Admin login failed, trying alternative credentials...');
      
      // Try alternative admin credentials
      const altLoginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        })
      });

      if (!altLoginResponse.ok) {
        console.log('❌ Alternative admin login also failed');
        console.log('Response status:', altLoginResponse.status);
        const errorText = await altLoginResponse.text();
        console.log('Error response:', errorText);
        return;
      }

      const altLoginData = await altLoginResponse.json();
      console.log('✅ Admin login successful with alternative credentials');
      console.log('Login response:', JSON.stringify(altLoginData, null, 2));

      // Now call the seeding endpoint
      const token = altLoginData.token;
      if (!token) {
        console.log('❌ No token found in login response');
        return;
      }
      await callSeedingEndpoint(token);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Admin login successful');

    // Now call the seeding endpoint
    const token = loginData.token;
    if (!token) {
      console.log('❌ No token found in login response');
      return;
    }
    await callSeedingEndpoint(token);

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

async function callSeedingEndpoint(token) {
  try {
    console.log('🌱 Calling property functions seeding endpoint...');
    
    const seedResponse = await fetch(`${BASE_URL}/api/admin/seed-property-functions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!seedResponse.ok) {
      console.log('❌ Seeding failed');
      console.log('Response status:', seedResponse.status);
      const errorText = await seedResponse.text();
      console.log('Error response:', errorText);
      return;
    }

    const seedData = await seedResponse.json();
    console.log('✅ Property functions seeding successful!');
    console.log('📊 Seeding Results:');
    console.log(`   - Properties seeded: ${seedData.data.seededCount}`);
    console.log(`   - Total properties: ${seedData.data.allPropertiesSummary.length}`);
    
    console.log('\n🏢 Property Functions Summary:');
    seedData.data.allPropertiesSummary.forEach(property => {
      console.log(`   ${property.name} (${property.type}): ${property.enabledFunctions.join(', ') || 'No enabled functions'}`);
    });

  } catch (error) {
    console.error('💥 Seeding endpoint call failed:', error.message);
  }
}

// Run the test
testPropertyFunctionsSeeding();
