import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"
import { setupHousehelpRole } from "@/app/actions/setup-househelp-role"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Check, AlertCircle } from "lucide-react"

export default async function SetupHousehelpPage() {
  // Simple, direct admin check
  const user = await getCurrentUser()

  if (!user || (user.username !== "admin1" && user.role !== "admin")) {
    redirect("/unauthorized")
  }

  const results = await setupHousehelpRole()

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Setup Househelp Role</h1>
        <p className="text-gray-600">Configure permissions and access for househelp users</p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Househelp Role Setup</CardTitle>
          <CardDescription>
            This page automatically sets up the Househelp role with access to Jublee Hills property
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Role Creation Result */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Role Creation</h3>
            {results.roleCreated ? (
              <Alert className="bg-green-50 text-green-700">
                <Check className="h-4 w-4" />
                <AlertDescription>Househelp role created successfully</AlertDescription>
              </Alert>
            ) : (
              <Alert>
                <AlertDescription>Househelp role already exists</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Permissions Creation Result */}
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Permissions Setup</h3>
            {results.permissionsCreated.length > 0 && (
              <Alert className="bg-green-50 text-green-700">
                <Check className="h-4 w-4" />
                <AlertDescription>
                  <div>The following permissions were created:</div>
                  <ul className="list-disc pl-5 mt-2">
                    {results.permissionsCreated.map((perm, i) => (
                      <li key={i}>{perm}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Errors */}
          {results.errors.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Errors</h3>
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <ul className="list-disc pl-5">
                    {results.errors.map((error, i) => (
                      <li key={i}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            </div>
          )}

          <div className="pt-4 flex space-x-4">
            <Button asChild>
              <a href="/admin/roles">Go to Role Management</a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/admin/permissions">Go to Permission Management</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
