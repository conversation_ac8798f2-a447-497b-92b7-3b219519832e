import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';

/// Security tab - Access control, alarms, and security systems
class SecurityTab extends StatefulWidget {
  const SecurityTab({super.key});

  @override
  State<SecurityTab> createState() => _SecurityTabState();
}

class _SecurityTabState extends State<SecurityTab> {
  bool _systemArmed = false;
  bool _motionDetectionEnabled = true;
  bool _doorAlarmsEnabled = true;
  bool _fireAlarmEnabled = true;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildSecurityStatus(context),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildQuickControls(context),
          const SizedBox(height: AppConstants.defaultPadding),
          Expanded(
            child: _buildSecurityDevices(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityStatus(BuildContext context) {
    return Card(
      color: _systemArmed ? Colors.red.shade50 : Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          children: [
            Icon(
              _systemArmed ? Icons.security : Icons.shield,
              color: _systemArmed ? Colors.red : Colors.green,
              size: 32,
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _systemArmed ? 'SYSTEM ARMED' : 'SYSTEM DISARMED',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _systemArmed ? Colors.red : Colors.green,
                    ),
                  ),
                  Text(
                    _systemArmed 
                        ? 'All security systems are active'
                        : 'Security monitoring is disabled',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => setState(() => _systemArmed = !_systemArmed),
              style: ElevatedButton.styleFrom(
                backgroundColor: _systemArmed ? Colors.green : Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(_systemArmed ? 'DISARM' : 'ARM'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickControls(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Card(
            child: SwitchListTile(
              title: const Text('Motion Detection'),
              subtitle: const Text('Detect movement'),
              value: _motionDetectionEnabled,
              onChanged: (value) => setState(() => _motionDetectionEnabled = value),
              secondary: const Icon(Icons.motion_photos_on),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: Card(
            child: SwitchListTile(
              title: const Text('Door Alarms'),
              subtitle: const Text('Monitor access'),
              value: _doorAlarmsEnabled,
              onChanged: (value) => setState(() => _doorAlarmsEnabled = value),
              secondary: const Icon(Icons.door_front_door),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecurityDevices(BuildContext context) {
    return ListView(
      children: [
        _buildDeviceCard('Main Entrance', 'Access Control', Icons.door_front_door, true),
        _buildDeviceCard('Fire Alarm System', 'Fire Detection', Icons.local_fire_department, true),
        _buildDeviceCard('CCTV Network', 'Video Surveillance', Icons.videocam, true),
        _buildDeviceCard('Intrusion Detection', 'Perimeter Security', Icons.security, false),
        _buildDeviceCard('Emergency Exits', 'Exit Monitoring', Icons.exit_to_app, true),
      ],
    );
  }

  Widget _buildDeviceCard(String name, String type, IconData icon, bool isActive) {
    return Card(
      child: ListTile(
        leading: Icon(
          icon,
          color: isActive ? Colors.green : Colors.red,
          size: 32,
        ),
        title: Text(name),
        subtitle: Text(type),
        trailing: Chip(
          label: Text(isActive ? 'ACTIVE' : 'OFFLINE'),
          backgroundColor: isActive ? Colors.green.shade100 : Colors.red.shade100,
        ),
        onTap: () => _showDeviceDetails(name),
      ),
    );
  }

  void _showDeviceDetails(String deviceName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(deviceName),
        content: const Text('Security device details coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
