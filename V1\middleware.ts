import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createServerClient } from "@/lib/supabase/server"

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname

  // Public paths that don't require authentication
  const publicPaths = ["/login", "/register", "/registration-success", "/unauthorized"]
  if (publicPaths.some((publicPath) => path === publicPath)) {
    return NextResponse.next()
  }

  // Check if user is authenticated
  const sessionToken = request.cookies.get("session_token")?.value

  if (!sessionToken) {
    // Store the original URL to redirect back after login
    const redirectUrl = new URL("/login", request.url)
    redirectUrl.searchParams.set("redirect", request.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // For authenticated users, check permissions for protected routes
  try {
    const supabase = createServerClient()

    // Get session and user
    const { data: session, error: sessionError } = await supabase
      .from("sessions")
      .select("user_id")
      .eq("token", sessionToken)
      .gt("expires_at", new Date().toISOString())
      .single()

    if (sessionError || !session) {
      const redirectUrl = new URL("/login", request.url)
      redirectUrl.searchParams.set("redirect", request.nextUrl.pathname)
      return NextResponse.redirect(redirectUrl)
    }

    // Get user details
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("role, username")
      .eq("id", session.user_id)
      .eq("is_active", true)
      .single()

    if (userError || !user) {
      const redirectUrl = new URL("/login", request.url)
      return NextResponse.redirect(redirectUrl)
    }

    // Admin users (admin role or admin1 username) can access everything
    if (user.role === "admin" || user.username === "admin1") {
      return NextResponse.next()
    }

    // Check specific route permissions for non-admin users
    if (path.startsWith("/admin")) {
      // Only admins can access admin routes
      return NextResponse.redirect(new URL("/unauthorized", request.url))
    }

    // For dashboard routes, check if user has appropriate permissions
    if (path.startsWith("/dashboard")) {
      // Get user roles from user_roles table
      const { data: userRoles, error: userRolesError } = await supabase
        .from("user_roles")
        .select("role_id")
        .eq("user_id", session.user_id)

      if (userRolesError) {
        console.error("Error fetching user roles:", userRolesError)
        return NextResponse.redirect(new URL("/unauthorized", request.url))
      }

      // If user has no assigned roles and is not admin, restrict access
      if (!userRoles || userRoles.length === 0) {
        // Allow basic dashboard access for users with specific roles in the users table
        const allowedRoles = ["househelp", "maintenance", "security", "manager"]
        if (!allowedRoles.includes(user.role)) {
          return NextResponse.redirect(new URL("/unauthorized", request.url))
        }
      } else {
        // Check permissions based on assigned roles
        const roleIds = userRoles.map((ur) => ur.role_id)

        const { data: permissions, error: permissionsError } = await supabase
          .from("permissions")
          .select("url_pattern")
          .in("role_id", roleIds)

        if (permissionsError) {
          console.error("Error fetching permissions:", permissionsError)
          return NextResponse.redirect(new URL("/unauthorized", request.url))
        }

        // Check if any permission pattern matches the current path
        const hasPermission = permissions?.some((permission) => {
          const pattern = permission.url_pattern.replace(/\*/g, ".*").replace(/\//g, "\\/")
          const regex = new RegExp(`^${pattern}$`)
          return regex.test(path)
        })

        if (!hasPermission) {
          return NextResponse.redirect(new URL("/unauthorized", request.url))
        }
      }
    }

    return NextResponse.next()
  } catch (error) {
    console.error("Middleware error:", error)
    return NextResponse.redirect(new URL("/login", request.url))
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public).*)",
  ],
}
