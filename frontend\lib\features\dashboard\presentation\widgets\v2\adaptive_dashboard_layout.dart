import 'package:flutter/material.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../providers/dashboard_config_provider.dart';

/// Adaptive dashboard layout that responds to screen size and user preferences
class AdaptiveDashboardLayout extends StatelessWidget {
  final List<Widget> children;
  final DashboardLayout layout;
  final EdgeInsets? padding;
  final double? spacing;

  const AdaptiveDashboardLayout({
    super.key,
    required this.children,
    this.layout = DashboardLayout.adaptive,
    this.padding,
    this.spacing,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return SingleChildScrollView(
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _buildLayoutChildren(context, isTablet, isLandscape),
      ),
    );
  }

  List<Widget> _buildLayoutChildren(BuildContext context, bool isTablet, bool isLandscape) {
    final spacing = this.spacing ?? AppConstants.defaultPadding;
    
    switch (layout) {
      case DashboardLayout.compact:
        return _buildCompactLayout(spacing);
      case DashboardLayout.detailed:
        return _buildDetailedLayout(spacing);
      case DashboardLayout.grid:
        return _buildGridLayout(context, isTablet);
      case DashboardLayout.adaptive:
      default:
        return _buildAdaptiveLayout(context, isTablet, isLandscape, spacing);
    }
  }

  List<Widget> _buildCompactLayout(double spacing) {
    final List<Widget> layoutChildren = [];
    
    for (int i = 0; i < children.length; i++) {
      layoutChildren.add(
        Card(
          margin: EdgeInsets.only(bottom: spacing),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            child: children[i],
          ),
        ),
      );
    }
    
    // Add bottom padding for navigation bar
    layoutChildren.add(const SizedBox(height: 80));
    
    return layoutChildren;
  }

  List<Widget> _buildDetailedLayout(double spacing) {
    final List<Widget> layoutChildren = [];
    
    for (int i = 0; i < children.length; i++) {
      layoutChildren.add(
        Card(
          margin: EdgeInsets.only(bottom: spacing),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: children[i],
          ),
        ),
      );
    }
    
    // Add bottom padding for navigation bar
    layoutChildren.add(const SizedBox(height: 80));
    
    return layoutChildren;
  }

  List<Widget> _buildGridLayout(BuildContext context, bool isTablet) {
    final List<Widget> layoutChildren = [];
    final crossAxisCount = isTablet ? 3 : 2;
    
    // Group children into rows for grid layout
    for (int i = 0; i < children.length; i += crossAxisCount) {
      final rowChildren = <Widget>[];
      
      for (int j = 0; j < crossAxisCount && (i + j) < children.length; j++) {
        rowChildren.add(
          Expanded(
            child: Card(
              margin: const EdgeInsets.all(AppConstants.smallPadding / 2),
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                child: children[i + j],
              ),
            ),
          ),
        );
      }
      
      // Fill remaining slots with empty expanded widgets
      while (rowChildren.length < crossAxisCount) {
        rowChildren.add(const Expanded(child: SizedBox()));
      }
      
      layoutChildren.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: rowChildren,
        ),
      );
      
      if (i + crossAxisCount < children.length) {
        layoutChildren.add(const SizedBox(height: AppConstants.smallPadding));
      }
    }
    
    // Add bottom padding for navigation bar
    layoutChildren.add(const SizedBox(height: 80));
    
    return layoutChildren;
  }

  List<Widget> _buildAdaptiveLayout(
    BuildContext context,
    bool isTablet,
    bool isLandscape,
    double spacing,
  ) {
    final List<Widget> layoutChildren = [];
    
    // Critical alerts always at top (full width)
    if (children.isNotEmpty) {
      layoutChildren.add(
        Card(
          margin: EdgeInsets.only(bottom: spacing),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: children[0],
          ),
        ),
      );
    }
    
    // For tablets in landscape, use two-column layout for middle content
    if (isTablet && isLandscape && children.length > 1) {
      layoutChildren.addAll(_buildTwoColumnSection(children.sublist(1), spacing));
    } else {
      // Single column for phones or portrait tablets
      for (int i = 1; i < children.length; i++) {
        layoutChildren.add(
          Card(
            margin: EdgeInsets.only(bottom: spacing),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: children[i],
            ),
          ),
        );
      }
    }
    
    // Add bottom padding for navigation bar
    layoutChildren.add(const SizedBox(height: 80));
    
    return layoutChildren;
  }

  List<Widget> _buildTwoColumnSection(List<Widget> sectionChildren, double spacing) {
    final List<Widget> layoutChildren = [];
    
    for (int i = 0; i < sectionChildren.length; i += 2) {
      final leftChild = sectionChildren[i];
      final rightChild = i + 1 < sectionChildren.length ? sectionChildren[i + 1] : null;
      
      layoutChildren.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Card(
                margin: EdgeInsets.only(
                  right: spacing / 2,
                  bottom: spacing,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: leftChild,
                ),
              ),
            ),
            if (rightChild != null)
              Expanded(
                child: Card(
                  margin: EdgeInsets.only(
                    left: spacing / 2,
                    bottom: spacing,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: rightChild,
                  ),
                ),
              )
            else
              const Expanded(child: SizedBox()),
          ],
        ),
      );
    }
    
    return layoutChildren;
  }
}

/// Responsive section widget that adapts its content based on available space
class ResponsiveDashboardSection extends StatelessWidget {
  final String title;
  final Widget child;
  final bool showTitle;
  final EdgeInsets? padding;
  final Color? backgroundColor;

  const ResponsiveDashboardSection({
    super.key,
    required this.title,
    required this.child,
    this.showTitle = true,
    this.padding,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTitle) ...[
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
          ],
          child,
        ],
      ),
    );
  }
}

/// Priority-based layout that reorders content based on importance
class PriorityBasedLayout extends StatelessWidget {
  final List<PriorityWidget> priorityWidgets;
  final EdgeInsets? padding;

  const PriorityBasedLayout({
    super.key,
    required this.priorityWidgets,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    // Sort widgets by priority (higher priority first)
    final sortedWidgets = List<PriorityWidget>.from(priorityWidgets)
      ..sort((a, b) => b.priority.compareTo(a.priority));

    return SingleChildScrollView(
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: sortedWidgets.map((pw) => pw.widget).toList(),
      ),
    );
  }
}

/// Widget with priority for ordering
class PriorityWidget {
  final Widget widget;
  final int priority;
  final String id;

  const PriorityWidget({
    required this.widget,
    required this.priority,
    required this.id,
  });
}

/// Breakpoint-based responsive helper
class DashboardBreakpoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }

  static int getGridColumns(BuildContext context) {
    if (isDesktop(context)) return 4;
    if (isTablet(context)) return 3;
    return 2;
  }
}
