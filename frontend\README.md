# srsr_property_management

A new Flutter project.

## Getting Started

# SRSR Property Management

A comprehensive Flutter application for property management, built with modern architecture and best practices. This app provides a complete solution for managing residential and office properties, maintenance issues, attendance tracking, and generator fuel monitoring.

## 🚀 Features

### Core Modules
- **Authentication** - Secure login/register with JWT tokens
- **Dashboard** - Real-time overview of system status and metrics
- **Properties** - Manage residential and office properties
- **Maintenance** - Track and manage maintenance issues
- **Attendance** - Site and office attendance management
- **Generator Fuel** - Monitor fuel levels and consumption
- **Security Logs** - Track security activities

### Key Capabilities
- 📱 **Cross-platform** - Runs on Android, iOS, Web, Windows, macOS, and Linux
- 🎨 **Modern UI** - Material Design 3 with light/dark theme support
- 🔐 **Secure Authentication** - JWT-based authentication with token management
- 📊 **Real-time Dashboard** - Live metrics and alerts
- 🔄 **Offline Support** - Local storage with Hive for offline functionality
- 🌐 **API Integration** - Complete REST API integration with error handling
- 📱 **Responsive Design** - Optimized for mobile, tablet, and desktop

## 🏗️ Architecture

This project follows **Clean Architecture** principles with feature-based organization:

```
lib/
├── core/                    # Core functionality
│   ├── constants/          # App constants and API endpoints
│   ├── network/            # HTTP client and network configuration
│   ├── router/             # Navigation and routing
│   ├── storage/            # Local storage management
│   ├── theme/              # App theming and styling
│   └── utils/              # Utility functions and helpers
├── features/               # Feature modules
│   ├── auth/               # Authentication feature
│   │   ├── data/           # API services and repositories
│   │   ├── domain/         # Business logic and entities
│   │   └── presentation/   # UI screens and state management
│   ├── dashboard/          # Dashboard feature
│   ├── properties/         # Properties management
│   ├── maintenance/        # Maintenance tracking
│   ├── attendance/         # Attendance management
│   └── generator_fuel/     # Fuel monitoring
└── shared/                 # Shared components
    ├── models/             # Data models
    └── widgets/            # Reusable UI components
```

## 🛠️ Tech Stack

### Framework & Language
- **Flutter 3.27.4** - Cross-platform UI framework
- **Dart** - Programming language

### State Management
- **Riverpod 2.5.1** - Reactive state management
- **StateNotifier** - State management pattern

### Networking & API
- **Dio 5.4.3** - HTTP client for API calls
- **Retrofit 4.1.0** - Type-safe HTTP client generator
- **JSON Annotation** - JSON serialization

### Local Storage
- **Hive 2.2.3** - Fast, lightweight NoSQL database
- **SharedPreferences** - Simple key-value storage

### Navigation
- **GoRouter 14.2.7** - Declarative routing solution

### UI & Design
- **Material Design 3** - Modern design system
- **Custom Themes** - Light and dark theme support
- **Responsive Layout** - Adaptive UI for all screen sizes

### Development Tools
- **Build Runner** - Code generation
- **JSON Serializable** - Automatic JSON serialization
- **Flutter Lints** - Code quality and style enforcement

## 📋 Prerequisites

- Flutter SDK 3.27.4 or higher
- Dart SDK 3.6.2 or higher
- Android Studio / VS Code
- Git

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd srsr_property_management
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Generate Code
```bash
flutter packages pub run build_runner build
```

### 4. Run the Application
```bash
# For web
flutter run -d chrome

# For Android
flutter run -d android

# For iOS
flutter run -d ios

# For Windows
flutter run -d windows
```

## 🔧 Configuration

### API Configuration
Update the API endpoints in `lib/core/constants/api_constants.dart`:

```dart
class ApiConstants {
  static const String baseUrlDev = 'http://localhost:3000';
  static const String baseUrlProd = 'https://your-production-api.com';
  // ... other endpoints
}
```

### Environment Setup
The app automatically detects debug/release mode and uses appropriate API endpoints.

## 📱 Screens & Navigation

### Authentication Flow
- **Login Screen** - Email/password authentication
- **Register Screen** - New user registration

### Main Application
- **Dashboard** - System overview with metrics and alerts
- **Properties** - Property listing and management
- **Maintenance** - Issue tracking and management
- **Attendance** - Site attendance tracking
- **Profile** - User profile management
- **Settings** - App configuration

## 🔐 Authentication

The app uses JWT-based authentication with:
- Secure token storage using Hive
- Automatic token refresh
- Route protection
- Session management

## 📊 API Integration

### Supported Endpoints
- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `GET /api/properties` - Get properties
- `GET /api/maintenance` - Get maintenance issues
- `GET /api/dashboard/status` - Get dashboard metrics
- And more...

### Error Handling
- Network error handling
- API error responses
- User-friendly error messages
- Retry mechanisms

## 🎨 Theming

The app supports both light and dark themes with:
- Material Design 3 color system
- Custom color schemes
- Responsive typography
- Consistent spacing and elevation

## 🧪 Testing

Run tests with:
```bash
flutter test
```

The project includes:
- Widget tests for UI components
- Unit tests for business logic
- Integration tests for complete flows

## 📦 Building for Production

### Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

### Web
```bash
flutter build web --release
```

### Windows
```bash
flutter build windows --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔄 Version History

- **v1.0.0** - Initial release with core features
  - Authentication system
  - Dashboard with real-time metrics
  - Property management
  - Maintenance tracking
  - Responsive UI design

---

Built with ❤️ using Flutter
