import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, getQueryParams, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const createPermissionSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().max(255).optional(),
  resource: Joi.string().min(2).max(100).required(),
  action: Joi.string().min(2).max(50).required(),
});

async function getPermissionsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const params = getQueryParams(request);
    const { resource, action, include_roles } = params;

    const where: any = {};
    if (resource) {
      where.resource = resource;
    }
    if (action) {
      where.action = action;
    }

    const includeOptions: any = {};
    if (include_roles === 'true') {
      includeOptions.rolePermissions = {
        include: {
          role: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
        },
      };
    }

    const permissions = await prisma.permission.findMany({
      where,
      include: includeOptions,
      orderBy: [
        { resource: 'asc' },
        { action: 'asc' },
      ],
    });

    const transformedPermissions = permissions.map(permission => ({
      id: permission.id,
      name: permission.name,
      description: permission.description,
      resource: permission.resource,
      action: permission.action,
      roles: permission.rolePermissions?.map(rp => ({
        id: rp.role.id,
        name: rp.role.name,
        description: rp.role.description,
      })) || [],
      role_count: permission.rolePermissions?.length || 0,
      created_at: permission.createdAt,
    }));

    // Group by resource for better organization
    const groupedPermissions = transformedPermissions.reduce((acc, permission) => {
      if (!acc[permission.resource]) {
        acc[permission.resource] = [];
      }
      acc[permission.resource].push(permission);
      return acc;
    }, {} as Record<string, any[]>);

    return Response.json(
      createApiResponse({
        permissions: transformedPermissions,
        grouped_permissions: groupedPermissions,
        total: transformedPermissions.length,
        resources: [...new Set(transformedPermissions.map(p => p.resource))],
        actions: [...new Set(transformedPermissions.map(p => p.action))],
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch permissions');
  }
}

async function createPermissionHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    const validation = validateRequest(createPermissionSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { name, description, resource, action } = validation.data;

    // Check if permission already exists
    const existingPermission = await prisma.permission.findFirst({
      where: {
        OR: [
          { name },
          { resource, action },
        ],
      },
    });

    if (existingPermission) {
      if (existingPermission.name === name) {
        return Response.json(
          createApiResponse(null, 'Permission with this name already exists', 'DUPLICATE_PERMISSION'),
          { status: 409 }
        );
      } else {
        return Response.json(
          createApiResponse(null, 'Permission for this resource and action already exists', 'DUPLICATE_PERMISSION'),
          { status: 409 }
        );
      }
    }

    // Create permission
    const permission = await prisma.permission.create({
      data: {
        name,
        description,
        resource: resource.toLowerCase(),
        action: action.toLowerCase(),
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Permission created successfully',
        permission: {
          id: permission.id,
          name: permission.name,
          description: permission.description,
          resource: permission.resource,
          action: permission.action,
          created_at: permission.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create permission');
  }
}

// Bulk create permissions for common patterns
async function bulkCreatePermissionsHandler(request: NextRequest, context: any, currentUser: any) {
  try {
    const body = await getRequestBody(request);
    
    const bulkCreateSchema = Joi.object({
      resource: Joi.string().min(2).max(100).required(),
      actions: Joi.array().items(Joi.string().min(2).max(50)).min(1).required(),
      description_template: Joi.string().optional(),
    });

    const validation = validateRequest(bulkCreateSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { resource, actions, description_template } = validation.data;

    // Check for existing permissions
    const existingPermissions = await prisma.permission.findMany({
      where: {
        resource: resource.toLowerCase(),
        action: { in: actions.map((a: string) => a.toLowerCase()) },
      },
    });

    const existingActions = existingPermissions.map(p => p.action);
    const newActions = actions.filter((action: string) => !existingActions.includes(action.toLowerCase()));

    if (newActions.length === 0) {
      return Response.json(
        createApiResponse(null, 'All permissions already exist for this resource', 'DUPLICATE_PERMISSION'),
        { status: 409 }
      );
    }

    // Create new permissions
    const permissionsData = newActions.map((action: string) => ({
      name: `${resource.toLowerCase()}.${action.toLowerCase()}`,
      description: description_template 
        ? description_template.replace('{action}', action).replace('{resource}', resource)
        : `${action.charAt(0).toUpperCase() + action.slice(1)} ${resource}`,
      resource: resource.toLowerCase(),
      action: action.toLowerCase(),
    }));

    const createdPermissions = await prisma.permission.createMany({
      data: permissionsData,
    });

    // Fetch created permissions
    const newPermissions = await prisma.permission.findMany({
      where: {
        resource: resource.toLowerCase(),
        action: { in: newActions.map((a: string) => a.toLowerCase()) },
      },
    });

    return Response.json(
      createApiResponse({
        message: `${createdPermissions.count} permissions created successfully`,
        permissions: newPermissions.map(p => ({
          id: p.id,
          name: p.name,
          description: p.description,
          resource: p.resource,
          action: p.action,
          created_at: p.createdAt,
        })),
        skipped_existing: existingActions.length,
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to bulk create permissions');
  }
}

export const GET = requireAuth(getPermissionsHandler);
export const POST = requireRole(['admin'])(createPermissionHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
