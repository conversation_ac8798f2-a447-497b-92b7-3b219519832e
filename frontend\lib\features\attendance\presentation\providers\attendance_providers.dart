import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/attendance.dart';
import '../../data/attendance_api_service.dart';
import '../../data/attendance_repository_impl.dart';
import '../../domain/attendance_repository.dart';

// API Service Provider
final attendanceApiServiceProvider = Provider<AttendanceApiService>((ref) {
  return AttendanceApiService(DioClient.instance.dio);
});

// Repository Provider
final attendanceRepositoryProvider = Provider<AttendanceRepository>((ref) {
  return AttendanceRepositoryImpl(ref.watch(attendanceApiServiceProvider));
});

// Attendance Records Provider
final attendanceRecordsProvider = StateNotifierProvider<AttendanceRecordsNotifier, AsyncValue<List<AttendanceRecord>>>((ref) {
  return AttendanceRecordsNotifier(ref.watch(attendanceRepositoryProvider));
});

// Date-specific providers
final attendanceRecordsByDateProvider = Provider.family<AsyncValue<List<AttendanceRecord>>, DateTime>((ref, date) {
  final recordsAsync = ref.watch(attendanceRecordsProvider);
  return recordsAsync.when(
    data: (records) {
      final filteredRecords = records.where((record) =>
        record.date.year == date.year &&
        record.date.month == date.month &&
        record.date.day == date.day
      ).toList();
      return AsyncValue.data(filteredRecords);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

final siteAttendanceRecordsProvider = Provider.family<AsyncValue<List<AttendanceRecord>>, DateTime>((ref, date) {
  final recordsAsync = ref.watch(attendanceRecordsByDateProvider(date));
  return recordsAsync.when(
    data: (records) => AsyncValue.data(records.where((record) => record.isSiteAttendance).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

final officeAttendanceRecordsProvider = Provider.family<AsyncValue<List<AttendanceRecord>>, DateTime>((ref, date) {
  final recordsAsync = ref.watch(attendanceRecordsByDateProvider(date));
  return recordsAsync.when(
    data: (records) => AsyncValue.data(records.where((record) => record.isOfficeAttendance).toList()),
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Attendance Statistics Provider (legacy - returns Map)
final attendanceStatsMapProvider = Provider.family<AsyncValue<Map<String, int>>, DateTime>((ref, date) {
  final recordsAsync = ref.watch(attendanceRecordsByDateProvider(date));
  return recordsAsync.when(
    data: (records) {
      final stats = <String, int>{
        'present': records.where((r) => r.isPresent).length,
        'absent': records.where((r) => r.isAbsent).length,
        'late': records.where((r) => r.isLateArrival).length,
        'overtime': records.where((r) => r.isOvertime).length,
      };
      return AsyncValue.data(stats);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

// Weekly/Monthly attendance providers
final weeklyAttendanceProvider = FutureProvider.family<List<AttendanceRecord>, DateTime>((ref, weekStart) async {
  final repository = ref.watch(attendanceRepositoryProvider);
  final weekEnd = weekStart.add(const Duration(days: 6));
  return await repository.getAttendanceByDateRange(weekStart, weekEnd);
});

final monthlyAttendanceProvider = FutureProvider.family<List<AttendanceRecord>, DateTime>((ref, month) async {
  final repository = ref.watch(attendanceRepositoryProvider);
  final monthStart = DateTime(month.year, month.month, 1);
  final monthEnd = DateTime(month.year, month.month + 1, 0);
  return await repository.getAttendanceByDateRange(monthStart, monthEnd);
});

// Daily attendance provider for enhanced attendance screen
final attendanceProvider = FutureProvider.family<List<AttendanceRecord>, DateTime>((ref, date) async {
  final repository = ref.watch(attendanceRepositoryProvider);
  final startOfDay = DateTime(date.year, date.month, date.day);
  final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
  return await repository.getAttendanceByDateRange(startOfDay, endOfDay);
});

// Attendance stats provider
final attendanceStatsProvider = FutureProvider.family<AttendanceStats, DateTime>((ref, date) async {
  final attendanceRecords = await ref.watch(attendanceProvider(date).future);
  return AttendanceStats.fromRecords(attendanceRecords, date);
});

class AttendanceRecordsNotifier extends StateNotifier<AsyncValue<List<AttendanceRecord>>> {
  final AttendanceRepository _repository;

  AttendanceRecordsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadRecords();
  }

  Future<void> loadRecords() async {
    try {
      state = const AsyncValue.loading();
      final records = await _repository.getAllAttendanceRecords();
      state = AsyncValue.data(records);
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
    }
  }

  Future<void> addRecord(AttendanceRecord record) async {
    try {
      await _repository.createAttendanceRecord(record);
      await loadRecords();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> createAttendanceRecord(AttendanceRecord record) async {
    return addRecord(record);
  }

  Future<void> updateRecord(AttendanceRecord record) async {
    try {
      await _repository.updateAttendanceRecord(record);
      await loadRecords();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> updateAttendanceRecord(AttendanceRecord record) async {
    return updateRecord(record);
  }

  Future<void> deleteRecord(String recordId) async {
    try {
      await _repository.deleteAttendanceRecord(recordId);
      await loadRecords();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> markPresent(String workerId, DateTime date, {
    DateTime? checkInTime,
    String? notes,
    String attendanceType = 'site',
  }) async {
    try {
      await _repository.markPresent(workerId, date,
        checkInTime: checkInTime,
        notes: notes,
        attendanceType: attendanceType
      );
      await loadRecords();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> markAbsent(String workerId, DateTime date, {String? reason}) async {
    try {
      await _repository.markAbsent(workerId, date, reason: reason);
      await loadRecords();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }

  Future<void> checkOut(String recordId, DateTime checkOutTime) async {
    try {
      await _repository.checkOut(recordId, checkOutTime);
      await loadRecords();
    } catch (e, stack) {
      state = AsyncValue.error(e, stack);
      rethrow;
    }
  }
}
