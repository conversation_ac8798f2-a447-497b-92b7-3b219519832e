import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/user.dart';
import '../../data/auth_api_service.dart';
import '../../data/auth_repository_impl.dart';
import '../../domain/auth_repository.dart';

// API Service Provider
final authApiServiceProvider = Provider<AuthApiService>((ref) {
  return AuthApiService(DioClient.instance.dio);
});

// Repository Provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final apiService = ref.read(authApiServiceProvider);
  return AuthRepositoryImpl(apiService);
});

// Auth State Provider
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final repository = ref.read(authRepositoryProvider);
  return AuthNotifier(repository);
});

// Current User Provider - Returns user from auth state (no additional API call)
final currentUserProvider = Provider<AsyncValue<User?>>((ref) {
  final authState = ref.watch(authStateProvider);

  if (authState.isLoading) {
    return const AsyncValue.loading();
  }

  if (authState.error != null) {
    return AsyncValue.error(authState.error!, StackTrace.current);
  }

  return AsyncValue.data(authState.user);
});

class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final String? error;
  final User? user;

  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.error,
    this.user,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    String? error,
    User? user,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      user: user ?? this.user,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _repository;

  AuthNotifier(this._repository) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    print('🔐 Checking auth status...');
    final isLoggedIn = _repository.isLoggedIn;
    print('🔐 isLoggedIn: $isLoggedIn');

    if (isLoggedIn) {
      // If user is logged in, fetch user data
      try {
        print('🔐 Fetching current user...');
        final user = await _repository.getCurrentUser();
        print('🔐 User fetched successfully: ${user.email}');
        state = state.copyWith(
          isAuthenticated: true,
          user: user,
        );
      } catch (e) {
        // If fetching user data fails, logout
        print('🔐 AUTH ERROR: Failed to fetch user data: $e');
        await _repository.logout();
        state = const AuthState();
      }
    } else {
      print('🔐 No token found, user not authenticated');
      state = state.copyWith(isAuthenticated: false);
    }
  }

  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _repository.login(email, password);

      if (result.success) {
        state = state.copyWith(
          isAuthenticated: true,
          isLoading: false,
          user: result.user,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  Future<bool> loginWithIdentifier({
    required String identifier,
    required String password,
    required String loginType,
  }) async {
    print('🔐 Starting login with identifier: $identifier, type: $loginType');
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _repository.loginWithIdentifier(
        identifier: identifier,
        password: password,
        loginType: loginType,
      );

      if (result.success) {
        print('🔐 Login successful for user: ${result.user?.email}');
        state = state.copyWith(
          isAuthenticated: true,
          isLoading: false,
          user: result.user,
        );
        return true;
      } else {
        print('🔐 Login failed: ${result.error}');
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
        return false;
      }
    } catch (e) {
      print('🔐 Login exception: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  Future<bool> register(String email, String password, String fullName, String? phone) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _repository.register(email, password, fullName, phone);

      if (result.success) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  Future<void> logout() async {
    print('🔐 Logging out user');
    await _repository.logout();
    state = const AuthState();
    print('🔐 Logout completed');
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}
