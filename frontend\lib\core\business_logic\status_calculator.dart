/// Status calculation engine ported from V1's business logic
/// Provides real-time status calculations for properties and systems
library;

import 'threshold_manager.dart';

export 'threshold_manager.dart' show StatusLevel;

class StatusMetric {
  final String name;
  final dynamic value;
  final String unit;
  final StatusLevel status;
  final String displayValue;

  const StatusMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.status,
    required this.displayValue,
  });

  factory StatusMetric.create(String name, dynamic value, String unit, StatusLevel status) {
    return StatusMetric(
      name: name,
      value: value,
      unit: unit,
      status: status,
      displayValue: _formatDisplayValue(value, unit),
    );
  }

  static String _formatDisplayValue(dynamic value, String unit) {
    if (value is double) {
      return '${value.toStringAsFixed(1)}$unit';
    } else if (value is int) {
      return '$value$unit';
    } else if (value is bool) {
      return value ? 'Active' : 'Inactive';
    }
    return '$value$unit';
  }
}

class FunctionalAreaStatus {
  final StatusLevel status;
  final List<StatusMetric> metrics;
  final int issueCount;
  final DateTime lastUpdated;

  const FunctionalAreaStatus({
    required this.status,
    required this.metrics,
    required this.issueCount,
    required this.lastUpdated,
  });
}

class PropertyStatus {
  final String id;
  final String name;
  final String type;
  final StatusLevel overallStatus;
  final String? imageUrl;
  final Map<String, FunctionalAreaStatus> functionalAreas;
  final DateTime lastUpdated;

  const PropertyStatus({
    required this.id,
    required this.name,
    required this.type,
    required this.overallStatus,
    this.imageUrl,
    required this.functionalAreas,
    required this.lastUpdated,
  });
}

class StatusCalculator {
  static final _thresholdManager = ThresholdManager.instance;

  /// Calculate electricity/generator status based on fuel data
  static FunctionalAreaStatus calculateElectricityStatus({
    required double fuelLevelLiters,
    required double consumptionRate,
    required double runtimeHours,
    required double efficiencyPercentage,
    String propertyType = 'residential',
  }) {
    final metrics = <StatusMetric>[];

    // Calculate backup hours
    final backupHours = fuelLevelLiters / (consumptionRate > 0 ? consumptionRate : 1.0);

    // Use threshold manager for status calculation
    final fuelLevelPercentage = (fuelLevelLiters / 100) * 100; // Assuming 100L is full tank
    final fuelStatus = _thresholdManager.calculateStatus('fuel', 'fuel_level', fuelLevelPercentage, propertyType);
    final backupStatus = _thresholdManager.calculateStatus('fuel', 'backup_hours', backupHours, propertyType);
    final efficiencyStatus = _thresholdManager.calculateStatus('fuel', 'efficiency', efficiencyPercentage, propertyType);

    metrics.add(StatusMetric.create('fuel_level', fuelLevelLiters, ' L', fuelStatus));
    metrics.add(StatusMetric.create('backup_hours', backupHours, ' hrs', backupStatus));
    metrics.add(StatusMetric.create('consumption_rate', consumptionRate, ' L/hr', StatusLevel.green));
    metrics.add(StatusMetric.create('efficiency', efficiencyPercentage, '%', efficiencyStatus));

    // Overall status is worst of all metrics
    final overallStatus = _getWorstStatus([fuelStatus, backupStatus, efficiencyStatus]);

    return FunctionalAreaStatus(
      status: overallStatus,
      metrics: metrics,
      issueCount: overallStatus == StatusLevel.red ? 1 : 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// Calculate maintenance status based on issues
  static FunctionalAreaStatus calculateMaintenanceStatus({
    required List<MaintenanceIssueData> issues,
    String propertyType = 'all',
  }) {
    final metrics = <StatusMetric>[];

    final openIssues = issues.where((i) => i.status == 'open').length;
    final criticalIssues = issues.where((i) => i.priority == 'critical').length;
    final overdueIssues = issues.where((i) => i.isOverdue).length;

    // Use threshold manager for open issues status
    final openIssuesStatus = _thresholdManager.calculateStatus('maintenance', 'open_issues', openIssues.toDouble(), propertyType);

    // Critical and overdue issues are always red if > 0
    final criticalStatus = criticalIssues > 0 ? StatusLevel.red : StatusLevel.green;
    final overdueStatus = overdueIssues > 0 ? StatusLevel.red : StatusLevel.green;

    metrics.add(StatusMetric.create('open_issues', openIssues, ' open', openIssuesStatus));
    metrics.add(StatusMetric.create('critical_issues', criticalIssues, ' critical', criticalStatus));
    metrics.add(StatusMetric.create('overdue_issues', overdueIssues, ' overdue', overdueStatus));

    // Overall status is worst of all metrics
    final overallStatus = _getWorstStatus([openIssuesStatus, criticalStatus, overdueStatus]);

    return FunctionalAreaStatus(
      status: overallStatus,
      metrics: metrics,
      issueCount: openIssues,
      lastUpdated: DateTime.now(),
    );
  }

  /// Helper method to get the worst status from a list
  static StatusLevel _getWorstStatus(List<StatusLevel> statuses) {
    if (statuses.contains(StatusLevel.red)) return StatusLevel.red;
    if (statuses.contains(StatusLevel.orange)) return StatusLevel.orange;
    return StatusLevel.green;
  }

  /// Calculate internet/connectivity status
  static FunctionalAreaStatus calculateInternetStatus({
    required double uptimePercentage,
    required int disruptionCount,
    required double avgResponseTime,
  }) {
    final metrics = <StatusMetric>[];

    StatusLevel status;
    if (uptimePercentage < 97 || disruptionCount > 5 || avgResponseTime > 1000) {
      status = StatusLevel.red;
    } else if (uptimePercentage < 99 || disruptionCount > 2 || avgResponseTime > 500) {
      status = StatusLevel.orange;
    } else {
      status = StatusLevel.green;
    }

    metrics.add(StatusMetric.create('uptime', uptimePercentage, '%',
        uptimePercentage >= 99 ? StatusLevel.green :
        uptimePercentage >= 97 ? StatusLevel.orange : StatusLevel.red));
    metrics.add(StatusMetric.create('disruptions', disruptionCount, ' count',
        disruptionCount <= 2 ? StatusLevel.green :
        disruptionCount <= 5 ? StatusLevel.orange : StatusLevel.red));
    metrics.add(StatusMetric.create('response_time', avgResponseTime, ' ms',
        avgResponseTime <= 500 ? StatusLevel.green :
        avgResponseTime <= 1000 ? StatusLevel.orange : StatusLevel.red));

    return FunctionalAreaStatus(
      status: status,
      metrics: metrics,
      issueCount: status == StatusLevel.red ? 1 : 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// Calculate security status
  static FunctionalAreaStatus calculateSecurityStatus({
    required int camerasOnline,
    required int totalCameras,
    required bool accessControlActive,
    required List<SecurityIncident> recentIncidents,
  }) {
    final metrics = <StatusMetric>[];

    final cameraPercentage = totalCameras > 0 ? (camerasOnline / totalCameras) * 100 : 100.0;
    final criticalIncidents = recentIncidents.where((i) => i.severity == 'critical').length;

    StatusLevel status;
    if (!accessControlActive || cameraPercentage < 80 || criticalIncidents > 0) {
      status = StatusLevel.red;
    } else if (cameraPercentage < 95) {
      status = StatusLevel.orange;
    } else {
      status = StatusLevel.green;
    }

    metrics.add(StatusMetric.create('cameras_online', camerasOnline, '/$totalCameras',
        cameraPercentage >= 95 ? StatusLevel.green :
        cameraPercentage >= 80 ? StatusLevel.orange : StatusLevel.red));
    metrics.add(StatusMetric.create('access_control', accessControlActive, '',
        accessControlActive ? StatusLevel.green : StatusLevel.red));
    metrics.add(StatusMetric.create('recent_incidents', recentIncidents.length, ' incidents',
        criticalIncidents > 0 ? StatusLevel.red : StatusLevel.green));

    return FunctionalAreaStatus(
      status: status,
      metrics: metrics,
      issueCount: criticalIncidents,
      lastUpdated: DateTime.now(),
    );
  }

  /// Calculate overall property status
  static StatusLevel calculateOverallStatus(Map<String, FunctionalAreaStatus> functionalAreas) {
    if (functionalAreas.isEmpty) return StatusLevel.green;

    final statuses = functionalAreas.values.map((area) => area.status).toList();

    if (statuses.contains(StatusLevel.red)) {
      return StatusLevel.red;
    } else if (statuses.contains(StatusLevel.orange)) {
      return StatusLevel.orange;
    } else {
      return StatusLevel.green;
    }
  }

  /// Calculate attendance compliance
  static FunctionalAreaStatus calculateAttendanceStatus({
    required List<AttendanceRecord> records,
    required DateTime startDate,
    required DateTime endDate,
  }) {
    final metrics = <StatusMetric>[];

    final totalExpectedDays = endDate.difference(startDate).inDays + 1;
    final actualDays = records.length;
    final attendanceRate = totalExpectedDays > 0 ? (actualDays / totalExpectedDays) * 100 : 100.0;

    final avgHours = records.isNotEmpty
        ? records.map((r) => r.hoursWorked).reduce((a, b) => a + b) / records.length
        : 0.0;

    StatusLevel status;
    if (attendanceRate < 80 || avgHours < 6) {
      status = StatusLevel.red;
    } else if (attendanceRate < 95 || avgHours < 7) {
      status = StatusLevel.orange;
    } else {
      status = StatusLevel.green;
    }

    metrics.add(StatusMetric.create('attendance_rate', attendanceRate, '%',
        attendanceRate >= 95 ? StatusLevel.green :
        attendanceRate >= 80 ? StatusLevel.orange : StatusLevel.red));
    metrics.add(StatusMetric.create('avg_hours', avgHours, ' hrs/day',
        avgHours >= 7 ? StatusLevel.green :
        avgHours >= 6 ? StatusLevel.orange : StatusLevel.red));
    metrics.add(StatusMetric.create('total_days', actualDays, ' days', StatusLevel.green));

    return FunctionalAreaStatus(
      status: status,
      metrics: metrics,
      issueCount: status == StatusLevel.red ? 1 : 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// Generate property dashboard status
  static PropertyStatus generatePropertyStatus({
    required String id,
    required String name,
    required String type,
    String? imageUrl,
    double? fuelLevel,
    double? consumptionRate,
    double? runtimeHours,
    double? efficiency,
    List<MaintenanceIssueData>? maintenanceIssues,
    double? uptimePercentage,
    int? disruptionCount,
    double? avgResponseTime,
    int? camerasOnline,
    int? totalCameras,
    bool? accessControlActive,
    List<SecurityIncident>? securityIncidents,
    List<AttendanceRecord>? attendanceRecords,
  }) {
    final functionalAreas = <String, FunctionalAreaStatus>{};

    // Electricity status
    if (fuelLevel != null && consumptionRate != null && runtimeHours != null && efficiency != null) {
      functionalAreas['electricity'] = calculateElectricityStatus(
        fuelLevelLiters: fuelLevel,
        consumptionRate: consumptionRate,
        runtimeHours: runtimeHours,
        efficiencyPercentage: efficiency,
      );
    }

    // Maintenance status
    if (maintenanceIssues != null) {
      functionalAreas['maintenance'] = calculateMaintenanceStatus(
        issues: maintenanceIssues,
      );
    }

    // Internet status
    if (uptimePercentage != null && disruptionCount != null && avgResponseTime != null) {
      functionalAreas['internet'] = calculateInternetStatus(
        uptimePercentage: uptimePercentage,
        disruptionCount: disruptionCount,
        avgResponseTime: avgResponseTime,
      );
    }

    // Security status
    if (camerasOnline != null && totalCameras != null && accessControlActive != null && securityIncidents != null) {
      functionalAreas['security'] = calculateSecurityStatus(
        camerasOnline: camerasOnline,
        totalCameras: totalCameras,
        accessControlActive: accessControlActive,
        recentIncidents: securityIncidents,
      );
    }

    // Attendance status
    if (attendanceRecords != null) {
      final now = DateTime.now();
      functionalAreas['attendance'] = calculateAttendanceStatus(
        records: attendanceRecords,
        startDate: now.subtract(const Duration(days: 30)),
        endDate: now,
      );
    }

    final overallStatus = calculateOverallStatus(functionalAreas);

    return PropertyStatus(
      id: id,
      name: name,
      type: type,
      overallStatus: overallStatus,
      imageUrl: imageUrl,
      functionalAreas: functionalAreas,
      lastUpdated: DateTime.now(),
    );
  }
}

// Supporting data classes
class MaintenanceIssueData {
  final String id;
  final String status;
  final String priority;
  final DateTime? dueDate;

  const MaintenanceIssueData({
    required this.id,
    required this.status,
    required this.priority,
    this.dueDate,
  });

  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!) && status != 'completed';
  }
}

class SecurityIncident {
  final String id;
  final String severity;
  final DateTime timestamp;

  const SecurityIncident({
    required this.id,
    required this.severity,
    required this.timestamp,
  });
}

class AttendanceRecord {
  final String id;
  final DateTime date;
  final double hoursWorked;

  const AttendanceRecord({
    required this.id,
    required this.date,
    required this.hoursWorked,
  });
}
