{"openapi": "3.0.0", "info": {"title": "SRSR Property Management API", "description": "Comprehensive property management system API for residential and office properties", "version": "1.0.0", "contact": {"name": "SRSR Property Management", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://your-vercel-app.vercel.app", "description": "Production server"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "format": "email"}, "full_name": {"type": "string"}, "phone": {"type": "string"}, "is_active": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string", "format": "date-time"}}}, "Property": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["residential", "office"]}, "address": {"type": "string"}, "description": {"type": "string"}, "image_url": {"type": "string"}, "is_active": {"type": "boolean"}, "services": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyService"}}, "created_at": {"type": "string", "format": "date-time"}}}, "PropertyService": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "service_type": {"type": "string", "enum": ["electricity", "water", "internet", "security", "ott"]}, "status": {"type": "string", "enum": ["operational", "warning", "critical", "maintenance"]}, "last_checked": {"type": "string", "format": "date-time"}, "notes": {"type": "string"}}}, "MaintenanceIssue": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "status": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}, "service_type": {"type": "string"}, "department": {"type": "string"}, "reported_by": {"type": "string", "format": "uuid"}, "assigned_to": {"type": "string", "format": "uuid"}, "due_date": {"type": "string", "format": "date"}, "created_at": {"type": "string", "format": "date-time"}}}, "AttendanceRecord": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user_id": {"type": "string", "format": "uuid"}, "date": {"type": "string", "format": "date"}, "check_in_time": {"type": "string", "format": "time"}, "check_out_time": {"type": "string", "format": "time"}, "hours_worked": {"type": "number", "format": "float"}, "status": {"type": "string", "enum": ["present", "absent", "late", "half_day"]}, "notes": {"type": "string"}}}, "GeneratorFuelLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "fuel_level_liters": {"type": "number", "format": "float"}, "consumption_rate": {"type": "number", "format": "float"}, "runtime_hours": {"type": "number", "format": "float"}, "efficiency_percentage": {"type": "number", "format": "float"}, "last_maintenance": {"type": "string", "format": "date"}, "next_maintenance": {"type": "string", "format": "date"}, "notes": {"type": "string"}, "recorded_at": {"type": "string", "format": "date-time"}}}, "SecurityLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "property_id": {"type": "string", "format": "uuid"}, "guard_name": {"type": "string"}, "shift_start": {"type": "string", "format": "date-time"}, "shift_end": {"type": "string", "format": "date-time"}, "patrol_rounds": {"type": "integer"}, "incidents_reported": {"type": "integer"}, "visitors_logged": {"type": "integer"}, "notes": {"type": "string"}}}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object"}, "error": {"type": "string"}, "code": {"type": "string"}}}, "PaginationResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "object"}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}, "has_next": {"type": "boolean"}, "has_prev": {"type": "boolean"}}}}}}}, "paths": {"/api/auth/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return JWT token", "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "token": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}}}}, "401": {"description": "Invalid credentials"}}}}, "/api/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register new user", "description": "Create a new user account (admin only)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password", "full_name"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}, "full_name": {"type": "string"}, "phone": {"type": "string"}}}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "403": {"description": "Insufficient permissions"}}}}, "/api/auth/me": {"get": {"tags": ["Authentication"], "summary": "Get current user profile", "description": "Retrieve the authenticated user's profile information", "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "Unauthorized"}}}}, "/api/properties": {"get": {"tags": ["Properties"], "summary": "Get all properties", "description": "Retrieve a list of all properties with optional filtering", "parameters": [{"name": "type", "in": "query", "description": "Filter by property type", "schema": {"type": "string", "enum": ["residential", "office", "construction_site"]}}, {"name": "status", "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["active", "inactive"]}}], "responses": {"200": {"description": "Properties retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Property"}}}}}}}}}, "post": {"tags": ["Properties"], "summary": "Create new property", "description": "Create a new property (admin/property_manager only)", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["residential", "office"]}, "address": {"type": "string"}, "description": {"type": "string"}}}}}}, "responses": {"201": {"description": "Property created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}": {"get": {"tags": ["Properties"], "summary": "Get property by ID", "description": "Retrieve detailed information about a specific property", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"$ref": "#/components/schemas/Property"}}}}}}, "404": {"description": "Property not found"}}}}, "/api/maintenance": {"get": {"tags": ["Maintenance"], "summary": "Get maintenance issues", "description": "Retrieve maintenance issues with optional filtering and pagination", "parameters": [{"name": "property_id", "in": "query", "description": "Filter by property ID", "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "description": "Filter by status", "schema": {"type": "string", "enum": ["open", "in_progress", "resolved", "closed"]}}, {"name": "priority", "in": "query", "description": "Filter by priority", "schema": {"type": "string", "enum": ["low", "medium", "high", "critical"]}}, {"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "description": "Items per page", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10}}], "responses": {"200": {"description": "Maintenance issues retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponse"}}}}}}, "post": {"tags": ["Maintenance"], "summary": "Create maintenance issue", "description": "Create a new maintenance issue", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["property_id", "title", "description", "priority"], "properties": {"property_id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "service_type": {"type": "string"}, "department": {"type": "string"}, "due_date": {"type": "string", "format": "date"}}}}}}, "responses": {"201": {"description": "Maintenance issue created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}/members": {"get": {"tags": ["Properties"], "summary": "Get property members", "description": "Retrieve members for any property type (office, construction site, residential)", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Property members retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"members": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyMember"}}, "property": {"$ref": "#/components/schemas/Property"}}}}}}}}}}, "post": {"tags": ["Properties"], "summary": "Add property member", "description": "Add a member to any property type", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "role"], "properties": {"user_id": {"type": "string", "format": "uuid"}, "role": {"type": "string"}, "position": {"type": "string"}, "department": {"type": "string"}, "hourly_rate": {"type": "number", "format": "float"}, "start_date": {"type": "string", "format": "date"}, "end_date": {"type": "string", "format": "date"}}}}}}, "responses": {"201": {"description": "Property member added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/properties/{id}/attendance": {"get": {"tags": ["Properties"], "summary": "Get property attendance", "description": "Retrieve attendance records for any property type", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}, {"name": "date", "in": "query", "description": "Specific date (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "start_date", "in": "query", "description": "Start date for range", "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "End date for range", "schema": {"type": "string", "format": "date"}}, {"name": "user_id", "in": "query", "description": "Filter by specific user", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Attendance records retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"attendance": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyAttendance"}}, "property": {"$ref": "#/components/schemas/Property"}}}}}}}}}}, "post": {"tags": ["Properties"], "summary": "Record property attendance", "description": "Record attendance for any property type", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_id", "date"], "properties": {"user_id": {"type": "string", "format": "uuid"}, "date": {"type": "string", "format": "date"}, "check_in_time": {"type": "string", "format": "time"}, "check_out_time": {"type": "string", "format": "time"}, "hours_worked": {"type": "number", "format": "float"}, "notes": {"type": "string"}}}}}}, "responses": {"201": {"description": "Attendance recorded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/generator-fuel/{propertyId}": {"get": {"tags": ["Generator Fuel"], "summary": "Get generator fuel logs", "description": "Retrieve fuel logs for a specific property", "parameters": [{"name": "propertyId", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Fuel logs retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/GeneratorFuelLog"}}}}}}}}}, "post": {"tags": ["Generator Fuel"], "summary": "Add fuel log entry", "description": "Record new generator fuel log entry", "parameters": [{"name": "propertyId", "in": "path", "required": true, "description": "Property ID", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["fuel_level_liters"], "properties": {"fuel_level_liters": {"type": "number", "format": "float"}, "consumption_rate": {"type": "number", "format": "float"}, "runtime_hours": {"type": "number", "format": "float"}, "efficiency_percentage": {"type": "number", "format": "float"}, "notes": {"type": "string"}}}}}}, "responses": {"201": {"description": "Fuel log entry created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/dashboard/status": {"get": {"tags": ["Dashboard"], "summary": "Get system status", "description": "Retrieve overall system status and metrics", "responses": {"200": {"description": "System status retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"properties": {"type": "object", "properties": {"total": {"type": "integer"}, "operational": {"type": "integer"}, "warning": {"type": "integer"}, "critical": {"type": "integer"}}}, "maintenance_issues": {"type": "object", "properties": {"total": {"type": "integer"}, "open": {"type": "integer"}, "in_progress": {"type": "integer"}, "critical": {"type": "integer"}}}, "recent_alerts": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}}}}}}}, "tags": [{"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Properties", "description": "Property management operations"}, {"name": "Maintenance", "description": "Maintenance issue tracking"}, {"name": "Attendance", "description": "Site and office attendance management"}, {"name": "Generator Fuel", "description": "Generator fuel monitoring and logging"}, {"name": "Dashboard", "description": "Dashboard and system overview"}]}