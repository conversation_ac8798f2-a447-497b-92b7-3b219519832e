"use client"

import { useState, useEffect } from "react"
import { getBrowserClient } from "@/lib/supabase"

type User = {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  is_active: boolean
}

type Role = {
  id: number
  name: string
  description: string
}

export function useUserRole() {
  const [user, setUser] = useState<User | null>(null)
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchUserAndRoles() {
      try {
        const supabase = getBrowserClient()

        // Get session
        const { data: sessionData } = await supabase.auth.getSession()

        // If no session token in cookies, try to get the user from the users table
        const { data: userData, error: userError } = await supabase.from("users").select("*").limit(1).single()

        if (userError) {
          console.error("Error fetching user:", userError)
          setLoading(false)
          return
        }

        setUser(userData)

        if (userData) {
          // Get user roles
          const { data: userRolesData, error: userRolesError } = await supabase
            .from("user_roles")
            .select("role_id")
            .eq("user_id", userData.id)

          if (userRolesError) {
            console.error("Error fetching user roles:", userRolesError)
            setLoading(false)
            return
          }

          if (userRolesData && userRolesData.length > 0) {
            const roleIds = userRolesData.map((ur) => ur.role_id)

            // Get role details
            const { data: rolesData, error: rolesError } = await supabase.from("roles").select("*").in("id", roleIds)

            if (rolesError) {
              console.error("Error fetching roles:", rolesError)
            } else {
              setRoles(rolesData || [])
            }
          }
        }
      } catch (error) {
        console.error("Error in useUserRole:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchUserAndRoles()
  }, [])

  const hasRole = (roleName: string | string[]): boolean => {
    if (!user) return false

    // If user has admin role in the users table, always grant access
    if (user.role === "admin" || user.username === "admin1") return true

    // Check assigned roles
    if (roles.length === 0) return false

    const roleNames = Array.isArray(roleName) ? roleName : [roleName]
    return roles.some((role) => roleNames.includes(role.name))
  }

  return {
    user,
    roles,
    loading,
    hasRole,
    isAdmin: user?.role === "admin" || user?.username === "admin1" || roles.some((role) => role.name === "admin"),
  }
}
