"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Edit, Trash2, Plus, Download, Search } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import {
  getFunctionProcesses,
  deleteFunctionProcess,
  exportFunctionProcessesToCSV,
  type FunctionProcess,
} from "@/app/actions/function-process"
import { FunctionProcessForm } from "@/components/function-process-form"

export function FunctionProcessMatrix() {
  const [functionProcesses, setFunctionProcesses] = useState<FunctionProcess[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [currentProcess, setCurrentProcess] = useState<FunctionProcess | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterFunction, setFilterFunction] = useState<string>("")
  const [uniqueFunctions, setUniqueFunctions] = useState<string[]>([])

  const fetchFunctionProcesses = async () => {
    setLoading(true)
    try {
      const data = await getFunctionProcesses()
      setFunctionProcesses(data)

      // Extract unique function names for filtering
      const uniqueFunctionNames = Array.from(new Set(data.map((item) => item.function_name)))
      setUniqueFunctions(uniqueFunctionNames)
    } catch (error) {
      console.error("Error fetching function processes:", error)
      toast({
        title: "Error",
        description: "Failed to load function processes. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchFunctionProcesses()
  }, [])

  const handleDelete = async (id: string) => {
    try {
      const result = await deleteFunctionProcess(id)
      if (result.success) {
        toast({
          title: "Success",
          description: "Function process deleted successfully.",
        })
        fetchFunctionProcesses()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete function process.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting function process:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive",
      })
    } finally {
      setShowDeleteDialog(false)
      setCurrentProcess(null)
    }
  }

  const handleExport = async () => {
    try {
      const csv = await exportFunctionProcessesToCSV()

      // Create a blob and download link
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.setAttribute("href", url)
      link.setAttribute("download", "function_processes.csv")
      link.style.visibility = "hidden"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Success",
        description: "Function processes exported to CSV.",
      })
    } catch (error) {
      console.error("Error exporting function processes:", error)
      toast({
        title: "Error",
        description: "Failed to export function processes.",
        variant: "destructive",
      })
    }
  }

  const filteredProcesses = functionProcesses.filter((process) => {
    const matchesSearch =
      process.function_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      process.sub_function.toLowerCase().includes(searchTerm.toLowerCase()) ||
      process.responsible_agent.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesFilter = filterFunction ? process.function_name === filterFunction : true

    return matchesSearch && matchesFilter
  })

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div>
            <CardTitle>Functions List</CardTitle>
            <CardDescription>Manage operational functions and processes</CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" size="sm" onClick={handleExport} className="flex items-center gap-1">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button size="sm" onClick={() => setShowAddDialog(true)} className="flex items-center gap-1">
              <Plus className="h-4 w-4" />
              Add Function
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search functions..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="w-full sm:w-64">
              <Select value={filterFunction} onValueChange={setFilterFunction}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by function" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Functions</SelectItem>
                  {uniqueFunctions.map((func) => (
                    <SelectItem key={func} value={func}>
                      {func}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <p>Loading function processes...</p>
            </div>
          ) : filteredProcesses.length === 0 ? (
            <div className="flex justify-center items-center h-64">
              <p>No function processes found.</p>
            </div>
          ) : (
            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Function</TableHead>
                      <TableHead>Sub-Function</TableHead>
                      <TableHead>Input</TableHead>
                      <TableHead>Process</TableHead>
                      <TableHead>Output</TableHead>
                      <TableHead>Threshold Limits</TableHead>
                      <TableHead>Responsible Agent</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProcesses.map((process) => (
                      <TableRow key={process.id}>
                        <TableCell className="font-medium">{process.function_name}</TableCell>
                        <TableCell>{process.sub_function}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{process.input}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{process.process}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{process.output}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{process.threshold_limits}</TableCell>
                        <TableCell>{process.responsible_agent}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setCurrentProcess(process)
                                setShowEditDialog(true)
                              }}
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setCurrentProcess(process)
                                setShowDeleteDialog(true)
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Function Process Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add New Function Process</DialogTitle>
            <DialogDescription>Create a new function process to add to the matrix</DialogDescription>
          </DialogHeader>
          <FunctionProcessForm
            onSuccess={() => {
              setShowAddDialog(false)
              fetchFunctionProcesses()
            }}
            onCancel={() => setShowAddDialog(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Function Process Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Function Process</DialogTitle>
            <DialogDescription>Update the details of this function process</DialogDescription>
          </DialogHeader>
          {currentProcess && (
            <FunctionProcessForm
              existingProcess={currentProcess}
              onSuccess={() => {
                setShowEditDialog(false)
                fetchFunctionProcesses()
              }}
              onCancel={() => setShowEditDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the function process. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => currentProcess && handleDelete(currentProcess.id)}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
