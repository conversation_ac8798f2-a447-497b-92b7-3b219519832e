"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Home, Bell } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { AlertTriangle, AlertCircle, Wrench, Calendar } from "lucide-react"
import { getDetailedStatusBreakdown } from "@/app/actions/status-data"

interface PageNavigationProps {
  className?: string
}

interface ActionableNotification {
  id: string
  propertyName: string
  systemType: string
  message: string
  severity: "orange" | "red"
  actionUrl: string
  actionText: string
}

const scrollbarStyles = `
  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }
  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`

// Cache for notifications
let notificationCache: {
  data: ActionableNotification[]
  timestamp: number
} = {
  data: [],
  timestamp: 0,
}

const NOTIFICATION_CACHE_DURATION = 90000 // 90 seconds (increased from 30)

export function PageNavigation({ className }: PageNavigationProps) {
  const router = useRouter()

  const [notifications, setNotifications] = useState<ActionableNotification[]>([])
  const [notificationCount, setNotificationCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const now = Date.now()

        // Check cache first
        if (notificationCache.data.length > 0 && now - notificationCache.timestamp < NOTIFICATION_CACHE_DURATION) {
          console.log("📦 Using cached notifications")
          setNotifications(notificationCache.data)
          setNotificationCount(notificationCache.data.length)
          return
        }

        console.log("🔔 Fetching fresh notifications...")
        const alerts: ActionableNotification[] = []

        // Use Promise.allSettled with shorter timeouts to handle failures gracefully
        const [criticalResult, warningResult] = await Promise.allSettled([
          Promise.race([
            getDetailedStatusBreakdown("critical"),
            new Promise((_, reject) => setTimeout(() => reject(new Error("Critical timeout")), 5000)),
          ]),
          Promise.race([
            getDetailedStatusBreakdown("warning"),
            new Promise((_, reject) => setTimeout(() => reject(new Error("Warning timeout")), 5000)),
          ]),
        ])

        // Process critical issues (RED status - Open/On Hold maintenance issues + Overdue recurring)
        if (criticalResult.status === "fulfilled") {
          const criticalIssues = criticalResult.value as any[]
          console.log("🔴 Critical issues for bell:", criticalIssues.length)

          for (const issue of criticalIssues) {
            let message = issue.details[0] || "System requires immediate attention"

            // Customize message for maintenance issues
            if (issue.systemType === "Maintenance") {
              message = issue.details[0] || "Critical maintenance issues require attention"
            } else if (issue.systemType === "Recurring Maintenance") {
              message = issue.details[0] || "Recurring maintenance tasks are overdue"
            }

            alerts.push({
              id: `critical-${issue.propertyName}-${issue.systemType}`,
              propertyName: issue.propertyName,
              systemType: issue.systemType,
              message,
              severity: "red",
              actionUrl: getActionUrl(issue.propertyName, issue.systemType),
              actionText:
                issue.systemType === "Maintenance" || issue.systemType === "Recurring Maintenance"
                  ? "View Issues"
                  : "Resolve Issue",
            })
          }
        } else {
          console.warn("⚠️ Failed to fetch critical issues:", criticalResult.reason)
        }

        // Process warning issues (ORANGE status - In Progress maintenance issues + Due soon recurring)
        if (warningResult.status === "fulfilled") {
          const warningIssues = warningResult.value as any[]
          console.log("🟠 Warning issues for bell:", warningIssues.length)

          for (const issue of warningIssues) {
            let message = issue.details[0] || "System performance degraded"

            // Customize message for maintenance issues
            if (issue.systemType === "Maintenance") {
              message = issue.details[0] || "Maintenance issues in progress"
            } else if (issue.systemType === "Recurring Maintenance") {
              message = issue.details[0] || "Recurring maintenance tasks due soon"
            }

            alerts.push({
              id: `warning-${issue.propertyName}-${issue.systemType}`,
              propertyName: issue.propertyName,
              systemType: issue.systemType,
              message,
              severity: "orange",
              actionUrl: getActionUrl(issue.propertyName, issue.systemType),
              actionText:
                issue.systemType === "Maintenance" || issue.systemType === "Recurring Maintenance"
                  ? "Track Progress"
                  : "View Details",
            })
          }
        } else {
          console.warn("⚠️ Failed to fetch warning issues:", warningResult.reason)
        }

        // Cache the results
        notificationCache = {
          data: alerts,
          timestamp: now,
        }

        console.log(
          `✅ Bell notifications synchronized: ${alerts.length} total (${alerts.filter((a) => a.severity === "red").length} critical, ${alerts.filter((a) => a.severity === "orange").length} warning)`,
        )
        setNotifications(alerts)
        setNotificationCount(alerts.length)
      } catch (error) {
        console.error("Error fetching notifications:", error)
        // Use cached data if available
        if (notificationCache.data.length > 0) {
          console.log("📦 Using stale cached notifications due to error")
          setNotifications(notificationCache.data)
          setNotificationCount(notificationCache.data.length)
        }
      }
    }

    fetchNotifications()
    // Refresh every 2 minutes for better performance
    const interval = setInterval(fetchNotifications, 120000)
    return () => clearInterval(interval)
  }, [])

  const getActionUrl = (propertyName: string, systemType: string): string => {
    // Convert property name to URL format
    const propertyId = propertyName
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace("home", "")
      .replace("office", "")
      .replace("guest-house", "guest-house")
      .trim()
      .replace(/^-+|-+$/g, "")

    // Map system types to URLs
    if (systemType === "Electricity") {
      return `/dashboard/home/<USER>/electricity`
    } else if (systemType === "OTT Services") {
      return `/dashboard/home/<USER>/ott`
    } else if (systemType === "Maintenance" || systemType === "Recurring Maintenance") {
      return `/dashboard/home/<USER>// Go to property overview where maintenance issues are shown
    } else if (systemType === "Water") {
      return `/dashboard/home/<USER>/water`
    } else if (systemType === "Security") {
      return `/dashboard/home/<USER>/security`
    }

    // Default fallback
    return `/dashboard/home/<USER>
  }

  const handleNotificationClick = (notification: ActionableNotification) => {
    setIsOpen(false) // Close the popover
    router.push(notification.actionUrl)
  }

  const goBack = () => {
    router.back()
  }

  const goHome = () => {
    router.push("/dashboard")
  }

  const getNotificationIcon = (notification: ActionableNotification) => {
    if (notification.systemType === "Maintenance") {
      return <Wrench className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
    } else if (notification.systemType === "Recurring Maintenance") {
      return <Calendar className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
    } else if (notification.severity === "red") {
      return <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
    } else {
      return <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
    }
  }

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: scrollbarStyles }} />
      <div className={`flex items-center gap-2 ${className}`}>
        <Button variant="outline" size="sm" onClick={goBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="relative">
              <Bell className="h-4 w-4" />
              {notificationCount > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  {notificationCount > 99 ? "99+" : notificationCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-96 p-0" align="end">
            <div className="p-4 border-b">
              <h4 className="font-semibold">System Alerts</h4>
              <p className="text-sm text-muted-foreground">
                {notificationCount === 0
                  ? "All systems operational"
                  : `${notificationCount} issue${notificationCount !== 1 ? "s" : ""} detected`}
              </p>
            </div>
            <div className="relative">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No alerts at this time</p>
                </div>
              ) : (
                <div
                  className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-600"
                  style={{
                    scrollbarWidth: "thin",
                    scrollbarColor: "#888 #f1f1f1",
                  }}
                >
                  <div className="p-2 space-y-1">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors border-b border-border/50 last:border-b-0 cursor-pointer"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        {getNotificationIcon(notification)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">{notification.propertyName}</span>
                            <Badge
                              variant={
                                notification.systemType === "Maintenance"
                                  ? "outline"
                                  : notification.systemType === "Recurring Maintenance"
                                    ? "secondary"
                                    : notification.severity === "red"
                                      ? "destructive"
                                      : "secondary"
                              }
                              className="text-xs"
                            >
                              {notification.systemType}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{notification.message}</p>
                          <p className="text-xs text-blue-600 font-medium">{notification.actionText} →</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  {notifications.length > 4 && (
                    <div className="sticky bottom-0 bg-background/95 backdrop-blur-sm p-2 text-center text-xs text-muted-foreground border-t">
                      Scroll to see all {notifications.length} alerts
                    </div>
                  )}
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>

        <Button variant="outline" size="sm" onClick={goHome}>
          <Home className="mr-2 h-4 w-4" />
          Home
        </Button>
      </div>
    </>
  )
}
