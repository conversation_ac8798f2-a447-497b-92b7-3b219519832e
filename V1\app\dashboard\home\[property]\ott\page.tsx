"use client"

import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"
import { OttServicesList } from "@/components/ott-services-list"
import { getOttServices, type OttService } from "@/app/actions/ott-services"
import { Tv } from "lucide-react"

export default function OttServicesPage() {
  const params = useParams()
  const propertyId = params.property as string
  const [services, setServices] = useState<OttService[]>([])
  const [loading, setLoading] = useState(true)

  const propertyNames = {
    "jublee-hills": "Jublee Hills Home",
    "gandipet-guest-house": "Gandipet Guest House",
  }

  const propertyName = propertyNames[propertyId as keyof typeof propertyNames] || "Property"

  const fetchServices = async () => {
    setLoading(true)
    try {
      const data = await getOttServices(propertyId)
      setServices(data)
    } catch (error) {
      console.error("Error fetching OTT services:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchServices()
  }, [propertyId])

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/home">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/ott`}>OTT Services</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <div className="mb-8 flex items-center gap-3">
          <Tv className="h-6 w-6 text-red-500" />
          <h1 className="text-3xl font-bold">OTT Services</h1>
        </div>

        <div className="space-y-6">
          <OttServicesList services={services} propertyId={propertyId} onRefresh={fetchServices} />
        </div>
      </div>
    </div>
  )
}
