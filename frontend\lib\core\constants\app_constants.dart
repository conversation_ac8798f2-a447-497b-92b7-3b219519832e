class AppConstants {
  // App Info
  static const String appName = 'SRSR Property Management';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Hive Box Names
  static const String authBox = 'auth_box';
  static const String settingsBox = 'settings_box';
  static const String cacheBox = 'cache_box';
  
  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 100;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Property Types
  static const String residential = 'residential';
  static const String office = 'office';
  
  // Service Types
  static const String electricity = 'electricity';
  static const String water = 'water';
  static const String internet = 'internet';
  static const String security = 'security';
  static const String ott = 'ott';
  
  // Status Types
  static const String operational = 'operational';
  static const String warning = 'warning';
  static const String critical = 'critical';
  static const String maintenance = 'maintenance';
  
  // Priority Levels
  static const String low = 'low';
  static const String medium = 'medium';
  static const String high = 'high';
  
  // Maintenance Status
  static const String open = 'open';
  static const String inProgress = 'in_progress';
  static const String resolved = 'resolved';
  static const String closed = 'closed';
  
  // Attendance Status
  static const String present = 'present';
  static const String absent = 'absent';
  static const String late = 'late';
  static const String halfDay = 'half_day';
}
