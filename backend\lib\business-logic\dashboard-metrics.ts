/**
 * Dashboard metrics calculation and business logic
 * Enhanced version combining V1's rich domain logic with Backend's data processing
 */

import { prisma } from '@/lib/prisma';
import { calculateGeneratorStats, getFuelStatus } from './generator-calculations';

export interface PropertyStatusMetric {
  name: string;
  value: string | number;
  unit: string;
  status: 'green' | 'orange' | 'red';
  displayValue: string;
}

export interface FunctionalAreaStatus {
  status: 'green' | 'orange' | 'red';
  metrics: PropertyStatusMetric[];
  issueCount: number;
  lastUpdated: Date;
}

export interface PropertyDashboardStatus {
  id: string;
  name: string;
  type: 'residential' | 'office';
  overallStatus: 'green' | 'orange' | 'red';
  functionalAreas: {
    electricity: FunctionalAreaStatus;
    internet: FunctionalAreaStatus;
    maintenance: FunctionalAreaStatus;
    ott_services: FunctionalAreaStatus;
  };
  lastUpdated: Date;
}

export interface SystemDashboardMetrics {
  properties: {
    total: number;
    operational: number;
    warning: number;
    critical: number;
  };
  maintenance_issues: {
    total: number;
    open: number;
    in_progress: number;
    critical: number;
  };
  recent_alerts: Array<{
    id: string;
    type: 'maintenance' | 'service' | 'fuel' | 'payment';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    property_name: string;
    timestamp: Date;
  }>;
  system_health: {
    overall_score: number;
    uptime_percentage: number;
    active_services: number;
    total_services: number;
  };
}

/**
 * Create a safe metric object with formatted display value
 */
export function createSafeMetric(
  name: string,
  value: string | number,
  unit: string,
  status: 'green' | 'orange' | 'red'
): PropertyStatusMetric {
  let displayValue: string;
  
  if (typeof value === 'number') {
    if (Number.isInteger(value)) {
      displayValue = `${value}${unit}`;
    } else {
      displayValue = `${value.toFixed(1)}${unit}`;
    }
  } else {
    displayValue = `${value}${unit}`;
  }

  return {
    name,
    value,
    unit,
    status,
    displayValue,
  };
}

/**
 * Calculate electricity/generator status for a property
 */
export async function calculateElectricityStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    // Get latest generator fuel data
    const latestFuelLog = await prisma.generatorFuelLog.findFirst({
      where: { propertyId },
      orderBy: { recordedAt: 'desc' },
    });

    if (!latestFuelLog) {
      return {
        status: 'red',
        metrics: [createSafeMetric('fuel', 'No data', '', 'red')],
        issueCount: 1,
        lastUpdated: new Date(),
      };
    }

    // Calculate generator statistics using business logic
    const stats = calculateGeneratorStats({
      fuelLevelLiters: latestFuelLog.fuelLevelLiters,
      consumptionRate: latestFuelLog.consumptionRate || undefined,
      runtimeHours: latestFuelLog.runtimeHours || undefined,
      efficiencyPercentage: latestFuelLog.efficiencyPercentage || undefined,
    });

    const fuelStatus = getFuelStatus(stats.powerBackupHours);
    const status = fuelStatus === 'critical' ? 'red' : fuelStatus === 'warning' ? 'orange' : 'green';

    return {
      status,
      metrics: [
        createSafeMetric('fuel_level', latestFuelLog.fuelLevelLiters, ' L', status),
        createSafeMetric('backup_hours', stats.powerBackupHours, ' hrs', status),
        createSafeMetric('efficiency', stats.efficiency, '%', 'green'),
      ],
      issueCount: status === 'green' ? 0 : 1,
      lastUpdated: latestFuelLog.recordedAt,
    };
  } catch (error) {
    console.error(`Error calculating electricity status for ${propertyId}:`, error);
    return {
      status: 'red',
      metrics: [createSafeMetric('status', 'Error', '', 'red')],
      issueCount: 1,
      lastUpdated: new Date(),
    };
  }
}

/**
 * Calculate internet/uptime status for a property
 */
export async function calculateInternetStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    // Get recent uptime reports (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const uptimeReports = await prisma.uptimeReport.findMany({
      where: {
        propertyId,
        date: { gte: sevenDaysAgo },
      },
      orderBy: { date: 'desc' },
    });

    if (uptimeReports.length === 0) {
      return {
        status: 'green',
        metrics: [
          createSafeMetric('connection', 'Stable', '', 'green'),
          createSafeMetric('uptime', 99.9, '%', 'green'),
        ],
        issueCount: 0,
        lastUpdated: new Date(),
      };
    }

    // Calculate average uptime
    const totalReports = uptimeReports.length;
    const averageUptime = uptimeReports.reduce((sum, report) => {
      const uptime = report.uptimePercentage ? parseFloat(report.uptimePercentage.toString()) : 100;
      return sum + uptime;
    }, 0) / totalReports;

    const totalDowntime = uptimeReports.reduce((sum, report) => sum + (report.downtimeMinutes || 0), 0);
    const totalIncidents = uptimeReports.reduce((sum, report) => sum + (report.incidentsCount || 0), 0);

    // Determine status based on uptime
    let status: 'green' | 'orange' | 'red' = 'green';
    if (averageUptime < 97) status = 'red';
    else if (averageUptime < 99) status = 'orange';

    return {
      status,
      metrics: [
        createSafeMetric('weekly_uptime', averageUptime, '%', status),
        createSafeMetric('total_downtime', totalDowntime, ' mins', totalDowntime > 60 ? 'orange' : 'green'),
        createSafeMetric('incidents', totalIncidents, ' events', totalIncidents > 2 ? 'orange' : 'green'),
      ],
      issueCount: status === 'green' ? 0 : 1,
      lastUpdated: uptimeReports[0].createdAt,
    };
  } catch (error) {
    console.error(`Error calculating internet status for ${propertyId}:`, error);
    return {
      status: 'green',
      metrics: [createSafeMetric('connection', 'Unknown', '', 'green')],
      issueCount: 0,
      lastUpdated: new Date(),
    };
  }
}

/**
 * Calculate maintenance status for a property
 */
export async function calculateMaintenanceStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    const issues = await prisma.maintenanceIssue.findMany({
      where: { propertyId },
      select: { status: true, priority: true },
    });

    if (issues.length === 0) {
      return {
        status: 'green',
        metrics: [
          createSafeMetric('open_issues', 0, ' open', 'green'),
          createSafeMetric('total_issues', 0, ' total', 'green'),
        ],
        issueCount: 0,
        lastUpdated: new Date(),
      };
    }

    const openIssues = issues.filter(issue => issue.status === 'OPEN');
    const inProgressIssues = issues.filter(issue => issue.status === 'IN_PROGRESS');
    const criticalIssues = issues.filter(issue => 
      issue.priority === 'CRITICAL' && (issue.status === 'OPEN' || issue.status === 'IN_PROGRESS')
    );
    const highPriorityOpen = issues.filter(issue => 
      issue.priority === 'HIGH' && issue.status === 'OPEN'
    );

    let status: 'green' | 'orange' | 'red' = 'green';
    let issueCount = 0;

    if (criticalIssues.length > 0) {
      status = 'red';
      issueCount = criticalIssues.length;
    } else if (highPriorityOpen.length > 0 || openIssues.length > 3) {
      status = 'orange';
      issueCount = highPriorityOpen.length + Math.max(0, openIssues.length - 3);
    }

    return {
      status,
      metrics: [
        createSafeMetric('open_issues', openIssues.length, ' open', openIssues.length > 0 ? 'red' : 'green'),
        createSafeMetric('in_progress', inProgressIssues.length, ' in progress', inProgressIssues.length > 0 ? 'orange' : 'green'),
        createSafeMetric('critical', criticalIssues.length, ' critical', criticalIssues.length > 0 ? 'red' : 'green'),
        createSafeMetric('total_issues', issues.length, ' total', 'green'),
      ],
      issueCount,
      lastUpdated: new Date(),
    };
  } catch (error) {
    console.error(`Error calculating maintenance status for ${propertyId}:`, error);
    return {
      status: 'green',
      metrics: [createSafeMetric('issues', 'Error', '', 'green')],
      issueCount: 0,
      lastUpdated: new Date(),
    };
  }
}

/**
 * Calculate OTT services status for a property
 */
export async function calculateOttServicesStatus(propertyId: string): Promise<FunctionalAreaStatus> {
  try {
    const ottServices = await prisma.ottService.findMany({
      where: { propertyId },
    });

    if (ottServices.length === 0) {
      return {
        status: 'green',
        metrics: [
          createSafeMetric('services', 0, ' services', 'green'),
        ],
        issueCount: 0,
        lastUpdated: new Date(),
      };
    }

    const activeServices = ottServices.filter(service => service.status === 'ACTIVE');
    let status: 'green' | 'orange' | 'red' = 'green';
    let issueCount = 0;

    const today = new Date();
    for (const service of activeServices) {
      if (service.renewalDate) {
        const daysUntilRenewal = Math.ceil(
          (service.renewalDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysUntilRenewal < 0) {
          status = 'red';
          issueCount++;
        } else if (daysUntilRenewal <= 7 && status !== 'red') {
          status = 'orange';
          issueCount++;
        }
      }
    }

    const expiredServices = ottServices.filter(service => service.status === 'EXPIRED').length;
    if (expiredServices > 0 && status === 'green') {
      status = 'orange';
      issueCount += expiredServices;
    }

    return {
      status,
      metrics: [
        createSafeMetric('active_services', activeServices.length, ' active', 'green'),
        createSafeMetric('total_services', ottServices.length, ' total', 'green'),
        createSafeMetric('payment_status', status === 'green' ? 'Up to date' : 'Due soon', '', status),
        createSafeMetric('expired', expiredServices, ' expired', expiredServices > 0 ? 'orange' : 'green'),
      ],
      issueCount,
      lastUpdated: new Date(),
    };
  } catch (error) {
    console.error(`Error calculating OTT services status for ${propertyId}:`, error);
    return {
      status: 'green',
      metrics: [createSafeMetric('services', 'Error', '', 'green')],
      issueCount: 0,
      lastUpdated: new Date(),
    };
  }
}

/**
 * Generate comprehensive property dashboard status
 */
export async function generatePropertyDashboardStatus(propertyId: string): Promise<PropertyDashboardStatus> {
  // Get property information
  const property = await prisma.property.findUnique({
    where: { id: propertyId },
    select: { name: true, type: true },
  });

  if (!property) {
    throw new Error('Property not found');
  }

  // Calculate all functional area statuses
  const [electricity, internet, maintenance, ottServices] = await Promise.all([
    calculateElectricityStatus(propertyId),
    calculateInternetStatus(propertyId),
    calculateMaintenanceStatus(propertyId),
    calculateOttServicesStatus(propertyId),
  ]);

  // Determine overall status
  const statuses = [electricity.status, internet.status, maintenance.status, ottServices.status];
  let overallStatus: 'green' | 'orange' | 'red' = 'green';

  if (statuses.includes('red')) {
    overallStatus = 'red';
  } else if (statuses.includes('orange')) {
    overallStatus = 'orange';
  }

  return {
    id: propertyId,
    name: property.name,
    type: property.type.toLowerCase() as 'residential' | 'office',
    overallStatus,
    functionalAreas: {
      electricity,
      internet,
      maintenance,
      ott_services: ottServices,
    },
    lastUpdated: new Date(),
  };
}

/**
 * Generate system-wide dashboard metrics
 */
export async function generateSystemDashboardMetrics(): Promise<SystemDashboardMetrics> {
  // Get property statistics
  const totalProperties = await prisma.property.count({
    where: { isActive: true },
  });

  const propertyServiceStats = await prisma.propertyService.groupBy({
    by: ['status'],
    _count: { status: true },
  });

  // Transform property service stats
  const serviceStatusCounts = {
    operational: 0,
    warning: 0,
    critical: 0,
    maintenance: 0,
  };

  propertyServiceStats.forEach(stat => {
    const status = stat.status.toLowerCase();
    if (status in serviceStatusCounts) {
      serviceStatusCounts[status as keyof typeof serviceStatusCounts] = stat._count.status;
    }
  });

  // Get maintenance statistics
  const totalMaintenanceIssues = await prisma.maintenanceIssue.count();
  const maintenanceStats = await prisma.maintenanceIssue.groupBy({
    by: ['status'],
    _count: { status: true },
  });

  const criticalIssues = await prisma.maintenanceIssue.count({
    where: { priority: 'CRITICAL' },
  });

  const maintenanceStatusCounts = {
    open: 0,
    in_progress: 0,
    resolved: 0,
    closed: 0,
  };

  maintenanceStats.forEach(stat => {
    const status = stat.status.toLowerCase();
    if (status in maintenanceStatusCounts) {
      maintenanceStatusCounts[status as keyof typeof maintenanceStatusCounts] = stat._count.status;
    }
  });

  // Get recent alerts
  const recentAlerts = await generateRecentAlerts();

  // Calculate system health score
  const systemHealth = calculateSystemHealthScore(
    serviceStatusCounts,
    maintenanceStatusCounts,
    totalProperties
  );

  return {
    properties: {
      total: totalProperties,
      operational: serviceStatusCounts.operational,
      warning: serviceStatusCounts.warning,
      critical: serviceStatusCounts.critical,
    },
    maintenance_issues: {
      total: totalMaintenanceIssues,
      open: maintenanceStatusCounts.open,
      in_progress: maintenanceStatusCounts.in_progress,
      critical: criticalIssues,
    },
    recent_alerts: recentAlerts,
    system_health: systemHealth,
  };
}

/**
 * Generate recent alerts from various sources
 */
async function generateRecentAlerts(): Promise<Array<{
  id: string;
  type: 'maintenance' | 'service' | 'fuel' | 'payment';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  property_name: string;
  timestamp: Date;
}>> {
  const alerts: any[] = [];

  // Critical maintenance issues
  const criticalIssues = await prisma.maintenanceIssue.findMany({
    where: {
      priority: { in: ['CRITICAL', 'HIGH'] },
      status: { in: ['OPEN', 'IN_PROGRESS'] },
    },
    include: {
      property: { select: { name: true } },
    },
    orderBy: { createdAt: 'desc' },
    take: 5,
  });

  criticalIssues.forEach(issue => {
    alerts.push({
      id: issue.id,
      type: 'maintenance',
      severity: issue.priority === 'CRITICAL' ? 'critical' : 'high',
      message: `${issue.priority.toLowerCase()} maintenance issue: ${issue.title}`,
      property_name: issue.property.name,
      timestamp: issue.createdAt,
    });
  });

  // Low fuel alerts
  const lowFuelProperties = await prisma.generatorFuelLog.findMany({
    where: {
      fuelLevelLiters: { lt: 25 }, // Less than 25 liters
    },
    include: {
      property: { select: { name: true } },
    },
    orderBy: { recordedAt: 'desc' },
    take: 3,
  });

  lowFuelProperties.forEach(fuelLog => {
    alerts.push({
      id: fuelLog.id,
      type: 'fuel',
      severity: fuelLog.fuelLevelLiters < 10 ? 'critical' : 'medium',
      message: `Low fuel level: ${fuelLog.fuelLevelLiters}L remaining`,
      property_name: fuelLog.property.name,
      timestamp: fuelLog.recordedAt,
    });
  });

  // Expired OTT services
  const expiredServices = await prisma.ottService.findMany({
    where: {
      OR: [
        { status: 'EXPIRED' },
        {
          renewalDate: { lt: new Date() },
          status: 'ACTIVE',
        },
      ],
    },
    include: {
      property: { select: { name: true } },
    },
    orderBy: { updatedAt: 'desc' },
    take: 3,
  });

  expiredServices.forEach(service => {
    alerts.push({
      id: service.id,
      type: 'payment',
      severity: 'medium',
      message: `OTT service payment due: ${service.serviceName}`,
      property_name: service.property.name,
      timestamp: service.updatedAt,
    });
  });

  return alerts
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 8);
}

/**
 * Calculate overall system health score
 */
function calculateSystemHealthScore(
  serviceStatusCounts: any,
  maintenanceStatusCounts: any,
  totalProperties: number
): {
  overall_score: number;
  uptime_percentage: number;
  active_services: number;
  total_services: number;
} {
  const totalServices = Object.values(serviceStatusCounts).reduce((sum: number, count: any) => sum + count, 0);
  const activeServices = serviceStatusCounts.operational;
  
  const serviceHealthScore = totalServices > 0 ? (activeServices / totalServices) * 100 : 100;
  const maintenanceHealthScore = totalProperties > 0 
    ? Math.max(0, 100 - (maintenanceStatusCounts.open * 10) - (maintenanceStatusCounts.in_progress * 5))
    : 100;

  const overallScore = (serviceHealthScore + maintenanceHealthScore) / 2;
  const uptimePercentage = Math.max(95, serviceHealthScore); // Assume minimum 95% uptime

  return {
    overall_score: Math.round(overallScore * 100) / 100,
    uptime_percentage: Math.round(uptimePercentage * 100) / 100,
    active_services: activeServices,
    total_services: totalServices,
  };
}
