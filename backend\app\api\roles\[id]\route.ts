import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Jo<PERSON> from 'joi';

const updateRoleSchema = Joi.object({
  name: Joi.string().min(2).max(50).optional(),
  description: Joi.string().max(255).optional(),
  permissions: Joi.array().items(Joi.string().uuid()).optional(),
});

async function getRoleHandler(request: NextRequest, context: { params: { id: string } }, currentUser: any) {
  try {
    const { id } = context.params;

    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
        userRoles: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                fullName: true,
                isActive: true,
              },
            },
          },
        },
      },
    });

    if (!role) {
      return Response.json(
        createApiResponse(null, 'Role not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const transformedRole = {
      id: role.id,
      name: role.name,
      description: role.description,
      is_system_role: role.isSystemRole,
      permissions: role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description,
      })),
      users: role.userRoles.map(ur => ({
        id: ur.user.id,
        email: ur.user.email,
        full_name: ur.user.fullName,
        is_active: ur.user.isActive,
        assigned_at: ur.assignedAt,
      })),
      user_count: role.userRoles.length,
      permission_count: role.rolePermissions.length,
      created_at: role.createdAt,
      updated_at: role.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedRole),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch role');
  }
}

async function updateRoleHandler(request: NextRequest, context: { params: { id: string } }, currentUser: any) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    const validation = validateRequest(updateRoleSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      return Response.json(
        createApiResponse(null, 'Role not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Prevent modification of system roles
    if (existingRole.isSystemRole && currentUser.role !== 'admin') {
      return Response.json(
        createApiResponse(null, 'Cannot modify system roles', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    const { name, description, permissions } = validation.data;

    // Check for name conflicts if name is being updated
    if (name && name !== existingRole.name) {
      const nameConflict = await prisma.role.findUnique({
        where: { name },
      });

      if (nameConflict) {
        return Response.json(
          createApiResponse(null, 'Role with this name already exists', 'DUPLICATE_ROLE'),
          { status: 409 }
        );
      }
    }

    // Update role basic info
    const updateData: any = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;

    const updatedRole = await prisma.role.update({
      where: { id },
      data: updateData,
    });

    // Update permissions if provided
    if (permissions !== undefined) {
      // Remove existing permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: id },
      });

      if (permissions.length > 0) {
        // Verify permissions exist
        const existingPermissions = await prisma.permission.findMany({
          where: {
            id: { in: permissions },
          },
        });

        if (existingPermissions.length !== permissions.length) {
          console.warn('Some permissions not found:', permissions.filter(p => !existingPermissions.find(ep => ep.id === p)));
        }

        // Create new role-permission assignments
        const rolePermissionData = existingPermissions.map(permission => ({
          roleId: id,
          permissionId: permission.id,
        }));

        await prisma.rolePermission.createMany({
          data: rolePermissionData,
        });
      }
    }

    // Fetch updated role with permissions
    const roleWithPermissions = await prisma.role.findUnique({
      where: { id },
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
        userRoles: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                fullName: true,
              },
            },
          },
        },
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Role updated successfully',
        role: {
          id: roleWithPermissions!.id,
          name: roleWithPermissions!.name,
          description: roleWithPermissions!.description,
          is_system_role: roleWithPermissions!.isSystemRole,
          permissions: roleWithPermissions!.rolePermissions.map(rp => ({
            id: rp.permission.id,
            name: rp.permission.name,
            resource: rp.permission.resource,
            action: rp.permission.action,
          })),
          users: roleWithPermissions!.userRoles.map(ur => ({
            id: ur.user.id,
            email: ur.user.email,
            full_name: ur.user.fullName,
          })),
          updated_at: roleWithPermissions!.updatedAt,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update role');
  }
}

async function deleteRoleHandler(request: NextRequest, context: { params: { id: string } }, currentUser: any) {
  try {
    const { id } = context.params;

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        userRoles: true,
      },
    });

    if (!existingRole) {
      return Response.json(
        createApiResponse(null, 'Role not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Prevent deletion of system roles
    if (existingRole.isSystemRole) {
      return Response.json(
        createApiResponse(null, 'Cannot delete system roles', 'FORBIDDEN'),
        { status: 403 }
      );
    }

    // Check if role is assigned to users
    if (existingRole.userRoles.length > 0) {
      return Response.json(
        createApiResponse(null, 'Cannot delete role that is assigned to users', 'ROLE_IN_USE'),
        { status: 409 }
      );
    }

    // Delete role (permissions will be deleted automatically due to cascade)
    await prisma.role.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'Role deleted successfully',
        deleted_role: {
          id: existingRole.id,
          name: existingRole.name,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete role');
  }
}

export const GET = requireAuth(getRoleHandler);
export const PUT = requireRole(['admin'])(updateRoleHandler);
export const DELETE = requireRole(['admin'])(deleteRoleHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
