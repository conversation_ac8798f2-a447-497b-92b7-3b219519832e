"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"

export interface FunctionProcess {
  id: string
  function_name: string
  sub_function: string
  input: string
  process: string
  output: string
  threshold_limits: string
  responsible_agent: string
  created_at: string
  updated_at: string
}

export async function getFunctionProcesses(): Promise<FunctionProcess[]> {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from("function_process_matrix")
      .select("*")
      .order("function_name", { ascending: true })
      .order("sub_function", { ascending: true })

    if (error) {
      console.error("Error fetching function processes:", error)
      throw new Error(error.message)
    }

    return data as FunctionProcess[]
  } catch (error) {
    console.error("Error in getFunctionProcesses:", error)
    throw error
  }
}

export async function getFunctionProcessById(id: string): Promise<FunctionProcess | null> {
  try {
    const supabase = createClient()

    const { data, error } = await supabase.from("function_process_matrix").select("*").eq("id", id).single()

    if (error) {
      console.error("Error fetching function process by ID:", error)
      throw new Error(error.message)
    }

    return data as FunctionProcess
  } catch (error) {
    console.error("Error in getFunctionProcessById:", error)
    throw error
  }
}

export async function createFunctionProcess(
  functionProcess: Omit<FunctionProcess, "id" | "created_at" | "updated_at">,
) {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from("function_process_matrix")
      .insert([
        {
          function_name: functionProcess.function_name,
          sub_function: functionProcess.sub_function,
          input: functionProcess.input,
          process: functionProcess.process,
          output: functionProcess.output,
          threshold_limits: functionProcess.threshold_limits,
          responsible_agent: functionProcess.responsible_agent,
        },
      ])
      .select()

    if (error) {
      console.error("Error creating function process:", error)
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/home/<USER>/maintenance")
    return { success: true, data: data[0] }
  } catch (error) {
    console.error("Error in createFunctionProcess:", error)
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function updateFunctionProcess(
  id: string,
  functionProcess: Omit<FunctionProcess, "id" | "created_at" | "updated_at">,
) {
  try {
    const supabase = createClient()

    const { data, error } = await supabase
      .from("function_process_matrix")
      .update({
        function_name: functionProcess.function_name,
        sub_function: functionProcess.sub_function,
        input: functionProcess.input,
        process: functionProcess.process,
        output: functionProcess.output,
        threshold_limits: functionProcess.threshold_limits,
        responsible_agent: functionProcess.responsible_agent,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select()

    if (error) {
      console.error("Error updating function process:", error)
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/home/<USER>/maintenance")
    return { success: true, data: data[0] }
  } catch (error) {
    console.error("Error in updateFunctionProcess:", error)
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function deleteFunctionProcess(id: string) {
  try {
    const supabase = createClient()

    const { error } = await supabase.from("function_process_matrix").delete().eq("id", id)

    if (error) {
      console.error("Error deleting function process:", error)
      return { success: false, error: error.message }
    }

    revalidatePath("/dashboard/home/<USER>/maintenance")
    return { success: true }
  } catch (error) {
    console.error("Error in deleteFunctionProcess:", error)
    return { success: false, error: "An unexpected error occurred" }
  }
}

export async function exportFunctionProcessesToCSV(): Promise<string> {
  try {
    const functionProcesses = await getFunctionProcesses()

    // Create CSV header
    const header = [
      "Function",
      "Sub-Function",
      "Input",
      "Process",
      "Output",
      "Threshold Limits",
      "Responsible Agent",
    ].join(",")

    // Create CSV rows
    const rows = functionProcesses.map((fp) => {
      return [
        `"${fp.function_name.replace(/"/g, '""')}"`,
        `"${fp.sub_function.replace(/"/g, '""')}"`,
        `"${fp.input.replace(/"/g, '""')}"`,
        `"${fp.process.replace(/"/g, '""')}"`,
        `"${fp.output.replace(/"/g, '""')}"`,
        `"${fp.threshold_limits.replace(/"/g, '""')}"`,
        `"${fp.responsible_agent.replace(/"/g, '""')}"`,
      ].join(",")
    })

    // Combine header and rows
    return [header, ...rows].join("\n")
  } catch (error) {
    console.error("Error in exportFunctionProcessesToCSV:", error)
    throw error
  }
}
