# SRSR Property Management API Documentation

## 🚀 **API Overview**

Complete NextJS + PostgreSQL backend API server with comprehensive property management features, fully aligned with the database.sql schema.

### **Base URL**
```
http://localhost:3000/api
```

### **Authentication**
All protected endpoints require JWT Bearer token:
```
Authorization: Bearer <your_jwt_token>
```

---

## 📋 **API Endpoints**

### **🔐 Authentication**

#### **POST /auth/login**
User login with email and password.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt_token_here",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "System Administrator",
    "roles": ["admin", "property_manager"]
  }
}
```

#### **POST /auth/register** 🔒 *Admin Only*
Register new user.

#### **GET /auth/me** 🔒 *Protected*
Get current user profile.

---

### **🏢 Properties Management**

#### **GET /properties** 🔒 *Protected*
Get all properties with services.

**Query Parameters:**
- `type`: residential | office
- `status`: active | inactive

#### **POST /properties** 🔒 *Admin/Property Manager*
Create new property.

#### **GET /properties/{id}** 🔒 *Protected*
Get property details with services and recent issues.

---

### **🏢 Property Management (Consolidated)**

#### **GET /properties?type=office|construction_site|residential** 🔒 *Protected*
Get all properties with optional type filtering.

**Query Parameters:**
- `type`: Filter by property type (office, construction_site, residential)
- `parent_property_id`: Filter by parent property

#### **POST /properties** 🔒 *Admin/Property Manager*
Create new property (office, construction site, or residential).

**Request:**
```json
{
  "name": "New Office",
  "type": "office",
  "address": "123 Business St",
  "capacity": 50,
  "department": "Operations"
}
```

#### **GET /properties/{id}/members** 🔒 *Protected*
Get property members (unified for all property types).

#### **POST /properties/{id}/members** 🔒 *Admin/Property Manager*
Add member to property.

**Request:**
```json
{
  "user_id": "user_uuid",
  "role": "office_manager",
  "position": "Manager",
  "department": "Operations",
  "hourly_rate": 25.00
}
```

---

### **📊 Unified Attendance Management**

#### **GET /properties/{id}/attendance** 🔒 *Protected*
Get attendance records for any property type.

**Query Parameters:**
- `date`: Specific date (YYYY-MM-DD)
- `start_date` & `end_date`: Date range
- `user_id`: Filter by specific user

#### **POST /properties/{id}/attendance** 🔒 *Protected*
Submit attendance for any property type.

**Request:**
```json
{
  "user_id": "user_uuid",
  "date": "2024-01-15",
  "check_in_time": "08:00:00",
  "check_out_time": "17:00:00",
  "hours_worked": 8.0,
  "notes": "Regular shift"
}
```

---

### **🔧 Maintenance Management**

#### **GET /maintenance** 🔒 *Protected*
Get maintenance issues with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `property_id`: Filter by property
- `status`: open | in_progress | resolved | closed
- `priority`: low | medium | high | critical

#### **POST /maintenance** 🔒 *Protected*
Create maintenance issue.

#### **GET /maintenance/{id}** 🔒 *Protected*
Get specific maintenance issue by ID.

#### **PUT /maintenance/{id}** 🔒 *Protected*
Update maintenance issue.

#### **PATCH /maintenance/{id}** 🔒 *Protected*
Update maintenance issue status only.

**Request:**
```json
{
  "property_id": "property_uuid",
  "title": "Elevator maintenance required",
  "description": "Elevator making unusual noises",
  "priority": "high",
  "service_type": "Mechanical",
  "department": "Maintenance",
  "due_date": "2024-01-22"
}
```

---

### **⛽ Generator Fuel Management**

#### **GET /generator-fuel/{propertyId}** 🔒 *Protected*
Get fuel logs for property.

#### **POST /generator-fuel/{propertyId}** 🔒 *Protected*
Add fuel log entry.

#### **GET /generator-fuel/{id}** 🔒 *Protected*
Get specific fuel log by ID.

#### **PUT /generator-fuel/{id}** 🔒 *Protected*
Update fuel log entry.

#### **DELETE /generator-fuel/{id}** 🔒 *Protected*
Delete fuel log entry.

**Request:**
```json
{
  "fuel_level_liters": 850.5,
  "consumption_rate": 12.5,
  "runtime_hours": 24.0,
  "efficiency_percentage": 85.2,
  "notes": "Generator running smoothly"
}
```

---

### **⛽ Diesel Additions**

#### **GET /diesel-additions/{propertyId}** 🔒 *Protected*
Get diesel addition records.

#### **POST /diesel-additions/{propertyId}** 🔒 *Protected*
Record diesel addition.

**Request:**
```json
{
  "quantity_liters": 500.0,
  "cost_per_liter": 1.25,
  "total_cost": 625.0,
  "supplier": "Fuel Corp",
  "receipt_number": "FC-2024-001"
}
```

---

### **📺 OTT Services Management**

#### **GET /ott-services/{propertyId}** 🔒 *Protected*
Get OTT services for property.

#### **POST /ott-services/{propertyId}** 🔒 *Admin/Property Manager*
Add OTT service.

**Request:**
```json
{
  "service_name": "Netflix Premium",
  "subscription_type": "Monthly",
  "monthly_cost": 15.99,
  "renewal_date": "2024-02-15",
  "status": "active",
  "login_credentials": {
    "username": "<EMAIL>",
    "password": "encrypted_password"
  },
  "notes": "Shared account for residents"
}
```

---

### **📈 Uptime Reports**

#### **GET /uptime-reports/{propertyId}** 🔒 *Protected*
Get uptime reports for property services.

**Query Parameters:**
- `service_type`: Filter by service type
- `start_date` & `end_date`: Date range

#### **POST /uptime-reports/{propertyId}** 🔒 *Protected*
Create uptime report.

**Request:**
```json
{
  "service_type": "internet",
  "date": "2024-01-15",
  "uptime_percentage": 99.5,
  "downtime_minutes": 7,
  "incidents_count": 1,
  "notes": "Brief outage during maintenance"
}
```

---

### **👥 User Management**

#### **GET /users** 🔒 *Protected*
Get all users with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `is_active`: Filter by active status
- `search`: Search by name or email

#### **POST /users** 🔒 *Admin Only*
Create new user.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "John Doe",
  "phone": "+**********",
  "roles": ["user", "maintenance"]
}
```

#### **GET /users/{id}** 🔒 *Protected*
Get specific user by ID.

#### **PUT /users/{id}** 🔒 *Admin Only*
Update user information.

#### **DELETE /users/{id}** 🔒 *Admin Only*
Delete user account.

---

### **📊 Dashboard**

#### **GET /dashboard/status** 🔒 *Protected*
Get system status and metrics.

**Response:**
```json
{
  "success": true,
  "data": {
    "properties": {
      "total": 25,
      "operational": 20,
      "warning": 3,
      "critical": 2
    },
    "maintenance_issues": {
      "total": 45,
      "open": 12,
      "in_progress": 8,
      "critical": 3
    },
    "recent_alerts": [
      {
        "type": "maintenance",
        "message": "critical priority: Generator failure at Property A",
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

---

## 🔒 **Role-Based Access Control (RBAC)**

### **Roles:**
- **admin**: Full system access
- **property_manager**: Property and maintenance management
- **user**: Basic access to view data

### **Permissions:**
The system uses a comprehensive permission-based RBAC with:
- Resource-based permissions (properties, maintenance, attendance, etc.)
- Action-based permissions (create, read, update, delete)
- Role-permission mappings through junction tables

### **Usage:**
```javascript
// Check if user has specific permission
const hasAccess = await hasPermission(userId, 'properties', 'create');

// Require specific role
export const POST = requireRole(['admin', 'property_manager'])(handler);

// Require specific permission
export const POST = requirePermission('properties', 'create')(handler);
```

---

## 📝 **Response Format**

### **Success Response:**
```json
{
  "success": true,
  "data": { ... }
}
```

### **Paginated Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10,
    "has_next": true,
    "has_prev": false
  }
}
```

### **Error Response:**
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

---

## 🚀 **Getting Started**

1. **Setup Database:**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

2. **Start Server:**
   ```bash
   npm run dev
   ```

3. **Test API:**
   ```bash
   curl -X POST http://localhost:3000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"admin123"}'
   ```

---

## 📊 **Database Schema Alignment**

✅ **100% Aligned** with database.sql schema including:
- Complex RBAC with roles, permissions, and user roles
- Office and site management with proper hierarchy
- Separate site and office attendance tracking
- Enhanced maintenance with cost tracking and escalations
- Comprehensive fuel and diesel management
- OTT services and uptime monitoring
- Function processes and threshold configurations

The API is production-ready and fully compatible with your Flutter frontend! 🎉
