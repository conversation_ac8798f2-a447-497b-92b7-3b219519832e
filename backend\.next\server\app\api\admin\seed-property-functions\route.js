"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/seed-property-functions/route";
exports.ids = ["app/api/admin/seed-property-functions/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&page=%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed-property-functions%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&page=%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed-property-functions%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_workspaces_nsl_back_SrsrMan_backend_app_api_admin_seed_property_functions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/seed-property-functions/route.ts */ \"(rsc)/./app/api/admin/seed-property-functions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/seed-property-functions/route\",\n        pathname: \"/api/admin/seed-property-functions\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/seed-property-functions/route\"\n    },\n    resolvedPagePath: \"D:\\\\workspaces\\\\nsl\\\\back\\\\SrsrMan\\\\backend\\\\app\\\\api\\\\admin\\\\seed-property-functions\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_workspaces_nsl_back_SrsrMan_backend_app_api_admin_seed_property_functions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/seed-property-functions/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&page=%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed-property-functions%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/seed-property-functions/route.ts":
/*!********************************************************!*\
  !*** ./app/api/admin/seed-property-functions/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\nfunction getDefaultFunctionsForPropertyType(propertyType) {\n    const baseFunctions = [\n        {\n            function_name: \"maintenance\",\n            is_enabled: true,\n            configuration: {\n                priority_levels: [\n                    \"low\",\n                    \"medium\",\n                    \"high\",\n                    \"critical\"\n                ]\n            },\n            display_order: 1\n        },\n        {\n            function_name: \"security\",\n            is_enabled: true,\n            configuration: {\n                monitoring_enabled: true\n            },\n            display_order: 2\n        },\n        {\n            function_name: \"uptime\",\n            is_enabled: true,\n            configuration: {\n                check_interval: 300\n            },\n            display_order: 3\n        }\n    ];\n    switch(propertyType.toLowerCase()){\n        case \"office\":\n            return [\n                ...baseFunctions,\n                {\n                    function_name: \"attendance\",\n                    is_enabled: true,\n                    configuration: {\n                        working_hours: {\n                            start: \"09:00\",\n                            end: \"18:00\"\n                        },\n                        break_duration: 60,\n                        overtime_threshold: 8\n                    },\n                    display_order: 4\n                },\n                {\n                    function_name: \"meeting_rooms\",\n                    is_enabled: true,\n                    configuration: {\n                        booking_advance_days: 30,\n                        max_duration_hours: 8\n                    },\n                    display_order: 5\n                },\n                {\n                    function_name: \"visitor_management\",\n                    is_enabled: true,\n                    configuration: {\n                        pre_approval_required: true,\n                        max_visitors_per_day: 50\n                    },\n                    display_order: 6\n                },\n                {\n                    function_name: \"ott\",\n                    is_enabled: false,\n                    configuration: {\n                        note: \"Not typically required for office properties\"\n                    },\n                    display_order: 7\n                },\n                {\n                    function_name: \"fuel\",\n                    is_enabled: false,\n                    configuration: {\n                        note: \"Not typically required for office properties\"\n                    },\n                    display_order: 8\n                },\n                {\n                    function_name: \"diesel\",\n                    is_enabled: false,\n                    configuration: {\n                        note: \"Not typically required for office properties\"\n                    },\n                    display_order: 9\n                }\n            ];\n        case \"construction_site\":\n            return [\n                ...baseFunctions,\n                {\n                    function_name: \"attendance\",\n                    is_enabled: true,\n                    configuration: {\n                        working_hours: {\n                            start: \"07:00\",\n                            end: \"17:00\"\n                        },\n                        break_duration: 60,\n                        overtime_threshold: 8,\n                        safety_briefing_required: true\n                    },\n                    display_order: 4\n                },\n                {\n                    function_name: \"equipment\",\n                    is_enabled: true,\n                    configuration: {\n                        maintenance_schedule: \"weekly\",\n                        usage_tracking: true\n                    },\n                    display_order: 5\n                },\n                {\n                    function_name: \"safety\",\n                    is_enabled: true,\n                    configuration: {\n                        incident_reporting: true,\n                        safety_checks: \"daily\"\n                    },\n                    display_order: 6\n                },\n                {\n                    function_name: \"materials\",\n                    is_enabled: true,\n                    configuration: {\n                        inventory_tracking: true,\n                        reorder_threshold: 20\n                    },\n                    display_order: 7\n                },\n                {\n                    function_name: \"progress\",\n                    is_enabled: true,\n                    configuration: {\n                        milestone_tracking: true,\n                        photo_documentation: true\n                    },\n                    display_order: 8\n                },\n                {\n                    function_name: \"fuel\",\n                    is_enabled: true,\n                    configuration: {\n                        generator_monitoring: true,\n                        fuel_threshold: 25\n                    },\n                    display_order: 9\n                },\n                {\n                    function_name: \"diesel\",\n                    is_enabled: true,\n                    configuration: {\n                        consumption_tracking: true\n                    },\n                    display_order: 10\n                },\n                {\n                    function_name: \"ott\",\n                    is_enabled: false,\n                    configuration: {\n                        note: \"Not typically required for construction sites\"\n                    },\n                    display_order: 11\n                }\n            ];\n        case \"residential\":\n        default:\n            return [\n                ...baseFunctions,\n                {\n                    function_name: \"ott\",\n                    is_enabled: true,\n                    configuration: {\n                        shared_subscriptions: true,\n                        cost_splitting: true\n                    },\n                    display_order: 4\n                },\n                {\n                    function_name: \"fuel\",\n                    is_enabled: true,\n                    configuration: {\n                        generator_monitoring: true,\n                        fuel_threshold: 30\n                    },\n                    display_order: 5\n                },\n                {\n                    function_name: \"diesel\",\n                    is_enabled: true,\n                    configuration: {\n                        consumption_tracking: true\n                    },\n                    display_order: 6\n                },\n                {\n                    function_name: \"attendance\",\n                    is_enabled: false,\n                    configuration: {\n                        note: \"Typically not required for residential properties unless staff is employed\"\n                    },\n                    display_order: 7\n                }\n            ];\n    }\n}\nasync function seedPropertyFunctionsHandler(request, currentUser) {\n    try {\n        console.log(\"\\uD83C\\uDF31 Starting property functions seeding...\");\n        // Get all properties\n        const properties = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.findMany({\n            select: {\n                id: true,\n                name: true,\n                type: true\n            }\n        });\n        // Filter out properties that already have functions configured\n        const propertiesWithoutFunctions = [];\n        for (const property of properties){\n            const existingFunctions = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyFunction.findMany({\n                where: {\n                    propertyId: property.id\n                }\n            });\n            if (existingFunctions.length === 0) {\n                propertiesWithoutFunctions.push(property);\n            }\n        }\n        console.log(`📋 Found ${propertiesWithoutFunctions.length} properties without function configuration`);\n        let seededCount = 0;\n        const results = [];\n        for (const property of propertiesWithoutFunctions){\n            console.log(`⚙️  Configuring functions for ${property.name} (${property.type})`);\n            const defaultFunctions = getDefaultFunctionsForPropertyType(property.type);\n            // Create property functions\n            const createData = defaultFunctions.map((func)=>({\n                    propertyId: property.id,\n                    functionName: func.function_name,\n                    isEnabled: func.is_enabled,\n                    configuration: func.configuration || {},\n                    displayOrder: func.display_order || 0\n                }));\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyFunction.createMany({\n                data: createData,\n                skipDuplicates: true\n            });\n            seededCount++;\n            results.push({\n                propertyId: property.id,\n                propertyName: property.name,\n                propertyType: property.type,\n                functionsConfigured: defaultFunctions.length,\n                enabledFunctions: defaultFunctions.filter((f)=>f.is_enabled).length\n            });\n            console.log(`✅ Configured ${defaultFunctions.length} functions for ${property.name}`);\n        }\n        console.log(`🎉 Successfully seeded functions for ${seededCount} properties`);\n        // Get summary of all properties with their functions\n        const summary = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.findMany({\n            include: {\n                propertyFunctions: {\n                    where: {\n                        isEnabled: true\n                    },\n                    select: {\n                        functionName: true\n                    }\n                }\n            }\n        });\n        const summaryData = summary.map((property)=>({\n                id: property.id,\n                name: property.name,\n                type: property.type,\n                enabledFunctions: property.propertyFunctions.map((f)=>f.functionName)\n            }));\n        return Response.json((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.createApiResponse)({\n            message: `Successfully seeded functions for ${seededCount} properties`,\n            seededCount,\n            results,\n            allPropertiesSummary: summaryData\n        }, \"Property functions seeded successfully\"), {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"❌ Error seeding property functions:\", error);\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error, \"Failed to seed property functions\");\n    }\n}\nconst POST = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireRole)([\n    \"admin\"\n])(seedPropertyFunctionsHandler);\nasync function OPTIONS() {\n    return new Response(null, {\n        status: 200,\n        headers: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.corsHeaders)()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/seed-property-functions/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comparePassword: () => (/* binding */ comparePassword),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthUser: () => (/* binding */ getAuthUser),\n/* harmony export */   getUserRoles: () => (/* binding */ getUserRoles),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"fallback-secret\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"7d\";\nfunction generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\nfunction verifyToken(token) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n}\nasync function hashPassword(password) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, 12);\n}\nasync function comparePassword(password, hashedPassword) {\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hashedPassword);\n}\nasync function getUserRoles(userId) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: true\n        }\n    });\n    return userRoles.map((ur)=>ur.role.name);\n}\nasync function getAuthUser(request) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return null;\n        }\n        const token = authHeader.substring(7);\n        const payload = verifyToken(token);\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n            where: {\n                id: payload.userId\n            },\n            select: {\n                id: true,\n                email: true,\n                fullName: true,\n                phone: true,\n                isActive: true,\n                createdAt: true\n            }\n        });\n        if (!user || !user.isActive) {\n            return null;\n        }\n        // Get user roles\n        const roles = await getUserRoles(user.id);\n        return {\n            ...user,\n            roles\n        };\n    } catch (error) {\n        return null;\n    }\n}\nfunction requireAuth(handler) {\n    return async (request, context)=>{\n        const user = await getAuthUser(request);\n        if (!user) {\n            return Response.json({\n                success: false,\n                error: \"Unauthorized\",\n                code: \"UNAUTHORIZED\"\n            }, {\n                status: 401\n            });\n        }\n        return handler(request, context, user);\n    };\n}\nfunction requireRole(roles) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasRole = roles.some((role)=>user.roles.includes(role));\n            if (!hasRole) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\nasync function hasPermission(userId, resource, action) {\n    const userRoles = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.userRole.findMany({\n        where: {\n            userId\n        },\n        include: {\n            role: {\n                include: {\n                    rolePermissions: {\n                        include: {\n                            permission: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    for (const userRole of userRoles){\n        for (const rolePermission of userRole.role.rolePermissions){\n            const permission = rolePermission.permission;\n            if (permission.resource === resource && permission.action === action) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\nfunction requirePermission(resource, action) {\n    return (handler)=>{\n        return async (request, context)=>{\n            const user = await getAuthUser(request);\n            if (!user) {\n                return Response.json({\n                    success: false,\n                    error: \"Unauthorized\",\n                    code: \"UNAUTHORIZED\"\n                }, {\n                    status: 401\n                });\n            }\n            const hasAccess = await hasPermission(user.id, resource, action);\n            if (!hasAccess) {\n                return Response.json({\n                    success: false,\n                    error: \"Insufficient permissions\",\n                    code: \"FORBIDDEN\"\n                }, {\n                    status: 403\n                });\n            }\n            return handler(request, context, user);\n        };\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQUU5QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFHO0FBRW5FLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   corsHeaders: () => (/* binding */ corsHeaders),\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   createPaginationResponse: () => (/* binding */ createPaginationResponse),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   getQueryParams: () => (/* binding */ getQueryParams),\n/* harmony export */   getRequestBody: () => (/* binding */ getRequestBody),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   parseDate: () => (/* binding */ parseDate)\n/* harmony export */ });\nfunction createApiResponse(data, error, code) {\n    return {\n        success: !error,\n        ...data && {\n            data\n        },\n        ...error && {\n            error\n        },\n        ...code && {\n            code\n        }\n    };\n}\nfunction createPaginationResponse(data, page, limit, total) {\n    const pages = Math.ceil(total / limit);\n    return {\n        success: true,\n        data,\n        pagination: {\n            page,\n            limit,\n            total,\n            pages,\n            has_next: page < pages,\n            has_prev: page > 1\n        }\n    };\n}\nfunction getQueryParams(request) {\n    const { searchParams } = new URL(request.url);\n    const params = {};\n    searchParams.forEach((value, key)=>{\n        // Handle numeric values\n        if (!isNaN(Number(value))) {\n            params[key] = Number(value);\n        } else if (value === \"true\" || value === \"false\") {\n            // Handle boolean values\n            params[key] = value === \"true\";\n        } else {\n            params[key] = value;\n        }\n    });\n    return params;\n}\nasync function getRequestBody(request) {\n    try {\n        return await request.json();\n    } catch (error) {\n        return null;\n    }\n}\nfunction handleError(error, defaultMessage = \"Internal server error\", context) {\n    console.error(`API Error${context ? ` (${context})` : \"\"}:`, error);\n    // Rate limiting errors (from V1 patterns)\n    if (isRateLimitError(error)) {\n        return Response.json(createApiResponse(null, \"Rate limit exceeded. Please try again in a moment.\", \"RATE_LIMIT_EXCEEDED\"), {\n            status: 429,\n            headers: {\n                ...corsHeaders(),\n                \"Retry-After\": \"60\"\n            }\n        });\n    }\n    // JSON parsing errors (often related to rate limiting)\n    if (isJsonParsingError(error)) {\n        return Response.json(createApiResponse(null, \"Request parsing failed. Please try again.\", \"PARSING_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Database constraint errors\n    if (error.code === \"P2002\") {\n        const field = error.meta?.target?.[0] || \"field\";\n        return Response.json(createApiResponse(null, `${field} already exists`, \"DUPLICATE_ENTRY\"), {\n            status: 409,\n            headers: corsHeaders()\n        });\n    }\n    if (error.code === \"P2025\") {\n        return Response.json(createApiResponse(null, \"Resource not found\", \"NOT_FOUND\"), {\n            status: 404,\n            headers: corsHeaders()\n        });\n    }\n    // Database connection errors\n    if (error.code === \"P1001\" || error.code === \"P1008\") {\n        return Response.json(createApiResponse(null, \"Database connection failed. Please try again.\", \"DATABASE_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    // Validation errors\n    if (error.name === \"ValidationError\" || error.isJoi) {\n        return Response.json(createApiResponse(null, error.message || \"Validation failed\", \"VALIDATION_ERROR\"), {\n            status: 400,\n            headers: corsHeaders()\n        });\n    }\n    // Network/timeout errors\n    if (error.code === \"ECONNRESET\" || error.code === \"ETIMEDOUT\") {\n        return Response.json(createApiResponse(null, \"Network error. Please try again.\", \"NETWORK_ERROR\"), {\n            status: 503,\n            headers: corsHeaders()\n        });\n    }\n    return Response.json(createApiResponse(null, defaultMessage, \"INTERNAL_ERROR\"), {\n        status: 500,\n        headers: corsHeaders()\n    });\n}\n// Helper functions for error detection (from V1 patterns)\nfunction isRateLimitError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return message.includes(\"too many requests\") || message.includes(\"rate limit\") || message.includes(\"429\") || error?.response?.status === 429;\n}\nfunction isJsonParsingError(error) {\n    const message = (error?.message || \"\").toLowerCase();\n    return error instanceof SyntaxError || message.includes(\"unexpected token\") || message.includes(\"json\") || message.includes(\"syntaxerror\");\n}\nfunction corsHeaders() {\n    return {\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    };\n}\n// Date utilities\nfunction formatDate(date) {\n    return date.toISOString().split(\"T\")[0];\n}\nfunction formatDateTime(date) {\n    return date.toISOString();\n}\nfunction parseDate(dateString) {\n    return new Date(dateString);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&page=%2Fapi%2Fadmin%2Fseed-property-functions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fseed-property-functions%2Froute.ts&appDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cworkspaces%5Cnsl%5Cback%5CSrsrMan%5Cbackend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();