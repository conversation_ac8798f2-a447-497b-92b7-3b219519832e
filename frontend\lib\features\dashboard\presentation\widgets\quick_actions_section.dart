import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/navigation/intelligent_navigation.dart';

class QuickActionsSection extends ConsumerWidget {
  const QuickActionsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildQuickAction(
                  context,
                  ref,
                  'Add Property',
                  Icons.add_business,
                  Colors.blue,
                  '/properties/add',
                  'properties.create',
                ),
                _buildQuickAction(
                  context,
                  ref,
                  'Report Issue',
                  Icons.report_problem,
                  Colors.orange,
                  '/maintenance/add',
                  'maintenance.create',
                ),
                _buildQuickAction(
                  context,
                  ref,
                  'Check Attendance',
                  Icons.people,
                  Colors.green,
                  '/attendance',
                  'attendance.read',
                ),
                _buildQuickAction(
                  context,
                  ref,
                  'Fuel Log',
                  Icons.local_gas_station,
                  Colors.purple,
                  '/fuel/add',
                  'fuel.create',
                ),
                _buildQuickAction(
                  context,
                  ref,
                  'User Management',
                  Icons.manage_accounts,
                  Colors.red,
                  '/admin/users',
                  'users.manage',
                ),
                _buildQuickAction(
                  context,
                  ref,
                  'Reports',
                  Icons.analytics,
                  Colors.teal,
                  '/reports',
                  'reports.read',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context,
    WidgetRef ref,
    String title,
    IconData icon,
    Color color,
    String route,
    String permission,
  ) {
    return PermissionWidget(
      permission: permission,
      child: InkWell(
        onTap: () => _handleQuickAction(context, ref, route),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: color.withValues(alpha:0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha:0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color.withValues(alpha:0.8),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleQuickAction(
    BuildContext context,
    WidgetRef ref,
    String route,
  ) async {
    try {
      final success = await IntelligentNavigator.navigateIfAuthorized(
        context,
        ref,
        route,
        showUnauthorizedMessage: true,
      );
      
      if (!success) {
        // Handle navigation failure if needed
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Navigation error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Alternative implementation using GoRouter directly
class QuickActionButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color color;
  final String route;
  final VoidCallback? onTap;

  const QuickActionButton({
    super.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.route,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ?? () => context.go(route),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: color.withValues(alpha:0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha:0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color.withValues(alpha:0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
