"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"

export type GeneratorFuelUpdate = {
  id?: number
  property_id: string
  date: string
  starting_reading: number
  ending_reading: number
  fuel_in_generator_percentage: number
  fuel_in_tank_liters: number
}

export async function createGeneratorFuelUpdate(formData: FormData) {
  const supabase = createServerClient()

  const update: GeneratorFuelUpdate = {
    property_id: formData.get("property_id") as string,
    date: formData.get("date") as string,
    starting_reading: Number.parseFloat(formData.get("starting_reading") as string),
    ending_reading: Number.parseFloat(formData.get("ending_reading") as string),
    fuel_in_generator_percentage: Number.parseInt(formData.get("fuel_in_generator_percentage") as string),
    fuel_in_tank_liters: Number.parseFloat(formData.get("fuel_in_tank_liters") as string),
  }

  const { data, error } = await supabase.from("generator_fuel_updates").insert(update).select()

  if (error) {
    return { success: false, error: error.message }
  }

  revalidatePath(`/dashboard/home/<USER>/electricity`)
  return { success: true, data }
}

export async function getGeneratorFuelUpdates(propertyId: string, limit = 5) {
  const supabase = createServerClient()

  const { data, error } = await supabase
    .from("generator_fuel_updates")
    .select("*")
    .eq("property_id", propertyId)
    .order("date", { ascending: false })
    .limit(limit)

  if (error) {
    console.error("Error fetching generator fuel updates:", error)
    return []
  }

  return data
}

export async function getLatestGeneratorFuelUpdate(propertyId: string) {
  const supabase = createServerClient()

  const { data, error } = await supabase
    .from("generator_fuel_updates")
    .select("*")
    .eq("property_id", propertyId)
    .order("date", { ascending: false })
    .limit(1)
    .single()

  if (error) {
    console.error("Error fetching latest generator fuel update:", error)
    return null
  }

  return data
}

// Calculate total fuel and power backup hours
export async function calculateGeneratorStats(update: GeneratorFuelUpdate) {
  if (!update) {
    return {
      fuelInGenerator: 0,
      totalFuel: 0,
      powerBackupHours: 0,
    }
  }

  // Generator capacity is 100 liters
  const generatorCapacity = 100

  // Calculate fuel in generator (liters)
  const fuelInGenerator = update.fuel_in_generator_percentage
    ? (generatorCapacity * update.fuel_in_generator_percentage) / 100
    : 0

  // Calculate total fuel available
  const fuelInTank = update.fuel_in_tank_liters || 0
  const totalFuel = fuelInGenerator + fuelInTank

  // Average fuel consumption is 6.5 liters per hour
  const fuelConsumptionRate = 6.5

  // Calculate power backup hours
  const powerBackupHours = totalFuel > 0 ? Math.round((totalFuel / fuelConsumptionRate) * 10) / 10 : 0

  return {
    fuelInGenerator,
    totalFuel,
    powerBackupHours,
  }
}

export async function updateGeneratorFuelEntry(id: number, updates: Partial<GeneratorFuelUpdate>) {
  const supabase = createServerClient()

  const { data, error } = await supabase.from("generator_fuel_updates").update(updates).eq("id", id).select()

  if (error) {
    console.error("Error updating generator fuel update:", error)
    return { success: false, error: error.message }
  }

  if (data && data.length > 0) {
    revalidatePath(`/dashboard/home/<USER>/electricity`)
  }

  return { success: true, data }
}

export async function deleteGeneratorFuelEntry(id: number) {
  const supabase = createServerClient()

  // Get the property_id before deleting the record
  const { data: fuelUpdate, error: fetchError } = await supabase
    .from("generator_fuel_updates")
    .select("property_id")
    .eq("id", id)
    .single()

  if (fetchError) {
    console.error("Error fetching generator fuel update:", fetchError)
    return { success: false, error: fetchError.message }
  }

  const { error } = await supabase.from("generator_fuel_updates").delete().eq("id", id)

  if (error) {
    console.error("Error deleting generator fuel update:", error)
    return { success: false, error: error.message }
  }

  if (fuelUpdate?.property_id) {
    revalidatePath(`/dashboard/home/<USER>/electricity`)
  }

  return { success: true }
}
