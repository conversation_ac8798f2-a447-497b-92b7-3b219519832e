"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, Trash2, Loader2, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  addOfficeMember,
  updateOfficeMember,
  deleteOfficeMember,
  type OfficeMember,
} from "@/app/actions/office-management"
import { getDefaultDutyTime, OFFICE_ROLES } from "@/lib/office-defaults"

interface OfficeMembersManagementProps {
  officeId: string
  officeName: string
  initialMembers: OfficeMember[]
}

export function OfficeMembersManagement({ officeId, officeName, initialMembers }: OfficeMembersManagementProps) {
  const [members, setMembers] = useState<OfficeMember[]>(initialMembers)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formError, setFormError] = useState("")
  const [memberToEdit, setMemberToEdit] = useState<OfficeMember | null>(null)
  const defaultDutyTime = getDefaultDutyTime(officeId)

  const [newMember, setNewMember] = useState({
    name: "",
    mobile_number: "",
    role: "",
    duty_time: defaultDutyTime,
    remarks: "",
  })
  const { toast } = useToast()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setNewMember((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setNewMember((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const resetForm = () => {
    setNewMember({
      name: "",
      mobile_number: "",
      role: "",
      duty_time: defaultDutyTime,
      remarks: "",
    })
    setFormError("")
  }

  const handleAddMember = async () => {
    setFormError("")
    setIsSubmitting(true)

    // Validate form
    if (!newMember.name) {
      setFormError("Name is required")
      setIsSubmitting(false)
      return
    }

    try {
      const result = await addOfficeMember({
        office_location: officeId,
        name: newMember.name,
        mobile_number: newMember.mobile_number || "",
        role: newMember.role || "",
        duty_time: newMember.duty_time || defaultDutyTime,
        remarks: newMember.remarks || "",
      })

      if (result.success && result.data) {
        setMembers((prev) => [...prev, result.data as OfficeMember])
        resetForm()

        toast({
          title: "Member Added",
          description: `${newMember.name} has been added to ${officeName}.`,
        })

        setIsAddDialogOpen(false)
      } else {
        setFormError(result.error || "Failed to add member")
      }
    } catch (error) {
      setFormError((error as Error).message || "An error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEditMember = async () => {
    if (!memberToEdit) return

    setFormError("")
    setIsSubmitting(true)

    // Validate form
    if (!memberToEdit.name) {
      setFormError("Name is required")
      setIsSubmitting(false)
      return
    }

    try {
      const result = await updateOfficeMember({
        ...memberToEdit,
        office_location: officeId,
      })

      if (result.success) {
        setMembers((prev) => prev.map((member) => (member.id === memberToEdit.id ? memberToEdit : member)))

        toast({
          title: "Member Updated",
          description: `${memberToEdit.name} has been updated.`,
        })

        setIsEditDialogOpen(false)
        setMemberToEdit(null)
      } else {
        setFormError(result.error || "Failed to update member")
      }
    } catch (error) {
      setFormError((error as Error).message || "An error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleRemoveMember = async (id: string, name: string) => {
    try {
      const result = await deleteOfficeMember(id, officeId)

      if (result.success) {
        setMembers((prev) => prev.filter((member) => member.id !== id))

        toast({
          title: "Member Removed",
          description: `${name} has been removed from ${officeName}.`,
        })
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.error || "Failed to remove member",
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "An error occurred",
      })
    }
  }

  const openEditDialog = (member: OfficeMember) => {
    setMemberToEdit({ ...member })
    setIsEditDialogOpen(true)
  }

  const openAddDialog = () => {
    resetForm()
    setIsAddDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{officeName} Members</h3>
          <p className="text-sm text-muted-foreground">Manage members for this office</p>
        </div>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openAddDialog}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Member
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Member</DialogTitle>
              <DialogDescription>
                Add a new member to {officeName}. They will be included in the attendance records.
              </DialogDescription>
            </DialogHeader>

            {formError && (
              <Alert variant="destructive">
                <AlertDescription>{formError}</AlertDescription>
              </Alert>
            )}

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={newMember.name}
                  onChange={handleInputChange}
                  placeholder="Enter full name"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="mobile_number">Mobile Number</Label>
                <Input
                  id="mobile_number"
                  name="mobile_number"
                  value={newMember.mobile_number}
                  onChange={handleInputChange}
                  placeholder="Enter mobile number"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Select value={newMember.role} onValueChange={(value) => handleSelectChange("role", value)}>
                  <SelectTrigger id="role" className="w-full">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {OFFICE_ROLES.map((role) => (
                      <SelectItem key={role} value={role}>
                        {role}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="duty_time">Duty Time</Label>
                <Input
                  id="duty_time"
                  name="duty_time"
                  value={newMember.duty_time}
                  onChange={handleInputChange}
                  placeholder={defaultDutyTime}
                />
                <p className="text-xs text-muted-foreground">Default: {defaultDutyTime}</p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="remarks">Remarks</Label>
                <Textarea
                  id="remarks"
                  name="remarks"
                  value={newMember.remarks}
                  onChange={handleInputChange}
                  placeholder="Any additional information"
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddMember} disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  "Add Member"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Mobile Number</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Duty Time</TableHead>
              <TableHead>Remarks</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.length > 0 ? (
              members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">{member.name}</TableCell>
                  <TableCell>{member.mobile_number}</TableCell>
                  <TableCell>{member.role || "N/A"}</TableCell>
                  <TableCell>{member.duty_time || defaultDutyTime}</TableCell>
                  <TableCell>
                    <div className="max-w-[200px] truncate" title={member.remarks || ""}>
                      {member.remarks || "N/A"}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(member)}
                        className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveMember(member.id, member.name)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No members found for this office
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Member Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Member</DialogTitle>
            <DialogDescription>Update member information for {officeName}.</DialogDescription>
          </DialogHeader>

          {formError && (
            <Alert variant="destructive">
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}

          {memberToEdit && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Full Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={memberToEdit.name}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, name: e.target.value })}
                  placeholder="Enter full name"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-mobile">Mobile Number</Label>
                <Input
                  id="edit-mobile"
                  name="mobile_number"
                  value={memberToEdit.mobile_number}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, mobile_number: e.target.value })}
                  placeholder="Enter mobile number"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-role">Role</Label>
                <Select
                  value={memberToEdit.role || ""}
                  onValueChange={(value) => setMemberToEdit({ ...memberToEdit, role: value })}
                >
                  <SelectTrigger id="edit-role" className="w-full">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {OFFICE_ROLES.map((role) => (
                      <SelectItem key={role} value={role}>
                        {role}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-dutyTime">Duty Time</Label>
                <Input
                  id="edit-dutyTime"
                  name="duty_time"
                  value={memberToEdit.duty_time || defaultDutyTime}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, duty_time: e.target.value })}
                  placeholder={defaultDutyTime}
                />
                <p className="text-xs text-muted-foreground">Default: {defaultDutyTime}</p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="edit-remarks">Remarks</Label>
                <Textarea
                  id="edit-remarks"
                  name="remarks"
                  value={memberToEdit.remarks || ""}
                  onChange={(e) => setMemberToEdit({ ...memberToEdit, remarks: e.target.value })}
                  placeholder="Any additional information"
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditMember} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Member"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
