import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../features/auth/presentation/providers/auth_providers.dart';

/// Widget that conditionally shows content based on user permissions
class PermissionWidget extends ConsumerWidget {
  final String permission;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackForUnauthenticated;

  const PermissionWidget({
    super.key,
    required this.permission,
    required this.child,
    this.fallback,
    this.showFallbackForUnauthenticated = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);

    return currentUser.when(
      data: (user) {
        if (user == null) {
          return showFallbackForUnauthenticated
              ? (fallback ?? const SizedBox.shrink())
              : const SizedBox.shrink();
        }

        if (user.hasPermission(permission)) {
          return child;
        }

        return fallback ?? const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}

/// Widget that conditionally shows content based on user roles
class RoleWidget extends ConsumerWidget {
  final List<String> allowedRoles;
  final Widget child;
  final Widget? fallback;

  const RoleWidget({
    super.key,
    required this.allowedRoles,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);

    return currentUser.when(
      data: (user) {
        if (user == null) return const SizedBox.shrink();

        // Check if user has any of the allowed roles
        bool hasAccess = false;

        // First check using the user's role detection logic
        if (allowedRoles.contains('admin') && user.isAdmin) {
          hasAccess = true;
        } else if (allowedRoles.contains('manager') && user.isManager) {
          hasAccess = true;
        } else if (allowedRoles.contains('security') && user.isSecurity) {
          hasAccess = true;
        } else if (allowedRoles.contains('maintenance') && user.isMaintenance) {
          hasAccess = true;
        } else if (allowedRoles.contains('viewer') && user.isViewer) {
          hasAccess = true;
        }

        // Fallback: check primaryRole if available
        if (!hasAccess && user.primaryRole != null) {
          final userRole = user.primaryRole!.toLowerCase();
          hasAccess = allowedRoles.contains(userRole);
        }

        // Fallback: check roles array
        if (!hasAccess && user.roles.isNotEmpty) {
          hasAccess = user.roles.any((role) => allowedRoles.contains(role.toLowerCase()));
        }

        if (hasAccess) {
          return child;
        }

        return fallback ?? const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}

// RoleBasedWidget has been moved to role_based_widget.dart to avoid duplication
// This file now contains the legacy role-specific widget implementation
// For new development, use the RoleBasedWidget from role_based_widget.dart

/// Widget that shows different content based on user role (Legacy implementation)
/// Use RoleBasedWidget from role_based_widget.dart for new development
class LegacyRoleBasedWidget extends ConsumerWidget {
  final Widget? adminWidget;
  final Widget? managerWidget;
  final Widget? securityWidget;
  final Widget? maintenanceWidget;
  final Widget? viewerWidget;
  final Widget? defaultWidget;

  const LegacyRoleBasedWidget({
    super.key,
    this.adminWidget,
    this.managerWidget,
    this.securityWidget,
    this.maintenanceWidget,
    this.viewerWidget,
    this.defaultWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);

    return currentUser.when(
      data: (user) {
        if (user == null) return defaultWidget ?? const SizedBox.shrink();

        // Use the user's role detection logic instead of primaryRole
        if (user.isAdmin) {
          return adminWidget ?? defaultWidget ?? const SizedBox.shrink();
        } else if (user.isManager) {
          return managerWidget ?? defaultWidget ?? const SizedBox.shrink();
        } else if (user.isSecurity) {
          return securityWidget ?? defaultWidget ?? const SizedBox.shrink();
        } else if (user.isMaintenance) {
          return maintenanceWidget ?? defaultWidget ?? const SizedBox.shrink();
        } else if (user.isViewer) {
          return viewerWidget ?? defaultWidget ?? const SizedBox.shrink();
        }

        // Fallback: check primaryRole if available
        if (user.primaryRole != null) {
          switch (user.primaryRole!.toLowerCase()) {
            case 'admin':
              return adminWidget ?? defaultWidget ?? const SizedBox.shrink();
            case 'manager':
              return managerWidget ?? defaultWidget ?? const SizedBox.shrink();
            case 'security':
              return securityWidget ?? defaultWidget ?? const SizedBox.shrink();
            case 'maintenance':
              return maintenanceWidget ?? defaultWidget ?? const SizedBox.shrink();
            case 'viewer':
              return viewerWidget ?? defaultWidget ?? const SizedBox.shrink();
          }
        }

        return defaultWidget ?? const SizedBox.shrink();
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => defaultWidget ?? const SizedBox.shrink(),
    );
  }
}

// RoleBasedAppBar has been moved to role_based_widget.dart to avoid duplication

/// Bottom navigation bar with role-based items
class RoleBasedBottomNavBar extends ConsumerWidget {
  final int currentIndex;
  final Function(int) onTap;

  const RoleBasedBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);

    return currentUser.when(
      data: (user) {
        if (user == null) {
          return const SizedBox.shrink();
        }

        final items = user.getBottomNavItems();
        final roleColor = user.roleColor;

        // Safety check: BottomNavigationBar requires at least 2 items
        if (items.length < 2) {
          return const SizedBox.shrink();
        }

        return BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: currentIndex < items.length ? currentIndex : 0,
          onTap: onTap,
          items: items,
          selectedItemColor: roleColor,
          unselectedItemColor: Colors.grey,
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }
}

// RoleBasedFAB has been moved to role_based_widget.dart to avoid duplication

/// Helper function to check permissions
bool hasPermission(WidgetRef ref, String permission) {
  final currentUser = ref.read(currentUserProvider);
  return currentUser.when(
    data: (user) => user?.hasPermission(permission) ?? false,
    loading: () => false,
    error: (_, __) => false,
  );
}

/// Helper function to check if user has any of the specified roles
bool hasAnyRole(WidgetRef ref, List<String> roles) {
  final currentUser = ref.read(currentUserProvider);
  return currentUser.when(
    data: (user) {
      if (user == null) return false;

      // Check using role detection logic
      if (roles.contains('admin') && user.isAdmin) return true;
      if (roles.contains('manager') && user.isManager) return true;
      if (roles.contains('security') && user.isSecurity) return true;
      if (roles.contains('maintenance') && user.isMaintenance) return true;
      if (roles.contains('viewer') && user.isViewer) return true;

      // Fallback: check primaryRole
      if (user.primaryRole != null) {
        return roles.contains(user.primaryRole!.toLowerCase());
      }

      // Fallback: check roles array
      return user.roles.any((role) => roles.contains(role.toLowerCase()));
    },
    loading: () => false,
    error: (_, __) => false,
  );
}

/// Helper function to get current user role
String? getCurrentUserRole(WidgetRef ref) {
  final currentUser = ref.read(currentUserProvider);
  return currentUser.when(
    data: (user) {
      if (user == null) return null;

      // Return the detected role
      if (user.isAdmin) return 'admin';
      if (user.isManager) return 'manager';
      if (user.isSecurity) return 'security';
      if (user.isMaintenance) return 'maintenance';
      if (user.isViewer) return 'viewer';

      // Fallback to primaryRole
      return user.primaryRole;
    },
    loading: () => null,
    error: (_, __) => null,
  );
}
