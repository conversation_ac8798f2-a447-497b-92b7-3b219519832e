import 'package:flutter/material.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/utils/app_utils.dart';

/// Critical alerts panel that prominently displays urgent issues
class CriticalAlertsPanel extends StatelessWidget {
  final List<dynamic> alerts;
  final VoidCallback? onViewAll;
  final bool showActions;

  const CriticalAlertsPanel({
    super.key,
    required this.alerts,
    this.onViewAll,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    if (alerts.isEmpty) {
      return _buildNoAlertsState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: AppConstants.smallPadding),
        _buildAlertsList(context),
        if (showActions && alerts.length > 3) ...[
          const SizedBox(height: AppConstants.smallPadding),
          _buildViewAllButton(context),
        ],
      ],
    );
  }

  Widget _buildNoAlertsState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.largePadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade50,
            Colors.green.shade100,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: Colors.green.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle,
              size: 32,
              color: Colors.green.shade600,
            ),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'All Systems Operational',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green.shade800,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'No critical alerts at this time',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.green.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.priority_high,
            color: Colors.red.shade600,
            size: 20,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Critical Alerts',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade800,
                ),
              ),
              Text(
                '${alerts.length} ${alerts.length == 1 ? 'issue' : 'issues'} requiring immediate attention',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.red.shade600,
                ),
              ),
            ],
          ),
        ),
        if (showActions)
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Colors.red.shade600,
            ),
            onPressed: () {
              // Refresh alerts logic
            },
            tooltip: 'Refresh alerts',
          ),
      ],
    );
  }

  Widget _buildAlertsList(BuildContext context) {
    final displayAlerts = alerts.take(3).toList();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.red.shade200,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: displayAlerts.length,
        separatorBuilder: (context, index) => Divider(
          height: 1,
          color: Colors.red.shade100,
        ),
        itemBuilder: (context, index) {
          final alert = displayAlerts[index];
          return _buildAlertItem(context, alert, index == 0);
        },
      ),
    );
  }

  Widget _buildAlertItem(BuildContext context, dynamic alert, bool isFirst) {
    return Container(
      decoration: BoxDecoration(
        color: isFirst ? Colors.red.shade50 : null,
        borderRadius: isFirst
            ? const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadius),
                topRight: Radius.circular(AppConstants.borderRadius),
              )
            : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.smallPadding,
        ),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getAlertTypeColor(alert.type).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getAlertIcon(alert.type),
            color: _getAlertTypeColor(alert.type),
            size: 20,
          ),
        ),
        title: Text(
          alert.message,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    alert.propertyName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey.shade700,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  AppUtils.formatRelativeTime(alert.timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: Colors.red.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'CRITICAL',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade800,
                ),
              ),
            ),
            if (showActions) ...[
              const SizedBox(height: 4),
              GestureDetector(
                onTap: () => _handleAlertAction(context, alert),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ],
        ),
        onTap: () => _handleAlertTap(context, alert),
      ),
    );
  }

  Widget _buildViewAllButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onViewAll ?? () => _handleViewAll(context),
        icon: Icon(
          Icons.list,
          size: 16,
          color: Colors.red.shade600,
        ),
        label: Text(
          'View All ${alerts.length} Critical Alerts',
          style: TextStyle(
            color: Colors.red.shade600,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: Colors.red.shade300),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  Color _getAlertTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'maintenance':
        return Colors.orange;
      case 'security':
        return Colors.purple;
      case 'fuel':
        return Colors.amber;
      case 'power':
        return Colors.red;
      case 'system':
        return Colors.blue;
      default:
        return Colors.red;
    }
  }

  IconData _getAlertIcon(String type) {
    switch (type.toLowerCase()) {
      case 'maintenance':
        return Icons.build;
      case 'security':
        return Icons.security;
      case 'fuel':
        return Icons.local_gas_station;
      case 'power':
        return Icons.power_off;
      case 'system':
        return Icons.computer;
      default:
        return Icons.warning;
    }
  }

  void _handleAlertTap(BuildContext context, dynamic alert) {
    // Navigate to alert details
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Alert Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Message: ${alert.message}'),
            const SizedBox(height: 8),
            Text('Property: ${alert.propertyName}'),
            const SizedBox(height: 8),
            Text('Type: ${alert.type}'),
            const SizedBox(height: 8),
            Text('Time: ${AppUtils.formatRelativeTime(alert.timestamp)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _handleAlertAction(context, alert);
            },
            child: const Text('Take Action'),
          ),
        ],
      ),
    );
  }

  void _handleAlertAction(BuildContext context, dynamic alert) {
    // Handle alert action based on type
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Action taken for ${alert.type} alert'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleViewAll(BuildContext context) {
    // Navigate to all alerts page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigating to all alerts...'),
      ),
    );
  }
}
