import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../widgets/cameras_tab.dart';
import '../widgets/motors_pumps_tab.dart';
import '../widgets/sensors_tab.dart';
import '../widgets/lighting_tab.dart';
import '../widgets/security_tab.dart';
import '../widgets/climate_tab.dart';
import '../widgets/automation_tab.dart';
import '../widgets/analytics_tab.dart';

/// Comprehensive Device Control Hub - IoT & Automation Center
class DeviceControlHubScreen extends ConsumerStatefulWidget {
  const DeviceControlHubScreen({super.key});

  @override
  ConsumerState<DeviceControlHubScreen> createState() => _DeviceControlHubScreenState();
}

class _DeviceControlHubScreenState extends ConsumerState<DeviceControlHubScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  final List<DeviceControlTab> _tabs = [
    DeviceControlTab(
      title: 'Cameras',
      icon: Icons.videocam,
      color: Colors.purple,
      widget: const CamerasTab(),
    ),
    DeviceControlTab(
      title: 'Motors & Pumps',
      icon: Icons.settings,
      color: Colors.blue,
      widget: const MotorsPumpsTab(),
    ),
    DeviceControlTab(
      title: 'Sensors',
      icon: Icons.sensors,
      color: Colors.green,
      widget: const SensorsTab(),
    ),
    DeviceControlTab(
      title: 'Lighting',
      icon: Icons.lightbulb,
      color: Colors.amber,
      widget: const LightingTab(),
    ),
    DeviceControlTab(
      title: 'Security',
      icon: Icons.security,
      color: Colors.red,
      widget: const SecurityTab(),
    ),
    DeviceControlTab(
      title: 'Climate',
      icon: Icons.thermostat,
      color: Colors.orange,
      widget: const ClimateTab(),
    ),
    DeviceControlTab(
      title: 'Automation',
      icon: Icons.auto_awesome,
      color: Colors.indigo,
      widget: const AutomationTab(),
    ),
    DeviceControlTab(
      title: 'Analytics',
      icon: Icons.analytics,
      color: Colors.teal,
      widget: const AnalyticsTab(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentTabIndex = _tabController.index;
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: _tabs.length,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Device Control Hub'),
          backgroundColor: _tabs[_currentTabIndex].color,
          foregroundColor: Colors.white,
          actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAllDevices,
            tooltip: 'Refresh All Devices',
          ),
          IconButton(
            icon: const Icon(Icons.emergency),
            onPressed: _emergencyStop,
            tooltip: 'Emergency Stop All',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Hub Settings'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_device',
                child: Row(
                  children: [
                    Icon(Icons.add_circle),
                    SizedBox(width: 8),
                    Text('Add Device'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'scan_devices',
                child: Row(
                  children: [
                    Icon(Icons.search),
                    SizedBox(width: 8),
                    Text('Scan for Devices'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_config',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export Config'),
                  ],
                ),
              ),
            ],
          ),
          ],
          bottom: TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: _tabs.map((tab) => Tab(
              icon: Icon(tab.icon, size: 20),
              text: tab.title,
            )).toList(),
          ),
        ),
      body: Column(
        children: [
          _buildStatusBar(context),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: _tabs.map((tab) => tab.widget).toList(),
            ),
          ),
        ],
      ),
        floatingActionButton: _buildFloatingActionButton(context),
        bottomNavigationBar: RoleBasedBottomNavBar(
          currentIndex: 0, // Dashboard index
          onTap: (index) => _handleBottomNavTap(context, index),
        ),
      ),
    );
  }

  Widget _buildStatusBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: _tabs[_currentTabIndex].color.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: _tabs[_currentTabIndex].color.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          _buildStatusItem(
            'Connected',
            '24/26',
            Icons.wifi,
            Colors.green,
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          _buildStatusItem(
            'Active',
            '18',
            Icons.power,
            Colors.blue,
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          _buildStatusItem(
            'Alerts',
            '2',
            Icons.warning,
            Colors.orange,
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.green.shade600,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  'ALL SYSTEMS ONLINE',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, IconData icon, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          '$value $label',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    final currentTab = _tabs[_currentTabIndex];

    return FloatingActionButton.extended(
      onPressed: () => _handleTabSpecificAction(currentTab.title),
      backgroundColor: currentTab.color,
      icon: Icon(currentTab.icon),
      label: Text(_getActionLabel(currentTab.title)),
    );
  }

  String _getActionLabel(String tabTitle) {
    switch (tabTitle) {
      case 'Cameras':
        return 'View All';
      case 'Motors & Pumps':
        return 'Quick Start';
      case 'Sensors':
        return 'Calibrate';
      case 'Lighting':
        return 'Scene Mode';
      case 'Security':
        return 'Arm System';
      case 'Climate':
        return 'Auto Mode';
      case 'Automation':
        return 'New Rule';
      case 'Analytics':
        return 'Generate Report';
      default:
        return 'Action';
    }
  }

  void _refreshAllDevices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Refreshing all devices...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _emergencyStop() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.emergency, color: Colors.red),
            SizedBox(width: 8),
            Text('Emergency Stop'),
          ],
        ),
        content: const Text(
          'This will immediately stop all motors, pumps, and automated systems. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _performEmergencyStop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('EMERGENCY STOP'),
          ),
        ],
      ),
    );
  }

  void _performEmergencyStop() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('EMERGENCY STOP ACTIVATED - All systems halted'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 5),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _showHubSettings();
        break;
      case 'add_device':
        _showAddDeviceDialog();
        break;
      case 'scan_devices':
        _scanForDevices();
        break;
      case 'export_config':
        _exportConfiguration();
        break;
    }
  }

  void _showHubSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hub Settings'),
        content: const Text('Device hub configuration options coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAddDeviceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Device'),
        content: const Text('Device discovery and pairing coming soon...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _scanForDevices() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Scanning for nearby devices...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _exportConfiguration() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exporting device configuration...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleTabSpecificAction(String tabTitle) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_getActionLabel(tabTitle)} action for $tabTitle'),
        backgroundColor: _tabs[_currentTabIndex].color,
      ),
    );
  }

  void _handleBottomNavTap(BuildContext context, int index) {
    final routes = [
      '/dashboard',
      '/properties',
      '/maintenance',
      '/attendance',
      '/admin',
    ];

    if (index < routes.length) {
      Navigator.pop(context); // Go back to dashboard first
    }
  }
}

/// Device control tab data model
class DeviceControlTab {
  final String title;
  final IconData icon;
  final Color color;
  final Widget widget;

  const DeviceControlTab({
    required this.title,
    required this.icon,
    required this.color,
    required this.widget,
  });
}
