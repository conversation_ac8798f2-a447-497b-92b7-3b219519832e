import 'dart:io';
import 'package:http/http.dart' as http;

// Test configuration for SRSR Flutter tests
class TestConfig {
  // API Configuration
  static const String baseUrl = 'http://localhost:3000';
  static const String apiVersion = 'v1';
  static const String apiBaseUrl = '$baseUrl/api';

  // Test Data Configuration
  static const String testDataPrefix = 'test_srsr';
  static const int defaultTimeout = 30000; // 30 seconds
  static const int longTimeout = 60000; // 60 seconds

  // Test User Credentials
  static const Map<String, String> testUsers = {
    'admin': '<EMAIL>',
    'property_manager': '<EMAIL>',
    'maintenance_staff': '<EMAIL>',
    'viewer': '<EMAIL>',
  };

  static const Map<String, String> testPasswords = {
    'admin': 'Admin123!',
    'property_manager': 'Manager123!',
    'maintenance_staff': 'Maintenance123!',
    'viewer': 'Viewer123!',
  };

  // Screen Configuration
  static const List<String> adminScreens = [
    'user_management',
    'role_management',
    'permission_config',
    'screen_management',
    'widget_management',
  ];

  static const List<String> mainScreens = [
    'properties',
    'maintenance',
    'attendance',
    'dashboard',
  ];

  static const List<String> additionalScreens = [
    'profile',
    'settings',
    'reports',
    'security',
    'fuel',
  ];

  // Test Environment Configuration
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableScreenshots = true;

  // Property Types for Testing
  static const List<String> propertyTypes = [
    'residential',
    'office',
    'construction',
  ];

  // Maintenance Issue Types
  static const List<String> maintenanceTypes = [
    'hvac',
    'plumbing',
    'electrical',
    'general',
  ];

  // Priority Levels
  static const List<String> priorityLevels = [
    'high',
    'medium',
    'low',
  ];

  // Attendance Status Types
  static const List<String> attendanceStatuses = [
    'present',
    'absent',
    'late',
  ];

  // Test Data Cleanup Configuration
  static const bool cleanupAfterTests = true;
  static const bool preserveTestData = false;

  // Helper Methods
  static String getTestUserEmail(String role) {
    return testUsers[role] ?? '<EMAIL>';
  }

  static String getTestUserPassword(String role) {
    return testPasswords[role] ?? 'Test123!';
  }

  static List<String> getAllScreens() {
    return [...adminScreens, ...mainScreens, ...additionalScreens];
  }

  static bool isAdminScreen(String screen) {
    return adminScreens.contains(screen);
  }

  static bool isMainScreen(String screen) {
    return mainScreens.contains(screen);
  }

  static bool isAdditionalScreen(String screen) {
    return additionalScreens.contains(screen);
  }

  // Connectivity Methods
  static Future<bool> checkBackendConnectivity() async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(Duration(milliseconds: defaultTimeout));

      return response.statusCode == 200;
    } catch (e) {
      print('Backend connectivity check failed: $e');
      return false;
    }
  }

  static Future<bool> checkEndpoint(String endpoint) async {
    try {
      final url = endpoint.startsWith('/') ? '$baseUrl$endpoint' : '$apiBaseUrl/$endpoint';
      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      ).timeout(Duration(milliseconds: defaultTimeout));

      // Consider 200, 401, 403 as accessible (401/403 means endpoint exists but requires auth)
      return [200, 401, 403].contains(response.statusCode);
    } catch (e) {
      print('Endpoint check failed for $endpoint: $e');
      return false;
    }
  }
}
