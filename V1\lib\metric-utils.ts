// Utility functions for safely handling and formatting metric values

export type MetricValue = number | boolean | string

export interface SafeMetric {
  name: string
  value: MetricValue
  unit: string
  status: "green" | "orange" | "red"
  displayValue: string // Pre-formatted safe display value
}

/**
 * Safely formats a metric value for display
 */
export function formatMetricValue(value: MetricValue, unit: string): string {
  if (typeof value === "boolean") {
    return value ? "On" : "Off"
  }

  if (typeof value === "number") {
    if (isNaN(value) || !isFinite(value)) {
      return "N/A"
    }

    // Only use toFixed for percentage and decimal values that need it
    if (unit === "%" && value % 1 !== 0) {
      return `${value.toFixed(1)}${unit}`
    }

    // For other numbers, show as integer if it's a whole number
    if (value % 1 === 0) {
      return `${Math.round(value)}${unit}`
    }

    // For decimals, limit to 2 decimal places
    return `${value.toFixed(2)}${unit}`
  }

  if (typeof value === "string") {
    const num = Number.parseFloat(value)
    if (!isNaN(num) && isFinite(num)) {
      if (unit === "%" && num % 1 !== 0) {
        return `${num.toFixed(1)}${unit}`
      }
      if (num % 1 === 0) {
        return `${Math.round(num)}${unit}`
      }
      return `${num.toFixed(2)}${unit}`
    }
    // If it's a string that's not a number, return as-is with unit
    return `${value}${unit}`
  }

  return `${value}${unit}`
}

/**
 * Safely converts any value to a number
 */
export function safeToNumber(value: any): number {
  if (typeof value === "number") {
    return isNaN(value) || !isFinite(value) ? 0 : value
  }

  if (typeof value === "string") {
    const num = Number.parseFloat(value)
    return isNaN(num) || !isFinite(num) ? 0 : num
  }

  if (typeof value === "boolean") {
    return value ? 1 : 0
  }

  return 0
}

/**
 * Creates a safe metric object with pre-formatted display value
 */
export function createSafeMetric(
  name: string,
  value: MetricValue,
  unit: string,
  status: "green" | "orange" | "red",
): SafeMetric {
  return {
    name,
    value,
    unit,
    status,
    displayValue: formatMetricValue(value, unit),
  }
}

/**
 * Ensures a metric value is safe for any operation
 */
export function sanitizeMetricValue(value: any): MetricValue {
  if (typeof value === "boolean") {
    return value
  }

  if (typeof value === "number") {
    return isNaN(value) || !isFinite(value) ? 0 : value
  }

  if (typeof value === "string") {
    // Try to convert to number if it looks like one
    const num = Number.parseFloat(value)
    if (!isNaN(num) && isFinite(num)) {
      return num
    }
    return value
  }

  return String(value)
}
