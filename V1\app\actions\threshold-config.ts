"use server"

export interface ThresholdConfig {
  id?: number
  functional_area: string
  metric_name: string
  green_min?: number
  green_max?: number
  orange_min?: number
  orange_max?: number
  red_min?: number
  red_max?: number
  unit?: string
  description?: string
  property_type: string
  created_at?: string
  updated_at?: string
}

// Static thresholds to avoid database calls during development
const STATIC_THRESHOLDS: ThresholdConfig[] = [
  // Electricity thresholds
  {
    id: 1,
    functional_area: "electricity",
    metric_name: "generator_fuel_percentage",
    green_min: 50,
    green_max: 100,
    orange_min: 20,
    orange_max: 49.99,
    red_min: 0,
    red_max: 19.99,
    unit: "%",
    description: "Generator fuel level percentage",
    property_type: "all",
  },
  {
    id: 2,
    functional_area: "electricity",
    metric_name: "backup_hours",
    green_min: 12,
    green_max: 999,
    orange_min: 6,
    orange_max: 11.99,
    red_min: 0,
    red_max: 5.99,
    unit: "hours",
    description: "Backup power available in hours",
    property_type: "all",
  },
  {
    id: 3,
    functional_area: "electricity",
    metric_name: "power_status",
    green_min: 1,
    green_max: 1,
    orange_min: null,
    orange_max: null,
    red_min: 0,
    red_max: 0,
    unit: "boolean",
    description: "Power status (1=on, 0=off)",
    property_type: "all",
  },
  {
    id: 4,
    functional_area: "electricity",
    metric_name: "fuel_in_tank",
    green_min: 100,
    green_max: 999,
    orange_min: 50,
    orange_max: 99.99,
    red_min: 0,
    red_max: 49.99,
    unit: "liters",
    description: "Fuel in tank",
    property_type: "all",
  },
  // Water thresholds
  {
    id: 5,
    functional_area: "water",
    metric_name: "pressure_level",
    green_min: 80,
    green_max: 100,
    orange_min: 50,
    orange_max: 79.99,
    red_min: 0,
    red_max: 49.99,
    unit: "%",
    description: "Water pressure level",
    property_type: "all",
  },
  {
    id: 6,
    functional_area: "water",
    metric_name: "usage_percentage",
    green_min: 0,
    green_max: 110,
    orange_min: 110.01,
    orange_max: 125,
    red_min: 125.01,
    red_max: 999,
    unit: "%",
    description: "Water usage compared to normal",
    property_type: "all",
  },
  // Security thresholds
  {
    id: 7,
    functional_area: "security",
    metric_name: "active_incidents",
    green_min: 0,
    green_max: 0,
    orange_min: 1,
    orange_max: 2,
    red_min: 3,
    red_max: 999,
    unit: "count",
    description: "Number of active security incidents",
    property_type: "all",
  },
  {
    id: 8,
    functional_area: "security",
    metric_name: "offline_cameras",
    green_min: 0,
    green_max: 0,
    orange_min: 1,
    orange_max: 2,
    red_min: 3,
    red_max: 999,
    unit: "count",
    description: "Number of offline CCTV cameras",
    property_type: "all",
  },
  // Internet thresholds
  {
    id: 9,
    functional_area: "internet",
    metric_name: "connection_status",
    green_min: 1,
    green_max: 1,
    orange_min: null,
    orange_max: null,
    red_min: 0,
    red_max: 0,
    unit: "boolean",
    description: "Internet connection status (1=on, 0=off)",
    property_type: "all",
  },
  {
    id: 10,
    functional_area: "internet",
    metric_name: "speed_percentage",
    green_min: 80,
    green_max: 100,
    orange_min: 50,
    orange_max: 79.99,
    red_min: 0,
    red_max: 49.99,
    unit: "%",
    description: "Internet speed as percentage of expected",
    property_type: "all",
  },
  // OTT thresholds
  {
    id: 11,
    functional_area: "ott",
    metric_name: "days_to_expiry",
    green_min: 8,
    green_max: 999,
    orange_min: 1,
    orange_max: 7,
    red_min: -999,
    red_max: 0,
    unit: "days",
    description: "Days until OTT service payment due",
    property_type: "all",
  },
  {
    id: 12,
    functional_area: "ott",
    metric_name: "active_services",
    green_min: 100,
    green_max: 100,
    orange_min: 80,
    orange_max: 99.99,
    red_min: 0,
    red_max: 79.99,
    unit: "%",
    description: "Percentage of OTT services active",
    property_type: "all",
  },
  // Maintenance thresholds
  {
    id: 13,
    functional_area: "maintenance",
    metric_name: "open_issues",
    green_min: 0,
    green_max: 2,
    orange_min: 3,
    orange_max: 7,
    red_min: 8,
    red_max: 999,
    unit: "count",
    description: "Number of open maintenance issues",
    property_type: "all",
  },
  {
    id: 14,
    functional_area: "maintenance",
    metric_name: "high_priority_issues",
    green_min: 0,
    green_max: 0,
    orange_min: 1,
    orange_max: 2,
    red_min: 3,
    red_max: 999,
    unit: "count",
    description: "Number of high priority maintenance issues",
    property_type: "all",
  },
  // Attendance thresholds
  {
    id: 15,
    functional_area: "attendance",
    metric_name: "attendance_percentage",
    green_min: 90,
    green_max: 100,
    orange_min: 70,
    orange_max: 89.99,
    red_min: 0,
    red_max: 69.99,
    unit: "%",
    description: "Staff attendance percentage",
    property_type: "all",
  },
]

// Use static thresholds during development to avoid rate limits
export async function getThresholdConfigurations(propertyType?: string): Promise<ThresholdConfig[]> {
  console.log("Using static threshold configurations to avoid rate limits")

  if (propertyType && propertyType !== "all") {
    return STATIC_THRESHOLDS.filter((t) => t.property_type === propertyType || t.property_type === "all")
  }

  return STATIC_THRESHOLDS
}

export async function createThresholdConfiguration(config: Omit<ThresholdConfig, "id" | "created_at" | "updated_at">) {
  console.log("Mock: Created threshold configuration", config)
  return { ...config, id: Math.floor(Math.random() * 1000) } as ThresholdConfig
}

export async function updateThresholdConfiguration(id: number, config: Partial<ThresholdConfig>) {
  console.log("Mock: Updated threshold configuration", id, config)
  return { ...config, id } as ThresholdConfig
}

export async function deleteThresholdConfiguration(id: number) {
  console.log("Mock: Deleted threshold configuration", id)
}

// Get thresholds by functional area using static data
export async function getThresholdsByFunctionalArea(
  functionalArea: string,
  propertyType = "all",
): Promise<ThresholdConfig[]> {
  return STATIC_THRESHOLDS.filter(
    (t) => t.functional_area === functionalArea && (t.property_type === propertyType || t.property_type === "all"),
  )
}

export type StatusLevel = "green" | "orange" | "red"

export async function calculateStatus(
  functionalArea: string,
  metricName: string,
  value: number,
  propertyType = "all",
): Promise<StatusLevel> {
  try {
    // Get thresholds for this specific metric using static data
    const thresholds = await getThresholdsByFunctionalArea(functionalArea, propertyType)
    const threshold = thresholds.find((t) => t.metric_name === metricName)

    if (!threshold) {
      console.warn(`No threshold found for ${functionalArea}.${metricName}`)
      return "red" // Default to red if no threshold found
    }

    // Check green range
    if (threshold.green_min !== null && threshold.green_max !== null) {
      if (value >= threshold.green_min && value <= threshold.green_max) {
        return "green"
      }
    }

    // Check orange range
    if (threshold.orange_min !== null && threshold.orange_max !== null) {
      if (value >= threshold.orange_min && value <= threshold.orange_max) {
        return "orange"
      }
    }

    // Check red range
    if (threshold.red_min !== null && threshold.red_max !== null) {
      if (value >= threshold.red_min && value <= threshold.red_max) {
        return "red"
      }
    }

    // Default fallback logic
    return "red"
  } catch (error) {
    console.error("Error calculating status:", error)
    return "red" // Default to red on error
  }
}
