import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const assignRolesSchema = Joi.object({
  role_ids: Joi.array().items(Joi.string().uuid()).min(0).required(),
  replace_existing: Joi.boolean().default(true),
});

async function getUserRolesHandler(request: NextRequest, context: { params: { id: string } }, currentUser: any) {
  try {
    const { id } = context.params;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        fullName: true,
        isActive: true,
      },
    });

    if (!user) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Get user roles with permissions
    const userRoles = await prisma.userRole.findMany({
      where: { userId: id },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
      orderBy: {
        assignedAt: 'desc',
      },
    });

    const transformedRoles = userRoles.map(ur => ({
      id: ur.role.id,
      name: ur.role.name,
      description: ur.role.description,
      is_system_role: ur.role.isSystemRole,
      assigned_at: ur.assignedAt,
      assigned_by: ur.assignedBy,
      permissions: ur.role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
      })),
    }));

    // Get all permissions for this user (flattened)
    const allPermissions = userRoles.flatMap(ur => 
      ur.role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        from_role: ur.role.name,
      }))
    );

    // Remove duplicates
    const uniquePermissions = allPermissions.filter((permission, index, self) =>
      index === self.findIndex(p => p.id === permission.id)
    );

    return Response.json(
      createApiResponse({
        user: {
          id: user.id,
          email: user.email,
          full_name: user.fullName,
          is_active: user.isActive,
        },
        roles: transformedRoles,
        permissions: uniquePermissions,
        role_count: transformedRoles.length,
        permission_count: uniquePermissions.length,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch user roles');
  }
}

async function assignUserRolesHandler(request: NextRequest, context: { params: { id: string } }, currentUser: any) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    const validation = validateRequest(assignRolesSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { role_ids, replace_existing } = validation.data;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Verify roles exist
    const existingRoles = await prisma.role.findMany({
      where: {
        id: { in: role_ids },
      },
    });

    if (existingRoles.length !== role_ids.length) {
      const missingRoles = role_ids.filter(roleId => !existingRoles.find(r => r.id === roleId));
      return Response.json(
        createApiResponse(null, `Some roles not found: ${missingRoles.join(', ')}`, 'INVALID_ROLES'),
        { status: 400 }
      );
    }

    // Handle role assignment
    if (replace_existing) {
      // Remove all existing roles
      await prisma.userRole.deleteMany({
        where: { userId: id },
      });
    }

    // Add new roles (skip duplicates if not replacing)
    if (role_ids.length > 0) {
      const userRoleData = role_ids.map((roleId: string) => ({
        userId: id,
        roleId: roleId,
        assignedBy: currentUser.id,
      }));

      if (replace_existing) {
        await prisma.userRole.createMany({
          data: userRoleData,
        });
      } else {
        // Check for existing assignments to avoid duplicates
        const existingAssignments = await prisma.userRole.findMany({
          where: {
            userId: id,
            roleId: { in: role_ids },
          },
        });

        const existingRoleIds = existingAssignments.map(ua => ua.roleId);
        const newRoleIds = role_ids.filter((roleId: string) => !existingRoleIds.includes(roleId));

        if (newRoleIds.length > 0) {
          const newUserRoleData = newRoleIds.map((roleId: string) => ({
            userId: id,
            roleId: roleId,
            assignedBy: currentUser.id,
          }));

          await prisma.userRole.createMany({
            data: newUserRoleData,
          });
        }
      }
    }

    // Fetch updated user with roles
    const userWithRoles = await prisma.user.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    const transformedRoles = userWithRoles!.userRoles.map(ur => ({
      id: ur.role.id,
      name: ur.role.name,
      description: ur.role.description,
      assigned_at: ur.assignedAt,
    }));

    return Response.json(
      createApiResponse({
        message: 'User roles updated successfully',
        user: {
          id: userWithRoles!.id,
          email: userWithRoles!.email,
          full_name: userWithRoles!.fullName,
          roles: transformedRoles,
        },
        operation: replace_existing ? 'replaced' : 'added',
        roles_assigned: role_ids.length,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to assign user roles');
  }
}

async function removeUserRoleHandler(request: NextRequest, context: { params: { id: string } }, currentUser: any) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    const removeRoleSchema = Joi.object({
      role_id: Joi.string().uuid().required(),
    });

    const validation = validateRequest(removeRoleSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { role_id } = validation.data;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      return Response.json(
        createApiResponse(null, 'User not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Check if user has this role
    const userRole = await prisma.userRole.findFirst({
      where: {
        userId: id,
        roleId: role_id,
      },
      include: {
        role: true,
      },
    });

    if (!userRole) {
      return Response.json(
        createApiResponse(null, 'User does not have this role', 'ROLE_NOT_ASSIGNED'),
        { status: 404 }
      );
    }

    // Remove the role
    await prisma.userRole.delete({
      where: {
        id: userRole.id,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Role removed from user successfully',
        removed_role: {
          id: userRole.role.id,
          name: userRole.role.name,
        },
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to remove user role');
  }
}

export const GET = requireAuth(getUserRolesHandler);
export const POST = requireRole(['admin'])(assignUserRolesHandler);
export const DELETE = requireRole(['admin'])(removeUserRoleHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
