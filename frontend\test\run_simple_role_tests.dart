#!/usr/bin/env dart

import 'dart:io';

/// Simple script to run role-based and permission tests
/// Usage: dart test/run_simple_role_tests.dart

Future<void> main(List<String> args) async {
  print('🧪 Running SRSR Role-Based and Permission Tests');
  print('=' * 60);

  final bool runRoleTests = !args.contains('--skip-role-tests');
  final bool runPermissionTests = !args.contains('--skip-permission-tests');
  
  var allTestsPassed = true;

  try {
    // Check if backend is running
    print('📡 Checking backend server...');
    final isBackendRunning = await checkBackendHealth();
    
    if (!isBackendRunning) {
      print('❌ Backend server is not running!');
      print('Please start the backend server first:');
      print('  cd backend && npm run dev');
      exit(1);
    }
    
    print('✅ Backend server is running');

    // Run role-based screen tests
    if (runRoleTests) {
      print('\n🎭 Running Role-Based Screen Tests...');
      print('-' * 40);
      
      final roleTestResult = await runFlutterTest('test/role_based_screen_tests.dart');
      if (!roleTestResult) {
        allTestsPassed = false;
        print('❌ Role-based screen tests failed');
      } else {
        print('✅ Role-based screen tests passed');
      }
    }

    // Run dynamic permission tests
    if (runPermissionTests) {
      print('\n🔐 Running Dynamic Permission Tests...');
      print('-' * 40);
      
      final permissionTestResult = await runFlutterTest('test/dynamic_permission_tests.dart');
      if (!permissionTestResult) {
        allTestsPassed = false;
        print('❌ Dynamic permission tests failed');
      } else {
        print('✅ Dynamic permission tests passed');
      }
    }

    // Final result
    if (allTestsPassed) {
      print('\n🎉 All tests passed!');
      exit(0);
    } else {
      print('\n❌ Some tests failed.');
      exit(1);
    }

  } catch (e) {
    print('❌ Error running tests: $e');
    exit(1);
  }
}

/// Check if backend server is healthy
Future<bool> checkBackendHealth() async {
  try {
    final result = await Process.run('curl', [
      '-s',
      '-o', '/dev/null',
      '-w', '%{http_code}',
      'http://192.168.1.3:3000/api'
    ]);
    
    return result.stdout.toString().trim() == '200';
  } catch (e) {
    // If curl is not available, try with dart http
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse('http://192.168.1.3:3000/api'));
      final response = await request.close();
      client.close();
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}

/// Run a Flutter test file
Future<bool> runFlutterTest(String testFile) async {
  try {
    final result = await Process.run(
      'flutter',
      ['test', testFile],
      workingDirectory: '.',
    );

    print(result.stdout);
    if (result.stderr.isNotEmpty) {
      print('STDERR: ${result.stderr}');
    }

    return result.exitCode == 0;
  } catch (e) {
    print('Error running test $testFile: $e');
    return false;
  }
}
