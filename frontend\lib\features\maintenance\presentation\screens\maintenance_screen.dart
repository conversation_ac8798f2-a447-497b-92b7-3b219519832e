import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/maintenance_issue.dart';
import '../providers/maintenance_providers.dart';
import '../widgets/maintenance_issue_card.dart';
import '../widgets/maintenance_filter_chip.dart';
import '../widgets/add_maintenance_issue_dialog.dart';

class MaintenanceScreen extends ConsumerStatefulWidget {
  const MaintenanceScreen({super.key});

  @override
  ConsumerState<MaintenanceScreen> createState() => _MaintenanceScreenState();
}

class _MaintenanceScreenState extends ConsumerState<MaintenanceScreen> {
  String _selectedFilter = 'all';
  String _selectedPriority = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('🔧 MAINTENANCE SCREEN: Building maintenance screen...');
    final maintenanceIssuesAsync = ref.watch(maintenanceIssuesProvider);
    print('🔧 MAINTENANCE SCREEN: Provider watched, async state: ${maintenanceIssuesAsync.runtimeType}');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance Issues'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddIssueDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(maintenanceIssuesProvider),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search issues...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Status Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      MaintenanceFilterChip(
                        label: 'All',
                        isSelected: _selectedFilter == 'all',
                        onSelected: () => setState(() => _selectedFilter = 'all'),
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Open',
                        isSelected: _selectedFilter == 'open',
                        onSelected: () => setState(() => _selectedFilter = 'open'),
                        icon: Icons.new_releases,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'In Progress',
                        isSelected: _selectedFilter == 'in_progress',
                        onSelected: () => setState(() => _selectedFilter = 'in_progress'),
                        icon: Icons.engineering,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Resolved',
                        isSelected: _selectedFilter == 'resolved',
                        onSelected: () => setState(() => _selectedFilter = 'resolved'),
                        icon: Icons.check_circle_outline,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Closed',
                        isSelected: _selectedFilter == 'closed',
                        onSelected: () => setState(() => _selectedFilter = 'closed'),
                        icon: Icons.check_circle,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),

                // Priority Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      MaintenanceFilterChip(
                        label: 'All Priorities',
                        isSelected: _selectedPriority == 'all',
                        onSelected: () => setState(() => _selectedPriority = 'all'),
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Critical',
                        isSelected: _selectedPriority == 'critical',
                        onSelected: () => setState(() => _selectedPriority = 'critical'),
                        icon: Icons.warning,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'High',
                        isSelected: _selectedPriority == 'high',
                        onSelected: () => setState(() => _selectedPriority = 'high'),
                        icon: Icons.priority_high,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Medium',
                        isSelected: _selectedPriority == 'medium',
                        onSelected: () => setState(() => _selectedPriority = 'medium'),
                        icon: Icons.remove,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Low',
                        isSelected: _selectedPriority == 'low',
                        onSelected: () => setState(() => _selectedPriority = 'low'),
                        icon: Icons.low_priority,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Issues List
          Expanded(
            child: maintenanceIssuesAsync.when(
              data: (issues) {
                print('🔧 MAINTENANCE SCREEN: Data received with ${issues.length} issues');
                final filteredIssues = _filterIssues(issues);
                print('🔧 MAINTENANCE SCREEN: After filtering: ${filteredIssues.length} issues');

                if (filteredIssues.isEmpty) {
                  print('🔧 MAINTENANCE SCREEN: No filtered issues, showing empty state');
                  return _buildEmptyState();
                }

                print('🔧 MAINTENANCE SCREEN: Building ListView with ${filteredIssues.length} issues');
                return RefreshIndicator(
                  onRefresh: () async {
                    print('🔧 MAINTENANCE SCREEN: Refresh triggered');
                    ref.invalidate(maintenanceIssuesProvider);
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredIssues.length,
                    itemBuilder: (context, index) {
                      final issue = filteredIssues[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                        child: MaintenanceIssueCard(
                          issue: issue,
                          onTap: () => _navigateToIssueDetails(issue),
                          onStatusUpdate: (newStatus) => _updateIssueStatus(issue, newStatus),
                          onPriorityUpdate: (newPriority) => _updateIssuePriority(issue, newPriority),
                          onAssign: () => _showAssignDialog(issue),
                          onEdit: () => _showEditIssueDialog(issue),
                          onDelete: () => _showDeleteConfirmation(issue),
                        ),
                      );
                    },
                  ),
                );
              },
              loading: () {
                print('🔧 MAINTENANCE SCREEN: Showing loading state');
                return const Center(child: CircularProgressIndicator());
              },
              error: (error, stack) {
                print('🔧 MAINTENANCE SCREEN: Showing error state: $error');
                return _buildErrorState(error);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddIssueDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  List<MaintenanceIssue> _filterIssues(List<MaintenanceIssue> issues) {
    var filtered = issues;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((issue) {
        return issue.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               issue.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (issue.serviceType?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply status filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((issue) => issue.status == _selectedFilter).toList();
    }

    // Apply priority filter
    if (_selectedPriority != 'all') {
      filtered = filtered.where((issue) => issue.priority == _selectedPriority).toList();
    }

    // Sort by priority and creation date
    filtered.sort((a, b) {
      // First sort by priority
      final priorityOrder = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3};
      final aPriority = priorityOrder[a.priority] ?? 4;
      final bPriority = priorityOrder[b.priority] ?? 4;

      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }

      // Then by creation date (newest first)
      return b.createdAt.compareTo(a.createdAt);
    });

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.build_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all' || _selectedPriority != 'all'
                ? 'No issues found'
                : 'No maintenance issues yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all' || _selectedPriority != 'all'
                ? 'Try adjusting your search or filters'
                : 'Create your first maintenance issue',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          if (_searchQuery.isEmpty && _selectedFilter == 'all' && _selectedPriority == 'all')
            ElevatedButton.icon(
              onPressed: () => _showAddIssueDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Create Issue'),
            ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load maintenance issues',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(maintenanceIssuesProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _navigateToIssueDetails(MaintenanceIssue issue) {
    context.push('/maintenance/${issue.id}');
  }

  void _showAddIssueDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddMaintenanceIssueDialog(),
    );
  }

  void _showEditIssueDialog(MaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => EditMaintenanceIssueDialog(issue: issue),
    );
  }

  void _showAssignDialog(MaintenanceIssue issue) {
    // TODO: Implement assign dialog
    AppUtils.showInfoSnackBar(context, 'Assign feature coming soon');
  }

  void _showDeleteConfirmation(MaintenanceIssue issue) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Issue'),
        content: Text('Are you sure you want to delete "${issue.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteIssue(issue);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _updateIssueStatus(MaintenanceIssue issue, String newStatus) {
    final updatedIssue = issue.copyWith(status: newStatus);
    ref.read(maintenanceIssuesNotifierProvider.notifier).updateIssue(updatedIssue);
    AppUtils.showSuccessSnackBar(context, 'Issue status updated');
  }

  void _updateIssuePriority(MaintenanceIssue issue, String newPriority) {
    final updatedIssue = issue.copyWith(priority: newPriority);
    ref.read(maintenanceIssuesNotifierProvider.notifier).updateIssue(updatedIssue);
    AppUtils.showSuccessSnackBar(context, 'Issue priority updated');
  }

  void _deleteIssue(MaintenanceIssue issue) {
    ref.read(maintenanceIssuesNotifierProvider.notifier).deleteIssue(issue.id);
    AppUtils.showSuccessSnackBar(context, 'Issue deleted successfully');
  }
}

// Placeholder dialogs - these would be implemented separately
class EditMaintenanceIssueDialog extends StatelessWidget {
  final MaintenanceIssue issue;

  const EditMaintenanceIssueDialog({super.key, required this.issue});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Issue'),
      content: Text('Edit form for ${issue.title} would go here'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Save'),
        ),
      ],
    );
  }
}
