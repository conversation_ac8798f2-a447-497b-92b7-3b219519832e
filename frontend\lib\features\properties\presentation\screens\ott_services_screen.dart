import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/ott_service.dart';
import '../providers/ott_services_providers.dart';
import '../widgets/ott_service_card.dart';
import '../widgets/add_ott_service_dialog.dart';

class OttServicesScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const OttServicesScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<OttServicesScreen> createState() => _OttServicesScreenState();
}

class _OttServicesScreenState extends ConsumerState<OttServicesScreen> {
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ottServicesAsync = ref.watch(ottServicesByPropertyProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('OTT Services'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['properties.update'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddOttServiceDialog(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(ottServicesByPropertyProvider(widget.propertyId)),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search OTT services...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('all', 'All Services'),
                      const SizedBox(width: 8),
                      _buildFilterChip('active', 'Active'),
                      const SizedBox(width: 8),
                      _buildFilterChip('inactive', 'Inactive'),
                      const SizedBox(width: 8),
                      _buildFilterChip('maintenance', 'Maintenance'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Services List
          Expanded(
            child: ottServicesAsync.when(
              data: (services) {
                final filteredServices = _filterServices(services);

                if (filteredServices.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(ottServicesByPropertyProvider(widget.propertyId));
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredServices.length,
                    itemBuilder: (context, index) {
                      final service = filteredServices[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                        child: OttServiceCard(
                          service: service,
                          onTap: () => _showServiceDetails(service),
                          onEdit: () => _showEditServiceDialog(service),
                          onDelete: () => _showDeleteConfirmation(service),
                        ),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  List<OttService> _filterServices(List<OttService> services) {
    var filtered = services;

    // Apply status filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((service) {
        switch (_selectedFilter) {
          case 'active':
            return service.isActive;
          case 'inactive':
            return !service.isActive;
          case 'maintenance':
            return service.status.toLowerCase() == 'maintenance';
          default:
            return true;
        }
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((service) {
        return service.serviceName.toLowerCase().contains(query) ||
               service.provider.toLowerCase().contains(query) ||
               (service.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.tv,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No OTT Services Found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all'
                ? 'Try adjusting your search or filters'
                : 'Add your first OTT service to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          RoleBasedWidget(
            requiredPermissions: const ['properties.update'],
            child: ElevatedButton.icon(
              onPressed: () => _showAddOttServiceDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add OTT Service'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load OTT services',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(ottServicesByPropertyProvider(widget.propertyId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showAddOttServiceDialog() {
    showDialog(
      context: context,
      builder: (context) => AddOttServiceDialog(
        propertyId: widget.propertyId,
        onServiceAdded: () {
          ref.invalidate(ottServicesByPropertyProvider(widget.propertyId));
          AppUtils.showSuccessSnackBar(context, 'OTT service added successfully');
        },
      ),
    );
  }

  void _showServiceDetails(OttService service) {
    // TODO: Navigate to service details screen
    AppUtils.showInfoSnackBar(context, 'Service details: ${service.serviceName}');
  }

  void _showEditServiceDialog(OttService service) {
    showDialog(
      context: context,
      builder: (context) => AddOttServiceDialog(
        propertyId: widget.propertyId,
        service: service,
        onServiceAdded: () {
          ref.invalidate(ottServicesByPropertyProvider(widget.propertyId));
          AppUtils.showSuccessSnackBar(context, 'OTT service updated successfully');
        },
      ),
    );
  }

  void _showDeleteConfirmation(OttService service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete OTT Service'),
        content: Text('Are you sure you want to delete "${service.serviceName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(ottServicesNotifierProvider(widget.propertyId).notifier)
                    .deleteOttService(service.id);
                AppUtils.showSuccessSnackBar(context, 'OTT service deleted successfully');
              } catch (e) {
                AppUtils.showErrorSnackBar(context, 'Failed to delete OTT service: $e');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
