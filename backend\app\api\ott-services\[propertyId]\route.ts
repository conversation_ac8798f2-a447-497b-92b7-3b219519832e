import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createOttServiceSchema = Joi.object({
  service_name: Joi.string().min(2).required(),
  subscription_type: Joi.string().optional(),
  monthly_cost: Joi.number().positive().optional(),
  renewal_date: Joi.date().optional(),
  status: Joi.string().valid('active', 'inactive', 'expired').default('active'),
  login_credentials: Joi.object().optional(),
  notes: Joi.string().optional(),
});

async function getOttServicesHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Get OTT services for the property
    const ottServices = await prisma.ottService.findMany({
      where: {
        propertyId,
      },
      orderBy: {
        serviceName: 'asc',
      },
    });

    // Transform data to match API response format
    const transformedServices = ottServices.map(service => ({
      id: service.id,
      property_id: service.propertyId,
      service_name: service.serviceName,
      subscription_type: service.subscriptionType,
      monthly_cost: service.monthlyCost ? parseFloat(service.monthlyCost.toString()) : null,
      renewal_date: service.renewalDate,
      status: service.status.toLowerCase(),
      login_credentials: service.loginCredentials,
      notes: service.notes,
      created_at: service.createdAt,
      updated_at: service.updatedAt,
    }));

    return Response.json(
      createApiResponse(transformedServices),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch OTT services');
  }
}

async function createOttServiceHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createOttServiceSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      service_name, 
      subscription_type, 
      monthly_cost, 
      renewal_date, 
      status, 
      login_credentials, 
      notes 
    } = validation.data;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Create OTT service
    const ottService = await prisma.ottService.create({
      data: {
        propertyId,
        serviceName: service_name,
        subscriptionType: subscription_type,
        monthlyCost: monthly_cost,
        renewalDate: renewal_date ? new Date(renewal_date) : null,
        status: status.toUpperCase(),
        loginCredentials: login_credentials,
        notes,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'OTT service created successfully',
        service: {
          id: ottService.id,
          property_id: ottService.propertyId,
          service_name: ottService.serviceName,
          subscription_type: ottService.subscriptionType,
          monthly_cost: ottService.monthlyCost ? parseFloat(ottService.monthlyCost.toString()) : null,
          renewal_date: ottService.renewalDate,
          status: ottService.status.toLowerCase(),
          login_credentials: ottService.loginCredentials,
          notes: ottService.notes,
          created_at: ottService.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create OTT service');
  }
}

export const GET = requireAuth(getOttServicesHandler);
export const POST = requireRole(['admin', 'property_manager'])(createOttServiceHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
