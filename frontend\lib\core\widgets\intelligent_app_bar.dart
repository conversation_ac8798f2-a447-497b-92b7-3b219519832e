import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../auth/providers/permission_providers.dart';
import '../navigation/intelligent_navigation.dart';

/// Intelligent app bar that shows actions based on user permissions
class IntelligentAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final String title;
  final List<IntelligentAction> actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final double elevation;
  final bool centerTitle;

  const IntelligentAppBar({
    super.key,
    required this.title,
    this.actions = const [],
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.elevation = 4.0,
    this.centerTitle = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<Widget>>(
      future: _buildAuthorizedActions(ref),
      builder: (context, snapshot) {
        final authorizedActions = snapshot.data ?? [];

        return AppBar(
          title: Text(title),
          leading: leading,
          automaticallyImplyLeading: automaticallyImplyLeading,
          backgroundColor: backgroundColor,
          elevation: elevation,
          centerTitle: centerTitle,
          actions: authorizedActions.isNotEmpty ? authorizedActions : null,
        );
      },
    );
  }

  Future<List<Widget>> _buildAuthorizedActions(WidgetRef ref) async {
    final authorizedActions = <Widget>[];

    for (final action in actions) {
      bool hasPermission = true;

      if (action.requiredPermissions.isNotEmpty) {
        final permissionChecker = ref.read(permissionCheckerProvider);

        if (action.requireAll) {
          hasPermission = await permissionChecker.hasAllPermissions(action.requiredPermissions);
        } else {
          hasPermission = await permissionChecker.hasAnyPermission(action.requiredPermissions);
        }
      }

      if (action.requiredRoute != null) {
        hasPermission = hasPermission && await IntelligentNavigator.checkRoutePermission(ref, action.requiredRoute!);
      }

      if (hasPermission) {
        authorizedActions.add(action.widget);
      }
    }

    return authorizedActions;
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Action item with permission requirements
class IntelligentAction {
  final Widget widget;
  final List<String> requiredPermissions;
  final String? requiredRoute;
  final bool requireAll;

  const IntelligentAction({
    required this.widget,
    this.requiredPermissions = const [],
    this.requiredRoute,
    this.requireAll = true,
  });

  /// Create an icon button action
  factory IntelligentAction.iconButton({
    required IconData icon,
    required VoidCallback onPressed,
    String? tooltip,
    List<String> requiredPermissions = const [],
    String? requiredRoute,
    bool requireAll = true,
  }) {
    return IntelligentAction(
      widget: IconButton(
        icon: Icon(icon),
        onPressed: onPressed,
        tooltip: tooltip,
      ),
      requiredPermissions: requiredPermissions,
      requiredRoute: requiredRoute,
      requireAll: requireAll,
    );
  }

  /// Create a navigation action
  factory IntelligentAction.navigation({
    required IconData icon,
    required String routeName,
    required BuildContext context,
    required WidgetRef ref,
    String? tooltip,
    Object? arguments,
  }) {
    return IntelligentAction(
      widget: IconButton(
        icon: Icon(icon),
        onPressed: () {
          IntelligentNavigator.navigateIfAuthorized(
            context,
            ref,
            routeName,
            arguments: arguments,
          );
        },
        tooltip: tooltip,
      ),
      requiredRoute: routeName,
    );
  }

  /// Create a popup menu action
  factory IntelligentAction.popupMenu({
    required List<IntelligentPopupMenuItem> items,
    IconData icon = Icons.more_vert,
    String? tooltip,
  }) {
    return IntelligentAction(
      widget: IntelligentPopupMenuButton(
        items: items,
        icon: icon,
        tooltip: tooltip,
      ),
    );
  }
}

/// Intelligent popup menu button
class IntelligentPopupMenuButton extends ConsumerWidget {
  final List<IntelligentPopupMenuItem> items;
  final IconData icon;
  final String? tooltip;

  const IntelligentPopupMenuButton({
    super.key,
    required this.items,
    this.icon = Icons.more_vert,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<PopupMenuEntry<String>>>(
      future: _buildAuthorizedMenuItems(ref),
      builder: (context, snapshot) {
        final authorizedItems = snapshot.data ?? [];

        if (authorizedItems.isEmpty) {
          return const SizedBox.shrink();
        }

        return PopupMenuButton<String>(
          icon: Icon(icon),
          tooltip: tooltip,
          itemBuilder: (context) => authorizedItems,
          onSelected: (value) {
            final item = items.firstWhere((item) => item.value == value);
            item.onSelected?.call();
          },
        );
      },
    );
  }

  Future<List<PopupMenuEntry<String>>> _buildAuthorizedMenuItems(WidgetRef ref) async {
    final authorizedItems = <PopupMenuEntry<String>>[];

    for (final item in items) {
      bool hasPermission = true;

      if (item.requiredPermissions.isNotEmpty) {
        final permissionChecker = ref.read(permissionCheckerProvider);

        if (item.requireAll) {
          hasPermission = await permissionChecker.hasAllPermissions(item.requiredPermissions);
        } else {
          hasPermission = await permissionChecker.hasAnyPermission(item.requiredPermissions);
        }
      }

      if (item.requiredRoute != null) {
        hasPermission = hasPermission && await IntelligentNavigator.checkRoutePermission(ref, item.requiredRoute!);
      }

      if (hasPermission) {
        if (item.isDivider) {
          authorizedItems.add(const PopupMenuDivider());
        } else {
          authorizedItems.add(
            PopupMenuItem<String>(
              value: item.value,
              child: Row(
                children: [
                  if (item.icon != null) ...[
                    Icon(item.icon, size: 20),
                    const SizedBox(width: 12),
                  ],
                  Text(item.title),
                ],
              ),
            ),
          );
        }
      }
    }

    return authorizedItems;
  }
}

/// Popup menu item with permission requirements
class IntelligentPopupMenuItem {
  final String value;
  final String title;
  final IconData? icon;
  final VoidCallback? onSelected;
  final List<String> requiredPermissions;
  final String? requiredRoute;
  final bool requireAll;
  final bool isDivider;

  const IntelligentPopupMenuItem({
    required this.value,
    required this.title,
    this.icon,
    this.onSelected,
    this.requiredPermissions = const [],
    this.requiredRoute,
    this.requireAll = true,
    this.isDivider = false,
  });

  /// Create a divider item
  factory IntelligentPopupMenuItem.divider() {
    return const IntelligentPopupMenuItem(
      value: 'divider',
      title: '',
      isDivider: true,
    );
  }

  /// Create a navigation menu item
  factory IntelligentPopupMenuItem.navigation({
    required String value,
    required String title,
    required String routeName,
    required BuildContext context,
    required WidgetRef ref,
    IconData? icon,
    Object? arguments,
  }) {
    return IntelligentPopupMenuItem(
      value: value,
      title: title,
      icon: icon,
      requiredRoute: routeName,
      onSelected: () {
        IntelligentNavigator.navigateIfAuthorized(
          context,
          ref,
          routeName,
          arguments: arguments,
        );
      },
    );
  }
}

/// Intelligent floating action button that shows based on permissions
class IntelligentFAB extends ConsumerWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final List<String> requiredPermissions;
  final String? requiredRoute;
  final bool requireAll;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const IntelligentFAB({
    super.key,
    this.onPressed,
    this.child,
    this.tooltip,
    this.requiredPermissions = const [],
    this.requiredRoute,
    this.requireAll = true,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<bool>(
      future: _checkPermissions(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink(); // Don't show while checking
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink(); // Don't show if no permission
        }

        return FloatingActionButton(
          onPressed: onPressed,
          tooltip: tooltip,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          child: child,
        );
      },
    );
  }

  Future<bool> _checkPermissions(WidgetRef ref) async {
    bool hasPermission = true;

    if (requiredPermissions.isNotEmpty) {
      final permissionChecker = ref.read(permissionCheckerProvider);

      if (requireAll) {
        hasPermission = await permissionChecker.hasAllPermissions(requiredPermissions);
      } else {
        hasPermission = await permissionChecker.hasAnyPermission(requiredPermissions);
      }
    }

    if (requiredRoute != null) {
      hasPermission = hasPermission && await IntelligentNavigator.checkRoutePermission(ref, requiredRoute!);
    }

    return hasPermission;
  }
}

/// Intelligent speed dial that shows actions based on permissions
class IntelligentSpeedDial extends ConsumerWidget {
  final List<IntelligentSpeedDialChild> children;
  final Widget? child;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const IntelligentSpeedDial({
    super.key,
    required this.children,
    this.child,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<Widget>>(
      future: _buildAuthorizedChildren(ref, context),
      builder: (context, snapshot) {
        final authorizedChildren = snapshot.data ?? [];

        if (authorizedChildren.isEmpty) {
          return const SizedBox.shrink();
        }

        // This is a simplified speed dial - you might want to use a package like flutter_speed_dial
        return FloatingActionButton(
          onPressed: () => _showSpeedDialMenu(context, authorizedChildren),
          tooltip: tooltip,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          child: child ?? const Icon(Icons.add),
        );
      },
    );
  }

  Future<List<Widget>> _buildAuthorizedChildren(WidgetRef ref, BuildContext context) async {
    final authorizedChildren = <Widget>[];

    for (final child in children) {
      bool hasPermission = true;

      if (child.requiredPermissions.isNotEmpty) {
        final permissionChecker = ref.read(permissionCheckerProvider);

        if (child.requireAll) {
          hasPermission = await permissionChecker.hasAllPermissions(child.requiredPermissions);
        } else {
          hasPermission = await permissionChecker.hasAnyPermission(child.requiredPermissions);
        }
      }

      if (child.requiredRoute != null) {
        hasPermission = hasPermission && await IntelligentNavigator.checkRoutePermission(ref, child.requiredRoute!);
      }

      if (hasPermission) {
        authorizedChildren.add(
          ListTile(
            leading: child.child,
            title: Text(child.label),
            onTap: () {
              Navigator.pop(context);
              child.onTap?.call();
            },
          ),
        );
      }
    }

    return authorizedChildren;
  }

  void _showSpeedDialMenu(BuildContext context, List<Widget> children) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }
}

/// Speed dial child with permission requirements
class IntelligentSpeedDialChild {
  final Widget child;
  final String label;
  final VoidCallback? onTap;
  final List<String> requiredPermissions;
  final String? requiredRoute;
  final bool requireAll;

  const IntelligentSpeedDialChild({
    required this.child,
    required this.label,
    this.onTap,
    this.requiredPermissions = const [],
    this.requiredRoute,
    this.requireAll = true,
  });
}
