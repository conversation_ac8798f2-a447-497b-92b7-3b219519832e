import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/ott_service.dart';
import '../../data/ott_service_api_service.dart' as api;
import '../providers/ott_services_providers.dart';

class AddOttServiceDialog extends ConsumerStatefulWidget {
  final String propertyId;
  final OttService? service; // For editing
  final VoidCallback? onServiceAdded;

  const AddOttServiceDialog({
    super.key,
    required this.propertyId,
    this.service,
    this.onServiceAdded,
  });

  @override
  ConsumerState<AddOttServiceDialog> createState() => _AddOttServiceDialogState();
}

class _AddOttServiceDialogState extends ConsumerState<AddOttServiceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _serviceNameController = TextEditingController();
  final _providerController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _monthlyCostController = TextEditingController();

  String _selectedStatus = 'active';
  String _selectedSubscriptionType = 'monthly';
  DateTime? _subscriptionStartDate;
  DateTime? _subscriptionEndDate;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.service != null) {
      _populateFields();
    }
  }

  void _populateFields() {
    final service = widget.service!;
    _serviceNameController.text = service.serviceName;
    _providerController.text = service.provider;
    _descriptionController.text = service.description ?? '';
    _monthlyCostController.text = service.monthlyCost?.toString() ?? '';
    _selectedStatus = service.status;
    _selectedSubscriptionType = service.subscriptionType ?? 'monthly';
    _subscriptionStartDate = service.subscriptionStartDate;
    _subscriptionEndDate = service.subscriptionEndDate;
  }

  @override
  void dispose() {
    _serviceNameController.dispose();
    _providerController.dispose();
    _descriptionController.dispose();
    _monthlyCostController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.service != null;

    return AlertDialog(
      title: Text(isEditing ? 'Edit OTT Service' : 'Add OTT Service'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Service Name
                TextFormField(
                  controller: _serviceNameController,
                  decoration: const InputDecoration(
                    labelText: 'Service Name *',
                    hintText: 'e.g., Netflix, Amazon Prime',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Service name is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Provider
                TextFormField(
                  controller: _providerController,
                  decoration: const InputDecoration(
                    labelText: 'Provider *',
                    hintText: 'e.g., Netflix Inc., Amazon',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Provider is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    hintText: 'Optional description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Monthly Cost and Subscription Type Row
                Row(
                  children: [
                    // Monthly Cost
                    Expanded(
                      child: TextFormField(
                        controller: _monthlyCostController,
                        decoration: const InputDecoration(
                          labelText: 'Monthly Cost',
                          hintText: '0.00',
                          prefixText: '\$ ',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final cost = double.tryParse(value);
                            if (cost == null || cost < 0) {
                              return 'Invalid cost';
                            }
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),

                    // Subscription Type
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedSubscriptionType,
                        decoration: const InputDecoration(
                          labelText: 'Billing',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                          DropdownMenuItem(value: 'yearly', child: Text('Yearly')),
                          DropdownMenuItem(value: 'quarterly', child: Text('Quarterly')),
                          DropdownMenuItem(value: 'lifetime', child: Text('Lifetime')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedSubscriptionType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Status
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Status *',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'active', child: Text('Active')),
                    DropdownMenuItem(value: 'inactive', child: Text('Inactive')),
                    DropdownMenuItem(value: 'maintenance', child: Text('Under Maintenance')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Subscription Dates
                Row(
                  children: [
                    // Start Date
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDate(context, true),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Start Date',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _subscriptionStartDate != null
                                ? AppUtils.formatDate(_subscriptionStartDate!)
                                : 'Select date',
                            style: TextStyle(
                              color: _subscriptionStartDate != null
                                  ? Theme.of(context).textTheme.bodyLarge?.color
                                  : Theme.of(context).hintColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),

                    // End Date
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDate(context, false),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'End Date',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _subscriptionEndDate != null
                                ? AppUtils.formatDate(_subscriptionEndDate!)
                                : 'Select date',
                            style: TextStyle(
                              color: _subscriptionEndDate != null
                                  ? Theme.of(context).textTheme.bodyLarge?.color
                                  : Theme.of(context).hintColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveService,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final initialDate = isStartDate ? _subscriptionStartDate : _subscriptionEndDate;
    final firstDate = DateTime(2020);
    final lastDate = DateTime(2030);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (selectedDate != null) {
      setState(() {
        if (isStartDate) {
          _subscriptionStartDate = selectedDate;
          // If end date is before start date, clear it
          if (_subscriptionEndDate != null && _subscriptionEndDate!.isBefore(selectedDate)) {
            _subscriptionEndDate = null;
          }
        } else {
          _subscriptionEndDate = selectedDate;
        }
      });
    }
  }

  Future<void> _saveService() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate subscription dates
    if (_subscriptionStartDate != null && _subscriptionEndDate != null) {
      if (_subscriptionStartDate!.isAfter(_subscriptionEndDate!)) {
        AppUtils.showErrorSnackBar(context, 'Start date cannot be after end date');
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final monthlyCost = _monthlyCostController.text.isNotEmpty
          ? double.tryParse(_monthlyCostController.text)
          : null;

      if (widget.service != null) {
        // Update existing service
        final request = api.UpdateOttServiceRequest(
          serviceName: _serviceNameController.text.trim(),
          subscriptionType: _selectedSubscriptionType,
          monthlyCost: monthlyCost,
          renewalDate: _subscriptionEndDate,
          status: _selectedStatus,
          notes: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
        );

        await ref.read(ottServicesNotifierProvider(widget.propertyId).notifier)
            .updateOttService(widget.service!.id, request);
      } else {
        // Create new service
        final request = api.CreateOttServiceRequest(
          serviceName: _serviceNameController.text.trim(),
          subscriptionType: _selectedSubscriptionType,
          monthlyCost: monthlyCost,
          renewalDate: _subscriptionEndDate,
          status: _selectedStatus,
          notes: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
        );

        await ref.read(ottServicesNotifierProvider(widget.propertyId).notifier)
            .addOttService(request);
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onServiceAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to save service: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
