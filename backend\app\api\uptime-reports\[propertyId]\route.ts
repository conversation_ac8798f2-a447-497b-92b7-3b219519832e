import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getQueryParams, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const createUptimeReportSchema = Joi.object({
  service_type: Joi.string().required(),
  date: Joi.date().required(),
  uptime_percentage: Joi.number().min(0).max(100).optional(),
  downtime_minutes: Joi.number().integer().min(0).default(0),
  incidents_count: Joi.number().integer().min(0).default(0),
  notes: Joi.string().optional(),
});

async function getUptimeReportsHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;
    const params = getQueryParams(request);
    const { service_type, start_date, end_date } = params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Build where clause
    const where: any = { propertyId };
    
    if (service_type) {
      where.serviceType = service_type;
    }
    
    if (start_date && end_date) {
      where.date = {
        gte: new Date(start_date),
        lte: new Date(end_date),
      };
    }

    // Get uptime reports for the property
    const uptimeReports = await prisma.uptimeReport.findMany({
      where,
      orderBy: [
        { date: 'desc' },
        { serviceType: 'asc' },
      ],
    });

    // Transform data to match API response format
    const transformedReports = uptimeReports.map(report => ({
      id: report.id,
      property_id: report.propertyId,
      service_type: report.serviceType,
      date: report.date,
      uptime_percentage: report.uptimePercentage ? parseFloat(report.uptimePercentage.toString()) : null,
      downtime_minutes: report.downtimeMinutes,
      incidents_count: report.incidentsCount,
      notes: report.notes,
      created_at: report.createdAt,
    }));

    return Response.json(
      createApiResponse(transformedReports),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch uptime reports');
  }
}

async function createUptimeReportHandler(
  request: NextRequest, 
  context: { params: { propertyId: string } }, 
  currentUser: any
) {
  try {
    const { propertyId } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(createUptimeReportSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { 
      service_type, 
      date, 
      uptime_percentage, 
      downtime_minutes, 
      incidents_count, 
      notes 
    } = validation.data;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id: propertyId },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Check if report already exists for this property, service, and date
    const existingReport = await prisma.uptimeReport.findUnique({
      where: {
        propertyId_serviceType_date: {
          propertyId,
          serviceType: service_type,
          date: new Date(date),
        },
      },
    });

    if (existingReport) {
      return Response.json(
        createApiResponse(null, 'Uptime report already exists for this date and service', 'DUPLICATE_ENTRY'),
        { status: 409 }
      );
    }

    // Create uptime report
    const uptimeReport = await prisma.uptimeReport.create({
      data: {
        propertyId,
        serviceType: service_type,
        date: new Date(date),
        uptimePercentage: uptime_percentage,
        downtimeMinutes: downtime_minutes,
        incidentsCount: incidents_count,
        notes,
      },
    });

    return Response.json(
      createApiResponse({
        message: 'Uptime report created successfully',
        report: {
          id: uptimeReport.id,
          property_id: uptimeReport.propertyId,
          service_type: uptimeReport.serviceType,
          date: uptimeReport.date,
          uptime_percentage: uptimeReport.uptimePercentage ? parseFloat(uptimeReport.uptimePercentage.toString()) : null,
          downtime_minutes: uptimeReport.downtimeMinutes,
          incidents_count: uptimeReport.incidentsCount,
          notes: uptimeReport.notes,
          created_at: uptimeReport.createdAt,
        },
      }),
      { 
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to create uptime report');
  }
}

export const GET = requireAuth(getUptimeReportsHandler);
export const POST = requireAuth(createUptimeReportHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
