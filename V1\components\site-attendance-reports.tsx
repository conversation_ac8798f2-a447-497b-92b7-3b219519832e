"use client"

import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon, FileSpreadsheet, FileIcon as FilePdf, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"
import {
  getDailyAttendance,
  getWeeklyAttendance,
  getMonthlyAttendance,
  type AttendanceRecord,
} from "@/app/actions/site-management"

interface SiteAttendanceReportsProps {
  siteId: string
  siteName: string
}

export function SiteAttendanceReports({ siteId, siteName }: SiteAttendanceReportsProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [reportType, setReportType] = useState<"daily" | "weekly" | "monthly">("daily")
  const [isLoading, setIsLoading] = useState(false)
  const [records, setRecords] = useState<AttendanceRecord[]>([])
  const { toast } = useToast()

  const fetchReports = async () => {
    setIsLoading(true)

    try {
      let data: AttendanceRecord[] = []

      switch (reportType) {
        case "daily":
          data = await getDailyAttendance(siteId, format(selectedDate, "yyyy-MM-dd"))
          break
        case "weekly":
          data = await getWeeklyAttendance(siteId, selectedDate)
          break
        case "monthly":
          data = await getMonthlyAttendance(siteId, selectedDate)
          break
      }

      setRecords(data)

      if (data.length === 0) {
        toast({
          title: "No Records Found",
          description: "No attendance records found for the selected period.",
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "Failed to fetch attendance records",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getReportTitle = () => {
    switch (reportType) {
      case "daily":
        return `Daily Attendance Report - ${format(selectedDate, "PPP")}`
      case "weekly":
        return `Weekly Attendance Report - Week of ${format(selectedDate, "PPP")}`
      case "monthly":
        return `Monthly Attendance Report - ${format(selectedDate, "MMMM yyyy")}`
    }
  }

  const downloadExcel = () => {
    if (records.length === 0) {
      toast({
        title: "No Data",
        description: "There are no records to download.",
      })
      return
    }

    // Create CSV content
    const headers = ["Site", "Worker Name", "Role", "Date", "Status", "Hours Worked", "Notes"]
    const csvRows = [headers]

    records.forEach((record) => {
      csvRows.push([
        siteName,
        record.worker_name,
        record.worker_role,
        record.date,
        record.status,
        record.hours_worked.toString(),
        record.notes || "",
      ])
    })

    const csvContent = csvRows.map((row) => row.join(",")).join("\n")

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")

    link.setAttribute("href", url)
    link.setAttribute("download", `${siteName}_${reportType}_attendance_${format(selectedDate, "yyyy-MM-dd")}.csv`)
    link.style.visibility = "hidden"

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const downloadPDF = () => {
    if (records.length === 0) {
      toast({
        title: "No Data",
        description: "There are no records to download.",
      })
      return
    }

    // Use dynamic import for jsPDF to avoid SSR issues
    import("jspdf")
      .then(({ default: jsPDF }) => {
        // Create PDF document
        const doc = new jsPDF()

        // Add title
        doc.setFontSize(16)
        doc.text(getReportTitle(), 14, 22)

        // Add site name
        doc.setFontSize(12)
        doc.text(`Site: ${siteName}`, 14, 30)

        // Add date generated
        doc.setFontSize(10)
        doc.text(`Generated on: ${format(new Date(), "PPP")}`, 14, 36)

        // Create table data
        const tableColumn = ["Name", "Role", "Date", "Status", "Hours", "Notes"]
        const tableRows = records.map((record) => [
          record.worker_name,
          record.worker_role,
          record.date,
          record.status,
          record.hours_worked.toString(),
          record.notes || "",
        ])

        // Use simple table creation instead of autoTable
        const startY = 40
        const cellPadding = 10
        const cellHeight = 10
        const pageWidth = doc.internal.pageSize.width
        const colWidth = pageWidth / tableColumn.length

        // Draw header
        doc.setFillColor(66, 66, 66)
        doc.setTextColor(255, 255, 255)
        doc.setFontSize(10)

        tableColumn.forEach((header, i) => {
          doc.rect(i * colWidth, startY, colWidth, cellHeight, "F")
          doc.text(header, i * colWidth + 2, startY + 7)
        })

        // Draw rows
        doc.setTextColor(0, 0, 0)
        doc.setFontSize(8)

        tableRows.forEach((row, rowIndex) => {
          const y = startY + (rowIndex + 1) * cellHeight

          // Check if we need a new page
          if (y > doc.internal.pageSize.height - 20) {
            doc.addPage()
            return // Skip this row and continue on new page
          }

          row.forEach((cell, colIndex) => {
            doc.rect(colIndex * colWidth, y, colWidth, cellHeight, "S")
            doc.text(String(cell).substring(0, 20), colIndex * colWidth + 2, y + 7)
          })
        })

        // Save the PDF
        doc.save(`${siteName}_${reportType}_attendance_${format(selectedDate, "yyyy-MM-dd")}.pdf`)
      })
      .catch((error) => {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to generate PDF. Please try again.",
        })
        console.error("PDF generation error:", error)
      })
  }

  return (
    <div className="space-y-6">
      <Tabs value={reportType} onValueChange={(value) => setReportType(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="daily">Daily Report</TabsTrigger>
          <TabsTrigger value="weekly">Weekly Report</TabsTrigger>
          <TabsTrigger value="monthly">Monthly Report</TabsTrigger>
        </TabsList>

        <div className="mt-4 flex flex-col gap-4 sm:flex-row sm:items-end">
          <div className="grid gap-2">
            <div className="text-sm font-medium">Select Date</div>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant={"outline"} className={cn("w-full justify-start text-left font-normal")}>
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setSelectedDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <Button onClick={fetchReports} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Generate Report"
            )}
          </Button>
        </div>
      </Tabs>

      {records.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>{getReportTitle()}</CardTitle>
              <CardDescription>{records.length} records found</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={downloadExcel}>
                <FileSpreadsheet className="mr-2 h-4 w-4" />
                Excel
              </Button>
              <Button variant="outline" onClick={downloadPDF}>
                <FilePdf className="mr-2 h-4 w-4" />
                PDF
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Worker Name</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Hours Worked</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {records.map((record) => (
                    <TableRow key={`${record.worker_id}-${record.date}`}>
                      <TableCell className="font-medium">{record.worker_name}</TableCell>
                      <TableCell>{record.worker_role}</TableCell>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        <span
                          className={cn(
                            "inline-block px-2 py-1 text-xs font-medium rounded-full",
                            record.status === "present" && "bg-green-100 text-green-800",
                            record.status === "absent" && "bg-red-100 text-red-800",
                            record.status === "late" && "bg-yellow-100 text-yellow-800",
                            record.status === "leave" && "bg-blue-100 text-blue-800",
                          )}
                        >
                          {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        </span>
                      </TableCell>
                      <TableCell>{record.hours_worked}</TableCell>
                      <TableCell className="max-w-xs truncate">{record.notes}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {isLoading && (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
        </div>
      )}

      {!isLoading && records.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          No attendance records found. Select a date and generate a report.
        </div>
      )}
    </div>
  )
}
