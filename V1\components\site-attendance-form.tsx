"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { CalendarIcon, Check, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"
import { useToast } from "@/components/ui/use-toast"
import { submitAttendance, checkAttendanceExists, type SiteMember } from "@/app/actions/site-management"

interface SiteAttendanceFormProps {
  siteId: string
  siteName: string
  members?: SiteMember[] // Make members optional
}

export function SiteAttendanceForm({ siteId, siteName, members = [] }: SiteAttendanceFormProps) {
  const [date, setDate] = useState<Date>(new Date())
  const [attendance, setAttendance] = useState<Record<string, boolean>>({})
  const [hoursWorked, setHoursWorked] = useState<Record<string, number>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [attendanceExists, setAttendanceExists] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const { toast } = useToast()

  // Update attendance state when members change
  useEffect(() => {
    if (members && members.length > 0) {
      setAttendance(Object.fromEntries(members.map((member) => [member.id, true])))
      setHoursWorked(Object.fromEntries(members.map((member) => [member.id, 8])))
    } else {
      setAttendance({})
      setHoursWorked({})
    }
    setIsLoading(false)
  }, [members])

  // Check if attendance already exists for this date
  useEffect(() => {
    const checkAttendance = async () => {
      if (date) {
        try {
          const exists = await checkAttendanceExists(siteId, format(date, "yyyy-MM-dd"))
          setAttendanceExists(exists)
        } catch (error) {
          console.error("Error checking attendance:", error)
          setAttendanceExists(false)
        }
      }
    }

    checkAttendance()
  }, [date, siteId])

  const handleAttendanceChange = (memberId: string, isPresent: boolean) => {
    setAttendance((prev) => ({
      ...prev,
      [memberId]: isPresent,
    }))

    // If not present, set hours to 0
    if (!isPresent) {
      setHoursWorked((prev) => ({
        ...prev,
        [memberId]: 0,
      }))
    } else {
      setHoursWorked((prev) => ({
        ...prev,
        [memberId]: 8, // Default to 8 hours when marked present
      }))
    }
  }

  const handleHoursChange = (memberId: string, hours: number) => {
    setHoursWorked((prev) => ({
      ...prev,
      [memberId]: hours,
    }))
  }

  const handleSubmit = async () => {
    if (!members || members.length === 0) {
      toast({
        variant: "destructive",
        title: "No members",
        description: "There are no members to record attendance for.",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const records = members.map((member) => ({
        site_id: siteId,
        worker_id: member.id,
        worker_name: member.name,
        worker_role: member.role || "Staff",
        date: format(date, "yyyy-MM-dd"),
        status: attendance[member.id] ? "present" : "absent",
        hours_worked: hoursWorked[member.id] || 0,
        notes: "",
      }))

      const result = await submitAttendance(records)

      if (result.success) {
        toast({
          title: "Attendance Submitted",
          description: `Attendance for ${siteName} on ${format(date, "PPP")} has been recorded.`,
        })
        setAttendanceExists(true)
      } else {
        throw new Error(result.error || "Failed to submit attendance")
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: (error as Error).message || "An error occurred while submitting attendance",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">{siteName} Attendance</h3>
          <p className="text-sm text-muted-foreground">Record daily attendance for site members</p>
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className={cn("w-[240px] justify-start text-left font-normal")}>
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "PPP") : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar mode="single" selected={date} onSelect={(date) => date && setDate(date)} initialFocus />
          </PopoverContent>
        </Popover>
      </div>

      {attendanceExists && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 text-amber-800">
          Attendance for this date has already been submitted. Submitting again will update the existing records.
        </div>
      )}

      {members.length === 0 ? (
        <div className="rounded-md border p-8 text-center">
          <p className="text-muted-foreground mb-4">No members found for this site.</p>
          <p className="text-sm">Please add members in the "Add/Remove Members" tab before submitting attendance.</p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Mobile Number</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Duty Time</TableHead>
                <TableHead>Present</TableHead>
                <TableHead>Hours Worked</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">{member.name}</TableCell>
                  <TableCell>{member.mobile_number}</TableCell>
                  <TableCell>{member.role || "Staff"}</TableCell>
                  <TableCell>{member.duty_time || "Standard"}</TableCell>
                  <TableCell>
                    <Checkbox
                      checked={attendance[member.id]}
                      onCheckedChange={(checked) => handleAttendanceChange(member.id, checked === true)}
                    />
                  </TableCell>
                  <TableCell>
                    <input
                      type="number"
                      min="0"
                      max="24"
                      step="0.5"
                      value={hoursWorked[member.id] || 0}
                      onChange={(e) => handleHoursChange(member.id, Number.parseFloat(e.target.value) || 0)}
                      disabled={!attendance[member.id]}
                      className="w-16 p-1 border rounded"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      <Button onClick={handleSubmit} disabled={isSubmitting || members.length === 0} className="w-full">
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Submitting...
          </>
        ) : (
          <>
            <Check className="mr-2 h-4 w-4" />
            Submit Attendance
          </>
        )}
      </Button>
    </div>
  )
}
