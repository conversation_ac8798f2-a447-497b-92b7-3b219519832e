import Link from "next/link"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"

export default function OfficeLocationsPage() {
  // Hardcoded offices as requested
  const offices = [
    { id: "back-office-brane", name: "Back Office - Brane" },
    { id: "strf-office-brane", name: "STRF Office - Brane" },
    { id: "road-no-36-srsr", name: "Road No. 36 office - SRSR" },
    { id: "back-office-srsr", name: "Back Office - SRSR" },
  ]

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office">Office</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/office/office">Office Locations</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <h1 className="mb-8 text-3xl font-bold">Office Locations</h1>

        <div className="grid gap-6 md:grid-cols-2">
          {offices.map((office) => (
            <Card key={office.id} className="h-full transition-all hover:shadow-md">
              <CardHeader>
                <CardTitle>{office.name}</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-end gap-2">
                <Button variant="outline" asChild>
                  <Link href={`/dashboard/office/office/${office.id}`}>Manage</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href={`/dashboard/office/office/${office.id}?tab=attendance`}>Attendance</Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
