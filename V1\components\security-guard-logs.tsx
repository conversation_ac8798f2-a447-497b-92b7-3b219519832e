"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  ChevronDown,
  ChevronRight,
  Clock,
  User,
  Calendar,
  AlertCircle,
  Database,
  Sun,
  Moon,
  LogOut,
  LogIn,
  Edit,
  Trash2,
  MoreHorizontal,
  Timer,
} from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  createOrUpdateSecurityGuardLog,
  getSecurityGuardLogs,
  getOpenEntry,
  testDatabaseConnection,
  formatIndividualAwayTime,
  getCurrentTime,
  getTodayDate,
  deleteSecurityGuardLog,
  getSecurityGuardLogById,
  type LogSummary,
  type OpenEntry,
  type SecurityGuardLog,
} from "@/app/actions/security-guard-logs"
import { useToast } from "@/hooks/use-toast"
import { EditSecurityLogDialog } from "./edit-security-log-dialog"

export function SecurityGuardLogs() {
  const params = useParams()
  const propertyId = params.property as string
  const { toast } = useToast()

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [logs, setLogs] = useState<LogSummary[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [openDates, setOpenDates] = useState<{ [key: string]: boolean }>({})
  const [selectedGuards, setSelectedGuards] = useState<{ [key: string]: string }>({})

  // Form state
  const [selectedGuard, setSelectedGuard] = useState("")
  const [selectedShift, setSelectedShift] = useState("")
  const [selectedDate, setSelectedDate] = useState(getTodayDate())
  const [openEntry, setOpenEntry] = useState<OpenEntry | null>(null)
  const [leftTime, setLeftTime] = useState("")
  const [returnTime, setReturnTime] = useState("")
  const [isCheckingOpenEntry, setIsCheckingOpenEntry] = useState(false)
  const [dbConnectionStatus, setDbConnectionStatus] = useState<string>("")

  // Edit/Delete state
  const [editingLog, setEditingLog] = useState<SecurityGuardLog | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [deletingLogId, setDeletingLogId] = useState<string | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  // Property-specific configurations
  const siteConfig = {
    "jublee-hills": {
      siteName: "Jubilee Hills",
      guards: ["Bhudev Kumar", "Sanjay Sha"],
    },
    "gandipet-guest-house": {
      siteName: "Gandipet Guest House",
      guards: ["Sunab Sayad", "Goutam Singh"],
    },
  }

  const config = siteConfig[propertyId as keyof typeof siteConfig] || siteConfig["jublee-hills"]

  const reasons = ["Washroom", "Gardening", "Pool cleaning", "Other reason"]
  const shifts = ["Morning", "Night"]

  useEffect(() => {
    loadLogs()
    testConnection()
  }, [propertyId])

  useEffect(() => {
    if (selectedGuard && selectedDate) {
      checkForOpenEntry()
    } else {
      setOpenEntry(null)
      setLeftTime("")
      setReturnTime("")
    }
  }, [selectedGuard, selectedDate])

  const testConnection = async () => {
    try {
      const result = await testDatabaseConnection()
      if (result.success) {
        setDbConnectionStatus("✅ Database connected")
      } else {
        setDbConnectionStatus(`❌ Database error: ${result.error}`)
      }
    } catch (error) {
      setDbConnectionStatus("❌ Database connection failed")
    }
  }

  const loadLogs = async () => {
    setIsLoading(true)
    try {
      const data = await getSecurityGuardLogs(config.siteName)
      setLogs(data)
    } catch (error) {
      console.error("Error loading logs:", error)
      toast({
        title: "Error",
        description: "Failed to load security guard logs",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const checkForOpenEntry = async () => {
    if (!selectedGuard || !selectedDate) return

    setIsCheckingOpenEntry(true)
    try {
      const entry = await getOpenEntry(config.siteName, selectedGuard, selectedDate)
      setOpenEntry(entry)

      if (entry) {
        setLeftTime(entry.in_time)
        setReturnTime(getCurrentTime())
        setSelectedShift(entry.shift)
      } else {
        setLeftTime(getCurrentTime())
        setReturnTime("")
      }
    } catch (error) {
      console.error("Error checking for open entry:", error)
    } finally {
      setIsCheckingOpenEntry(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const formData = new FormData(e.currentTarget)

      formData.append("site_name", config.siteName)
      formData.append("log_date", selectedDate)
      formData.append("guard_name", selectedGuard)
      formData.append("shift", selectedShift)
      formData.append("left_time", leftTime)
      if (returnTime) {
        formData.append("return_time", returnTime)
      }
      if (openEntry) {
        formData.append("entry_id", openEntry.id)
      }

      const result = await createOrUpdateSecurityGuardLog(formData)

      if (result.success) {
        toast({
          title: "Success",
          description: openEntry
            ? "Security guard return time recorded successfully"
            : "Security guard departure time recorded successfully",
        })

        // Reset form
        setSelectedGuard("")
        setSelectedShift("")
        setSelectedDate(getTodayDate())
        setOpenEntry(null)
        setLeftTime("")
        setReturnTime("")

        // Reload logs
        await loadLogs()
      } else {
        console.error("Submission failed:", result.error)
        toast({
          title: "Error",
          description: result.error || "Failed to record log entry",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Form submission error:", error)
      toast({
        title: "Error",
        description: "Failed to record log entry",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEdit = async (logId: string) => {
    try {
      const log = await getSecurityGuardLogById(logId)
      if (log) {
        setEditingLog(log)
        setIsEditDialogOpen(true)
      } else {
        toast({
          title: "Error",
          description: "Failed to load log entry for editing",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error loading log for edit:", error)
      toast({
        title: "Error",
        description: "Failed to load log entry for editing",
        variant: "destructive",
      })
    }
  }

  const handleDelete = (logId: string) => {
    setDeletingLogId(logId)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingLogId) return

    try {
      const result = await deleteSecurityGuardLog(deletingLogId)
      if (result.success) {
        toast({
          title: "Success",
          description: "Security guard log deleted successfully",
        })
        await loadLogs()
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to delete log entry",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Delete error:", error)
      toast({
        title: "Error",
        description: "Failed to delete log entry",
        variant: "destructive",
      })
    } finally {
      setIsDeleteDialogOpen(false)
      setDeletingLogId(null)
    }
  }

  const toggleDate = (date: string) => {
    setOpenDates((prev) => ({
      ...prev,
      [date]: !prev[date],
    }))
  }

  const handleGuardSelection = (date: string, guardName: string) => {
    setSelectedGuards((prev) => ({
      ...prev,
      [date]: guardName,
    }))
  }

  const getGuardLogsForDate = (logs: LogSummary["logs"], guardName: string) => {
    return logs.filter((log) => log.guard_name === guardName)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":")
    const hour = Number.parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const canSubmitNewEntry = !openEntry || (openEntry && returnTime)
  const isRecordingReturn = !!openEntry

  return (
    <div className="space-y-6">
      {/* Database Status */}
      <Alert>
        <Database className="h-4 w-4" />
        <AlertDescription>{dbConnectionStatus}</AlertDescription>
      </Alert>

      {/* Log Entry Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Security Guard Log Entry
          </CardTitle>
          <CardDescription>Record when guards leave and return to their posts at {config.siteName}</CardDescription>
        </CardHeader>
        <CardContent>
          {openEntry && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {openEntry.guard_name} left their post but hasn't returned yet. Please record their return time.
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="log_date">Date</Label>
                <Input
                  id="log_date"
                  name="log_date"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="guard_name">Security Guard Name</Label>
                <Select
                  name="guard_name"
                  value={selectedGuard}
                  onValueChange={setSelectedGuard}
                  required
                  disabled={isCheckingOpenEntry}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select guard" />
                  </SelectTrigger>
                  <SelectContent>
                    {config.guards.map((guard) => (
                      <SelectItem key={guard} value={guard}>
                        {guard}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shift">Shift</Label>
                <Select
                  name="shift"
                  value={selectedShift}
                  onValueChange={setSelectedShift}
                  required
                  disabled={isRecordingReturn}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select shift" />
                  </SelectTrigger>
                  <SelectContent>
                    {shifts.map((shift) => (
                      <SelectItem key={shift} value={shift}>
                        <div className="flex items-center gap-2">
                          {shift === "Morning" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                          {shift} ({shift === "Morning" ? "8:00 AM - 8:00 PM" : "8:00 PM - 8:00 AM"})
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="left_time" className="flex items-center gap-2">
                  <LogOut className="h-4 w-4 text-red-500" />
                  Left Post Time
                </Label>
                <Input
                  id="left_time"
                  name="left_time"
                  type="time"
                  value={leftTime}
                  onChange={(e) => setLeftTime(e.target.value)}
                  required
                  readOnly={isRecordingReturn}
                  className={isRecordingReturn ? "bg-gray-100" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="return_time" className="flex items-center gap-2">
                  <LogIn className="h-4 w-4 text-green-500" />
                  Returned to Post Time
                </Label>
                <Input
                  id="return_time"
                  name="return_time"
                  type="time"
                  value={returnTime}
                  onChange={(e) => setReturnTime(e.target.value)}
                  required={isRecordingReturn}
                  disabled={!isRecordingReturn && !selectedGuard}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Reason for Leaving Post</Label>
                <Select name="reason" required={isRecordingReturn} disabled={!isRecordingReturn}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent>
                    {reasons.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {reason}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="replacement">Replacement Details</Label>
              <Textarea
                id="replacement"
                name="replacement"
                placeholder="Who covered the post while away..."
                className="min-h-[80px]"
                disabled={!isRecordingReturn}
              />
            </div>

            <Button
              type="submit"
              disabled={isSubmitting || !canSubmitNewEntry || isCheckingOpenEntry || !selectedGuard || !selectedShift}
              className="w-full"
            >
              {isSubmitting ? "Recording..." : isRecordingReturn ? "Record Return Time" : "Record Departure Time"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Date-wise Summary Viewer */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Away Time Summary by Date
          </CardTitle>
          <CardDescription>View completed away periods organized by date and shift</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading logs...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No completed away periods found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((logGroup) => (
                <Collapsible
                  key={logGroup.log_date}
                  open={openDates[logGroup.log_date]}
                  onOpenChange={() => toggleDate(logGroup.log_date)}
                >
                  <CollapsibleTrigger asChild>
                    <Button variant="outline" className="w-full justify-between p-4 h-auto">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium">{formatDate(logGroup.log_date)}</span>
                        <span className="text-sm text-muted-foreground">
                          ({logGroup.logs.length} {logGroup.logs.length === 1 ? "period" : "periods"})
                        </span>
                      </div>
                      {openDates[logGroup.log_date] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>

                  <CollapsibleContent className="mt-4">
                    <div className="border rounded-lg p-4 space-y-4">
                      <div className="space-y-2">
                        <Label>Select Security Guard</Label>
                        <Select
                          value={selectedGuards[logGroup.log_date] || ""}
                          onValueChange={(value) => handleGuardSelection(logGroup.log_date, value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select guard to view away periods" />
                          </SelectTrigger>
                          <SelectContent>
                            {config.guards.map((guard) => (
                              <SelectItem key={guard} value={guard}>
                                {guard}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {selectedGuards[logGroup.log_date] && (
                        <div className="space-y-4">
                          {(() => {
                            const guardLogs = getGuardLogsForDate(logGroup.logs, selectedGuards[logGroup.log_date])

                            const morningLogs = guardLogs.filter((log) => log.shift === "Morning")
                            const nightLogs = guardLogs.filter((log) => log.shift === "Night")

                            // Calculate total away minutes by summing individual away_minutes
                            const morningAwayMinutes = morningLogs.reduce(
                              (total, log) => total + (log.away_minutes || 0),
                              0,
                            )
                            const nightAwayMinutes = nightLogs.reduce(
                              (total, log) => total + (log.away_minutes || 0),
                              0,
                            )

                            return (
                              <>
                                {guardLogs.length > 0 ? (
                                  <>
                                    {/* Morning Shift Logs */}
                                    {morningLogs.length > 0 && (
                                      <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                          <Sun className="h-4 w-4 text-orange-500" />
                                          <h4 className="font-medium">Morning Shift (8:00 AM - 8:00 PM)</h4>
                                          <Badge variant="outline">{morningLogs.length} periods</Badge>
                                        </div>
                                        {/* Cumulative Away Time Display - FIXED */}
                                        <div className="bg-orange-50 border border-orange-200 rounded-md p-3 mb-3">
                                          <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                              <Clock className="h-4 w-4 text-orange-600" />
                                              <span className="font-medium text-orange-800">
                                                Total Away Time: {morningAwayMinutes || 0} minutes
                                              </span>
                                            </div>
                                            {morningAwayMinutes > 90 && (
                                              <div className="flex items-center gap-2 text-red-600">
                                                <AlertCircle className="h-4 w-4" />
                                                <span className="text-sm font-medium">
                                                  ⚠️ Away time exceeds permitted limit (90 mins). Please verify the logs.
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                        {morningLogs.map((log) => (
                                          <div key={log.id} className="border rounded-md p-3 bg-orange-50">
                                            <div className="flex justify-between items-start">
                                              <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm flex-1">
                                                <div className="flex items-center gap-1">
                                                  <LogOut className="h-3 w-3 text-red-500" />
                                                  <span className="font-medium">Left:</span> {formatTime(log.out_time)}
                                                </div>
                                                <div className="flex items-center gap-1">
                                                  <LogIn className="h-3 w-3 text-green-500" />
                                                  <span className="font-medium">Returned:</span>{" "}
                                                  {log.in_time ? formatTime(log.in_time) : "N/A"}
                                                </div>
                                                <div className="flex items-center gap-1">
                                                  <Timer className="h-3 w-3 text-blue-500" />
                                                  <span className="font-medium">Duration:</span>{" "}
                                                  <Badge variant="secondary">
                                                    {formatIndividualAwayTime(log.away_minutes)}
                                                  </Badge>
                                                </div>
                                                <div>
                                                  <span className="font-medium">Reason:</span> {log.reason || "N/A"}
                                                </div>
                                                <div>
                                                  <span className="font-medium">Replacement:</span>{" "}
                                                  {log.replacement || "N/A"}
                                                </div>
                                              </div>
                                              <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                  <Button variant="ghost" size="sm">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                  </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                  <DropdownMenuItem onClick={() => handleEdit(log.id)}>
                                                    <Edit className="h-4 w-4 mr-2" />
                                                    Edit
                                                  </DropdownMenuItem>
                                                  <DropdownMenuItem
                                                    onClick={() => handleDelete(log.id)}
                                                    className="text-red-600"
                                                  >
                                                    <Trash2 className="h-4 w-4 mr-2" />
                                                    Delete
                                                  </DropdownMenuItem>
                                                </DropdownMenuContent>
                                              </DropdownMenu>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    )}

                                    {/* Night Shift Logs */}
                                    {nightLogs.length > 0 && (
                                      <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                          <Moon className="h-4 w-4 text-blue-500" />
                                          <h4 className="font-medium">Night Shift (8:00 PM - 8:00 AM)</h4>
                                          <Badge variant="outline">{nightLogs.length} periods</Badge>
                                        </div>
                                        {/* Cumulative Away Time Display - FIXED */}
                                        <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3">
                                          <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                              <Clock className="h-4 w-4 text-blue-600" />
                                              <span className="font-medium text-blue-800">
                                                Total Away Time: {nightAwayMinutes || 0} minutes
                                              </span>
                                            </div>
                                            {nightAwayMinutes > 90 && (
                                              <div className="flex items-center gap-2 text-red-600">
                                                <AlertCircle className="h-4 w-4" />
                                                <span className="text-sm font-medium">
                                                  ⚠️ Away time exceeds permitted limit (90 mins). Please verify the logs.
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                        {nightLogs.map((log) => (
                                          <div key={log.id} className="border rounded-md p-3 bg-blue-50">
                                            <div className="flex justify-between items-start">
                                              <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm flex-1">
                                                <div className="flex items-center gap-1">
                                                  <LogOut className="h-3 w-3 text-red-500" />
                                                  <span className="font-medium">Left:</span> {formatTime(log.out_time)}
                                                </div>
                                                <div className="flex items-center gap-1">
                                                  <LogIn className="h-3 w-3 text-green-500" />
                                                  <span className="font-medium">Returned:</span>{" "}
                                                  {log.in_time ? formatTime(log.in_time) : "N/A"}
                                                </div>
                                                <div className="flex items-center gap-1">
                                                  <Timer className="h-3 w-3 text-blue-500" />
                                                  <span className="font-medium">Duration:</span>{" "}
                                                  <Badge variant="secondary">
                                                    {formatIndividualAwayTime(log.away_minutes)}
                                                  </Badge>
                                                </div>
                                                <div>
                                                  <span className="font-medium">Reason:</span> {log.reason || "N/A"}
                                                </div>
                                                <div>
                                                  <span className="font-medium">Replacement:</span>{" "}
                                                  {log.replacement || "N/A"}
                                                </div>
                                              </div>
                                              <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                  <Button variant="ghost" size="sm">
                                                    <MoreHorizontal className="h-4 w-4" />
                                                  </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                  <DropdownMenuItem onClick={() => handleEdit(log.id)}>
                                                    <Edit className="h-4 w-4 mr-2" />
                                                    Edit
                                                  </DropdownMenuItem>
                                                  <DropdownMenuItem
                                                    onClick={() => handleDelete(log.id)}
                                                    className="text-red-600"
                                                  >
                                                    <Trash2 className="h-4 w-4 mr-2" />
                                                    Delete
                                                  </DropdownMenuItem>
                                                </DropdownMenuContent>
                                              </DropdownMenu>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </>
                                ) : (
                                  <p className="text-muted-foreground text-sm">
                                    No completed away periods found for {selectedGuards[logGroup.log_date]} on this date
                                  </p>
                                )}
                              </>
                            )
                          })()}
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <EditSecurityLogDialog
        log={editingLog}
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false)
          setEditingLog(null)
        }}
        onSuccess={loadLogs}
        guards={config.guards}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Security Guard Log</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this log entry? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
