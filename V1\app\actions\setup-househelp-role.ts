"use server"

import { createServerClient } from "@/lib/supabase"
import { revalidatePath } from "next/cache"

export async function setupHousehelpRole() {
  const supabase = createServerClient()
  const results = {
    roleCreated: false,
    permissionsCreated: [] as string[],
    errors: [] as string[],
  }

  try {
    // Check if role already exists
    const { data: existingRole } = await supabase.from("roles").select("id").eq("name", "househelp").single()

    let roleId: number

    if (existingRole) {
      roleId = existingRole.id
      results.roleCreated = false
    } else {
      // Create the Househelp role
      const { data: newRole, error: roleError } = await supabase
        .from("roles")
        .insert({
          name: "househelp",
          description: "Staff with access only to Jublee Hills property",
        })
        .select("id")
        .single()

      if (roleError) {
        results.errors.push(`Failed to create role: ${roleError.message}`)
        return results
      }

      roleId = newRole.id
      results.roleCreated = true
    }

    // Define required permissions
    const permissions = ["/dashboard/home/<USER>", "/dashboard", "/dashboard/layout"]

    // Create permissions
    for (const urlPattern of permissions) {
      // Check if permission already exists
      const { data: existingPermission } = await supabase
        .from("permissions")
        .select("id")
        .eq("url_pattern", urlPattern)
        .eq("role_id", roleId)
        .single()

      if (existingPermission) {
        results.permissionsCreated.push(`Permission for ${urlPattern} already exists`)
        continue
      }

      const { error: permissionError } = await supabase.from("permissions").insert({
        url_pattern: urlPattern,
        role_id: roleId,
      })

      if (permissionError) {
        results.errors.push(`Failed to create permission for ${urlPattern}: ${permissionError.message}`)
      } else {
        results.permissionsCreated.push(urlPattern)
      }
    }

    // Revalidate admin pages
    revalidatePath("/admin/roles")
    revalidatePath("/admin/permissions")

    return results
  } catch (error) {
    results.errors.push(`Unexpected error: ${error instanceof Error ? error.message : String(error)}`)
    return results
  }
}
