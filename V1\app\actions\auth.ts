"use server"

import { createServer<PERSON><PERSON> } from "@/lib/supabase"
import { hashPassword, verifyPassword, createSession } from "@/lib/auth"
import { revalidatePath } from "next/cache"

// Register a new user
export async function register({
  username,
  password,
  fullName,
}: {
  username: string
  password: string
  fullName: string
}) {
  const supabase = createServerClient()

  // Check if username already exists
  const { data: existingUser } = await supabase.from("users").select("id").eq("username", username).single()

  if (existingUser) {
    return { success: false, error: "Username already exists" }
  }

  // Hash password
  const hashedPassword = await hashPassword(password)

  // Insert new user
  const { data, error } = await supabase
    .from("users")
    .insert({
      username,
      password: hashedPassword,
      full_name: fullName,
      email: `${username}@example.com`, // Placeholder email
      role: "user", // Default role in the users table
      is_active: false, // User needs to be activated by admin
    })
    .select()

  if (error) {
    console.error("Registration error:", error)
    return { success: false, error: "Failed to register user" }
  }

  return { success: true, userId: data[0].id }
}

// Login user
export async function login({
  username,
  password,
}: {
  username: string
  password: string
}) {
  const supabase = createServerClient()

  // Get user by username
  const { data: user, error } = await supabase.from("users").select("*").eq("username", username).single()

  if (error || !user) {
    return { success: false, error: "Invalid username or password" }
  }

  // Check if user is active
  if (!user.is_active) {
    return { success: false, error: "Your account is not active. Please contact an administrator." }
  }

  // Verify password
  const isPasswordValid = await verifyPassword(password, user.password)
  if (!isPasswordValid) {
    return { success: false, error: "Invalid username or password" }
  }

  // Create session
  await createSession(user.id)

  revalidatePath("/dashboard")
  return { success: true }
}

// Logout user
export async function logout() {
  const supabase = createServerClient()

  // This is handled in the auth.ts library
  // We just need to revalidate the path
  revalidatePath("/")
  return { success: true }
}
