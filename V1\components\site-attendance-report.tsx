"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { FileDown, Clock } from "lucide-react"
import type { AttendanceRecord } from "@/app/actions/site-attendance"

interface SiteAttendanceReportProps {
  attendanceData: AttendanceRecord[]
  isLoading?: boolean
}

export function SiteAttendanceReport({ attendanceData, isLoading = false }: SiteAttendanceReportProps) {
  const [groupedByDate, setGroupedByDate] = useState(true)

  const formatDate = (dateString: string) => {
    if (!dateString) return "-"
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const toggleGrouping = () => {
    setGroupedByDate(!groupedByDate)
  }

  // Group attendance data by date
  const groupByDate = (data: AttendanceRecord[]) => {
    const grouped: Record<string, AttendanceRecord[]> = {}

    data.forEach((record) => {
      if (!grouped[record.date]) {
        grouped[record.date] = []
      }
      grouped[record.date].push(record)
    })

    return grouped
  }

  // Group attendance data by worker
  const groupByWorker = (data: AttendanceRecord[]) => {
    const grouped: Record<string, AttendanceRecord[]> = {}

    data.forEach((record) => {
      if (!grouped[record.worker_id]) {
        grouped[record.worker_id] = []
      }
      grouped[record.worker_id].push(record)
    })

    return grouped
  }

  const downloadCSV = () => {
    // Create CSV content
    let csvContent = "Date,Worker ID,Worker Name,Role,Status,Hours Worked,Notes\n"

    attendanceData.forEach((record) => {
      csvContent += `${record.date},${record.worker_id},"${record.worker_name}","${record.worker_role}","${record.status}",${record.hours_worked},"${record.notes || ""}"\n`
    })

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `attendance-report-${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Attendance Report</CardTitle>
          <CardDescription>Loading attendance data...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center p-8">
          <Clock className="h-8 w-8 animate-spin text-slate-400" />
        </CardContent>
      </Card>
    )
  }

  if (!attendanceData || attendanceData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Attendance Report</CardTitle>
          <CardDescription>No attendance data found for the selected period</CardDescription>
        </CardHeader>
        <CardContent className="text-center p-8">
          <p className="text-slate-500">No records available</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Attendance Report</CardTitle>
          <CardDescription>{attendanceData.length} records found</CardDescription>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={toggleGrouping}>
            Group by {groupedByDate ? "Worker" : "Date"}
          </Button>
          <Button onClick={downloadCSV}>
            <FileDown className="mr-2 h-4 w-4" />
            Download CSV
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {groupedByDate ? (
          // Group by date view
          <div className="space-y-6">
            {Object.entries(groupByDate(attendanceData)).map(([date, records]) => (
              <div key={date} className="rounded-md border">
                <div className="bg-slate-50 px-4 py-2 font-medium">
                  {formatDate(date)} ({records.length} workers)
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Worker Name</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Hours Worked</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {records.map((record) => (
                      <TableRow key={`${record.date}-${record.worker_id}`}>
                        <TableCell className="font-medium">{record.worker_name}</TableCell>
                        <TableCell>{record.worker_role}</TableCell>
                        <TableCell>{record.status}</TableCell>
                        <TableCell>{record.hours_worked}</TableCell>
                        <TableCell>{record.notes}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ))}
          </div>
        ) : (
          // Group by worker view
          <div className="space-y-6">
            {Object.entries(groupByWorker(attendanceData)).map(([workerId, records]) => (
              <div key={workerId} className="rounded-md border">
                <div className="bg-slate-50 px-4 py-2 font-medium">
                  {records[0].worker_name} - {records[0].worker_role} ({records.length} days)
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Hours Worked</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {records.map((record) => (
                      <TableRow key={`${record.worker_id}-${record.date}`}>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell>{record.status}</TableCell>
                        <TableCell>{record.hours_worked}</TableCell>
                        <TableCell>{record.notes}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
