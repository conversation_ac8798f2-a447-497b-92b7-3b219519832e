import '../../../shared/models/property.dart';
import '../../../shared/models/property_service.dart';

abstract class PropertiesRepository {
  Future<List<Property>> getProperties();
  Future<Property> getPropertyById(String id);
  Future<Property> createProperty(Property property);
  Future<Property> updateProperty(Property property);
  Future<void> deleteProperty(String id);
  
  Future<List<PropertyService>> getPropertyServices(String propertyId);
  Future<PropertyService> addPropertyService(String propertyId, PropertyService service);
  Future<PropertyService> updatePropertyService(String propertyId, PropertyService service);
  Future<void> deletePropertyService(String propertyId, String serviceId);
}
