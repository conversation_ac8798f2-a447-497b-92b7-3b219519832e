import { NextResponse } from "next/server"
import { checkEscalations } from "@/app/actions/maintenance-issues"

export async function GET() {
  try {
    await checkEscalations()
    return NextResponse.json({ success: true, message: "Escalation check completed successfully" })
  } catch (error) {
    console.error("Error checking escalations:", error)
    return NextResponse.json({ success: false, error: "Failed to check escalations" }, { status: 500 })
  }
}
