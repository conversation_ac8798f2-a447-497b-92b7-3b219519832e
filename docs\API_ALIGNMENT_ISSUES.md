# API Alignment Issues with Database Schema

## 🚨 **Critical Misalignments Found**

After analyzing the backend code, entities, SQL schema, mappings, and API endpoints, several critical misalignments have been identified between the database structure and the API implementation.

## **1. Missing API Endpoints for New Entities**

### ❌ **Missing Endpoints:**
The comprehensive seeding added entities from database.sql, but the following API endpoints are **completely missing**:

1. **Function Processes** - No API endpoints
   - Should have: `GET /api/function-processes`, `POST /api/function-processes`
   - Should have: `GET /api/function-processes/{id}`, `PUT /api/function-processes/{id}`

2. **Function Process Logs** - No API endpoints  
   - Should have: `GET /api/function-processes/{id}/logs`
   - Should have: `POST /api/function-processes/{id}/logs`

3. **Threshold Configurations** - No API endpoints
   - Should have: `GET /api/thresholds`, `POST /api/thresholds`
   - Should have: `GET /api/thresholds/{id}`, `PUT /api/thresholds/{id}`

4. **Service Status Logs** - No API endpoints
   - Should have: `GET /api/properties/{id}/service-status-logs`
   - Should have: `POST /api/properties/{id}/service-status-logs`

5. **Escalation Logs** - No API endpoints
   - Should have: `GET /api/maintenance/{id}/escalations`
   - Should have: `POST /api/maintenance/{id}/escalations`

### ✅ **Existing Endpoints:**
- Properties ✅
- Offices ✅ (but with validation issues)
- Sites ✅ (via attendance endpoints)
- Generator Fuel Logs ✅
- Diesel Additions ✅
- OTT Services ✅
- Uptime Reports ✅
- Maintenance Issues ✅

## **2. Business Logic Validation Missing**

### ❌ **Office Creation Validation Issue:**
The `/api/offices` POST endpoint **does NOT validate** property type:

```typescript
// Current code - WRONG
const property = await prisma.property.findUnique({
  where: { id: property_id },
});

if (!property) {
  return Response.json(/* Property not found */);
}

// Missing validation: property.type should be 'OFFICE'
```

### ✅ **Should Be:**
```typescript
// Verify property exists AND is office type
const property = await prisma.property.findUnique({
  where: { id: property_id },
});

if (!property) {
  return Response.json(/* Property not found */);
}

if (property.type !== 'OFFICE') {
  return Response.json(/* Only office properties can have offices */);
}
```

## **3. API Response Format Inconsistencies**

### **Property Type Casing:**
- Database schema: `PropertyType.RESIDENTIAL`, `PropertyType.OFFICE`
- API responses: `type: office.property.type.toLowerCase()` (converts to lowercase)
- Frontend expects: Consistent casing

### **Date Format Inconsistencies:**
- Some endpoints return `created_at`, others return `createdAt`
- Some use ISO strings, others use Date objects

## **4. Missing Relationship Validations**

### **Site Creation Validation:**
- Sites should only be created under offices
- No validation exists to ensure office belongs to office-type property

### **Member Assignment Validation:**
- Office members should only be assigned to office-type properties
- Site members should only be assigned to construction sites under offices

## **5. Prisma Schema vs API Mapping Issues**

### **Field Name Mappings:**
```typescript
// Prisma Schema
model Property {
  isActive Boolean @map("is_active")
  createdAt DateTime @map("created_at")
}

// API Response - Inconsistent
{
  is_active: property.isActive,  // ✅ Correct mapping
  created_at: property.createdAt // ✅ Correct mapping
}
```

## **6. Missing Query Capabilities**

### **Properties API:**
- ✅ Has type filtering: `?type=residential|office`
- ❌ Missing status filtering by service status
- ❌ Missing search by name/address

### **Offices API:**
- ✅ Has property_id filtering
- ❌ Missing capacity filtering
- ❌ Missing active/inactive filtering

### **Missing Aggregation Endpoints:**
- No dashboard summary endpoints for new entities
- No statistics endpoints for function processes
- No threshold breach reporting endpoints

## **7. Authentication & Authorization Gaps**

### **Missing Permission Checks:**
- Function processes should require admin permissions
- Threshold configurations should require admin permissions
- Service status logs should have read-only access for most users

### **Role-Based Access:**
- Some endpoints missing proper role validation
- No granular permissions for new entities

## **8. Frontend API Service Alignment**

### **Flutter Frontend Expects:**
```dart
// From frontend/lib/features/admin/data/threshold_api_service.dart
@GET('/api/thresholds')
Future<ApiResponse<List<ThresholdConfig>>> getThresholds();

@POST('/api/thresholds')
Future<ApiResponse<ThresholdConfig>> createThreshold();
```

### **Backend Reality:**
- ❌ `/api/thresholds` endpoints don't exist
- ❌ ThresholdConfig model not exposed via API

## **9. Database Seeding vs API Capability Gap**

### **Seeding Creates:**
- 4 Function Processes + 4 Process Logs
- 8 Threshold Configurations  
- Office/Site member relationships
- Attendance records with proper hierarchy

### **API Cannot:**
- ❌ Retrieve function processes
- ❌ Manage threshold configurations
- ❌ Validate property-office-site hierarchy
- ❌ Enforce business rules from database.sql

## **10. Required Fixes**

### **Immediate Priority:**
1. **Add property type validation** to office creation
2. **Create missing API endpoints** for new entities
3. **Add business logic validation** for all relationships
4. **Standardize response formats** across all endpoints

### **Medium Priority:**
1. Add comprehensive query capabilities
2. Implement proper aggregation endpoints
3. Add missing permission checks
4. Standardize field naming conventions

### **Low Priority:**
1. Add advanced filtering options
2. Implement caching for frequently accessed data
3. Add API versioning for future changes

## **Impact Assessment**

### **Current State:**
- ❌ Frontend cannot access 40% of seeded data
- ❌ Business rules not enforced at API level
- ❌ Data integrity can be violated via API
- ❌ Inconsistent API behavior across endpoints

### **After Fixes:**
- ✅ Complete API coverage for all entities
- ✅ Business rules enforced at API level
- ✅ Data integrity maintained
- ✅ Consistent API behavior

The backend needs significant API development to align with the comprehensive database schema and seeding data!
