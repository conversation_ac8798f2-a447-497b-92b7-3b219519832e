/**
 * Attendance analytics and business logic
 * Enhanced version combining V1's business rules with Backend's data processing
 */

import { prisma } from '@/lib/prisma';

export interface AttendanceMetrics {
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  halfDays: number;
  attendancePercentage: number;
  averageHoursWorked: number;
  totalHoursWorked: number;
}

export interface AttendanceAnalytics {
  userId: string;
  userName: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  metrics: AttendanceMetrics;
  trends: {
    weeklyAttendance: number[];
    monthlyComparison: number;
    performanceRating: 'excellent' | 'good' | 'average' | 'poor';
  };
  alerts: string[];
}

export interface SiteAttendanceSummary {
  siteId: string;
  siteName: string;
  totalWorkers: number;
  averageAttendance: number;
  topPerformers: Array<{
    userId: string;
    userName: string;
    attendancePercentage: number;
  }>;
  attendanceIssues: Array<{
    userId: string;
    userName: string;
    issueType: 'frequent_absence' | 'frequent_late' | 'low_hours';
    severity: 'low' | 'medium' | 'high';
  }>;
}

/**
 * Calculate comprehensive attendance metrics for a user
 */
export async function calculateUserAttendanceMetrics(
  userId: string,
  startDate: Date,
  endDate: Date,
  siteId?: string
): Promise<AttendanceMetrics> {
  const whereClause: any = {
    userId,
    date: {
      gte: startDate,
      lte: endDate,
    },
  };

  if (siteId) {
    whereClause.siteId = siteId;
  }

  // Get all attendance records for the period
  const attendanceRecords = await prisma.siteAttendance.findMany({
    where: whereClause,
    orderBy: {
      date: 'asc',
    },
  });

  // Calculate metrics
  const totalDays = attendanceRecords.length;
  let presentDays = 0;
  let absentDays = 0;
  let lateDays = 0;
  let halfDays = 0;
  let totalHoursWorked = 0;

  for (const record of attendanceRecords) {
    if (record.checkInTime && record.checkOutTime) {
      presentDays++;
      
      // Check if late (assuming work starts at 9:00 AM)
      const checkInTime = new Date(`1970-01-01T${record.checkInTime}`);
      const standardStartTime = new Date('1970-01-01T09:00:00');
      
      if (checkInTime > standardStartTime) {
        lateDays++;
      }

      // Calculate hours worked
      if (record.hoursWorked) {
        const hours = parseFloat(record.hoursWorked.toString());
        totalHoursWorked += hours;
        
        // Check for half day (less than 4 hours)
        if (hours < 4) {
          halfDays++;
        }
      }
    } else {
      absentDays++;
    }
  }

  const attendancePercentage = totalDays > 0 ? (presentDays / totalDays) * 100 : 0;
  const averageHoursWorked = presentDays > 0 ? totalHoursWorked / presentDays : 0;

  return {
    totalDays,
    presentDays,
    absentDays,
    lateDays,
    halfDays,
    attendancePercentage: Math.round(attendancePercentage * 100) / 100,
    averageHoursWorked: Math.round(averageHoursWorked * 100) / 100,
    totalHoursWorked: Math.round(totalHoursWorked * 100) / 100,
  };
}

/**
 * Generate comprehensive attendance analytics for a user
 */
export async function generateUserAttendanceAnalytics(
  userId: string,
  startDate: Date,
  endDate: Date,
  siteId?: string
): Promise<AttendanceAnalytics> {
  // Get user information
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { fullName: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  // Calculate current period metrics
  const metrics = await calculateUserAttendanceMetrics(userId, startDate, endDate, siteId);

  // Calculate weekly attendance trend
  const weeklyAttendance = await calculateWeeklyAttendanceTrend(userId, startDate, endDate, siteId);

  // Calculate monthly comparison (current vs previous month)
  const previousMonthStart = new Date(startDate);
  previousMonthStart.setMonth(previousMonthStart.getMonth() - 1);
  const previousMonthEnd = new Date(endDate);
  previousMonthEnd.setMonth(previousMonthEnd.getMonth() - 1);

  const previousMetrics = await calculateUserAttendanceMetrics(
    userId, 
    previousMonthStart, 
    previousMonthEnd, 
    siteId
  );

  const monthlyComparison = metrics.attendancePercentage - previousMetrics.attendancePercentage;

  // Determine performance rating
  const performanceRating = getPerformanceRating(metrics.attendancePercentage, metrics.averageHoursWorked);

  // Generate alerts
  const alerts = generateAttendanceAlerts(metrics);

  return {
    userId,
    userName: user.fullName,
    period: { startDate, endDate },
    metrics,
    trends: {
      weeklyAttendance,
      monthlyComparison: Math.round(monthlyComparison * 100) / 100,
      performanceRating,
    },
    alerts,
  };
}

/**
 * Calculate weekly attendance trend
 */
async function calculateWeeklyAttendanceTrend(
  userId: string,
  startDate: Date,
  endDate: Date,
  siteId?: string
): Promise<number[]> {
  const weeklyAttendance: number[] = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    const weekStart = new Date(currentDate);
    const weekEnd = new Date(currentDate);
    weekEnd.setDate(weekEnd.getDate() + 6);

    if (weekEnd > endDate) {
      weekEnd.setTime(endDate.getTime());
    }

    const weekMetrics = await calculateUserAttendanceMetrics(userId, weekStart, weekEnd, siteId);
    weeklyAttendance.push(weekMetrics.attendancePercentage);

    currentDate.setDate(currentDate.getDate() + 7);
  }

  return weeklyAttendance;
}

/**
 * Determine performance rating based on attendance metrics
 */
function getPerformanceRating(
  attendancePercentage: number,
  averageHoursWorked: number
): 'excellent' | 'good' | 'average' | 'poor' {
  if (attendancePercentage >= 95 && averageHoursWorked >= 8) {
    return 'excellent';
  } else if (attendancePercentage >= 90 && averageHoursWorked >= 7) {
    return 'good';
  } else if (attendancePercentage >= 80 && averageHoursWorked >= 6) {
    return 'average';
  } else {
    return 'poor';
  }
}

/**
 * Generate attendance alerts based on metrics
 */
function generateAttendanceAlerts(metrics: AttendanceMetrics): string[] {
  const alerts: string[] = [];

  if (metrics.attendancePercentage < 80) {
    alerts.push('Low attendance rate - below 80%');
  }

  if (metrics.lateDays > metrics.totalDays * 0.3) {
    alerts.push('Frequent late arrivals - more than 30% of days');
  }

  if (metrics.averageHoursWorked < 6) {
    alerts.push('Low average working hours - less than 6 hours per day');
  }

  if (metrics.absentDays > metrics.totalDays * 0.2) {
    alerts.push('High absence rate - more than 20% of days');
  }

  return alerts;
}

/**
 * Generate site attendance summary
 */
export async function generateSiteAttendanceSummary(
  siteId: string,
  startDate: Date,
  endDate: Date
): Promise<SiteAttendanceSummary> {
  // Get site information
  const site = await prisma.site.findUnique({
    where: { id: siteId },
    select: { name: true },
  });

  if (!site) {
    throw new Error('Site not found');
  }

  // Get all users who have attendance records for this site
  const userAttendance = await prisma.siteAttendance.findMany({
    where: {
      siteId,
      date: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      user: {
        select: {
          id: true,
          fullName: true,
        },
      },
    },
  });

  // Group by user
  const userMap = new Map<string, any[]>();
  userAttendance.forEach(record => {
    const userId = record.userId;
    if (!userMap.has(userId)) {
      userMap.set(userId, []);
    }
    userMap.get(userId)!.push(record);
  });

  const totalWorkers = userMap.size;
  const userMetrics: Array<{
    userId: string;
    userName: string;
    attendancePercentage: number;
    metrics: AttendanceMetrics;
  }> = [];

  // Calculate metrics for each user
  for (const [userId, records] of userMap) {
    const metrics = await calculateUserAttendanceMetrics(userId, startDate, endDate, siteId);
    const userName = records[0].user.fullName;
    
    userMetrics.push({
      userId,
      userName,
      attendancePercentage: metrics.attendancePercentage,
      metrics,
    });
  }

  // Calculate average attendance
  const averageAttendance = userMetrics.length > 0
    ? userMetrics.reduce((sum, user) => sum + user.attendancePercentage, 0) / userMetrics.length
    : 0;

  // Get top performers (top 3 by attendance percentage)
  const topPerformers = userMetrics
    .sort((a, b) => b.attendancePercentage - a.attendancePercentage)
    .slice(0, 3)
    .map(user => ({
      userId: user.userId,
      userName: user.userName,
      attendancePercentage: user.attendancePercentage,
    }));

  // Identify attendance issues
  const attendanceIssues = userMetrics
    .filter(user => {
      const metrics = user.metrics;
      return (
        metrics.attendancePercentage < 80 ||
        metrics.lateDays > metrics.totalDays * 0.3 ||
        metrics.averageHoursWorked < 6
      );
    })
    .map(user => {
      const metrics = user.metrics;
      let issueType: 'frequent_absence' | 'frequent_late' | 'low_hours';
      let severity: 'low' | 'medium' | 'high';

      if (metrics.attendancePercentage < 60) {
        issueType = 'frequent_absence';
        severity = 'high';
      } else if (metrics.lateDays > metrics.totalDays * 0.5) {
        issueType = 'frequent_late';
        severity = 'medium';
      } else {
        issueType = 'low_hours';
        severity = 'low';
      }

      return {
        userId: user.userId,
        userName: user.userName,
        issueType,
        severity,
      };
    });

  return {
    siteId,
    siteName: site.name,
    totalWorkers,
    averageAttendance: Math.round(averageAttendance * 100) / 100,
    topPerformers,
    attendanceIssues,
  };
}

/**
 * Validate attendance submission business rules
 */
export function validateAttendanceSubmission(attendanceData: {
  date: Date;
  checkInTime?: string;
  checkOutTime?: string;
  hoursWorked?: number;
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  const { date, checkInTime, checkOutTime, hoursWorked } = attendanceData;

  // Check if date is in the future
  if (date > new Date()) {
    errors.push('Cannot submit attendance for future dates');
  }

  // Check if date is too old (more than 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  if (date < thirtyDaysAgo) {
    warnings.push('Submitting attendance for more than 30 days ago');
  }

  // Validate time consistency
  if (checkInTime && checkOutTime) {
    const checkIn = new Date(`1970-01-01T${checkInTime}`);
    const checkOut = new Date(`1970-01-01T${checkOutTime}`);

    if (checkOut <= checkIn) {
      errors.push('Check-out time must be after check-in time');
    }

    // Calculate expected hours worked
    const timeDiff = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
    
    if (hoursWorked && Math.abs(timeDiff - hoursWorked) > 1) {
      warnings.push(`Hours worked (${hoursWorked}) doesn't match time difference (${timeDiff.toFixed(2)})`);
    }

    if (timeDiff > 12) {
      warnings.push('Working more than 12 hours - please verify');
    }
  }

  // Validate hours worked
  if (hoursWorked) {
    if (hoursWorked < 0) {
      errors.push('Hours worked cannot be negative');
    }
    
    if (hoursWorked > 24) {
      errors.push('Hours worked cannot exceed 24 hours');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
