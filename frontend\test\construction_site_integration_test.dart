import 'package:flutter_test/flutter_test.dart';
import 'package:srsr_property_management/shared/models/property.dart';

void main() {
  group('Construction Site Integration Tests', () {
    test('should parse construction site property from backend JSON', () {
      // Simulate the JSON response from backend that was causing the crash
      final jsonData = {
        'id': 'test-construction-site-id',
        'name': 'Residential Complex A',
        'type': 'construction_site',
        'address': 'Whitefield, Bangalore',
        'description': 'Residential construction project',
        'image_url': null,
        'is_active': true,
        'services': [],
        'created_at': '2024-01-15T00:00:00.000Z',
      };

      // This should not throw an exception anymore
      expect(() => Property.fromJson(jsonData), returnsNormally);

      final property = Property.fromJson(jsonData);
      expect(property.type, 'construction_site');
      expect(property.isConstructionSite, true);
      expect(property.displayType, 'Construction Site');
      expect(property.name, 'Residential Complex A');
    });

    test('should handle multiple construction sites in a list', () {
      final jsonList = [
        {
          'id': 'site-1',
          'name': 'Residential Complex A',
          'type': 'construction_site',
          'address': 'Whitefield, Bangalore',
          'description': 'Residential construction project',
          'image_url': null,
          'is_active': true,
          'services': [],
          'created_at': '2024-01-15T00:00:00.000Z',
        },
        {
          'id': 'site-2',
          'name': 'Commercial Plaza B',
          'type': 'construction_site',
          'address': 'Electronic City, Bangalore',
          'description': 'Commercial construction project',
          'image_url': null,
          'is_active': true,
          'services': [],
          'created_at': '2024-03-01T00:00:00.000Z',
        },
        {
          'id': 'office-1',
          'name': 'Head Office',
          'type': 'office',
          'address': 'MG Road, Bangalore',
          'description': 'Main office building',
          'image_url': null,
          'is_active': true,
          'services': [],
          'created_at': '2024-01-01T00:00:00.000Z',
        },
      ];

      // Parse all properties - should not throw exceptions
      final properties = jsonList.map((json) => Property.fromJson(json)).toList();
      
      expect(properties.length, 3);
      expect(properties.where((p) => p.isConstructionSite).length, 2);
      expect(properties.where((p) => p.isOffice).length, 1);
      
      // Check construction sites have correct properties
      final constructionSites = properties.where((p) => p.isConstructionSite).toList();
      for (final site in constructionSites) {
        expect(site.displayType, 'Construction Site');
        expect(site.primaryColor.value, 0xFFE65100); // Orange color
        expect(site.availableFeatures, contains('project_management'));
        expect(site.defaultServices, contains('safety_equipment'));
      }
    });

    test('should provide construction site specific business logic', () {
      final constructionSite = Property(
        id: 'test-site',
        name: 'Test Construction Site',
        type: 'construction_site',
        address: 'Test Location',
        description: 'Test project',
        imageUrl: null,
        isActive: true,
        services: [],
        createdAt: DateTime.now(),
      );

      // Test construction site specific features
      expect(constructionSite.hasFeature('project_management'), true);
      expect(constructionSite.hasFeature('equipment_tracking'), true);
      expect(constructionSite.hasFeature('safety_monitoring'), true);
      expect(constructionSite.hasFeature('ott_services'), false); // Not available for construction sites

      // Test construction site specific services
      expect(constructionSite.hasService('safety_equipment'), true);
      expect(constructionSite.hasService('material_storage'), true);
      expect(constructionSite.hasService('equipment_monitoring'), true);

      // Test maintenance categories
      expect(constructionSite.maintenanceCategories, contains('safety_equipment'));
      expect(constructionSite.maintenanceCategories, contains('construction_equipment'));
      expect(constructionSite.maintenanceCategories, contains('site_infrastructure'));

      // Test thresholds (should be stricter for construction sites)
      final thresholds = constructionSite.defaultThresholds;
      expect(thresholds['fuel_level_critical'], 15.0); // Stricter than residential (20.0)
      expect(thresholds['maintenance_overdue_days'], 2); // Stricter than residential (7)
      expect(thresholds['safety_inspection_interval'], 24); // Construction site specific
    });
  });
}
