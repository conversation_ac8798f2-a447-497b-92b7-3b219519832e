"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { getBrowserClient } from "@/lib/supabase"

interface RoleBasedUIProps {
  roles: string | string[]
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function RoleBasedUI({ roles, children, fallback = null }: RoleBasedUIProps) {
  const [hasRequiredRole, setHasRequiredRole] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function checkUserRole() {
      try {
        const supabase = getBrowserClient()

        // Get current session token from cookies
        const sessionToken = document.cookie
          .split("; ")
          .find((row) => row.startsWith("session_token="))
          ?.split("=")[1]

        if (!sessionToken) {
          setLoading(false)
          return
        }

        // Get session and user
        const { data: session, error: sessionError } = await supabase
          .from("sessions")
          .select("user_id")
          .eq("token", sessionToken)
          .gt("expires_at", new Date().toISOString())
          .single()

        if (sessionError || !session) {
          setLoading(false)
          return
        }

        // Get user details
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("username, role")
          .eq("id", session.user_id)
          .single()

        if (!userError && userData) {
          // If username is admin1 or role is admin, always show content
          if (userData.username === "admin1" || userData.role === "admin") {
            setHasRequiredRole(true)
            setLoading(false)
            return
          }

          // Otherwise, check user roles
          const roleArray = Array.isArray(roles) ? roles : [roles]

          // Check if user's role matches any required role
          if (roleArray.includes(userData.role)) {
            setHasRequiredRole(true)
            setLoading(false)
            return
          }

          // Check user_roles table for assigned roles
          const { data: userRolesData, error: userRolesError } = await supabase
            .from("user_roles")
            .select("roles(name)")
            .eq("user_id", session.user_id)

          if (!userRolesError && userRolesData) {
            const userRoles = userRolesData.map((ur) => ur.roles?.name).filter(Boolean) as string[]

            // Check if user has any of the required roles
            if (userRoles.some((role) => roleArray.includes(role))) {
              setHasRequiredRole(true)
            }
          }
        }
      } catch (error) {
        console.error("Error checking user role:", error)
      } finally {
        setLoading(false)
      }
    }

    checkUserRole()
  }, [roles])

  // While loading, don't render anything to prevent flashing
  if (loading) {
    return null
  }

  // If user has the required role, render the children
  if (hasRequiredRole) {
    return <>{children}</>
  }

  // Otherwise, render the fallback
  return <>{fallback}</>
}
