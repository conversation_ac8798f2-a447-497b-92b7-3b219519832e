/// Generator fuel calculations and business logic
/// Ported from V1's sophisticated generator management system
library;

import 'dart:math';

/// Generator calculation constants
class GeneratorConstants {
  static const double defaultGeneratorCapacity = 100.0; // liters
  static const double defaultConsumptionRate = 6.5; // liters per hour
  static const double minEfficiency = 60.0; // percentage
  static const double maxEfficiency = 95.0; // percentage
  static const double criticalFuelLevel = 20.0; // percentage
  static const double warningFuelLevel = 40.0; // percentage
  static const double criticalBackupHours = 6.0; // hours
  static const double warningBackupHours = 12.0; // hours
}

/// Generator fuel data model
class GeneratorFuelData {
  final double fuelInGeneratorPercentage;
  final double fuelInTankLiters;
  final double? consumptionRate;
  final double? efficiencyPercentage;
  final double? runtimeHours;
  final DateTime timestamp;

  const GeneratorFuelData({
    required this.fuelInGeneratorPercentage,
    required this.fuelInTankLiters,
    this.consumptionRate,
    this.efficiencyPercentage,
    this.runtimeHours,
    required this.timestamp,
  });

  /// Create from API response
  factory GeneratorFuelData.fromJson(Map<String, dynamic> json) {
    return GeneratorFuelData(
      fuelInGeneratorPercentage: (json['fuel_in_generator_percentage'] ?? 0.0).toDouble(),
      fuelInTankLiters: (json['fuel_in_tank_liters'] ?? 0.0).toDouble(),
      consumptionRate: json['consumption_rate']?.toDouble(),
      efficiencyPercentage: json['efficiency_percentage']?.toDouble(),
      runtimeHours: json['runtime_hours']?.toDouble(),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }
}

/// Generator calculation results
class GeneratorCalculationResult {
  final double fuelInGenerator;
  final double totalFuel;
  final double powerBackupHours;
  final double consumptionRate;
  final double efficiency;
  final String status;
  final List<String> alerts;
  final Map<String, dynamic> insights;

  const GeneratorCalculationResult({
    required this.fuelInGenerator,
    required this.totalFuel,
    required this.powerBackupHours,
    required this.consumptionRate,
    required this.efficiency,
    required this.status,
    required this.alerts,
    required this.insights,
  });
}

/// Generator fuel prediction
class FuelPrediction {
  final double hoursUntilEmpty;
  final DateTime estimatedEmptyTime;
  final double recommendedRefillAmount;
  final String urgencyLevel;
  final List<String> recommendations;

  const FuelPrediction({
    required this.hoursUntilEmpty,
    required this.estimatedEmptyTime,
    required this.recommendedRefillAmount,
    required this.urgencyLevel,
    required this.recommendations,
  });
}

/// Enhanced generator calculations with V1 business logic
class GeneratorCalculations {
  /// Calculate comprehensive generator statistics
  /// Ported from V1's generator-fuel-history.tsx and enhanced
  static GeneratorCalculationResult calculateGeneratorStats(
    GeneratorFuelData currentData, {
    List<GeneratorFuelData>? historicalData,
    double generatorCapacity = GeneratorConstants.defaultGeneratorCapacity,
  }) {
    // Calculate fuel in generator (liters) - V1 pattern
    final fuelInGenerator = (generatorCapacity * currentData.fuelInGeneratorPercentage) / 100;

    // Calculate total fuel available - V1 pattern
    final totalFuel = fuelInGenerator + currentData.fuelInTankLiters;

    // Determine consumption rate with historical analysis
    final consumptionRate = _calculateConsumptionRate(currentData, historicalData);

    // Calculate power backup hours - V1 pattern with enhancements
    final powerBackupHours = totalFuel > 0 ? totalFuel / consumptionRate : 0.0;

    // Calculate efficiency with trend analysis
    final efficiency = _calculateEfficiency(currentData, historicalData);

    // Determine status based on multiple factors
    final status = _determineGeneratorStatus(
      fuelInGenerator,
      totalFuel,
      powerBackupHours,
      efficiency,
      generatorCapacity,
    );

    // Generate alerts and insights
    final alerts = _generateAlerts(fuelInGenerator, totalFuel, powerBackupHours, efficiency);
    final insights = _generateInsights(currentData, historicalData, consumptionRate);

    return GeneratorCalculationResult(
      fuelInGenerator: _roundToDecimal(fuelInGenerator, 1),
      totalFuel: _roundToDecimal(totalFuel, 1),
      powerBackupHours: _roundToDecimal(powerBackupHours, 1),
      consumptionRate: _roundToDecimal(consumptionRate, 2),
      efficiency: _roundToDecimal(efficiency, 1),
      status: status,
      alerts: alerts,
      insights: insights,
    );
  }

  /// Predict fuel consumption and requirements
  static FuelPrediction predictFuelConsumption(
    GeneratorFuelData currentData,
    List<GeneratorFuelData> historicalData, {
    double generatorCapacity = GeneratorConstants.defaultGeneratorCapacity,
  }) {
    final stats = calculateGeneratorStats(currentData, historicalData: historicalData);

    // Calculate hours until empty
    final hoursUntilEmpty = stats.totalFuel > 0 ? stats.totalFuel / stats.consumptionRate : 0.0;

    // Estimate empty time
    final estimatedEmptyTime = DateTime.now().add(Duration(
      hours: hoursUntilEmpty.floor(),
      minutes: ((hoursUntilEmpty % 1) * 60).round(),
    ));

    // Calculate recommended refill amount
    final recommendedRefillAmount = _calculateRecommendedRefill(
      stats.totalFuel,
      generatorCapacity,
      stats.consumptionRate,
    );

    // Determine urgency level
    final urgencyLevel = _determineUrgencyLevel(hoursUntilEmpty);

    // Generate recommendations
    final recommendations = _generateRecommendations(
      hoursUntilEmpty,
      stats.efficiency,
      stats.consumptionRate,
    );

    return FuelPrediction(
      hoursUntilEmpty: _roundToDecimal(hoursUntilEmpty, 1),
      estimatedEmptyTime: estimatedEmptyTime,
      recommendedRefillAmount: _roundToDecimal(recommendedRefillAmount, 1),
      urgencyLevel: urgencyLevel,
      recommendations: recommendations,
    );
  }

  /// Calculate consumption rate with historical analysis
  static double _calculateConsumptionRate(
    GeneratorFuelData currentData,
    List<GeneratorFuelData>? historicalData,
  ) {
    // Use provided consumption rate if available
    if (currentData.consumptionRate != null && currentData.consumptionRate! > 0) {
      return currentData.consumptionRate!;
    }

    // Calculate from historical data if available
    if (historicalData != null && historicalData.length >= 2) {
      final rates = <double>[];

      for (int i = 1; i < historicalData.length; i++) {
        final current = historicalData[i];
        final previous = historicalData[i - 1];

        final timeDiff = current.timestamp.difference(previous.timestamp).inHours;
        if (timeDiff > 0) {
          final currentTotal = (GeneratorConstants.defaultGeneratorCapacity * current.fuelInGeneratorPercentage / 100) + current.fuelInTankLiters;
          final previousTotal = (GeneratorConstants.defaultGeneratorCapacity * previous.fuelInGeneratorPercentage / 100) + previous.fuelInTankLiters;
          final fuelConsumed = previousTotal - currentTotal;

          if (fuelConsumed > 0) {
            rates.add(fuelConsumed / timeDiff);
          }
        }
      }

      if (rates.isNotEmpty) {
        return rates.reduce((a, b) => a + b) / rates.length;
      }
    }

    // Default consumption rate
    return GeneratorConstants.defaultConsumptionRate;
  }

  /// Calculate efficiency with trend analysis
  static double _calculateEfficiency(
    GeneratorFuelData currentData,
    List<GeneratorFuelData>? historicalData,
  ) {
    // Use provided efficiency if available
    if (currentData.efficiencyPercentage != null) {
      return currentData.efficiencyPercentage!;
    }

    // Calculate from historical data if available
    if (historicalData != null && historicalData.isNotEmpty) {
      final efficiencies = historicalData
          .where((data) => data.efficiencyPercentage != null)
          .map((data) => data.efficiencyPercentage!)
          .toList();

      if (efficiencies.isNotEmpty) {
        return efficiencies.reduce((a, b) => a + b) / efficiencies.length;
      }
    }

    // Default efficiency based on fuel level and consumption
    final fuelPercentage = currentData.fuelInGeneratorPercentage;
    if (fuelPercentage > 80) return 85.0;
    if (fuelPercentage > 60) return 80.0;
    if (fuelPercentage > 40) return 75.0;
    if (fuelPercentage > 20) return 70.0;
    return 65.0;
  }

  /// Determine generator status based on multiple factors
  static String _determineGeneratorStatus(
    double fuelInGenerator,
    double totalFuel,
    double powerBackupHours,
    double efficiency,
    double generatorCapacity,
  ) {
    final fuelPercentage = (fuelInGenerator / generatorCapacity) * 100;

    // Critical conditions
    if (fuelPercentage < GeneratorConstants.criticalFuelLevel ||
        powerBackupHours < GeneratorConstants.criticalBackupHours ||
        efficiency < GeneratorConstants.minEfficiency) {
      return 'critical';
    }

    // Warning conditions
    if (fuelPercentage < GeneratorConstants.warningFuelLevel ||
        powerBackupHours < GeneratorConstants.warningBackupHours ||
        efficiency < 75.0) {
      return 'warning';
    }

    return 'operational';
  }

  /// Generate alerts based on current conditions
  static List<String> _generateAlerts(
    double fuelInGenerator,
    double totalFuel,
    double powerBackupHours,
    double efficiency,
  ) {
    final alerts = <String>[];

    if (powerBackupHours < GeneratorConstants.criticalBackupHours) {
      alerts.add('CRITICAL: Less than ${GeneratorConstants.criticalBackupHours} hours of backup power remaining');
    } else if (powerBackupHours < GeneratorConstants.warningBackupHours) {
      alerts.add('WARNING: Less than ${GeneratorConstants.warningBackupHours} hours of backup power remaining');
    }

    if (efficiency < GeneratorConstants.minEfficiency) {
      alerts.add('CRITICAL: Generator efficiency below minimum threshold (${efficiency.toStringAsFixed(1)}%)');
    } else if (efficiency < 75.0) {
      alerts.add('WARNING: Generator efficiency declining (${efficiency.toStringAsFixed(1)}%)');
    }

    if (totalFuel < 50.0) {
      alerts.add('LOW FUEL: Total fuel level is ${totalFuel.toStringAsFixed(1)}L');
    }

    return alerts;
  }

  /// Generate insights and recommendations
  static Map<String, dynamic> _generateInsights(
    GeneratorFuelData currentData,
    List<GeneratorFuelData>? historicalData,
    double consumptionRate,
  ) {
    final insights = <String, dynamic>{};

    // Consumption trend
    if (historicalData != null && historicalData.length >= 3) {
      final recentData = historicalData.length >= 3
          ? historicalData.sublist(historicalData.length - 3)
          : historicalData;
      final recentRates = recentData
          .where((data) => data.consumptionRate != null)
          .map((data) => data.consumptionRate!)
          .toList();

      if (recentRates.length >= 2) {
        final avgRecent = recentRates.reduce((a, b) => a + b) / recentRates.length;
        final trend = consumptionRate > avgRecent * 1.1 ? 'increasing' :
                     consumptionRate < avgRecent * 0.9 ? 'decreasing' : 'stable';
        insights['consumption_trend'] = trend;
      }
    }

    // Efficiency trend
    insights['efficiency_status'] = currentData.efficiencyPercentage != null
        ? currentData.efficiencyPercentage! > 80 ? 'excellent' :
          currentData.efficiencyPercentage! > 70 ? 'good' : 'poor'
        : 'unknown';

    // Runtime analysis
    if (currentData.runtimeHours != null) {
      insights['runtime_status'] = currentData.runtimeHours! > 8 ? 'high_usage' :
                                  currentData.runtimeHours! > 4 ? 'moderate_usage' : 'low_usage';
    }

    return insights;
  }

  /// Calculate recommended refill amount
  static double _calculateRecommendedRefill(
    double currentTotal,
    double generatorCapacity,
    double consumptionRate,
  ) {
    // Target: 24 hours of backup power
    const targetBackupHours = 24.0;
    final targetFuel = consumptionRate * targetBackupHours;
    final maxCapacity = generatorCapacity + 500.0; // Assuming 500L tank capacity

    return min(targetFuel - currentTotal, maxCapacity - currentTotal);
  }

  /// Determine urgency level for refill
  static String _determineUrgencyLevel(double hoursUntilEmpty) {
    if (hoursUntilEmpty < 6) return 'critical';
    if (hoursUntilEmpty < 12) return 'high';
    if (hoursUntilEmpty < 24) return 'medium';
    return 'low';
  }

  /// Generate recommendations based on current state
  static List<String> _generateRecommendations(
    double hoursUntilEmpty,
    double efficiency,
    double consumptionRate,
  ) {
    final recommendations = <String>[];

    if (hoursUntilEmpty < 12) {
      recommendations.add('Schedule immediate fuel refill');
    } else if (hoursUntilEmpty < 24) {
      recommendations.add('Plan fuel refill within next 12 hours');
    }

    if (efficiency < 75) {
      recommendations.add('Schedule generator maintenance to improve efficiency');
    }

    if (consumptionRate > GeneratorConstants.defaultConsumptionRate * 1.2) {
      recommendations.add('Investigate high fuel consumption - possible maintenance required');
    }

    return recommendations;
  }

  /// Round number to specified decimal places
  static double _roundToDecimal(double value, int decimals) {
    final factor = pow(10, decimals);
    return (value * factor).round() / factor;
  }
}
