import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"
import { ThresholdManagement } from "@/components/threshold-management"

export default async function ThresholdsPage() {
  // Simple, direct admin check
  const user = await getCurrentUser()

  if (!user || (user.username !== "admin1" && user.role !== "admin")) {
    redirect("/unauthorized")
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Threshold Management</h1>
        <p className="text-gray-600">Configure system thresholds and alert settings</p>
      </div>
      <ThresholdManagement />
    </div>
  )
}
