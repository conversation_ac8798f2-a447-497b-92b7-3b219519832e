import type { FunctionProcess } from "@/components/function-process-matrix"

export const functionProcessData: FunctionProcess[] = [
  {
    id: "1",
    Function: "Water",
    "Sub-Function": "Tank Level Monitoring",
    Input: "Sensor reading (%, time)",
    Process: "Fetch from sensor/API",
    Output: "Current water level (%)",
    "Threshold Limits": "Min: 20%, Max: 80%",
    "Responsible Agent": "Facility Supervisor",
  },
  {
    id: "2",
    Function: "Water",
    "Sub-Function": "Motor Operation",
    Input: "Water level + Timer setting",
    Process: "Auto/manual motor control",
    Output: "Motor status: ON/OFF",
    "Threshold Limits": "Max runtime: 30 min",
    "Responsible Agent": "Electrician/Plumber",
  },
  {
    id: "3",
    Function: "Water",
    "Sub-Function": "Overhead Tank Cleanliness",
    Input: "Inspection date, photos",
    Process: "Monthly inspection checklist",
    Output: "Clean/Unclean status",
    "Threshold Limits": "Max interval: 30 days",
    "Responsible Agent": "Housekeeper",
  },
  {
    id: "4",
    Function: "Electricity",
    "Sub-Function": "Consumption Tracker",
    Input: "Energy meter data (kWh)",
    Process: "Calculate daily/monthly usage",
    Output: "kWh per day/month",
    "Threshold Limits": "Max daily: 10 kWh",
    "Responsible Agent": "Electrician",
  },
  {
    id: "5",
    Function: "Electricity",
    "Sub-Function": "Load Monitoring",
    Input: "Device-level wattage",
    Process: "Aggregation from devices",
    Output: "Real-time load in watts",
    "Threshold Limits": "Max: 3000W/appliance",
    "Responsible Agent": "Technical Officer",
  },
  {
    id: "6",
    Function: "Electricity",
    "Sub-Function": "Power Bill Status",
    Input: "Billing data",
    Process: "Read bill from dashboard/API",
    Output: "Paid/Unpaid & Due Date",
    "Threshold Limits": "Min 3 days before due",
    "Responsible Agent": "Admin",
  },
  {
    id: "7",
    Function: "Generator",
    "Sub-Function": "Fuel Level Monitoring",
    Input: "Manual entry or sensor",
    Process: "Log/check daily",
    Output: "Fuel % or litres",
    "Threshold Limits": "Min: 20%",
    "Responsible Agent": "Site Security In-Charge",
  },
  {
    id: "8",
    Function: "Generator",
    "Sub-Function": "Usage Hours",
    Input: "Runtime logs",
    Process: "Log generator run hours",
    Output: "Daily usage in hours",
    "Threshold Limits": "Max: 2 hrs/day",
    "Responsible Agent": "Site Engineer",
  },
  {
    id: "9",
    Function: "Generator",
    "Sub-Function": "Service Schedule",
    Input: "Last service date",
    Process: "Service calendar check",
    Output: "Service due or not",
    "Threshold Limits": "Every 3 months",
    "Responsible Agent": "Generator Vendor/AMC",
  },
  {
    id: "10",
    Function: "Security",
    "Sub-Function": "Door Lock Check",
    Input: "Manual or digital lock status",
    Process: "Daily inspection",
    Output: "Locked/Unlocked",
    "Threshold Limits": "Must be locked at night",
    "Responsible Agent": "Security Guard",
  },
  {
    id: "11",
    Function: "Security",
    "Sub-Function": "Alarm System Check",
    Input: "Alarm status report",
    Process: "Test alarm daily/weekly",
    Output: "Working/Not Working",
    "Threshold Limits": "Should not be inactive for >1 day",
    "Responsible Agent": "Security Vendor",
  },
  {
    id: "12",
    Function: "CCTV",
    "Sub-Function": "Feed Uptime",
    Input: "IP stream status",
    Process: "Ping every hour",
    Output: "Online/Offline",
    "Threshold Limits": "Max downtime: 2 hrs",
    "Responsible Agent": "CCTV Vendor",
  },
  {
    id: "13",
    Function: "CCTV",
    "Sub-Function": "Footage Backup",
    Input: "Drive backup logs",
    Process: "Check backup script",
    Output: "Success/Failure",
    "Threshold Limits": "Daily backup required",
    "Responsible Agent": "IT Assistant",
  },
  {
    id: "14",
    Function: "Internet",
    "Sub-Function": "Connectivity Check",
    Input: "Ping status",
    Process: "Scheduled speed/ping tests",
    Output: "Connected/Disconnected",
    "Threshold Limits": "Max downtime: 10 mins/hr",
    "Responsible Agent": "Network Admin",
  },
  {
    id: "15",
    Function: "Internet",
    "Sub-Function": "Speed Monitoring",
    Input: "Speedtest result",
    Process: "Log speed hourly",
    Output: "Mbps download/upload",
    "Threshold Limits": "Min: 50 Mbps",
    "Responsible Agent": "Internet Vendor",
  },
  {
    id: "16",
    Function: "OTTs",
    "Sub-Function": "Subscription Tracker",
    Input: "Renewal date, cost",
    Process: "Check plan status monthly",
    Output: "Active/Inactive",
    "Threshold Limits": "Renewal 2 days before expiry",
    "Responsible Agent": "Admin",
  },
  {
    id: "17",
    Function: "Attendance - Office",
    "Sub-Function": "Staff Presence",
    Input: "Check-in/check-out logs",
    Process: "Compare vs shift schedule",
    Output: "Present/Absent",
    "Threshold Limits": "Max 1 absence/week",
    "Responsible Agent": "Office Incharge",
  },
  {
    id: "18",
    Function: "Attendance - Site",
    "Sub-Function": "Security Presence",
    Input: "Log entry/exit",
    Process: "Auto-capture + manual verify",
    Output: "On Duty/Off Duty",
    "Threshold Limits": "Min 2 guards always",
    "Responsible Agent": "Site Security Incharge",
  },
  {
    id: "19",
    Function: "Maintenance",
    "Sub-Function": "Scheduled Maintenance",
    Input: "Last service record",
    Process: "Check calendar",
    Output: "Due/Not Due",
    "Threshold Limits": "Max interval: 60 days",
    "Responsible Agent": "Maintenance Supervisor",
  },
  {
    id: "20",
    Function: "Maintenance",
    "Sub-Function": "Issue Tracker",
    Input: "Reported issue + photos",
    Process: "Log & assign issue",
    Output: "Open/In Progress/Resolved",
    "Threshold Limits": "Resolve within 3 days",
    "Responsible Agent": "Admin",
  },
]
