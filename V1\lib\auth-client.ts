import { getBrowserClient } from "@/lib/supabase"

export async function isAdmin1User(): Promise<boolean> {
  try {
    const supabase = getBrowserClient()
    const { data, error } = await supabase.from("users").select("username").limit(1).single()

    if (error) {
      console.error("Error checking admin1:", error)
      return false
    }

    return data?.username === "admin1"
  } catch (error) {
    console.error("Error in isAdmin1User:", error)
    return false
  }
}
