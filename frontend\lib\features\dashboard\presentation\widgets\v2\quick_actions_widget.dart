import 'package:flutter/material.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/auth/models/user_role.dart';

/// Quick actions widget with role-based action shortcuts
class QuickActionsWidget extends StatelessWidget {
  final UserRole userRole;
  final Function(String)? onActionTap;

  const QuickActionsWidget({
    super.key,
    required this.userRole,
    this.onActionTap,
  });

  @override
  Widget build(BuildContext context) {
    final actions = _getActionsForRole(userRole);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppConstants.smallPadding,
            mainAxisSpacing: AppConstants.smallPadding,
            childAspectRatio: 2.5,
          ),
          itemCount: actions.length,
          itemBuilder: (context, index) {
            final action = actions[index];
            return _buildActionCard(context, action);
          },
        ),
      ],
    );
  }

  Widget _buildActionCard(BuildContext context, QuickAction action) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _handleActionTap(action.id),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: action.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  action.icon,
                  color: action.color,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      action.title,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (action.subtitle != null)
                      Text(
                        action.subtitle!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<QuickAction> _getActionsForRole(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return [
          QuickAction(
            id: 'add_user',
            title: 'Add User',
            subtitle: 'Create new account',
            icon: Icons.person_add,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'system_settings',
            title: 'Settings',
            subtitle: 'System config',
            icon: Icons.settings,
            color: Colors.grey,
          ),
          QuickAction(
            id: 'reports',
            title: 'Reports',
            subtitle: 'Generate reports',
            icon: Icons.analytics,
            color: Colors.green,
          ),
          QuickAction(
            id: 'backup',
            title: 'Backup',
            subtitle: 'System backup',
            icon: Icons.backup,
            color: Colors.orange,
          ),
        ];

      case UserRole.manager:
        return [
          QuickAction(
            id: 'schedule_maintenance',
            title: 'Schedule',
            subtitle: 'Plan maintenance',
            icon: Icons.schedule,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'assign_tasks',
            title: 'Assign Tasks',
            subtitle: 'Delegate work',
            icon: Icons.assignment,
            color: Colors.green,
          ),
          QuickAction(
            id: 'review_reports',
            title: 'Review',
            subtitle: 'Check reports',
            icon: Icons.rate_review,
            color: Colors.orange,
          ),
          QuickAction(
            id: 'approve_requests',
            title: 'Approve',
            subtitle: 'Pending requests',
            icon: Icons.approval,
            color: Colors.purple,
          ),
        ];

      case UserRole.maintenance:
        return [
          QuickAction(
            id: 'create_issue',
            title: 'Report Issue',
            subtitle: 'Log new problem',
            icon: Icons.report_problem,
            color: Colors.red,
          ),
          QuickAction(
            id: 'update_status',
            title: 'Update Status',
            subtitle: 'Progress update',
            icon: Icons.update,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'request_parts',
            title: 'Request Parts',
            subtitle: 'Order supplies',
            icon: Icons.inventory,
            color: Colors.orange,
          ),
          QuickAction(
            id: 'complete_task',
            title: 'Complete',
            subtitle: 'Mark as done',
            icon: Icons.check_circle,
            color: Colors.green,
          ),
        ];

      case UserRole.security:
        return [
          QuickAction(
            id: 'security_alert',
            title: 'Alert',
            subtitle: 'Security incident',
            icon: Icons.security,
            color: Colors.red,
          ),
          QuickAction(
            id: 'patrol_log',
            title: 'Patrol Log',
            subtitle: 'Record patrol',
            icon: Icons.route,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'access_control',
            title: 'Access',
            subtitle: 'Manage access',
            icon: Icons.key,
            color: Colors.purple,
          ),
          QuickAction(
            id: 'incident_report',
            title: 'Incident',
            subtitle: 'File report',
            icon: Icons.report,
            color: Colors.orange,
          ),
        ];

      case UserRole.viewer:
        return [
          QuickAction(
            id: 'view_properties',
            title: 'Properties',
            subtitle: 'Browse all',
            icon: Icons.business,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'view_reports',
            title: 'Reports',
            subtitle: 'View reports',
            icon: Icons.description,
            color: Colors.green,
          ),
          QuickAction(
            id: 'search',
            title: 'Search',
            subtitle: 'Find information',
            icon: Icons.search,
            color: Colors.grey,
          ),
          QuickAction(
            id: 'help',
            title: 'Help',
            subtitle: 'Get support',
            icon: Icons.help,
            color: Colors.orange,
          ),
        ];
    }
  }

  void _handleActionTap(String actionId) {
    if (onActionTap != null) {
      onActionTap!(actionId);
    } else {
      _performDefaultAction(actionId);
    }
  }

  void _performDefaultAction(String actionId) {
    // Default action implementations
    switch (actionId) {
      case 'add_user':
        print('Navigate to add user screen');
        break;
      case 'create_issue':
        print('Navigate to create maintenance issue');
        break;
      case 'security_alert':
        print('Trigger security alert');
        break;
      case 'view_properties':
        print('Navigate to properties list');
        break;
      default:
        print('Action not implemented: $actionId');
    }
  }
}

/// Quick action data model
class QuickAction {
  final String id;
  final String title;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final bool isEnabled;

  const QuickAction({
    required this.id,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.color,
    this.isEnabled = true,
  });
}

/// Expandable quick actions widget for more actions
class ExpandableQuickActions extends StatefulWidget {
  final UserRole userRole;
  final Function(String)? onActionTap;
  final int initialItemCount;

  const ExpandableQuickActions({
    super.key,
    required this.userRole,
    this.onActionTap,
    this.initialItemCount = 4,
  });

  @override
  State<ExpandableQuickActions> createState() => _ExpandableQuickActionsState();
}

class _ExpandableQuickActionsState extends State<ExpandableQuickActions> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final allActions = _getActionsForRole(widget.userRole);
    final displayActions = _isExpanded
        ? allActions
        : allActions.take(widget.initialItemCount).toList();

    return Column(
      children: [
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppConstants.smallPadding,
            mainAxisSpacing: AppConstants.smallPadding,
            childAspectRatio: 2.5,
          ),
          itemCount: displayActions.length,
          itemBuilder: (context, index) {
            final action = displayActions[index];
            return _buildActionCard(context, action);
          },
        ),
        if (allActions.length > widget.initialItemCount) ...[
          const SizedBox(height: AppConstants.smallPadding),
          TextButton.icon(
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            icon: Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
            ),
            label: Text(
              _isExpanded ? 'Show Less' : 'Show More Actions',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionCard(BuildContext context, QuickAction action) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: action.isEnabled ? () => _handleActionTap(action.id) : null,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Opacity(
          opacity: action.isEnabled ? 1.0 : 0.5,
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: action.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    action.icon,
                    color: action.color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        action.title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (action.subtitle != null)
                        Text(
                          action.subtitle!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
                if (action.isEnabled)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                    color: Colors.grey.shade400,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<QuickAction> _getActionsForRole(UserRole role) {
    // Duplicate implementation to avoid circular dependency
    switch (role) {
      case UserRole.admin:
        return [
          QuickAction(
            id: 'add_user',
            title: 'Add User',
            subtitle: 'Create new account',
            icon: Icons.person_add,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'system_settings',
            title: 'Settings',
            subtitle: 'System config',
            icon: Icons.settings,
            color: Colors.grey,
          ),
          QuickAction(
            id: 'reports',
            title: 'Reports',
            subtitle: 'Generate reports',
            icon: Icons.analytics,
            color: Colors.green,
          ),
          QuickAction(
            id: 'backup',
            title: 'Backup',
            subtitle: 'System backup',
            icon: Icons.backup,
            color: Colors.orange,
          ),
        ];

      case UserRole.manager:
        return [
          QuickAction(
            id: 'schedule_maintenance',
            title: 'Schedule',
            subtitle: 'Plan maintenance',
            icon: Icons.schedule,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'assign_tasks',
            title: 'Assign Tasks',
            subtitle: 'Delegate work',
            icon: Icons.assignment,
            color: Colors.green,
          ),
          QuickAction(
            id: 'review_reports',
            title: 'Review',
            subtitle: 'Check reports',
            icon: Icons.rate_review,
            color: Colors.orange,
          ),
          QuickAction(
            id: 'approve_requests',
            title: 'Approve',
            subtitle: 'Pending requests',
            icon: Icons.approval,
            color: Colors.purple,
          ),
        ];

      case UserRole.maintenance:
        return [
          QuickAction(
            id: 'create_issue',
            title: 'Report Issue',
            subtitle: 'Log new problem',
            icon: Icons.report_problem,
            color: Colors.red,
          ),
          QuickAction(
            id: 'update_status',
            title: 'Update Status',
            subtitle: 'Progress update',
            icon: Icons.update,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'request_parts',
            title: 'Request Parts',
            subtitle: 'Order supplies',
            icon: Icons.inventory,
            color: Colors.orange,
          ),
          QuickAction(
            id: 'complete_task',
            title: 'Complete',
            subtitle: 'Mark as done',
            icon: Icons.check_circle,
            color: Colors.green,
          ),
        ];

      case UserRole.security:
        return [
          QuickAction(
            id: 'security_alert',
            title: 'Alert',
            subtitle: 'Security incident',
            icon: Icons.security,
            color: Colors.red,
          ),
          QuickAction(
            id: 'patrol_log',
            title: 'Patrol Log',
            subtitle: 'Record patrol',
            icon: Icons.route,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'access_control',
            title: 'Access',
            subtitle: 'Manage access',
            icon: Icons.key,
            color: Colors.purple,
          ),
          QuickAction(
            id: 'incident_report',
            title: 'Incident',
            subtitle: 'File report',
            icon: Icons.report,
            color: Colors.orange,
          ),
        ];

      case UserRole.viewer:
        return [
          QuickAction(
            id: 'view_properties',
            title: 'Properties',
            subtitle: 'Browse all',
            icon: Icons.business,
            color: Colors.blue,
          ),
          QuickAction(
            id: 'view_reports',
            title: 'Reports',
            subtitle: 'View reports',
            icon: Icons.description,
            color: Colors.green,
          ),
          QuickAction(
            id: 'search',
            title: 'Search',
            subtitle: 'Find information',
            icon: Icons.search,
            color: Colors.grey,
          ),
          QuickAction(
            id: 'help',
            title: 'Help',
            subtitle: 'Get support',
            icon: Icons.help,
            color: Colors.orange,
          ),
        ];
    }
  }

  void _handleActionTap(String actionId) {
    if (widget.onActionTap != null) {
      widget.onActionTap!(actionId);
    }
  }
}
