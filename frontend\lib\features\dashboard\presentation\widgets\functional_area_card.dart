import 'package:flutter/material.dart';
import '../../../../core/business_logic/status_calculator.dart';
import '../../../../core/constants/app_constants.dart';

class FunctionalAreaCard extends StatelessWidget {
  final String areaName;
  final FunctionalAreaStatus areaStatus;
  final VoidCallback? onTap;

  const FunctionalAreaCard({
    super.key,
    required this.areaName,
    required this.areaStatus,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor(areaStatus.status);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getAreaIcon(areaName),
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _formatAreaName(areaName),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: color,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _getStatusText(areaStatus.status),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: color,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (areaStatus.issueCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        areaStatus.issueCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Metrics
              if (areaStatus.metrics.isNotEmpty) ...[
                Text(
                  'Metrics',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: AppConstants.smallPadding),

                ...areaStatus.metrics.take(3).map((metric) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _getStatusColor(metric.status),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _formatMetricName(metric.name),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                      Text(
                        metric.displayValue,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(metric.status),
                        ),
                      ),
                    ],
                  ),
                )),

                if (areaStatus.metrics.length > 3)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      '+${areaStatus.metrics.length - 3} more',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
              ],

              const SizedBox(height: AppConstants.smallPadding),

              // Last Updated
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 12,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Updated ${_formatLastUpdated(areaStatus.lastUpdated)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(StatusLevel status) {
    switch (status) {
      case StatusLevel.green:
        return Colors.green;
      case StatusLevel.orange:
        return Colors.orange;
      case StatusLevel.red:
        return Colors.red;
    }
  }

  String _getStatusText(StatusLevel status) {
    switch (status) {
      case StatusLevel.green:
        return 'Operational';
      case StatusLevel.orange:
        return 'Warning';
      case StatusLevel.red:
        return 'Critical';
    }
  }

  IconData _getAreaIcon(String areaName) {
    switch (areaName.toLowerCase()) {
      case 'electricity':
        return Icons.electrical_services;
      case 'maintenance':
        return Icons.build;
      case 'internet':
        return Icons.wifi;
      case 'security':
        return Icons.security;
      case 'attendance':
        return Icons.people;
      default:
        return Icons.miscellaneous_services;
    }
  }

  String _formatAreaName(String areaName) {
    switch (areaName.toLowerCase()) {
      case 'electricity':
        return 'Electricity';
      case 'maintenance':
        return 'Maintenance';
      case 'internet':
        return 'Internet';
      case 'security':
        return 'Security';
      case 'attendance':
        return 'Attendance';
      default:
        return areaName[0].toUpperCase() + areaName.substring(1);
    }
  }

  String _formatMetricName(String metricName) {
    switch (metricName) {
      case 'fuel_level':
        return 'Fuel Level';
      case 'backup_hours':
        return 'Backup Time';
      case 'consumption_rate':
        return 'Consumption';
      case 'efficiency':
        return 'Efficiency';
      case 'open_issues':
        return 'Open Issues';
      case 'critical_issues':
        return 'Critical Issues';
      case 'overdue_issues':
        return 'Overdue Issues';
      case 'uptime':
        return 'Uptime';
      case 'disruptions':
        return 'Disruptions';
      case 'response_time':
        return 'Response Time';
      case 'cameras_online':
        return 'Cameras Online';
      case 'access_control':
        return 'Access Control';
      case 'recent_incidents':
        return 'Recent Incidents';
      case 'attendance_rate':
        return 'Attendance Rate';
      case 'avg_hours':
        return 'Avg Hours/Day';
      case 'total_days':
        return 'Total Days';
      default:
        return metricName.split('_').map((word) =>
            word[0].toUpperCase() + word.substring(1)).join(' ');
    }
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
