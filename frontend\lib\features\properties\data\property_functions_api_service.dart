import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';

part 'property_functions_api_service.g.dart';

@RestApi()
abstract class PropertyFunctionsApiService {
  factory PropertyFunctionsApiService(Dio dio) = _PropertyFunctionsApiService;

  @GET('/api/properties/{propertyId}/functions')
  Future<PropertyFunctionsResponse> getPropertyFunctions(
    @Path('propertyId') String propertyId,
  );

  @PUT('/api/properties/{propertyId}/functions')
  Future<PropertyFunctionsResponse> updatePropertyFunctions(
    @Path('propertyId') String propertyId,
    @Body() UpdatePropertyFunctionsRequest request,
  );
}

// Response models
@JsonSerializable()
class PropertyFunctionsResponse {
  final bool success;
  final String message;
  final PropertyFunctionsData data;

  const PropertyFunctionsResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PropertyFunctionsResponse.fromJson(Map<String, dynamic> json) =>
      _$PropertyFunctionsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyFunctionsResponseToJson(this);
}

@JsonSerializable()
class PropertyFunctionsData {
  final PropertyInfo property;
  final List<PropertyFunction> functions;

  const PropertyFunctionsData({
    required this.property,
    required this.functions,
  });

  factory PropertyFunctionsData.fromJson(Map<String, dynamic> json) =>
      _$PropertyFunctionsDataFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyFunctionsDataToJson(this);
}

@JsonSerializable()
class PropertyInfo {
  final String id;
  final String name;
  final String type;

  const PropertyInfo({
    required this.id,
    required this.name,
    required this.type,
  });

  factory PropertyInfo.fromJson(Map<String, dynamic> json) =>
      _$PropertyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyInfoToJson(this);
}

@JsonSerializable()
class PropertyFunction {
  final String? id;
  @JsonKey(name: 'function_name')
  final String functionName;
  @JsonKey(name: 'is_enabled')
  final bool isEnabled;
  final Map<String, dynamic> configuration;
  @JsonKey(name: 'display_order')
  final int displayOrder;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const PropertyFunction({
    this.id,
    required this.functionName,
    required this.isEnabled,
    required this.configuration,
    required this.displayOrder,
    this.createdAt,
    this.updatedAt,
  });

  factory PropertyFunction.fromJson(Map<String, dynamic> json) =>
      _$PropertyFunctionFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyFunctionToJson(this);

  PropertyFunction copyWith({
    String? id,
    String? functionName,
    bool? isEnabled,
    Map<String, dynamic>? configuration,
    int? displayOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PropertyFunction(
      id: id ?? this.id,
      functionName: functionName ?? this.functionName,
      isEnabled: isEnabled ?? this.isEnabled,
      configuration: configuration ?? this.configuration,
      displayOrder: displayOrder ?? this.displayOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Request models
@JsonSerializable()
class UpdatePropertyFunctionsRequest {
  final List<PropertyFunctionUpdate> functions;

  const UpdatePropertyFunctionsRequest({
    required this.functions,
  });

  factory UpdatePropertyFunctionsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePropertyFunctionsRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdatePropertyFunctionsRequestToJson(this);
}

@JsonSerializable()
class PropertyFunctionUpdate {
  @JsonKey(name: 'function_name')
  final String functionName;
  @JsonKey(name: 'is_enabled')
  final bool isEnabled;
  final Map<String, dynamic>? configuration;
  @JsonKey(name: 'display_order')
  final int? displayOrder;

  const PropertyFunctionUpdate({
    required this.functionName,
    required this.isEnabled,
    this.configuration,
    this.displayOrder,
  });

  factory PropertyFunctionUpdate.fromJson(Map<String, dynamic> json) =>
      _$PropertyFunctionUpdateFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyFunctionUpdateToJson(this);
}

// Helper extensions
extension PropertyFunctionExtensions on PropertyFunction {
  String get displayName {
    switch (functionName) {
      case 'attendance':
        return 'Attendance';
      case 'maintenance':
        return 'Maintenance';
      case 'fuel':
        return 'Fuel Monitoring';
      case 'ott':
        return 'OTT Services';
      case 'security':
        return 'Security';
      case 'uptime':
        return 'Uptime Reports';
      case 'diesel':
        return 'Diesel Additions';
      case 'meeting_rooms':
        return 'Meeting Rooms';
      case 'visitor_management':
        return 'Visitor Management';
      case 'equipment':
        return 'Equipment';
      case 'safety':
        return 'Safety';
      case 'materials':
        return 'Materials';
      case 'progress':
        return 'Progress';
      default:
        return functionName.replaceAll('_', ' ').toUpperCase();
    }
  }

  String get description {
    switch (functionName) {
      case 'attendance':
        return 'Track employee attendance and working hours';
      case 'maintenance':
        return 'Manage maintenance issues and requests';
      case 'fuel':
        return 'Monitor generator fuel levels and consumption';
      case 'ott':
        return 'Manage OTT service subscriptions';
      case 'security':
        return 'Security monitoring and logs';
      case 'uptime':
        return 'Service uptime and availability reports';
      case 'diesel':
        return 'Track diesel fuel additions';
      default:
        return 'Manage ${displayName.toLowerCase()} for this property';
    }
  }

  String get iconName {
    switch (functionName) {
      case 'attendance':
        return 'people';
      case 'maintenance':
        return 'build';
      case 'fuel':
        return 'local_gas_station';
      case 'ott':
        return 'tv';
      case 'security':
        return 'security';
      case 'uptime':
        return 'trending_up';
      case 'diesel':
        return 'local_gas_station';
      case 'meeting_rooms':
        return 'meeting_room';
      case 'visitor_management':
        return 'badge';
      case 'equipment':
        return 'construction';
      case 'safety':
        return 'health_and_safety';
      case 'materials':
        return 'inventory';
      case 'progress':
        return 'timeline';
      default:
        return 'settings';
    }
  }
}
