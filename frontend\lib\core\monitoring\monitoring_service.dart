import 'dart:async';
import 'package:flutter/foundation.dart';
import '../notifications/notification_api_service.dart';

/// Service for monitoring metrics and sending them to the backend
class MonitoringService {
  static final MonitoringService _instance = MonitoringService._internal();
  factory MonitoringService() => _instance;
  MonitoringService._internal();

  NotificationApiService? _apiService;
  Timer? _monitoringTimer;
  bool _isMonitoring = false;
  final Duration _monitoringInterval = const Duration(minutes: 5);

  /// Initialize the monitoring service
  void initialize(NotificationApiService apiService) {
    _apiService = apiService;
  }

  /// Start monitoring metrics
  void startMonitoring() {
    if (_isMonitoring || _apiService == null) return;

    _isMonitoring = true;
    debugPrint('Starting metric monitoring...');

    _monitoringTimer = Timer.periodic(_monitoringInterval, (timer) {
      _collectAndSendMetrics();
    });

    // Send initial metrics
    _collectAndSendMetrics();
  }

  /// Stop monitoring metrics
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    debugPrint('Stopped metric monitoring');
  }

  /// Manually send a single metric
  Future<bool> sendMetric({
    required String propertyId,
    required String serviceType,
    required String metricName,
    required double value,
    String? unit,
    DateTime? timestamp,
  }) async {
    if (_apiService == null) {
      debugPrint('MonitoringService not initialized');
      return false;
    }

    try {
      final result = await _apiService!.monitorMetric(
        propertyId: propertyId,
        serviceType: serviceType,
        metricName: metricName,
        value: value,
        unit: unit,
        timestamp: timestamp,
      );

      return result.when(
        success: (alerts) {
          if (alerts.isNotEmpty) {
            debugPrint('Generated ${alerts.length} alerts for metric $metricName');
          }
          return true;
        },
        error: (error) {
          debugPrint('Failed to send metric $metricName: $error');
          return false;
        },
      );
    } catch (e) {
      debugPrint('Error sending metric: $e');
      return false;
    }
  }

  /// Send multiple metrics at once
  Future<bool> sendMetrics(List<MonitoringDataPoint> metrics) async {
    if (_apiService == null) {
      debugPrint('MonitoringService not initialized');
      return false;
    }

    try {
      final result = await _apiService!.monitorMultipleMetrics(metrics);

      return result.when(
        success: (alerts) {
          if (alerts.isNotEmpty) {
            debugPrint('Generated ${alerts.length} alerts from ${metrics.length} metrics');
          }
          return true;
        },
        error: (error) {
          debugPrint('Failed to send metrics: $error');
          return false;
        },
      );
    } catch (e) {
      debugPrint('Error sending metrics: $e');
      return false;
    }
  }

  /// Collect and send metrics from various sources
  Future<void> _collectAndSendMetrics() async {
    try {
      final metrics = <MonitoringDataPoint>[];

      // Collect fuel metrics
      metrics.addAll(await _collectFuelMetrics());

      // Collect attendance metrics
      metrics.addAll(await _collectAttendanceMetrics());

      // Collect maintenance metrics
      metrics.addAll(await _collectMaintenanceMetrics());

      // Collect system metrics
      metrics.addAll(await _collectSystemMetrics());

      if (metrics.isNotEmpty) {
        await sendMetrics(metrics);
        debugPrint('Sent ${metrics.length} metrics to backend');
      }
    } catch (e) {
      debugPrint('Error collecting metrics: $e');
    }
  }

  /// Collect fuel-related metrics
  Future<List<MonitoringDataPoint>> _collectFuelMetrics() async {
    final metrics = <MonitoringDataPoint>[];

    try {
      // TODO: Implement actual fuel data collection
      // This would typically fetch from local storage or API
      
      // Example fuel metrics
      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'fuel',
        metricName: 'fuel_level',
        value: 75.0,
        unit: 'liters',
        timestamp: DateTime.now(),
      ));

      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'fuel',
        metricName: 'fuel_percentage',
        value: 85.0,
        unit: 'percent',
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('Error collecting fuel metrics: $e');
    }

    return metrics;
  }

  /// Collect attendance-related metrics
  Future<List<MonitoringDataPoint>> _collectAttendanceMetrics() async {
    final metrics = <MonitoringDataPoint>[];

    try {
      // TODO: Implement actual attendance data collection
      
      // Example attendance metrics
      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'attendance',
        metricName: 'attendance_percentage',
        value: 92.0,
        unit: 'percent',
        timestamp: DateTime.now(),
      ));

      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'attendance',
        metricName: 'present_count',
        value: 23.0,
        unit: 'people',
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('Error collecting attendance metrics: $e');
    }

    return metrics;
  }

  /// Collect maintenance-related metrics
  Future<List<MonitoringDataPoint>> _collectMaintenanceMetrics() async {
    final metrics = <MonitoringDataPoint>[];

    try {
      // TODO: Implement actual maintenance data collection
      
      // Example maintenance metrics
      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'maintenance',
        metricName: 'open_issues',
        value: 5.0,
        unit: 'count',
        timestamp: DateTime.now(),
      ));

      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'maintenance',
        metricName: 'overdue_issues',
        value: 2.0,
        unit: 'count',
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('Error collecting maintenance metrics: $e');
    }

    return metrics;
  }

  /// Collect system-related metrics
  Future<List<MonitoringDataPoint>> _collectSystemMetrics() async {
    final metrics = <MonitoringDataPoint>[];

    try {
      // TODO: Implement actual system data collection
      
      // Example system metrics
      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'system',
        metricName: 'response_time',
        value: 150.0,
        unit: 'ms',
        timestamp: DateTime.now(),
      ));

      metrics.add(MonitoringDataPoint(
        propertyId: 'example-property-1',
        serviceType: 'system',
        metricName: 'uptime_percentage',
        value: 99.5,
        unit: 'percent',
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      debugPrint('Error collecting system metrics: $e');
    }

    return metrics;
  }

  /// Send fuel level metric
  Future<bool> sendFuelLevel({
    required String propertyId,
    required double fuelLevel,
    required double fuelPercentage,
  }) async {
    final metrics = [
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'fuel',
        metricName: 'fuel_level',
        value: fuelLevel,
        unit: 'liters',
        timestamp: DateTime.now(),
      ),
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'fuel',
        metricName: 'fuel_percentage',
        value: fuelPercentage,
        unit: 'percent',
        timestamp: DateTime.now(),
      ),
    ];

    return await sendMetrics(metrics);
  }

  /// Send attendance metric
  Future<bool> sendAttendanceMetric({
    required String propertyId,
    required double attendancePercentage,
    required int presentCount,
    required int totalCount,
  }) async {
    final metrics = [
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'attendance',
        metricName: 'attendance_percentage',
        value: attendancePercentage,
        unit: 'percent',
        timestamp: DateTime.now(),
      ),
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'attendance',
        metricName: 'present_count',
        value: presentCount.toDouble(),
        unit: 'people',
        timestamp: DateTime.now(),
      ),
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'attendance',
        metricName: 'total_count',
        value: totalCount.toDouble(),
        unit: 'people',
        timestamp: DateTime.now(),
      ),
    ];

    return await sendMetrics(metrics);
  }

  /// Send maintenance metrics
  Future<bool> sendMaintenanceMetrics({
    required String propertyId,
    required int openIssues,
    required int overdueIssues,
    required int criticalIssues,
  }) async {
    final metrics = [
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'maintenance',
        metricName: 'open_issues',
        value: openIssues.toDouble(),
        unit: 'count',
        timestamp: DateTime.now(),
      ),
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'maintenance',
        metricName: 'overdue_issues',
        value: overdueIssues.toDouble(),
        unit: 'count',
        timestamp: DateTime.now(),
      ),
      MonitoringDataPoint(
        propertyId: propertyId,
        serviceType: 'maintenance',
        metricName: 'critical_issues',
        value: criticalIssues.toDouble(),
        unit: 'count',
        timestamp: DateTime.now(),
      ),
    ];

    return await sendMetrics(metrics);
  }

  /// Check if monitoring is active
  bool get isMonitoring => _isMonitoring;

  /// Get monitoring interval
  Duration get monitoringInterval => _monitoringInterval;
}
