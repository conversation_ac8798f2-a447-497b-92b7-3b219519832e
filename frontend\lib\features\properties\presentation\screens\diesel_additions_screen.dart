import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../shared/models/diesel_addition.dart';
import '../providers/diesel_additions_providers.dart';
import '../widgets/diesel_addition_card.dart';
import '../widgets/add_diesel_addition_dialog.dart';

class DieselAdditionsScreen extends ConsumerStatefulWidget {
  final String propertyId;

  const DieselAdditionsScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<DieselAdditionsScreen> createState() => _DieselAdditionsScreenState();
}

class _DieselAdditionsScreenState extends ConsumerState<DieselAdditionsScreen> {
  String _selectedFilter = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dieselAdditionsAsync = ref.watch(dieselAdditionsByPropertyProvider(widget.propertyId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Diesel Additions'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['properties.update'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showAddDieselAdditionDialog(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(dieselAdditionsByPropertyProvider(widget.propertyId)),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search diesel additions...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: AppConstants.defaultPadding),

                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('all', 'All Additions'),
                      const SizedBox(width: 8),
                      _buildFilterChip('today', 'Today'),
                      const SizedBox(width: 8),
                      _buildFilterChip('week', 'This Week'),
                      const SizedBox(width: 8),
                      _buildFilterChip('month', 'This Month'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Additions List
          Expanded(
            child: dieselAdditionsAsync.when(
              data: (additions) {
                final filteredAdditions = _filterAdditions(additions);

                if (filteredAdditions.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(dieselAdditionsByPropertyProvider(widget.propertyId));
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: filteredAdditions.length,
                    itemBuilder: (context, index) {
                      final addition = filteredAdditions[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                        child: DieselAdditionCard(
                          addition: addition,
                          onTap: () => _showAdditionDetails(addition),
                          onEdit: () => _showEditAdditionDialog(addition),
                          onDelete: () => _showDeleteConfirmation(addition),
                        ),
                      );
                    },
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
      floatingActionButton: RoleBasedWidget(
        requiredPermissions: const ['properties.update'],
        child: FloatingActionButton(
          onPressed: () => _showAddDieselAdditionDialog(),
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = selected ? value : 'all';
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  List<DieselAddition> _filterAdditions(List<DieselAddition> additions) {
    var filtered = additions;

    // Apply date filter
    if (_selectedFilter != 'all') {
      final now = DateTime.now();
      filtered = filtered.where((addition) {
        switch (_selectedFilter) {
          case 'today':
            return AppUtils.isSameDay(addition.addedAt, now);
          case 'week':
            final weekAgo = now.subtract(const Duration(days: 7));
            return addition.addedAt.isAfter(weekAgo);
          case 'month':
            final monthAgo = DateTime(now.year, now.month - 1, now.day);
            return addition.addedAt.isAfter(monthAgo);
          default:
            return true;
        }
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((addition) {
        return addition.addedBy.toLowerCase().contains(query) ||
               (addition.notes?.toLowerCase().contains(query) ?? false) ||
               addition.quantity.toString().contains(query);
      }).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.addedAt.compareTo(a.addedAt));

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_gas_station,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'No Diesel Additions Found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedFilter != 'all'
                ? 'Try adjusting your search or filters'
                : 'Add your first diesel addition to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          RoleBasedWidget(
            requiredPermissions: const ['properties.update'],
            child: ElevatedButton.icon(
              onPressed: () => _showAddDieselAdditionDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add Diesel Addition'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Failed to load diesel additions',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => ref.invalidate(dieselAdditionsByPropertyProvider(widget.propertyId)),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showAddDieselAdditionDialog() {
    showDialog(
      context: context,
      builder: (context) => AddDieselAdditionDialog(
        propertyId: widget.propertyId,
        onAdditionAdded: () {
          ref.invalidate(dieselAdditionsByPropertyProvider(widget.propertyId));
          AppUtils.showSuccessSnackBar(context, 'Diesel addition recorded successfully');
        },
      ),
    );
  }

  void _showAdditionDetails(DieselAddition addition) {
    // TODO: Navigate to addition details screen
    AppUtils.showInfoSnackBar(context, 'Addition details: ${addition.quantity}L');
  }

  void _showEditAdditionDialog(DieselAddition addition) {
    showDialog(
      context: context,
      builder: (context) => AddDieselAdditionDialog(
        propertyId: widget.propertyId,
        addition: addition,
        onAdditionAdded: () {
          ref.invalidate(dieselAdditionsByPropertyProvider(widget.propertyId));
          AppUtils.showSuccessSnackBar(context, 'Diesel addition updated successfully');
        },
      ),
    );
  }

  void _showDeleteConfirmation(DieselAddition addition) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Diesel Addition'),
        content: Text('Are you sure you want to delete this diesel addition of ${addition.quantity}L?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await ref.read(dieselAdditionsNotifierProvider(widget.propertyId).notifier)
                    .deleteDieselAddition(addition.id);
                AppUtils.showSuccessSnackBar(context, 'Diesel addition deleted successfully');
              } catch (e) {
                AppUtils.showErrorSnackBar(context, 'Failed to delete diesel addition: $e');
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
