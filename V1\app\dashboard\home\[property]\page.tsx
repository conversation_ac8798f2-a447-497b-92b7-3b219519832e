"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>let, Zap, Shield, Wifi, Tv, <PERSON>ch, <PERSON>R<PERSON>, Settings } from "lucide-react"
import Link from "next/link"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"
import { useEffect, useState } from "react"
import { getBrowserClient } from "@/lib/supabase"

export default function PropertyDetailsPage() {
  const params = useParams()
  const propertyId = params.property as string
  const [username, setUsername] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchCurrentUser() {
      try {
        const supabase = getBrowserClient()
        const { data: userData, error } = await supabase.from("users").select("username").limit(1).single()

        if (!error && userData) {
          setUsername(userData.username)
        }
      } catch (error) {
        console.error("Error fetching user:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchCurrentUser()
  }, [])

  const propertyNames = {
    "jublee-hills": "Jublee Hills Home",
    "gandipet-guest-house": "Gandipet Guest House",
  }

  const propertyName = propertyNames[propertyId as keyof typeof propertyNames] || "Property"

  const services = [
    {
      id: "water",
      name: "Water",
      description: "Water supply management",
      icon: <Droplet className="h-8 w-8 text-blue-500" />,
    },
    {
      id: "electricity",
      name: "Electricity",
      description: "Electricity and power management",
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
    },
    {
      id: "security",
      name: "Security",
      description: "Security systems and personnel",
      icon: <Shield className="h-8 w-8 text-green-500" />,
    },
    {
      id: "internet",
      name: "Internet",
      description: "Internet connectivity",
      icon: <Wifi className="h-8 w-8 text-purple-500" />,
    },
    {
      id: "ott",
      name: "OTT",
      description: "Over-the-top media services",
      icon: <Tv className="h-8 w-8 text-red-500" />,
    },
    {
      id: "maintenance",
      name: "Maintenance",
      description: "Property maintenance and issues",
      icon: <Wrench className="h-8 w-8 text-slate-500" />,
    },
  ]

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50">
        <div className="container mx-auto p-4 py-8">
          <div className="mb-6 flex items-center justify-between">
            <div className="h-6 w-64 animate-pulse rounded bg-slate-200"></div>
            <div className="h-6 w-32 animate-pulse rounded bg-slate-200"></div>
          </div>
          <div className="mb-8">
            <div className="h-10 w-48 animate-pulse rounded bg-slate-200"></div>
          </div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-32 animate-pulse rounded-lg bg-slate-200"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // For admin1, show everything without restrictions
  const isAdmin1 = username === "admin1"

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/home">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <div className="mb-8 flex items-center justify-between">
          <h1 className="text-3xl font-bold">{propertyName}</h1>

          {/* Always show settings button for admin1 */}
          <Link href={`/dashboard/home/<USER>/settings`}>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Property Settings
            </Button>
          </Link>
        </div>

        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {services.map((service) => (
            <Link key={service.id} href={`/dashboard/home/<USER>/${service.id}`} className="block">
              <Card className="h-full transition-all hover:shadow-md">
                <CardHeader className="flex flex-row items-center gap-4">
                  {service.icon}
                  <div>
                    <CardTitle>{service.name}</CardTitle>
                    <CardDescription>{service.description}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="flex justify-end">
                  <ArrowRight className="h-5 w-5 text-slate-400" />
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
