import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

const updateFunctionProcessSchema = Joi.object({
  name: Joi.string().min(2).optional(),
  description: Joi.string().optional(),
  function_type: Joi.string().valid('monitoring', 'maintenance', 'reporting', 'automation', 'notification').optional(),
  status: Joi.string().valid('active', 'inactive', 'error', 'pending').optional(),
  schedule_expression: Joi.string().optional(),
  configuration: Joi.object().optional(),
  is_enabled: Joi.boolean().optional(),
});

async function getFunctionProcessHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    const functionProcess = await prisma.functionProcess.findUnique({
      where: { id },
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
        createdByUser: {
          select: {
            id: true,
            fullName: true,
            email: true,
          },
        },
        logs: {
          take: 5,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            status: true,
            message: true,
            createdAt: true,
          },
        },
      },
    });

    if (!functionProcess) {
      return Response.json(
        createApiResponse(null, 'Function process not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    const transformedProcess = {
      id: functionProcess.id,
      property_id: functionProcess.propertyId,
      name: functionProcess.name,
      description: functionProcess.description,
      function_type: functionProcess.functionType,
      status: functionProcess.status,
      schedule_expression: functionProcess.scheduleExpression,
      configuration: functionProcess.configuration,
      is_enabled: functionProcess.isEnabled,
      last_run: functionProcess.lastRun,
      next_run: functionProcess.nextRun,
      run_count: functionProcess.runCount,
      error_count: functionProcess.errorCount,
      property: functionProcess.property ? {
        id: functionProcess.property.id,
        name: functionProcess.property.name,
      } : null,
      created_by: functionProcess.createdByUser ? {
        id: functionProcess.createdByUser.id,
        full_name: functionProcess.createdByUser.fullName,
        email: functionProcess.createdByUser.email,
      } : null,
      recent_logs: functionProcess.logs,
      created_at: functionProcess.createdAt,
      updated_at: functionProcess.updatedAt,
    };

    return Response.json(
      createApiResponse(transformedProcess),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch function process');
  }
}

async function updateFunctionProcessHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);
    
    // Validate request body
    const validation = validateRequest(updateFunctionProcessSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400, headers: corsHeaders() }
      );
    }

    // Verify function process exists
    const existingProcess = await prisma.functionProcess.findUnique({
      where: { id },
    });

    if (!existingProcess) {
      return Response.json(
        createApiResponse(null, 'Function process not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    const updateData: any = {};
    const { 
      name, 
      description, 
      function_type, 
      status, 
      schedule_expression, 
      configuration, 
      is_enabled 
    } = validation.data;

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (function_type !== undefined) updateData.functionType = function_type;
    if (status !== undefined) updateData.status = status;
    if (schedule_expression !== undefined) updateData.scheduleExpression = schedule_expression;
    if (configuration !== undefined) updateData.configuration = configuration;
    if (is_enabled !== undefined) updateData.isEnabled = is_enabled;

    // Update function process
    const updatedProcess = await prisma.functionProcess.update({
      where: { id },
      data: updateData,
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const transformedProcess = {
      id: updatedProcess.id,
      property_id: updatedProcess.propertyId,
      name: updatedProcess.name,
      description: updatedProcess.description,
      function_type: updatedProcess.functionType,
      status: updatedProcess.status,
      schedule_expression: updatedProcess.scheduleExpression,
      configuration: updatedProcess.configuration,
      is_enabled: updatedProcess.isEnabled,
      last_run: updatedProcess.lastRun,
      next_run: updatedProcess.nextRun,
      run_count: updatedProcess.runCount,
      error_count: updatedProcess.errorCount,
      property: updatedProcess.property ? {
        id: updatedProcess.property.id,
        name: updatedProcess.property.name,
      } : null,
      updated_at: updatedProcess.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'Function process updated successfully',
        process: transformedProcess,
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update function process');
  }
}

async function deleteFunctionProcessHandler(
  request: NextRequest, 
  context: { params: { id: string } }, 
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Verify function process exists
    const existingProcess = await prisma.functionProcess.findUnique({
      where: { id },
    });

    if (!existingProcess) {
      return Response.json(
        createApiResponse(null, 'Function process not found', 'NOT_FOUND'),
        { status: 404, headers: corsHeaders() }
      );
    }

    // Delete related logs first
    await prisma.functionProcessLog.deleteMany({
      where: { functionProcessId: id },
    });

    // Delete function process
    await prisma.functionProcess.delete({
      where: { id },
    });

    return Response.json(
      createApiResponse({
        message: 'Function process deleted successfully',
      }),
      { 
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to delete function process');
  }
}

export const GET = requireAuth(getFunctionProcessHandler);
export const PUT = requireAuth(updateFunctionProcessHandler);
export const DELETE = requireAuth(deleteFunctionProcessHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
