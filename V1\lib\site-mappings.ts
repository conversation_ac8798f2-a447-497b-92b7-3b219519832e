// Mapping between route parameters and UUIDs for sites
export const siteIdMappings: Record<string, string> = {
  "gandipet-1": "f47ac10b-58cc-4372-a567-0e02b2c3d479", // Generated UUID
  "gandipet-2": "f47ac10b-58cc-4372-a567-0e02b2c3d480", // Generated UUID
  "gandipet-3-4": "f47ac10b-58cc-4372-a567-0e02b2c3d481", // Generated UUID
  bachupally: "f47ac10b-58cc-4372-a567-0e02b2c3d482", // Generated UUID
}

// Mapping between route parameters and site names
export const siteNames: Record<string, string> = {
  "gandipet-1": "Gandipet 1",
  "gandipet-2": "Gandipet 2",
  "gandipet-3-4": "Gandipet 3 & 4",
  bachupally: "Bachupally",
}

// Office mappings
export const officeIdMappings: Record<string, string> = {
  "back-office-brane": "f47ac10b-58cc-4372-a567-0e02b2c3d483", // Generated UUID
  "strf-office-brane": "f47ac10b-58cc-4372-a567-0e02b2c3d484", // Generated UUID
  "road-no-36-srsr": "f47ac10b-58cc-4372-a567-0e02b2c3d485", // Generated UUID
  "back-office-srsr": "f47ac10b-58cc-4372-a567-0e02b2c3d486", // Generated UUID
}

// Mapping between route parameters and office names
export const officeNames: Record<string, string> = {
  "back-office-brane": "Back Office - Brane",
  "strf-office-brane": "STRF Office - Brane",
  "road-no-36-srsr": "Road No. 36 office - SRSR",
  "back-office-srsr": "Back Office - SRSR",
}

// Mapping between route parameters and office locations (for database)
export const officeLocationMappings: Record<string, string> = {
  "back-office-brane": "Back Office - Brane",
  "strf-office-brane": "STRF Office - Brane",
  "road-no-36-srsr": "Road No. 36 office - SRSR",
  "back-office-srsr": "Back Office - SRSR",
}

// Function to get the actual UUID for a site route parameter
export function getSiteUuid(routeParam: string): string {
  return siteIdMappings[routeParam] || routeParam
}

// Function to get the actual UUID for an office route parameter
export function getOfficeUuid(routeParam: string): string {
  return officeIdMappings[routeParam] || routeParam
}

// Function to get the office location for a route parameter
export function getOfficeLocation(routeParam: string): string {
  return officeLocationMappings[routeParam] || routeParam
}
