import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../lib/helpers/auth_helper.dart';
import '../lib/helpers/test_config.dart';

void main() {
  IntegrationTestWidgetsBinding.ensureInitialized();

  group('Basic Connectivity Tests', () {
    setUpAll(() async {
      print('🚀 Starting basic connectivity tests...');
    });

    testWidgets('Backend connectivity test', (tester) async {
      print('🔗 Testing backend connectivity...');
      
      try {
        // Test if we can reach the backend
        final isConnected = await TestConfig.checkBackendConnectivity();
        expect(isConnected, isTrue, reason: 'Backend should be accessible');
        
        print('✅ Backend connectivity test passed!');
      } catch (e) {
        print('❌ Backend connectivity test failed: $e');
        rethrow;
      }
    });

    testWidgets('Authentication test', (tester) async {
      print('🔐 Testing authentication...');
      
      try {
        // Test admin authentication
        final authResult = await AuthHelper.authenticateAdmin();
        expect(authResult, isNotNull, reason: 'Admin authentication should succeed');
        
        print('✅ Authentication test passed!');
      } catch (e) {
        print('❌ Authentication test failed: $e');
        rethrow;
      }
    });

    testWidgets('Basic API endpoints test', (tester) async {
      print('🌐 Testing basic API endpoints...');
      
      try {
        // Test basic endpoints
        final endpoints = [
          '/api/auth/me',
          '/api/users',
          '/api/roles',
          '/api/properties'
        ];
        
        for (final endpoint in endpoints) {
          final isAccessible = await TestConfig.checkEndpoint(endpoint);
          print('Testing endpoint: $endpoint - ${isAccessible ? "✅" : "❌"}');
        }
        
        print('✅ API endpoints test completed!');
      } catch (e) {
        print('❌ API endpoints test failed: $e');
        rethrow;
      }
    });
  });
}
