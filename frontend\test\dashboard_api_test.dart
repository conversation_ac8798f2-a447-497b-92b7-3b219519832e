import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:srsr_property_management/core/network/dio_client.dart';
import 'package:srsr_property_management/features/dashboard/data/dashboard_api_service.dart';
import 'package:srsr_property_management/features/dashboard/domain/dashboard_models.dart';

void main() {
  group('Dashboard API Tests', () {
    test('should be able to create DioClient', () {
      expect(() => DioClient.instance, returnsNormally);
      expect(DioClient.instance.dio, isA<Dio>());
    });

    test('should be able to create DashboardApiService', () {
      final dio = DioClient.instance.dio;
      expect(() => DashboardApiService(dio), returnsNormally);
    });

    test('should have correct base URL configuration', () {
      final dio = DioClient.instance.dio;
      expect(dio.options.baseUrl, 'http://192.168.1.3:3000');
    });

    test('should be able to parse dashboard JSON manually', () {
      // Test with the actual JSON structure from the logs
      final jsonData = {
        "success": true,
        "data": {
          "properties": {
            "total": 12,
            "operational": 9,
            "warning": 1,
            "critical": 1
          },
          "maintenance_issues": {
            "total": 5,
            "open": 4,
            "in_progress": 1,
            "critical": 1
          },
          "recent_alerts": [
            {
              "id": "41aa47b3-165b-40a0-9f3d-2bc6e12321b0",
              "type": "payment",
              "severity": "medium",
              "message": "OTT service payment due: YouTube Premium",
              "property_name": "Guest House",
              "timestamp": "2025-05-31T13:28:16.020Z"
            },
            {
              "id": "730222db-3168-4c14-bfec-7be3d10e802c",
              "type": "maintenance",
              "severity": "high",
              "message": "high maintenance issue: Generator Fuel Low",
              "property_name": "Guest House",
              "timestamp": "2025-05-31T13:28:15.985Z"
            }
          ]
        }
      };

      // This should not throw an exception
      expect(() => DashboardStatus.fromJson(jsonData), returnsNormally);

      final dashboard = DashboardStatus.fromJson(jsonData);
      expect(dashboard.success, true);
      expect(dashboard.data.properties.total, 12);
      expect(dashboard.data.recentAlerts.length, 2);
      expect(dashboard.data.recentAlerts[0].severity, 'medium');
      expect(dashboard.data.recentAlerts[1].type, 'maintenance');
    });

    test('should handle empty alerts array', () {
      final jsonData = {
        "success": true,
        "data": {
          "properties": {
            "total": 0,
            "operational": 0,
            "warning": 0,
            "critical": 0
          },
          "maintenance_issues": {
            "total": 0,
            "open": 0,
            "in_progress": 0,
            "critical": 0
          },
          "recent_alerts": []
        }
      };

      expect(() => DashboardStatus.fromJson(jsonData), returnsNormally);
      final dashboard = DashboardStatus.fromJson(jsonData);
      expect(dashboard.data.recentAlerts, isEmpty);
    });

    test('should handle missing optional fields gracefully', () {
      final jsonData = {
        "success": true,
        "data": {
          "properties": {
            "total": 1,
            "operational": 1,
            "warning": 0,
            "critical": 0
          },
          "maintenance_issues": {
            "total": 0,
            "open": 0,
            "in_progress": 0,
            "critical": 0
          },
          "recent_alerts": [
            {
              "id": "test-id",
              "type": "test",
              "severity": "low",
              "message": "Test message",
              "property_name": "Test Property",
              "timestamp": "2025-05-31T13:28:16.020Z"
            }
          ]
        }
      };

      expect(() => DashboardStatus.fromJson(jsonData), returnsNormally);
      final dashboard = DashboardStatus.fromJson(jsonData);
      expect(dashboard.data.recentAlerts[0].id, 'test-id');
      expect(dashboard.data.recentAlerts[0].propertyName, 'Test Property');
    });

    test('should validate required fields are present', () {
      // Test missing required fields
      final invalidJsonData = {
        "success": true,
        "data": {
          "properties": {
            "total": 1,
            "operational": 1,
            "warning": 0,
            "critical": 0
          },
          "maintenance_issues": {
            "total": 0,
            "open": 0,
            "in_progress": 0,
            "critical": 0
          },
          "recent_alerts": [
            {
              // Missing required fields like id, type, etc.
              "message": "Test message",
              "timestamp": "2025-05-31T13:28:16.020Z"
            }
          ]
        }
      };

      // This should throw an exception due to missing required fields
      expect(() => DashboardStatus.fromJson(invalidJsonData), throwsA(isA<Error>()));
    });
  });
}
