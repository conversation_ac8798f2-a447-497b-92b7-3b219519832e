import 'package:flutter_test/flutter_test.dart';
import '../lib/helpers/auth_helper.dart';
import '../lib/helpers/test_config.dart';

void main() {
  group('Backend Connectivity Tests', () {
    setUpAll(() {
      print('🚀 Starting backend connectivity tests...');
    });

    test('Backend health check', () async {
      print('🔗 Testing backend connectivity...');
      
      final isConnected = await TestConfig.checkBackendConnectivity();
      print('Backend connectivity: ${isConnected ? "✅ Connected" : "❌ Failed"}');
      
      // Don't fail the test if backend is not available, just report
      expect(isConnected, isA<bool>());
    });

    test('Admin authentication', () async {
      print('🔐 Testing admin authentication...');
      
      final authResult = await AuthHelper.authenticateAdmin();
      print('Admin authentication: ${authResult != null ? "✅ Success" : "❌ Failed"}');
      
      // Don't fail the test if auth fails, just report
      expect(authResult, isA<String?>());
    });

    test('API endpoints accessibility', () async {
      print('🌐 Testing API endpoints...');
      
      final endpoints = [
        '/api/auth/login',
        '/api/users',
        '/api/roles',
        '/api/properties',
        '/api/maintenance',
        '/api/attendance'
      ];
      
      final results = <String, bool>{};
      
      for (final endpoint in endpoints) {
        final isAccessible = await TestConfig.checkEndpoint(endpoint);
        results[endpoint] = isAccessible;
        print('$endpoint: ${isAccessible ? "✅" : "❌"}');
      }
      
      // Report results
      final accessibleCount = results.values.where((v) => v).length;
      print('📊 Accessible endpoints: $accessibleCount/${endpoints.length}');
      
      expect(results, isA<Map<String, bool>>());
    });

    test('Backend API structure validation', () async {
      print('🔍 Testing API structure...');
      
      try {
        // Test if we can reach the base API
        final baseApiAccessible = await TestConfig.checkEndpoint('/api');
        print('Base API (/api): ${baseApiAccessible ? "✅" : "❌"}');
        
        // Test if health endpoint exists
        final healthAccessible = await TestConfig.checkEndpoint('/api/health');
        print('Health endpoint: ${healthAccessible ? "✅" : "❌"}');
        
        // Test if auth endpoints exist
        final authAccessible = await TestConfig.checkEndpoint('/api/auth');
        print('Auth endpoints: ${authAccessible ? "✅" : "❌"}');
        
        expect(baseApiAccessible, isA<bool>());
        expect(healthAccessible, isA<bool>());
        expect(authAccessible, isA<bool>());
        
      } catch (e) {
        print('❌ API structure test failed: $e');
        expect(e, isA<Exception>());
      }
    });

    test('Test configuration validation', () async {
      print('⚙️ Validating test configuration...');
      
      // Test configuration values
      expect(TestConfig.baseUrl, isNotEmpty);
      expect(TestConfig.apiBaseUrl, isNotEmpty);
      expect(TestConfig.testUsers, isNotEmpty);
      expect(TestConfig.testPasswords, isNotEmpty);
      
      print('✅ Test configuration is valid');
      print('Base URL: ${TestConfig.baseUrl}');
      print('API Base URL: ${TestConfig.apiBaseUrl}');
      print('Test users configured: ${TestConfig.testUsers.length}');
    });
  });

  group('Test Environment Validation', () {
    test('Required test data validation', () {
      print('📋 Validating test data...');
      
      // Check admin screens
      expect(TestConfig.adminScreens, isNotEmpty);
      print('Admin screens: ${TestConfig.adminScreens.length}');
      
      // Check main screens
      expect(TestConfig.mainScreens, isNotEmpty);
      print('Main screens: ${TestConfig.mainScreens.length}');
      
      // Check property types
      expect(TestConfig.propertyTypes, isNotEmpty);
      print('Property types: ${TestConfig.propertyTypes.length}');
      
      // Check maintenance types
      expect(TestConfig.maintenanceTypes, isNotEmpty);
      print('Maintenance types: ${TestConfig.maintenanceTypes.length}');
      
      print('✅ Test data validation passed');
    });

    test('Helper methods validation', () {
      print('🛠️ Validating helper methods...');
      
      // Test user email retrieval
      final adminEmail = TestConfig.getTestUserEmail('admin');
      expect(adminEmail, isNotEmpty);
      print('Admin email: $adminEmail');
      
      // Test password retrieval
      final adminPassword = TestConfig.getTestUserPassword('admin');
      expect(adminPassword, isNotEmpty);
      print('Admin password configured: ${adminPassword.isNotEmpty}');
      
      // Test screen categorization
      expect(TestConfig.isAdminScreen('user_management'), isTrue);
      expect(TestConfig.isMainScreen('properties'), isTrue);
      expect(TestConfig.isAdditionalScreen('profile'), isTrue);
      
      print('✅ Helper methods validation passed');
    });
  });
}
