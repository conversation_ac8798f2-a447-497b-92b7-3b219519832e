import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/app_utils.dart';
import '../../../../core/business_logic/escalation_manager.dart';
import '../../../../shared/models/maintenance_issue.dart';
import '../../../../shared/models/property.dart' as property_model;
import '../../../../shared/models/user.dart';
import '../providers/maintenance_providers.dart';
import '../widgets/maintenance_issue_card.dart';
import '../widgets/maintenance_filter_chips.dart';
import '../widgets/add_maintenance_issue_dialog.dart';
import '../widgets/escalation_timeline_widget.dart';

class EnhancedMaintenanceScreen extends ConsumerStatefulWidget {
  const EnhancedMaintenanceScreen({super.key});

  @override
  ConsumerState<EnhancedMaintenanceScreen> createState() => _EnhancedMaintenanceScreenState();
}

class _EnhancedMaintenanceScreenState extends ConsumerState<EnhancedMaintenanceScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';
  String _selectedPriority = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Initialize escalation manager
    EscalationManager.instance.initialize();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final maintenanceAsync = ref.watch(maintenanceIssuesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance'),
        actions: [
          RoleBasedWidget(
            requiredPermissions: const ['maintenance.create'],
            child: IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddIssueDialog,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.invalidate(maintenanceIssuesProvider),
          ),
          RoleBasedWidget(
            requiredPermissions: const ['maintenance.escalate'],
            child: IconButton(
              icon: const Icon(Icons.escalator_warning),
              onPressed: _checkEscalations,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Issues', icon: Icon(Icons.list)),
            Tab(text: 'My Issues', icon: Icon(Icons.person)),
            Tab(text: 'Critical', icon: Icon(Icons.priority_high)),
            Tab(text: 'Escalated', icon: Icon(Icons.escalator_warning)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search maintenance issues...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() => _searchQuery = '');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                // Filter Chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      MaintenanceFilterChip(
                        label: 'All Status',
                        isSelected: _selectedFilter == 'all',
                        onSelected: () => setState(() => _selectedFilter = 'all'),
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Open',
                        isSelected: _selectedFilter == 'open',
                        onSelected: () => setState(() => _selectedFilter = 'open'),
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'In Progress',
                        isSelected: _selectedFilter == 'in_progress',
                        onSelected: () => setState(() => _selectedFilter = 'in_progress'),
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Resolved',
                        isSelected: _selectedFilter == 'resolved',
                        onSelected: () => setState(() => _selectedFilter = 'resolved'),
                        color: Colors.green,
                      ),
                      const SizedBox(width: 16),
                      // Priority Filters
                      MaintenanceFilterChip(
                        label: 'Critical',
                        isSelected: _selectedPriority == 'critical',
                        onSelected: () => setState(() => _selectedPriority = 'critical'),
                        color: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'High',
                        isSelected: _selectedPriority == 'high',
                        onSelected: () => setState(() => _selectedPriority = 'high'),
                        color: Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      MaintenanceFilterChip(
                        label: 'Medium',
                        isSelected: _selectedPriority == 'medium',
                        onSelected: () => setState(() => _selectedPriority = 'medium'),
                        color: Colors.yellow,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildIssuesList(maintenanceAsync, 'all'),
                _buildIssuesList(maintenanceAsync, 'my_issues'),
                _buildIssuesList(maintenanceAsync, 'critical'),
                _buildEscalatedIssuesList(maintenanceAsync),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: RoleBasedFAB(
        requiredPermissions: const ['maintenance.create'],
        onPressed: _showAddIssueDialog,
        tooltip: 'Add Maintenance Issue',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildIssuesList(AsyncValue<List<MaintenanceIssue>> maintenanceAsync, String filter) {
    return maintenanceAsync.when(
      data: (issues) {
        final filteredIssues = _filterIssues(issues, filter);

        if (filteredIssues.isEmpty) {
          return _buildEmptyState(filter);
        }

        return RefreshIndicator(
          onRefresh: () async => ref.invalidate(maintenanceIssuesProvider),
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: filteredIssues.length,
            itemBuilder: (context, index) {
              final issue = filteredIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                child: MaintenanceIssueCard(
                  issue: issue,
                  onTap: () => _navigateToIssueDetails(issue),
                  onStatusUpdate: (newStatus) => _updateIssueStatus(issue, newStatus),
                  onAssign: () => _showAssignDialog(issue),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildEscalatedIssuesList(AsyncValue<List<MaintenanceIssue>> maintenanceAsync) {
    return maintenanceAsync.when(
      data: (issues) {
        // Filter for escalated issues (this would need to be implemented in the provider)
        final escalatedIssues = issues.where((issue) =>
          issue.priority == 'critical' || issue.isOverdue
        ).toList();

        if (escalatedIssues.isEmpty) {
          return _buildEmptyState('escalated');
        }

        return RefreshIndicator(
          onRefresh: () async => ref.invalidate(maintenanceIssuesProvider),
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: escalatedIssues.length,
            itemBuilder: (context, index) {
              final issue = escalatedIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                child: Card(
                  child: Column(
                    children: [
                      MaintenanceIssueCard(
                        issue: issue,
                        onTap: () => _navigateToIssueDetails(issue),
                        onStatusUpdate: (newStatus) => _updateIssueStatus(issue, newStatus),
                        onAssign: () => _showAssignDialog(issue),
                      ),
                      EscalationTimelineWidget(issueId: issue.id),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorState(error),
    );
  }

  Widget _buildEmptyState(String filter) {
    String title, subtitle;
    IconData icon;

    switch (filter) {
      case 'my_issues':
        icon = Icons.person_outline;
        title = 'No issues assigned to you';
        subtitle = 'Issues assigned to you will appear here';
        break;
      case 'critical':
        icon = Icons.priority_high_outlined;
        title = 'No critical issues';
        subtitle = 'Critical priority issues will appear here';
        break;
      case 'escalated':
        icon = Icons.escalator_warning_outlined;
        title = 'No escalated issues';
        subtitle = 'Issues that have been escalated will appear here';
        break;
      default:
        icon = Icons.build_outlined;
        title = 'No maintenance issues';
        subtitle = 'Create your first maintenance issue to get started';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(title, style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(subtitle, style: Theme.of(context).textTheme.bodyMedium),
          if (filter == 'all') ...[
            const SizedBox(height: 24),
            RoleBasedWidget(
              requiredPermissions: const ['maintenance.create'],
              child: ElevatedButton.icon(
                onPressed: _showAddIssueDialog,
                icon: const Icon(Icons.add),
                label: const Text('Add Issue'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('Failed to load maintenance issues',
               style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(error.toString(), style: Theme.of(context).textTheme.bodyMedium),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.invalidate(maintenanceIssuesProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  List<MaintenanceIssue> _filterIssues(List<MaintenanceIssue> issues, String filter) {
    var filtered = issues;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((issue) {
        return issue.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               issue.description.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (_selectedFilter != 'all') {
      filtered = filtered.where((issue) => issue.status == _selectedFilter).toList();
    }

    // Apply priority filter
    if (_selectedPriority != 'all') {
      filtered = filtered.where((issue) => issue.priority == _selectedPriority).toList();
    }

    // Apply tab-specific filters
    switch (filter) {
      case 'my_issues':
        // Filter for current user's issues (would need user context)
        break;
      case 'critical':
        filtered = filtered.where((issue) => issue.priority == 'critical').toList();
        break;
    }

    return filtered;
  }



  void _navigateToIssueDetails(MaintenanceIssue issue) {
    context.push('/maintenance/${issue.id}');
  }

  void _showAddIssueDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddMaintenanceIssueDialog(),
    );
  }

  void _updateIssueStatus(MaintenanceIssue issue, String newStatus) async {
    try {
      final updatedIssue = issue.copyWith(status: newStatus);
      await ref.read(maintenanceIssuesNotifierProvider.notifier).updateIssue(updatedIssue);

      // Resolve escalation if issue is resolved
      if (newStatus == 'resolved') {
        await EscalationManager.instance.resolveEscalation(issue.id, 'Issue resolved');
      }

      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'Issue status updated');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to update status: $e');
      }
    }
  }

  void _showAssignDialog(MaintenanceIssue issue) {
    // Show assignment dialog
    AppUtils.showInfoSnackBar(context, 'Assignment feature coming soon');
  }



  void _checkEscalations() async {
    try {
      final state = ref.read(maintenanceIssuesNotifierProvider);
      final openIssues = state.issues.where((i) => i.status == 'open').map((i) =>
        MaintenanceIssue(
          id: i.id,
          title: i.title,
          description: i.description,
          propertyId: i.propertyId,
          property: property_model.Property(id: i.propertyId, name: 'Unknown Property', type: 'unknown', isActive: true, createdAt: DateTime.now()), // Placeholder for property
          reportedBy: i.reportedBy,
          reporter: User(id: i.reportedBy, email: '<EMAIL>', fullName: 'Unknown Reporter', roles: ['viewer'], isActive: true, createdAt: DateTime.now()), // Placeholder for reporter
          status: i.status,
          priority: i.priority,
          createdAt: i.createdAt,
        )
      ).toList();

      final newEscalations = await EscalationManager.instance.checkEscalations(openIssues);

      if (mounted) {
        if (newEscalations.isNotEmpty) {
          AppUtils.showInfoSnackBar(context, '${newEscalations.length} issues escalated');
        } else {
          AppUtils.showInfoSnackBar(context, 'No issues need escalation');
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'Failed to check escalations: $e');
      }
    }
  }
}
