import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth, requireRole } from '@/lib/auth';
import { validateRequest } from '@/lib/validation';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import Joi from 'joi';

async function getPropertyByIdHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Get property with services and related data
    const property = await prisma.property.findUnique({
      where: { id },
      include: {
        services: {
          select: {
            id: true,
            serviceType: true,
            status: true,
            lastChecked: true,
            notes: true,
          },
        },
        maintenanceIssues: {
          select: {
            id: true,
            title: true,
            priority: true,
            status: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 5, // Latest 5 issues
        },
        generatorFuelLogs: {
          select: {
            id: true,
            fuelLevelLiters: true,
            recordedAt: true,
          },
          orderBy: {
            recordedAt: 'desc',
          },
          take: 1, // Latest fuel log
        },
      },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Transform data to match API response format
    const transformedProperty = {
      id: property.id,
      name: property.name,
      type: property.type.toLowerCase(),
      address: property.address,
      description: property.description,
      image_url: property.imageUrl,
      is_active: property.isActive,
      services: property.services.map(service => ({
        id: service.id,
        service_type: service.serviceType.toLowerCase(),
        status: service.status.toLowerCase(),
        last_checked: service.lastChecked,
        notes: service.notes,
      })),
      recent_maintenance_issues: property.maintenanceIssues.map(issue => ({
        id: issue.id,
        title: issue.title,
        priority: issue.priority.toLowerCase(),
        status: issue.status.toLowerCase(),
        created_at: issue.createdAt,
      })),
      latest_fuel_log: property.generatorFuelLogs[0] ? {
        id: property.generatorFuelLogs[0].id,
        fuel_level_liters: property.generatorFuelLogs[0].fuelLevelLiters,
        recorded_at: property.generatorFuelLogs[0].recordedAt,
      } : null,
      created_at: property.createdAt,
    };

    return Response.json(
      createApiResponse(transformedProperty),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch property');
  }
}

async function updatePropertyHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);

    // Validate request body
    const updatePropertySchema = Joi.object({
      name: Joi.string().min(2).optional(),
      type: Joi.string().valid('residential', 'office').optional(),
      address: Joi.string().optional(),
      description: Joi.string().optional(),
      is_active: Joi.boolean().optional(),
    });

    const validation = validateRequest(updatePropertySchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    // Verify property exists
    const existingProperty = await prisma.property.findUnique({
      where: { id },
    });

    if (!existingProperty) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    const updateData: any = {};

    if (validation.data.name) updateData.name = validation.data.name;
    if (validation.data.type) updateData.type = validation.data.type.toUpperCase();
    if (validation.data.address) updateData.address = validation.data.address;
    if (validation.data.description) updateData.description = validation.data.description;
    if (validation.data.is_active !== undefined) updateData.isActive = validation.data.is_active;

    // Update property
    const updatedProperty = await prisma.property.update({
      where: { id },
      data: updateData,
      include: {
        services: {
          select: {
            id: true,
            serviceType: true,
            status: true,
            lastChecked: true,
            notes: true,
          },
        },
      },
    });

    const transformedProperty = {
      id: updatedProperty.id,
      name: updatedProperty.name,
      type: updatedProperty.type.toLowerCase(),
      address: updatedProperty.address,
      description: updatedProperty.description,
      image_url: updatedProperty.imageUrl,
      is_active: updatedProperty.isActive,
      services: updatedProperty.services.map(service => ({
        id: service.id,
        service_type: service.serviceType.toLowerCase(),
        status: service.status.toLowerCase(),
        last_checked: service.lastChecked,
        notes: service.notes,
      })),
      created_at: updatedProperty.createdAt,
      updated_at: updatedProperty.updatedAt,
    };

    return Response.json(
      createApiResponse({
        message: 'Property updated successfully',
        property: transformedProperty,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update property');
  }
}

export const GET = requireAuth(getPropertyByIdHandler);
export const PUT = requireRole(['admin', 'property_manager'])(updatePropertyHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
