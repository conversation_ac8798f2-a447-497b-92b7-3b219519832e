/// Maintenance workflow and business logic
/// Enhanced from V1's sophisticated maintenance management system
library;

/// Maintenance workflow constants
class MaintenanceConstants {
  static const Map<String, int> priorityLevels = {
    'low': 1,
    'medium': 2,
    'high': 3,
    'critical': 4,
    'emergency': 5,
  };

  static const Map<String, int> statusLevels = {
    'open': 1,
    'assigned': 2,
    'in_progress': 3,
    'pending_review': 4,
    'resolved': 5,
    'closed': 6,
  };

  static const Map<String, int> escalationThresholds = {
    'low': 168, // 7 days in hours
    'medium': 72, // 3 days in hours
    'high': 24, // 1 day in hours
    'critical': 4, // 4 hours
    'emergency': 1, // 1 hour
  };

  static const Map<String, List<String>> allowedTransitions = {
    'open': ['assigned', 'closed'],
    'assigned': ['in_progress', 'open', 'closed'],
    'in_progress': ['pending_review', 'assigned', 'resolved'],
    'pending_review': ['resolved', 'in_progress'],
    'resolved': ['closed', 'in_progress'],
    'closed': ['open'], // For reopening
  };
}

/// Maintenance issue model
class MaintenanceIssue {
  final String id;
  final String title;
  final String description;
  final String priority;
  final String status;
  final String serviceType;
  final String? department;
  final String propertyId;
  final String? assignedTo;
  final DateTime createdAt;
  final DateTime? dueDate;
  final DateTime? resolvedAt;
  final DateTime? closedAt;
  final List<String> tags;
  final Map<String, dynamic> metadata;

  const MaintenanceIssue({
    required this.id,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.serviceType,
    this.department,
    required this.propertyId,
    this.assignedTo,
    required this.createdAt,
    this.dueDate,
    this.resolvedAt,
    this.closedAt,
    this.tags = const [],
    this.metadata = const {},
  });

  factory MaintenanceIssue.fromJson(Map<String, dynamic> json) {
    return MaintenanceIssue(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      priority: json['priority'] ?? 'medium',
      status: json['status'] ?? 'open',
      serviceType: json['service_type'] ?? '',
      department: json['department'],
      propertyId: json['property_id'] ?? '',
      assignedTo: json['assigned_to'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date']) : null,
      resolvedAt: json['resolved_at'] != null ? DateTime.parse(json['resolved_at']) : null,
      closedAt: json['closed_at'] != null ? DateTime.parse(json['closed_at']) : null,
      tags: List<String>.from(json['tags'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// Escalation rule
class EscalationRule {
  final String priority;
  final int thresholdHours;
  final List<String> escalationPath;
  final String action;

  const EscalationRule({
    required this.priority,
    required this.thresholdHours,
    required this.escalationPath,
    required this.action,
  });
}

/// Workflow analysis result
class WorkflowAnalysis {
  final bool needsEscalation;
  final String? escalationReason;
  final List<String> suggestedActions;
  final int urgencyScore;
  final String riskLevel;
  final Map<String, dynamic> insights;

  const WorkflowAnalysis({
    required this.needsEscalation,
    this.escalationReason,
    required this.suggestedActions,
    required this.urgencyScore,
    required this.riskLevel,
    required this.insights,
  });
}

/// Enhanced maintenance workflows with V1 business logic
class MaintenanceWorkflows {
  /// Validate status transition
  static bool isValidTransition(String currentStatus, String newStatus) {
    final allowedTransitions = MaintenanceConstants.allowedTransitions[currentStatus];
    return allowedTransitions?.contains(newStatus) ?? false;
  }

  /// Calculate urgency score based on multiple factors
  static int calculateUrgencyScore(MaintenanceIssue issue) {
    int score = 0;

    // Priority weight (40% of score)
    final priorityWeight = MaintenanceConstants.priorityLevels[issue.priority] ?? 2;
    score += priorityWeight * 20;

    // Age weight (30% of score)
    final ageHours = DateTime.now().difference(issue.createdAt).inHours;
    final ageScore = (ageHours / 24).clamp(0, 10).round();
    score += ageScore * 3;

    // Overdue weight (20% of score)
    if (issue.dueDate != null && DateTime.now().isAfter(issue.dueDate!)) {
      final overdueHours = DateTime.now().difference(issue.dueDate!).inHours;
      final overdueScore = (overdueHours / 24).clamp(0, 10).round();
      score += overdueScore * 2;
    }

    // Service type weight (10% of score)
    final criticalServices = ['electrical', 'plumbing', 'hvac', 'security'];
    if (criticalServices.contains(issue.serviceType.toLowerCase())) {
      score += 10;
    }

    return score.clamp(0, 100);
  }

  /// Determine if issue needs escalation
  static bool needsEscalation(MaintenanceIssue issue) {
    final threshold = MaintenanceConstants.escalationThresholds[issue.priority] ?? 72;
    final ageHours = DateTime.now().difference(issue.createdAt).inHours;

    // Check if issue is overdue based on priority
    if (ageHours >= threshold && issue.status != 'resolved' && issue.status != 'closed') {
      return true;
    }

    // Check if critical issue is unassigned for more than 1 hour
    if (issue.priority == 'critical' && issue.assignedTo == null && ageHours >= 1) {
      return true;
    }

    // Check if emergency issue is unresolved for more than threshold
    if (issue.priority == 'emergency' && issue.status != 'resolved' && ageHours >= 1) {
      return true;
    }

    return false;
  }

  /// Analyze workflow and provide recommendations
  static WorkflowAnalysis analyzeWorkflow(MaintenanceIssue issue, {
    List<MaintenanceIssue>? relatedIssues,
    Map<String, dynamic>? propertyContext,
  }) {
    final urgencyScore = calculateUrgencyScore(issue);
    final needsEscalation = MaintenanceWorkflows.needsEscalation(issue);

    // Determine risk level
    String riskLevel = 'low';
    if (urgencyScore >= 80) {
      riskLevel = 'critical';
    } else if (urgencyScore >= 60) {
      riskLevel = 'high';
    } else if (urgencyScore >= 40) {
      riskLevel = 'medium';
    }

    // Generate suggested actions
    final suggestedActions = _generateSuggestedActions(issue, urgencyScore, relatedIssues);

    // Generate escalation reason
    String? escalationReason;
    if (needsEscalation) {
      escalationReason = _generateEscalationReason(issue);
    }

    // Generate insights
    final insights = _generateWorkflowInsights(issue, relatedIssues, propertyContext);

    return WorkflowAnalysis(
      needsEscalation: needsEscalation,
      escalationReason: escalationReason,
      suggestedActions: suggestedActions,
      urgencyScore: urgencyScore,
      riskLevel: riskLevel,
      insights: insights,
    );
  }

  /// Suggest next best action for issue
  static String suggestNextAction(MaintenanceIssue issue) {
    switch (issue.status) {
      case 'open':
        if (issue.assignedTo == null) {
          return 'assign_technician';
        }
        return 'start_work';

      case 'assigned':
        return 'begin_diagnosis';

      case 'in_progress':
        final ageHours = DateTime.now().difference(issue.createdAt).inHours;
        if (ageHours > 24) {
          return 'provide_update';
        }
        return 'continue_work';

      case 'pending_review':
        return 'review_work';

      case 'resolved':
        return 'close_issue';

      default:
        return 'review_status';
    }
  }

  /// Calculate estimated completion time
  static DateTime? estimateCompletionTime(MaintenanceIssue issue) {
    final baseHours = _getBaseCompletionHours(issue.serviceType, issue.priority);

    // Adjust based on current status
    double remainingHours = baseHours;
    switch (issue.status) {
      case 'assigned':
        remainingHours = baseHours * 0.9;
        break;
      case 'in_progress':
        remainingHours = baseHours * 0.5;
        break;
      case 'pending_review':
        remainingHours = baseHours * 0.1;
        break;
      case 'resolved':
        remainingHours = 0;
        break;
    }

    return DateTime.now().add(Duration(hours: remainingHours.round()));
  }

  /// Detect recurring issues
  static bool isRecurringIssue(MaintenanceIssue issue, List<MaintenanceIssue> historicalIssues) {
    final similarIssues = historicalIssues.where((historical) =>
      historical.propertyId == issue.propertyId &&
      historical.serviceType == issue.serviceType &&
      _calculateSimilarity(issue.title, historical.title) > 0.7 &&
      historical.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 90)))
    ).toList();

    return similarIssues.length >= 3;
  }

  /// Generate suggested actions based on analysis
  static List<String> _generateSuggestedActions(
    MaintenanceIssue issue,
    int urgencyScore,
    List<MaintenanceIssue>? relatedIssues,
  ) {
    final actions = <String>[];

    // Urgency-based actions
    if (urgencyScore >= 80) {
      actions.add('Escalate to senior management');
      actions.add('Assign additional resources');
    } else if (urgencyScore >= 60) {
      actions.add('Prioritize in work queue');
      actions.add('Request status update');
    }

    // Status-based actions
    if (issue.assignedTo == null) {
      actions.add('Assign qualified technician');
    }

    if (issue.dueDate != null && DateTime.now().isAfter(issue.dueDate!)) {
      actions.add('Update due date or expedite completion');
    }

    // Related issues analysis
    if (relatedIssues != null && relatedIssues.length >= 2) {
      actions.add('Consider batch processing related issues');
    }

    return actions;
  }

  /// Generate escalation reason
  static String _generateEscalationReason(MaintenanceIssue issue) {
    final ageHours = DateTime.now().difference(issue.createdAt).inHours;
    final threshold = MaintenanceConstants.escalationThresholds[issue.priority] ?? 72;

    if (ageHours >= threshold) {
      return 'Issue has exceeded ${threshold}h threshold for ${issue.priority} priority';
    }

    if (issue.priority == 'critical' && issue.assignedTo == null) {
      return 'Critical issue remains unassigned after ${ageHours}h';
    }

    if (issue.priority == 'emergency') {
      return 'Emergency issue requires immediate attention';
    }

    return 'Issue requires escalation based on business rules';
  }

  /// Generate workflow insights
  static Map<String, dynamic> _generateWorkflowInsights(
    MaintenanceIssue issue,
    List<MaintenanceIssue>? relatedIssues,
    Map<String, dynamic>? propertyContext,
  ) {
    final insights = <String, dynamic>{};

    // Time analysis
    final ageHours = DateTime.now().difference(issue.createdAt).inHours;
    insights['age_hours'] = ageHours;
    insights['is_overdue'] = issue.dueDate != null && DateTime.now().isAfter(issue.dueDate!);

    // Pattern analysis
    if (relatedIssues != null) {
      insights['related_issues_count'] = relatedIssues.length;
      insights['is_recurring'] = isRecurringIssue(issue, relatedIssues);
    }

    // Completion estimate
    insights['estimated_completion'] = estimateCompletionTime(issue);

    // Next action
    insights['suggested_next_action'] = suggestNextAction(issue);

    return insights;
  }

  /// Get base completion hours for service type and priority
  static double _getBaseCompletionHours(String serviceType, String priority) {
    final baseHours = <String, double>{
      'electrical': 4.0,
      'plumbing': 3.0,
      'hvac': 6.0,
      'security': 2.0,
      'cleaning': 1.0,
      'general': 2.0,
    };

    final priorityMultiplier = <String, double>{
      'emergency': 0.5,
      'critical': 0.7,
      'high': 1.0,
      'medium': 1.5,
      'low': 2.0,
    };

    final base = baseHours[serviceType.toLowerCase()] ?? 2.0;
    final multiplier = priorityMultiplier[priority] ?? 1.0;

    return base * multiplier;
  }

  /// Calculate similarity between two strings (simple implementation)
  static double _calculateSimilarity(String str1, String str2) {
    if (str1 == str2) return 1.0;

    final words1 = str1.toLowerCase().split(' ');
    final words2 = str2.toLowerCase().split(' ');

    int commonWords = 0;
    for (final word in words1) {
      if (words2.contains(word)) {
        commonWords++;
      }
    }

    final totalWords = (words1.length + words2.length) / 2;
    return totalWords > 0 ? commonWords / totalWords : 0.0;
  }
}
