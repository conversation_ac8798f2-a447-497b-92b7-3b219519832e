import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import { NotificationService } from '@/lib/monitoring/notification-service';
import Joi from 'joi';

const sendNotificationSchema = Joi.object({
  type: Joi.string().valid('threshold_alert', 'alert_resolution', 'system_update', 'maintenance_reminder', 'custom').required(),
  title: Joi.string().min(1).max(200).required(),
  message: Joi.string().min(1).max(1000).required(),
  data: Joi.object().optional(),
  priority: Joi.string().valid('low', 'normal', 'high', 'critical').default('normal'),
  target_users: Joi.array().items(Joi.string().uuid()).optional(),
  target_roles: Joi.array().items(Joi.string()).optional(),
  property_id: Joi.string().uuid().optional(),
  send_immediately: Joi.boolean().default(true),
});

async function sendNotificationHandler(
  request: NextRequest,
  context: any,
  currentUser: any
) {
  try {
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(sendNotificationSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const {
      type,
      title,
      message,
      data,
      priority,
      target_users,
      target_roles,
      property_id,
      send_immediately,
    } = validation.data;

    // Determine target users
    let targetUserIds = target_users || [];

    // If target_roles specified, get users with those roles
    if (target_roles && target_roles.length > 0) {
      const usersWithRoles = await prisma.user.findMany({
        where: {
          roles: {
            some: {
              role: {
                name: { in: target_roles },
              },
            },
          },
        },
        select: { id: true },
      });
      
      const roleUserIds = usersWithRoles.map(u => u.id);
      targetUserIds = [...new Set([...targetUserIds, ...roleUserIds])];
    }

    // If no specific targets, send to all active users
    if (targetUserIds.length === 0) {
      const allUsers = await prisma.user.findMany({
        where: { isActive: true },
        select: { id: true },
      });
      targetUserIds = allUsers.map(u => u.id);
    }

    // Create notification in database
    const notification = await prisma.notification.create({
      data: {
        type,
        title,
        message,
        data: data || {},
        priority,
        propertyId: property_id,
        targetUsers: targetUserIds,
        targetRoles: target_roles || [],
      },
    });

    // Create user notification records
    const userNotificationData = targetUserIds.map(userId => ({
      userId,
      notificationId: notification.id,
    }));

    await prisma.userNotification.createMany({
      data: userNotificationData,
    });

    // Send real-time notification if requested
    if (send_immediately) {
      const notificationService = NotificationService.getInstance();
      await notificationService.sendNotification({
        id: notification.id,
        type: type as any,
        title,
        message,
        data: data || {},
        priority: priority as any,
        targetUsers: targetUserIds,
        targetRoles: target_roles,
        propertyId: property_id,
        timestamp: notification.createdAt,
      });
    }

    return Response.json(
      createApiResponse({
        notification: {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          property_id: notification.propertyId,
          target_users: notification.targetUsers,
          target_roles: notification.targetRoles,
          created_at: notification.createdAt,
        },
        delivery_status: {
          target_count: targetUserIds.length,
          sent_immediately: send_immediately,
        },
      }),
      {
        status: 201,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to send notification', 'notification-send');
  }
}

export const POST = requireAuth(sendNotificationHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
