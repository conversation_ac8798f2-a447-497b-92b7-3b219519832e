import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/business_logic/escalation_manager.dart';

class EscalationTimelineWidget extends StatefulWidget {
  final String issueId;
  final bool isExpanded;

  const EscalationTimelineWidget({
    super.key,
    required this.issueId,
    this.isExpanded = false,
  });

  @override
  State<EscalationTimelineWidget> createState() => _EscalationTimelineWidgetState();
}

class _EscalationTimelineWidgetState extends State<EscalationTimelineWidget> {
  bool _isExpanded = false;
  List<EscalationEntry> _escalations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.isExpanded;
    _loadEscalations();
  }

  Future<void> _loadEscalations() async {
    setState(() => _isLoading = true);

    try {
      await EscalationManager.instance.initialize();
      final escalations = EscalationManager.instance.getEscalationHistory(widget.issueId);

      setState(() {
        _escalations = escalations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_escalations.isEmpty && !_isLoading) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              Icons.escalator_warning,
              color: _escalations.any((e) => !e.isResolved) ? Colors.red : Colors.grey,
            ),
            title: Text(
              'Escalation History',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: _isLoading
                ? const Text('Loading...')
                : Text('${_escalations.length} escalation(s)'),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () => setState(() => _isExpanded = !_isExpanded),
            ),
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Center(child: CircularProgressIndicator()),
              )
            else if (_escalations.isEmpty)
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text('No escalations found'),
              )
            else
              _buildTimeline(),
          ],
        ],
      ),
    );
  }

  Widget _buildTimeline() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          for (int i = 0; i < _escalations.length; i++)
            _buildTimelineItem(_escalations[i], i == _escalations.length - 1),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(EscalationEntry escalation, bool isLast) {
    final isResolved = escalation.isResolved;
    final levelColor = _getLevelColor(escalation.level);
    final dateFormat = DateFormat('MMM dd, yyyy HH:mm');

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isResolved ? Colors.green : levelColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
              ),
              child: Icon(
                isResolved ? Icons.check : _getLevelIcon(escalation.level),
                size: 12,
                color: Colors.white,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: Colors.grey.shade300,
              ),
          ],
        ),
        const SizedBox(width: 12),
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Level ${escalation.level.index + 1} Escalation',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: levelColor,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: isResolved ? Colors.green.withValues(alpha: 0.1) : levelColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isResolved ? Colors.green : levelColor,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      isResolved ? 'Resolved' : 'Active',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: isResolved ? Colors.green : levelColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                'Escalated to: ${escalation.escalatedTo}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 2),
              Text(
                'Date: ${dateFormat.format(escalation.escalatedAt)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              if (isResolved) ...[
                const SizedBox(height: 2),
                Text(
                  'Resolved: ${dateFormat.format(escalation.resolvedAt!)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.green,
                  ),
                ),
                if (escalation.resolutionNotes != null) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.green.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      escalation.resolutionNotes!,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ],
              const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }

  Color _getLevelColor(EscalationLevel level) {
    switch (level) {
      case EscalationLevel.level1:
        return Colors.orange;
      case EscalationLevel.level2:
        return Colors.red;
      case EscalationLevel.level3:
        return Colors.purple;
      case EscalationLevel.level4:
        return Colors.indigo;
    }
  }

  IconData _getLevelIcon(EscalationLevel level) {
    switch (level) {
      case EscalationLevel.level1:
        return Icons.keyboard_arrow_up;
      case EscalationLevel.level2:
        return Icons.keyboard_double_arrow_up;
      case EscalationLevel.level3:
        return Icons.priority_high;
      case EscalationLevel.level4:
        return Icons.warning;
    }
  }
}

class EscalationSummaryCard extends StatelessWidget {
  final String issueId;
  final VoidCallback? onViewDetails;

  const EscalationSummaryCard({
    super.key,
    required this.issueId,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<EscalationEntry>>(
      future: _getEscalations(),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const SizedBox.shrink();
        }

        final escalations = snapshot.data!;
        final activeEscalations = escalations.where((e) => !e.isResolved).length;
        final totalEscalations = escalations.length;

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: activeEscalations > 0 ? Colors.red.withValues(alpha: 0.1) : Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: activeEscalations > 0 ? Colors.red : Colors.green,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.escalator_warning,
                size: 20,
                color: activeEscalations > 0 ? Colors.red : Colors.green,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activeEscalations > 0
                          ? '$activeEscalations active escalation(s)'
                          : 'All escalations resolved',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: activeEscalations > 0 ? Colors.red : Colors.green,
                      ),
                    ),
                    if (totalEscalations > 1)
                      Text(
                        '$totalEscalations total escalations',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ),
              if (onViewDetails != null)
                TextButton(
                  onPressed: onViewDetails,
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                  ),
                  child: const Text(
                    'View',
                    style: TextStyle(fontSize: 10),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Future<List<EscalationEntry>> _getEscalations() async {
    await EscalationManager.instance.initialize();
    return EscalationManager.instance.getEscalationHistory(issueId);
  }
}
