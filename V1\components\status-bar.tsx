"use client"

import type React from "react"

import { useState } from "react"
import { StatusDetailsModal } from "@/components/status-details-modal"
import { getDetailedStatusBreakdown } from "@/app/actions/status-data"

interface StatusData {
  healthy: number
  warning: number
  critical: number
  total: number
}

interface StatusBarProps {
  statusData?: StatusData
}

export const StatusBar: React.FC<StatusBarProps> = ({ statusData }) => {
  const [modalOpen, setModalOpen] = useState(false)
  const [modalType, setModalType] = useState<"healthy" | "warning" | "critical">("healthy")
  const [modalDetails, setModalDetails] = useState<any[]>([])

  // Add default values if statusData is undefined
  const safeStatusData = statusData || {
    healthy: 0,
    warning: 0,
    critical: 0,
    total: 0,
  }

  const handleStatusClick = async (type: "healthy" | "warning" | "critical") => {
    try {
      const details = await getDetailedStatusBreakdown(type)
      setModalType(type)
      setModalDetails(details)
      setModalOpen(true)
    } catch (error) {
      console.error("Error loading status details:", error)
    }
  }

  // Show loading state if no data
  if (safeStatusData.total === 0) {
    return (
      <div className="h-6 w-full bg-gray-200 rounded-full animate-pulse">
        <div className="text-xs text-center text-gray-500 leading-6">Loading status...</div>
      </div>
    )
  }

  return (
    <div className="h-6 w-full flex rounded-full overflow-hidden">
      {safeStatusData.healthy > 0 && (
        <button
          onClick={() => handleStatusClick("healthy")}
          className="h-full bg-green-500 hover:bg-green-600 transition-colors first:rounded-l-full flex items-center justify-center text-white text-xs font-medium px-2"
          style={{ width: `${(safeStatusData.healthy / safeStatusData.total) * 100}%` }}
          title={`${safeStatusData.healthy} healthy systems - Click for details`}
        >
          {safeStatusData.healthy}
        </button>
      )}
      {safeStatusData.warning > 0 && (
        <button
          onClick={() => handleStatusClick("warning")}
          className="h-full bg-orange-500 hover:bg-orange-600 transition-colors flex items-center justify-center text-white text-xs font-medium px-2"
          style={{ width: `${(safeStatusData.warning / safeStatusData.total) * 100}%` }}
          title={`${safeStatusData.warning} warning systems - Click for details`}
        >
          {safeStatusData.warning}
        </button>
      )}
      {safeStatusData.critical > 0 && (
        <button
          onClick={() => handleStatusClick("critical")}
          className="h-full bg-red-500 hover:bg-red-600 transition-colors last:rounded-r-full flex items-center justify-center text-white text-xs font-medium px-2"
          style={{ width: `${(safeStatusData.critical / safeStatusData.total) * 100}%` }}
          title={`${safeStatusData.critical} critical systems - Click for details`}
        >
          {safeStatusData.critical}
        </button>
      )}
      <StatusDetailsModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        statusType={modalType}
        details={modalDetails}
      />
    </div>
  )
}
