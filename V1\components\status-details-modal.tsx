"use client"

import { useState } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, CheckCircle, AlertCircle, X } from "lucide-react"
import { getDetailedMaintenanceIssues, type MaintenanceIssue } from "@/app/actions/status-data"

type StatusDetailsModalProps = {
  isOpen: boolean
  onClose: () => void
  statusType: "healthy" | "warning" | "critical"
  details: Array<{
    propertyName: string
    systemType: string
    status: "green" | "orange" | "red"
    details: string[]
  }>
}

const statusIcons = {
  healthy: <CheckCircle className="h-5 w-5 text-green-500" />,
  warning: <AlertTriangle className="h-5 w-5 text-orange-500" />,
  critical: <AlertCircle className="h-5 w-5 text-red-500" />,
}

const statusColors = {
  healthy: "bg-green-50 border-green-200",
  warning: "bg-orange-50 border-orange-200",
  critical: "bg-red-50 border-red-200",
}

const statusTextColors = {
  healthy: "text-green-700",
  warning: "text-orange-700",
  critical: "text-red-700",
}

const statusLabels = {
  healthy: "Operational",
  warning: "Requires Action",
  critical: "Critical",
}

export function StatusDetailsModal({ isOpen, onClose, statusType, details }: StatusDetailsModalProps) {
  const [maintenanceIssues, setMaintenanceIssues] = useState<MaintenanceIssue[]>([])
  const [loadingIssues, setLoadingIssues] = useState(false)
  const [showMaintenanceDetails, setShowMaintenanceDetails] = useState(false)

  const handleViewMaintenanceIssues = async () => {
    if (showMaintenanceDetails) {
      setShowMaintenanceDetails(false)
      return
    }

    setLoadingIssues(true)
    try {
      const issues = await getDetailedMaintenanceIssues(statusType)
      setMaintenanceIssues(issues)
      setShowMaintenanceDetails(true)
    } catch (error) {
      console.error("Error loading maintenance issues:", error)
    } finally {
      setLoadingIssues(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-2">
            {statusIcons[statusType]}
            <DialogTitle className={statusTextColors[statusType]}>
              {statusLabels[statusType]} Systems ({details.length})
            </DialogTitle>
          </div>
          <DialogDescription>
            Detailed information about {statusLabels[statusType].toLowerCase()} systems across all properties
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          {details.map((detail, index) => (
            <Card key={index} className={`border ${statusColors[statusType]}`}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-medium">{detail.propertyName}</h3>
                    <p className="text-sm text-muted-foreground">{detail.systemType}</p>
                  </div>
                  <Badge
                    variant="outline"
                    className={
                      detail.status === "green"
                        ? "bg-green-100 text-green-800 border-green-200"
                        : detail.status === "orange"
                          ? "bg-orange-100 text-orange-800 border-orange-200"
                          : "bg-red-100 text-red-800 border-red-200"
                    }
                  >
                    {detail.status === "green" ? "Healthy" : detail.status === "orange" ? "Attention Needed" : "Urgent"}
                  </Badge>
                </div>

                <ul className="space-y-1 text-sm">
                  {detail.details.map((item, i) => (
                    <li key={i} className="text-slate-600">
                      • {item}
                    </li>
                  ))}
                </ul>

                {detail.systemType === "Maintenance" && statusType === "critical" && (
                  <div className="mt-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleViewMaintenanceIssues}
                      disabled={loadingIssues}
                      className="text-xs"
                    >
                      {loadingIssues
                        ? "Loading..."
                        : showMaintenanceDetails
                          ? "Hide Maintenance Details"
                          : "View Maintenance Details"}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {showMaintenanceDetails && maintenanceIssues.length > 0 && (
            <div className="border rounded-lg p-4 bg-slate-50">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Maintenance Issues</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowMaintenanceDetails(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-3">
                {maintenanceIssues.map((issue) => (
                  <div key={issue.id} className="bg-white p-3 rounded border">
                    <div className="flex justify-between">
                      <span className="font-medium">{issue.issueType}</span>
                      <Badge
                        variant="outline"
                        className={
                          issue.status === "Resolved" || issue.status === "Closed" || issue.status === "Completed"
                            ? "bg-green-100 text-green-800"
                            : issue.status === "In Progress"
                              ? "bg-orange-100 text-orange-800"
                              : "bg-red-100 text-red-800"
                        }
                      >
                        {issue.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-slate-500 mt-1">
                      <p>Property: {issue.propertyName}</p>
                      <p>Priority: {issue.priority}</p>
                      <p>Category: {issue.category}</p>
                      <p>Reported: {new Date(issue.issueDate).toLocaleDateString()}</p>
                      {issue.resolutionDate && <p>Resolved: {new Date(issue.resolutionDate).toLocaleDateString()}</p>}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {details.length === 0 && (
            <div className="text-center py-8">
              <p className="text-slate-500">No {statusLabels[statusType].toLowerCase()} systems found</p>
            </div>
          )}
        </div>

        <div className="flex justify-end mt-4">
          <DialogClose asChild>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  )
}
