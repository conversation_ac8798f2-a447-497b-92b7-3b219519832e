"use client"
import React, { Suspense, useState, useEffect, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Home, Building2, Monitor, ArrowRight, AlertTriangle, CheckCircle, AlertCircle, RefreshCw } from "lucide-react"
import Link from "next/link"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { PageNavigation } from "@/components/page-navigation"
import { Skeleton } from "@/components/ui/skeleton"
import { getStatusData } from "@/app/actions/status-data"
import { StatusBar } from "@/components/status-bar"
import { StatusDetailsModal } from "@/components/status-details-modal"

// Loading component for summary cards
function SummaryLoading() {
  return (
    <div className="grid gap-4 md:grid-cols-4 mb-8">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="pb-2">
            <Skeleton className="h-4 w-24" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

// Status bar with data fetching
async function StatusBarWithData() {
  try {
    const statusData = await getStatusData("all")
    return <StatusBar statusData={statusData} />
  } catch (error) {
    console.error("Error loading status bar data:", error)
    return (
      <div className="h-6 w-full bg-gray-200 rounded-full">
        <div className="text-xs text-center text-red-500 leading-6">Error loading status</div>
      </div>
    )
  }
}

// Summary cards component - Convert to client-side with useEffect
const SummaryCards = React.memo(function SummaryCards() {
  const [modalOpen, setModalOpen] = useState(false)
  const [modalType, setModalType] = useState<"healthy" | "warning" | "critical">("healthy")
  const [modalDetails, setModalDetails] = useState<any[]>([])
  const [summary, setSummary] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Memoize the status data loading
  const loadSummary = useMemo(
    () => async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await getStatusData("all")
        setSummary(data)
        console.log("📊 Dashboard summary loaded:", data)
      } catch (err) {
        console.error("Error loading summary:", err)
        setError("Failed to load status data")
      } finally {
        setLoading(false)
      }
    },
    [],
  )

  // Load data on component mount
  useEffect(() => {
    loadSummary()
  }, [loadSummary])

  const handleStatusClick = async (type: "healthy" | "warning" | "critical") => {
    try {
      console.log(`🔍 Loading detailed breakdown for ${type}...`)
      // Import the detailed status function
      const { getDetailedStatusBreakdown } = await import("@/app/actions/status-data")
      const details = await getDetailedStatusBreakdown(type)

      console.log(
        `📋 Found ${details.length} ${type} details:`,
        details.map((d) => `${d.propertyName}:${d.systemType}`),
      )

      setModalType(type)
      setModalDetails(details)
      setModalOpen(true)
    } catch (error) {
      console.error("Error loading status details:", error)
    }
  }

  const handleRefresh = async () => {
    try {
      setLoading(true)
      const data = await getStatusData("all")
      setSummary(data)
      console.log("📊 Dashboard summary refreshed:", data)
    } catch (err) {
      console.error("Error refreshing summary:", err)
      setError("Failed to refresh status data")
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <SummaryLoading />
  }

  if (error) {
    return (
      <div className="mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="text-center text-red-600">{error}</div>
            <div className="text-center mt-2">
              <button onClick={handleRefresh} className="text-blue-600 hover:text-blue-800 underline">
                Try again
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!summary) {
    return <SummaryLoading />
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-2xl font-bold">Status</h2>
          <p className="text-muted-foreground">Real-time overview of all property systems</p>
        </div>
        <button
          onClick={handleRefresh}
          className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          Refresh
        </button>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Functions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.total}</div>
          </CardContent>
        </Card>

        <Card
          className="bg-green-50 cursor-pointer hover:bg-green-100 transition-colors"
          onClick={() => handleStatusClick("healthy")}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Operational</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <div className="text-2xl font-bold text-green-700">{summary.healthy}</div>
          </CardContent>
        </Card>

        <Card
          className="bg-orange-50 cursor-pointer hover:bg-orange-100 transition-colors"
          onClick={() => handleStatusClick("warning")}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Requires Action</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <div className="text-2xl font-bold text-orange-700">{summary.warning}</div>
          </CardContent>
        </Card>

        <Card
          className="bg-red-50 cursor-pointer hover:bg-red-100 transition-colors"
          onClick={() => handleStatusClick("critical")}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-700">Critical</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <div className="text-2xl font-bold text-red-700">{summary.critical}</div>
          </CardContent>
        </Card>
      </div>

      <StatusDetailsModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        statusType={modalType}
        details={modalDetails}
      />
    </div>
  )
})

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <h1 className="mb-8 text-3xl font-bold">SRSR Property Management</h1>

        {/* System Status Overview - First */}
        <div className="mb-8">
          <Suspense fallback={<div className="h-6 w-full bg-gray-200 rounded-full animate-pulse" />}>
            <StatusBarWithData />
          </Suspense>
        </div>

        {/* Current Statuses Summary - Second */}
        <SummaryCards />

        {/* Main Navigation Cards - Prominent */}
        <div className="grid gap-6 md:grid-cols-2 mb-6">
          <Link href="/dashboard/home" className="block">
            <Card className="h-full transition-all hover:shadow-lg border-2 hover:border-blue-200">
              <CardHeader className="flex flex-row items-center gap-4 p-6">
                <Home className="h-12 w-12 text-blue-600" />
                <div>
                  <CardTitle className="text-xl">Home</CardTitle>
                  <CardDescription className="text-base">Manage residential properties</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="flex justify-end p-6 pt-0">
                <ArrowRight className="h-6 w-6 text-slate-400" />
              </CardContent>
            </Card>
          </Link>

          <Link href="/dashboard/office" className="block">
            <Card className="h-full transition-all hover:shadow-lg border-2 hover:border-blue-200">
              <CardHeader className="flex flex-row items-center gap-4 p-6">
                <Building2 className="h-12 w-12 text-blue-600" />
                <div>
                  <CardTitle className="text-xl">Office</CardTitle>
                  <CardDescription className="text-base">Manage office spaces and sites</CardDescription>
                </div>
              </CardHeader>
              <CardContent className="flex justify-end p-6 pt-0">
                <ArrowRight className="h-6 w-6 text-slate-400" />
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Current Statuses Link - Small */}
        <div className="flex justify-center">
          <Link href="/dashboard/status" className="block">
            <Card className="transition-all hover:shadow-md w-64">
              <CardHeader className="flex flex-row items-center gap-3 p-4">
                <Monitor className="h-6 w-6 text-slate-600" />
                <div>
                  <CardTitle className="text-sm">View Detailed Statuses</CardTitle>
                  <CardDescription className="text-xs">Complete property overview</CardDescription>
                </div>
                <ArrowRight className="h-4 w-4 text-slate-400 ml-auto" />
              </CardHeader>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  )
}
