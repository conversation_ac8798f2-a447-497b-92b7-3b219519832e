"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns"
import { getOfficeLocation, getOfficeUuid } from "@/lib/site-mappings"

// Types
export type Office = {
  id: string
  name: string
  location: string
}

export type OfficeMember = {
  id: string
  name: string
  mobile_number: string
  office_location: string
  role?: string
  duty_time?: string
  remarks?: string
}

// Update the OfficeAttendanceRecord type to include member_name
export type OfficeAttendanceRecord = {
  id: string
  member_id: string
  member_name?: string // Add this field
  date: string
  status: string
  check_in?: string
  check_out?: string
  hours_worked?: number
  remarks?: string
  office_location?: string
}

// Get all offices
export async function getOffices() {
  const supabase = createClient()

  try {
    const { data, error } = await supabase.from("offices").select("*").order("name")

    if (error) {
      console.error("Error fetching offices:", error)
      return []
    }

    return data as Office[]
  } catch (error) {
    console.error("Error fetching offices:", error)
    return []
  }
}

// Get office by ID
export async function getOfficeById(officeId: string) {
  const supabase = createClient()
  const uuid = getOfficeUuid(officeId)

  try {
    const { data, error } = await supabase.from("offices").select("*").eq("id", uuid).single()

    if (error) {
      console.error("Error fetching office:", error)
      return null
    }

    return data as Office
  } catch (error) {
    console.error("Error fetching office:", error)
    return null
  }
}

// Get office members
export async function getOfficeMembers(officeId: string) {
  const supabase = createClient()
  const officeLocation = getOfficeLocation(officeId)

  try {
    const { data, error } = await supabase
      .from("office_members")
      .select("*")
      .eq("office_location", officeLocation)
      .order("name")

    if (error) {
      console.error("Error fetching office members:", error)
      return []
    }

    return data as OfficeMember[]
  } catch (error) {
    console.error("Error fetching office members:", error)
    return []
  }
}

// Add office member
export async function addOfficeMember(member: Omit<OfficeMember, "id">) {
  const supabase = createClient()

  try {
    // Use the office location from the mappings
    const officeLocation = getOfficeLocation(member.office_location)

    const memberToAdd = {
      ...member,
      office_location: officeLocation,
    }

    const { data, error } = await supabase.from("office_members").insert([memberToAdd]).select()

    if (error) {
      console.error("Error adding office member:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/office/${member.office_location}`)
    return { success: true, data: data[0] }
  } catch (error) {
    console.error("Error adding office member:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Update office member
export async function updateOfficeMember(member: OfficeMember) {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from("office_members")
      .update({
        name: member.name,
        mobile_number: member.mobile_number,
        role: member.role,
        duty_time: member.duty_time,
        remarks: member.remarks,
        updated_at: new Date().toISOString(),
      })
      .eq("id", member.id)
      .select()

    if (error) {
      console.error("Error updating office member:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/office/${member.office_location}`)
    return { success: true, data: data[0] }
  } catch (error) {
    console.error("Error updating office member:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Delete office member
export async function deleteOfficeMember(memberId: string, officeId: string) {
  const supabase = createClient()

  try {
    const { error } = await supabase.from("office_members").delete().eq("id", memberId)

    if (error) {
      console.error("Error deleting office member:", error)
      return { success: false, error: error.message }
    }

    revalidatePath(`/dashboard/office/office/${officeId}`)
    return { success: true }
  } catch (error) {
    console.error("Error deleting office member:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Submit attendance
export async function submitOfficeAttendance(records: Omit<OfficeAttendanceRecord, "id">[]) {
  const supabase = createClient()

  try {
    if (!records || records.length === 0) {
      return { success: false, error: "No attendance records provided" }
    }

    // Add office_location to each record if not present
    const recordsWithLocation = records.map((record) => {
      if (!record.office_location) {
        return record
      }
      return {
        ...record,
        office_location: getOfficeLocation(record.office_location),
      }
    })

    const { data, error } = await supabase
      .from("office_attendance")
      .upsert(recordsWithLocation, { onConflict: "member_id,date" })
      .select()

    if (error) {
      console.error("Error submitting attendance:", error)
      return { success: false, error: error.message }
    }

    // Use the first record's office_location for revalidation
    if (records[0].office_location) {
      revalidatePath(`/dashboard/office/office/${records[0].office_location}`)
    }

    return { success: true, data }
  } catch (error) {
    console.error("Error submitting attendance:", error)
    return { success: false, error: (error as Error).message }
  }
}

// Check if attendance exists for a specific date
export async function checkOfficeAttendanceExists(officeId: string, date: string) {
  const supabase = createClient()
  const officeLocation = getOfficeLocation(officeId)

  try {
    const { data, error } = await supabase
      .from("office_attendance")
      .select("id")
      .eq("office_location", officeLocation)
      .eq("date", date)
      .limit(1)

    if (error) {
      console.error("Error checking attendance:", error)
      return false
    }

    return data.length > 0
  } catch (error) {
    console.error("Error checking attendance:", error)
    return false
  }
}

// Update the functions that fetch attendance records to include member names
// Modify getDailyOfficeAttendance function
export async function getDailyOfficeAttendance(officeId: string, date: string) {
  const supabase = createClient()
  const officeLocation = getOfficeLocation(officeId)

  try {
    // Use a join to get member names
    const { data, error } = await supabase
      .from("office_attendance")
      .select(`
        *,
        office_members!inner(name)
      `)
      .eq("office_location", officeLocation)
      .eq("date", date)
      .order("created_at")

    if (error) {
      console.error("Error fetching daily attendance:", error)
      return []
    }

    // Transform the data to include member_name
    return data.map((record) => ({
      ...record,
      member_name: record.office_members.name,
    })) as OfficeAttendanceRecord[]
  } catch (error) {
    console.error("Error fetching daily attendance:", error)
    return []
  }
}

// Modify getWeeklyOfficeAttendance function
export async function getWeeklyOfficeAttendance(officeId: string, date: Date) {
  const supabase = createClient()
  const officeLocation = getOfficeLocation(officeId)

  try {
    const weekStart = format(startOfWeek(date), "yyyy-MM-dd")
    const weekEnd = format(endOfWeek(date), "yyyy-MM-dd")

    // Use a join to get member names
    const { data, error } = await supabase
      .from("office_attendance")
      .select(`
        *,
        office_members!inner(name)
      `)
      .eq("office_location", officeLocation)
      .gte("date", weekStart)
      .lte("date", weekEnd)
      .order("date")

    if (error) {
      console.error("Error fetching weekly attendance:", error)
      return []
    }

    // Transform the data to include member_name
    return data.map((record) => ({
      ...record,
      member_name: record.office_members.name,
    })) as OfficeAttendanceRecord[]
  } catch (error) {
    console.error("Error fetching weekly attendance:", error)
    return []
  }
}

// Modify getMonthlyOfficeAttendance function
export async function getMonthlyOfficeAttendance(officeId: string, date: Date) {
  const supabase = createClient()
  const officeLocation = getOfficeLocation(officeId)

  try {
    const monthStart = format(startOfMonth(date), "yyyy-MM-dd")
    const monthEnd = format(endOfMonth(date), "yyyy-MM-dd")

    // Use a join to get member names
    const { data, error } = await supabase
      .from("office_attendance")
      .select(`
        *,
        office_members!inner(name)
      `)
      .eq("office_location", officeLocation)
      .gte("date", monthStart)
      .lte("date", monthEnd)
      .order("date")

    if (error) {
      console.error("Error fetching monthly attendance:", error)
      return []
    }

    // Transform the data to include member_name
    return data.map((record) => ({
      ...record,
      member_name: record.office_members.name,
    })) as OfficeAttendanceRecord[]
  } catch (error) {
    console.error("Error fetching monthly attendance:", error)
    return []
  }
}
