import '../../../shared/models/property.dart';
import '../../../shared/models/property_service.dart';
import '../domain/properties_repository.dart';
import 'properties_api_service.dart';

class PropertiesRepositoryImpl implements PropertiesRepository {
  final PropertiesApiService _apiService;

  PropertiesRepositoryImpl(this._apiService);

  @override
  Future<List<Property>> getProperties() async {
    try {
      final response = await _apiService.getProperties();
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch properties');
      }
    } catch (e) {
      throw Exception('Failed to fetch properties: $e');
    }
  }

  @override
  Future<Property> getPropertyById(String id) async {
    try {
      final response = await _apiService.getPropertyById(id);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch property');
      }
    } catch (e) {
      throw Exception('Failed to fetch property: $e');
    }
  }

  @override
  Future<Property> createProperty(Property property) async {
    try {
      final request = CreatePropertyRequest(
        name: property.name,
        type: property.type,
        address: property.address,
        description: property.description,
        imageUrl: property.imageUrl,
      );

      final response = await _apiService.createProperty(request);

      if (response.response.statusCode == 201 && response.data != null) {
        // Handle nested response structure from backend
        final responseData = response.data as Map<String, dynamic>;

        // Check if it's a successful API response
        if (responseData['success'] == true && responseData['data'] != null) {
          final data = responseData['data'] as Map<String, dynamic>;

          if (data.containsKey('property')) {
            // Extract property data from nested structure
            final propertyData = data['property'] as Map<String, dynamic>;
            return Property.fromJson(propertyData);
          } else {
            // Fallback: try to parse the entire data as Property
            return Property.fromJson(data);
          }
        } else {
          throw Exception(responseData['error'] ?? 'Failed to create property');
        }
      } else {
        throw Exception('Failed to create property: HTTP ${response.response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to create property: $e');
    }
  }

  @override
  Future<Property> updateProperty(Property property) async {
    try {
      final request = UpdatePropertyRequest(
        name: property.name,
        type: property.type,
        address: property.address,
        description: property.description,
        imageUrl: property.imageUrl,
        isActive: property.isActive,
      );

      final response = await _apiService.updateProperty(property.id, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to update property');
      }
    } catch (e) {
      throw Exception('Failed to update property: $e');
    }
  }

  @override
  Future<void> deleteProperty(String id) async {
    try {
      final response = await _apiService.deleteProperty(id);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete property');
      }
    } catch (e) {
      throw Exception('Failed to delete property: $e');
    }
  }

  @override
  Future<List<PropertyService>> getPropertyServices(String propertyId) async {
    try {
      final response = await _apiService.getPropertyServices(propertyId);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to fetch property services');
      }
    } catch (e) {
      throw Exception('Failed to fetch property services: $e');
    }
  }

  @override
  Future<PropertyService> addPropertyService(String propertyId, PropertyService service) async {
    try {
      final request = CreatePropertyServiceRequest(
        serviceType: service.serviceType,
        status: service.status,
        notes: service.notes,
      );

      final response = await _apiService.addPropertyService(propertyId, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to add property service');
      }
    } catch (e) {
      throw Exception('Failed to add property service: $e');
    }
  }

  @override
  Future<PropertyService> updatePropertyService(String propertyId, PropertyService service) async {
    try {
      final request = UpdatePropertyServiceRequest(
        status: service.status,
        notes: service.notes,
      );

      final response = await _apiService.updatePropertyService(propertyId, service.id, request);
      if (response.success && response.data != null) {
        return response.data!;
      } else {
        throw Exception(response.message ?? 'Failed to update property service');
      }
    } catch (e) {
      throw Exception('Failed to update property service: $e');
    }
  }

  @override
  Future<void> deletePropertyService(String propertyId, String serviceId) async {
    try {
      final response = await _apiService.deletePropertyService(propertyId, serviceId);
      if (!response.success) {
        throw Exception(response.message ?? 'Failed to delete property service');
      }
    } catch (e) {
      throw Exception('Failed to delete property service: $e');
    }
  }
}
