"use client"

import { useParams } from "next/navigation"
import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { Shield, Info, Camera, Users, ExternalLink, User } from "lucide-react"
import { PageNavigation } from "@/components/page-navigation"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { SecurityGuardLogs } from "@/components/security-guard-logs"

export default function SecurityPage() {
  const params = useParams()
  const propertyId = params.property as string
  const [selectedCamera, setSelectedCamera] = useState("1")

  const propertyNames = {
    "jublee-hills": "Jublee Hills Home",
    "gandipet-guest-house": "Gandipet Guest House",
  }

  const propertyName = propertyNames[propertyId as keyof typeof propertyNames] || "Property"

  // Security personnel data
  const securityPersonnel = {
    "jublee-hills": [
      { name: "Bhudev Kumar", position: "Day Shift Security", phone: "9392892445" },
      { name: "Vikas Kumar", position: "Night Shift Security", phone: "7707067414" },
    ],
    "gandipet-guest-house": [
      { name: "Bhudev Kumar", position: "Day Shift Security", phone: "9392892445" },
      { name: "Vikas Kumar", position: "Night Shift Security", phone: "7707067414" },
    ],
  }

  const personnel = securityPersonnel[propertyId as keyof typeof securityPersonnel] || []

  // CCTV details
  const cctvDetails = {
    "jublee-hills": {
      totalCameras: 11,
      recordingCapacity: "30 days",
      resolution: "6 Mega Pixel",
      motionDetection: "Enabled",
      nextMaintenance: "May 15th, 2026",
      cameraLocations: [
        { id: "1", name: "Front Gate facing road" },
        { id: "2", name: "Front Gate facing Security" },
        { id: "3", name: "Front Gate facing main gate" },
        { id: "4", name: "Library Door" },
        { id: "5", name: "Garage, near the entry door" },
        { id: "6", name: "Left Corridor" },
        { id: "7", name: "Garden facing Back Door/Verandah" },
        { id: "8", name: "Garden facing Back Gate" },
        { id: "9", name: "Garden facing Kitchen" },
        { id: "10", name: "Near Kitchen Door" },
        { id: "11", name: "Pool" },
      ],
    },
    "gandipet-guest-house": {
      totalCameras: 8,
      recordingCapacity: "30 days",
      resolution: "6 Mega Pixel",
      motionDetection: "Enabled",
      nextMaintenance: "May 15th, 2026",
      cameraLocations: [
        { id: "1", name: "Main Gate" },
        { id: "2", name: "Front Entrance" },
        { id: "3", name: "Backyard" },
        { id: "4", name: "Parking Area" },
        { id: "5", name: "Side Entrance" },
        { id: "6", name: "Pool Area" },
        { id: "7", name: "Garden" },
        { id: "8", name: "Driveway" },
      ],
    },
  }

  const cctvInfo = cctvDetails[propertyId as keyof typeof cctvDetails] || cctvDetails["jublee-hills"]

  // Security overview content
  const securityOverview = {
    "jublee-hills": (
      <div className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Number of Security Guards:</h3>
          <p>
            <strong>1 guard per shift</strong> near the gate (12-hour shifts)
          </p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">CCTV Cameras:</h3>
          <p>
            <strong>11 cameras</strong> installed at home
          </p>
          <p>
            CCTV systems connected to <strong>UPS/Generator</strong>
          </p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Access Control System:</h3>
          <p>
            <strong>Passcode Type.</strong> One at the bedroom door and One at the door in the Garage.
          </p>
          <p>Batteries to checked monthly and replaced when capacity is less than 50%</p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Entry/Exit Logs:</h3>
          <p>Maintained by security at the Gate</p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Records of Workers:</h3>
          <p>
            Maintained by <strong>Ramana Prasad</strong>
          </p>
        </div>
      </div>
    ),
    "gandipet-guest-house": (
      <div className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Number of Security Guards:</h3>
          <p>
            <strong>1 guard per shift</strong> near the gate (12-hour shifts)
          </p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">CCTV Cameras:</h3>
          <p>
            <strong>8 cameras</strong> installed at guest house
          </p>
          <p>
            CCTV systems connected to <strong>UPS/Generator</strong>
          </p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Access Control System:</h3>
          <p>
            <strong>Passcode Type.</strong> One at the main entrance and one at the back door.
          </p>
          <p>Batteries to checked monthly and replaced when capacity is less than 50%</p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Entry/Exit Logs:</h3>
          <p>Maintained by security at the Gate</p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Records of Workers:</h3>
          <p>
            Maintained by <strong>Ramana Prasad</strong>
          </p>
        </div>
      </div>
    ),
  }

  const overview = securityOverview[propertyId as keyof typeof securityOverview] || securityOverview["jublee-hills"]

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/home">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/security`}>Security</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <div className="mb-8 flex items-center gap-3">
          <Shield className="h-6 w-6 text-green-500" />
          <h1 className="text-3xl font-bold">Security</h1>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-6 grid w-full grid-cols-4">
            <TabsTrigger value="overview">
              <Info className="mr-2 h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="cctv">
              <Camera className="mr-2 h-4 w-4" />
              CCTV
            </TabsTrigger>
            <TabsTrigger value="security-details">
              <Users className="mr-2 h-4 w-4" />
              Security Details
            </TabsTrigger>
            <TabsTrigger value="guard-logs">
              <User className="mr-2 h-4 w-4" />
              Security Guard Logs
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Security Overview
                </CardTitle>
                <CardDescription>General information about security systems for {propertyName}</CardDescription>
              </CardHeader>
              <CardContent>{overview}</CardContent>
            </Card>
          </TabsContent>

          {/* CCTV Tab */}
          <TabsContent value="cctv">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  CCTV System
                </CardTitle>
                <CardDescription>CCTV system information and status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="rounded-md bg-green-50 p-4 text-green-700">
                    <h3 className="mb-2 font-medium">System Status: Operational</h3>
                    <p>All cameras are functioning normally.</p>
                  </div>

                  {propertyId === "jublee-hills" && (
                    <div className="rounded-md border p-4">
                      <h3 className="mb-4 font-medium">CCTV Locations at Jublee Hills Home</h3>
                      <div className="relative h-[600px] w-full overflow-hidden rounded-md border">
                        <Image
                          src="/cctv-locations-map.png"
                          alt="CCTV Locations Map"
                          width={800}
                          height={600}
                          className="h-full w-full object-contain"
                        />
                      </div>
                    </div>
                  )}

                  {propertyId === "gandipet-guest-house" && (
                    <div className="rounded-md border p-4">
                      <h3 className="mb-4 font-medium">CCTV Locations at Gandipet Guest House</h3>
                      <div className="relative h-[400px] w-full overflow-hidden rounded-md border bg-slate-100 flex items-center justify-center">
                        <p className="text-slate-500">CCTV Layout Map</p>
                      </div>
                    </div>
                  )}

                  <div className="rounded-md border p-4">
                    <h3 className="mb-2 font-medium">Cloud Access</h3>
                    <p className="mb-4">
                      Access the CCTV footage remotely through the TrueView cloud service. Login credentials are
                      available from the security supervisor.
                    </p>
                    <Link
                      href="https://trueview.cloud.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex"
                    >
                      <Button variant="outline">
                        <ExternalLink className="mr-2 h-4 w-4" />
                        TrueView Cloud Access
                      </Button>
                    </Link>
                  </div>

                  <div className="space-y-4">
                    <h3 className="font-medium">Camera Selection</h3>
                    <div className="space-y-2">
                      <label htmlFor="camera-select" className="block text-sm font-medium">
                        Select Camera
                      </label>
                      <select
                        id="camera-select"
                        className="w-full rounded-md border border-input bg-background px-3 py-2"
                        value={selectedCamera}
                        onChange={(e) => setSelectedCamera(e.target.value)}
                      >
                        {cctvInfo.cameraLocations.map((camera) => (
                          <option key={camera.id} value={camera.id}>
                            Camera {camera.id} - {camera.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="relative h-[300px] w-full overflow-hidden rounded-md border">
                      <div className="absolute inset-0 flex items-center justify-center bg-slate-100">
                        <p className="text-slate-500">Camera {selectedCamera} Live Feed</p>
                        <p className="absolute bottom-4 left-4 rounded-md bg-black/70 px-2 py-1 text-xs text-white">
                          Live: Camera {selectedCamera}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-md border p-4">
                    <h3 className="mb-2 font-medium">CCTV System Details</h3>
                    <ul className="list-inside list-disc space-y-1">
                      <li>
                        <strong>Total cameras:</strong> {cctvInfo.totalCameras}
                      </li>
                      <li>
                        <strong>Recording Capacity:</strong> {cctvInfo.recordingCapacity}
                      </li>
                      <li>
                        <strong>Resolution:</strong> {cctvInfo.resolution}
                      </li>
                      <li>
                        <strong>Motion Detection:</strong> {cctvInfo.motionDetection}
                      </li>
                      <li>
                        <strong>Next Maintenance Date:</strong> {cctvInfo.nextMaintenance}
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Details Tab */}
          <TabsContent value="security-details">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Security Personnel
                </CardTitle>
                <CardDescription>Security staff information</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    {personnel.map((person, index) => (
                      <div key={index} className="rounded-md border p-4">
                        <h3 className="mb-2 font-medium">{person.name}</h3>
                        <p className="text-sm text-slate-600">{person.position}</p>
                        {person.shift && <p className="text-sm text-slate-600">{person.shift}</p>}
                        <p className="mt-2 text-sm font-medium">{person.phone}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Guard Logs Tab */}
          <TabsContent value="guard-logs">
            <SecurityGuardLogs />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
