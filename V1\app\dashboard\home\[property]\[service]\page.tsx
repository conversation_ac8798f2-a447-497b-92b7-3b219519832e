"use client"

import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/breadcrumb"
import { Droplet, Zap, Shield, Wifi, Tv, Wrench, Phone, Info, Camera, Users, Activity } from "lucide-react"
import { PageNavigation } from "@/components/page-navigation"
import { MaintenanceIssueForm } from "@/components/maintenance-issue-form"
import { MaintenanceIssuesList } from "@/components/maintenance-issues-list"
import { getMaintenanceIssues, type MaintenanceIssue } from "@/app/actions/maintenance-issues"
import { GeneratorStatusDisplay } from "@/components/generator-status-display"
import { GeneratorFuelForm } from "@/components/generator-fuel-form"
import { MaintenanceUpdates } from "@/components/maintenance-updates"
import {
  getGeneratorFuelUpdates,
  getLatestGeneratorFuelUpdate,
  type GeneratorFuelUpdate,
} from "@/app/actions/generator-fuel"
import { getDieselAdditions, type DieselAddition } from "@/app/actions/diesel-additions"
import { DieselAdditionForm } from "@/components/diesel-addition-form"
import { DieselAdditionsList } from "@/components/diesel-additions-list"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { FunctionProcessMatrix } from "@/components/function-process-matrix"
import { toast } from "@/components/ui/use-toast"
import { getFunctionProcesses, type FunctionProcess } from "@/app/actions/function-process"
import { UptimeReports } from "@/components/uptime-reports"

export default function ServiceDetailsPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const propertyId = params.property as string
  const serviceId = params.service as string
  const [issues, setIssues] = useState<MaintenanceIssue[]>([])
  const [loading, setLoading] = useState(true)
  const [fuelUpdates, setFuelUpdates] = useState<GeneratorFuelUpdate[]>([])
  const [latestFuelUpdate, setLatestFuelUpdate] = useState<GeneratorFuelUpdate | null>(null)
  const [dieselAdditions, setDieselAdditions] = useState<DieselAddition[]>([])
  const [functionProcesses, setFunctionProcesses] = useState<FunctionProcess[]>([])
  const [loadingFunctions, setLoadingFunctions] = useState(true)

  const propertyNames = {
    "jublee-hills": "Jublee Hills Home",
    "gandipet-guest-house": "Gandipet Guest House",
  }

  const serviceNames = {
    water: "Water",
    electricity: "Electricity",
    security: "Security",
    internet: "Internet",
    ott: "OTT",
    maintenance: "Maintenance",
  }

  const propertyName = propertyNames[propertyId as keyof typeof propertyNames] || "Property"
  const serviceName = serviceNames[serviceId as keyof typeof serviceNames] || "Service"

  const serviceIcons = {
    water: <Droplet className="h-6 w-6 text-blue-500" />,
    electricity: <Zap className="h-6 w-6 text-yellow-500" />,
    security: <Shield className="h-6 w-6 text-green-500" />,
    internet: <Wifi className="h-6 w-6 text-purple-500" />,
    ott: <Tv className="h-6 w-6 text-red-500" />,
    maintenance: <Wrench className="h-6 w-6 text-slate-500" />,
  }

  const serviceIcon = serviceIcons[serviceId as keyof typeof serviceIcons]

  // Define tabs based on service type
  const getTabs = () => {
    switch (serviceId) {
      case "water":
        return ["overview", "maintenance", "contact-details"]
      case "electricity":
        return ["overview", "maintenance", "generator-status", "contact-details"]
      case "security":
        return ["overview", "cctv", "security-details", "maintenance", "contact-details"]
      case "internet":
        return ["overview", "status", "contact-details"]
      case "ott":
        return ["overview"]
      case "maintenance":
        return ["submit-issue", "issue-status", "ops-as-functions"]
      default:
        return ["overview"]
    }
  }

  const getTabName = (tabId: string) => {
    const tabNames: Record<string, string> = {
      overview: "Overview",
      status: "Status",
      maintenance: "Maintenance",
      "contact-details": "Contact Details",
      "maintenance-updates": "Maintenance Updates",
      "generator-status": "Generator Status",
      cctv: "CCTV",
      "security-details": "Security Details",
      "submit-issue": "Submit an Issue",
      "issue-status": "Issue Status",
      "ops-as-functions": "Functions List",
    }

    return tabNames[tabId] || tabId
  }

  const tabs = getTabs()
  const defaultTab = searchParams.get("tab") || tabs[0]

  const fetchIssues = async () => {
    if (serviceId === "maintenance") {
      setLoading(true)
      try {
        const data = await getMaintenanceIssues(propertyId)
        console.log("🔍 Maintenance issues fetched:", data)
        console.log(
          "🔍 Issue statuses found:",
          data?.map((issue) => ({
            id: issue.id,
            title: issue.issue_type,
            status: issue.status,
            priority: issue.priority,
          })),
        )
        setIssues(data || [])
      } catch (error) {
        console.error("Error fetching issues:", error)
        setIssues([])
      } finally {
        setLoading(false)
      }
    }
  }

  const fetchFuelUpdates = async () => {
    if (serviceId === "electricity") {
      try {
        const updates = await getGeneratorFuelUpdates(propertyId)
        setFuelUpdates(updates || [])

        const latest = await getLatestGeneratorFuelUpdate(propertyId)
        setLatestFuelUpdate(latest)
      } catch (error) {
        console.error("Error fetching fuel updates:", error)
        setFuelUpdates([])
        setLatestFuelUpdate(null)
      }
    }
  }

  const fetchDieselAdditions = async () => {
    if (serviceId === "electricity") {
      try {
        const additions = await getDieselAdditions(propertyId)
        setDieselAdditions(additions || [])
      } catch (error) {
        console.error("Error fetching diesel additions:", error)
        setDieselAdditions([])
      }
    }
  }

  const fetchFunctionProcesses = async () => {
    if (serviceId === "maintenance") {
      setLoadingFunctions(true)
      try {
        const data = await getFunctionProcesses()
        setFunctionProcesses(data || [])
      } catch (error) {
        console.error("Error fetching function processes:", error)
        setFunctionProcesses([])
        toast({
          title: "Error",
          description: "Failed to load function processes. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoadingFunctions(false)
      }
    }
  }

  useEffect(() => {
    fetchIssues()
    fetchFuelUpdates()
    fetchDieselAdditions()
    fetchFunctionProcesses()
  }, [propertyId, serviceId])

  // Get electricity maintenance data
  const getElectricityMaintenanceData = () => {
    if (propertyId === "jublee-hills") {
      return [
        { item: "UPS Unit", status: "No Issues", frequency: "Monthly", lastChecked: "01.05.2025" },
        { item: "Generator Unit", status: "No Issues", frequency: "Monthly", lastChecked: "01.05.2025" },
        { item: "AC", status: "No Issues", frequency: "Annually", lastChecked: "01.04.2025" },
      ]
    } else if (propertyId === "gandipet-guest-house") {
      return [
        { item: "UPS Unit", status: "No Issues", frequency: "Monthly", lastChecked: "01.05.2025" },
        { item: "Generator Unit", status: "No Issues", frequency: "Monthly", lastChecked: "01.05.2025" },
        { item: "Electrical Wiring", status: "No Issues", frequency: "Annually", lastChecked: "01.03.2025" },
      ]
    }

    return []
  }

  // Add property-specific internet details
  const getInternetDetails = () => {
    if (propertyId === "jublee-hills") {
      return {
        primaryProvider: "Act Fibernet",
        planSpeed: "1 Giga Byte per second",
        connectionType: "Fiber Optic Cable",
        backupProvider: "Jio Fiber, 1 Giga byte/Second. Auto changed if needed.",
        networkSystem: "Wireless Mesh System with 5 devices",
        routerLocations: [
          "One router in 1st floor.",
          "Three routers in ground floor. (One in living room, one near Nandini ma'am's study room, one near cellar)",
          "One router in the Gym",
        ],
        contacts: [
          { name: "Pradeep Datla", phone: "98664 43311" },
          { name: "Act Fibernet", phone: "99459 99459" },
          { name: "Ramana Prasad", phone: "99516 55555" },
        ],
      }
    } else if (propertyId === "gandipet-guest-house") {
      return {
        primaryProvider: "Jio",
        planSpeed: "100 MBPS Connection",
        connectionType: "Wireless",
        backupProvider: "Not available.",
        networkSystem: "Wireless System with 3 devices",
        routerLocations: [
          "One router in the main hall.",
          "One router in the guest bedroom area.",
          "One router near the outdoor seating area.",
        ],
        contacts: [
          { name: "Vishnu Penmesta", phone: "73867 77677" },
          { name: "Pradeep Datla", phone: "98664 43311" },
          { name: "Jio Fibernet", phone: "1800-896-9999" },
        ],
      }
    }

    // Default fallback
    return {
      primaryProvider: "Unknown",
      planSpeed: "Unknown",
      connectionType: "Unknown",
      backupProvider: "Unknown",
      networkSystem: "Unknown",
      routerLocations: [],
      contacts: [],
    }
  }

  // Add property-specific electricity details
  const getElectricityDetails = () => {
    if (propertyId === "jublee-hills") {
      return {
        primarySource: "Through a dedicated 100 KV transformer located near the swimming pool.",
        distribution: [
          "Entire House, Swimming Pool, and 2nd Floor of Back Office are powered by the dedicated transformer.",
          "1st Floor of the Back Office is connected to a separate power transmission line through a Public transformer.",
          "When the electricity goes off, the Invertor immediately switches on. Meanwhile, the generator is manually started.",
          "Now, the process is being automated to ensure a seamless transition.",
        ],
        generatorBackup: {
          capacity: "62.5 KV",
          dieselStorageCapacity: "100 litres",
          fuelConsumption: "6-7 liters of diesel per hour",
          storedDiesel: "Minimum 80 liters at all times",
          fuelRefillSchedule: "Maintained by Ajay and replenished weekly",
        },
        upsBackup: [
          "5 KV Invertor in the house (provides 1 hour backup for fans, lights and internet). Invertor batteries are checked every month, and if needed, water is replenished in the batteries.",
          "30 KV UPS for office equipment (provides 45 minutes backup for entire office).",
          "Provides temporary power until the generator is turned on.",
        ],
      }
    } else if (propertyId === "gandipet-guest-house") {
      return {
        customOverview: `The property is equipped with two dedicated transformers:

A 33 kV transformer for residential use

A 75 kV transformer for agricultural operations

A 100 kVA diesel generator is available as a backup power source. It has a fuel tank capacity of 200 litres and consumes approximately 20 litres of diesel per hour during operation.

The fuel level is monitored weekly or after each use. If the fuel gauge drops below 35%, the tank is refilled to ensure uninterrupted backup power availability.`,
        primarySource: "Through a 50 KV transformer located at the entrance gate.",
        distribution: [
          "The entire guest house is powered by a single transformer.",
          "When the electricity goes off, the Invertor immediately switches on. The generator needs to be manually started.",
          "Plans are in place to automate the transition process.",
        ],
        generatorBackup: {
          capacity: "30 KV",
          dieselStorageCapacity: "50 litres",
          fuelConsumption: "3-4 liters of diesel per hour",
          storedDiesel: "Minimum 40 liters at all times",
          fuelRefillSchedule: "Maintained by Vishnu and replenished weekly",
        },
        upsBackup: [
          "3 KV Invertor in the guest house (provides 1 hour backup for essential lights and fans).",
          "Batteries are checked monthly and maintained as needed.",
          "Provides temporary power until the generator is turned on.",
        ],
      }
    }

    // Default fallback
    return {
      primarySource: "Information not available",
      distribution: [],
      generatorBackup: {
        capacity: "Unknown",
        dieselStorageCapacity: "Unknown",
        fuelConsumption: "Unknown",
        storedDiesel: "Unknown",
        fuelRefillSchedule: "Unknown",
      },
      upsBackup: [],
    }
  }

  // Add property-specific water details
  const getWaterDetails = () => {
    if (propertyId === "jublee-hills") {
      return {
        municipalSupply:
          "4 water connections. 2 connections for House, 1 for Garden and other uses, 1 for Swimming pool.",
        borewell: "1 borewell, used only for non-residential purposes in case of shortfall.",
        contingency: "If municipal water supply is disrupted, water tankers are used for residential purposes",
        distribution: {
          sumps: "Two water sumps, one near the gate and one near the compound wall on the right side of the gate.",
          tanks:
            "Two water tanks with a combined capacity of 5000 litres on the terrace. These tanks have two dedicated municipal water connection.",
          connections: [
            "Tank 1 and Tank 2 are connected with a pipe in the center.",
            "Tank 1 has input from the Sump near the gate. This sump gets water from 2 seperate municipal connections.",
            "Tank 1 serves exclusively for Ground Floor and 1st Floor.",
            "Tank 2 serves exclusively for 2nd Floor and it doesn't have any water input.",
            "Water tanks need 3 hours to fill when completely empty",
            "Of the remaining 2 municipal connections, one is used for Gardening and one is used for Swimming Pool.",
          ],
        },
        automation: {
          provider: "Ktronics Technologies Solution",
          technology: "Solar-powered, radio signal communication",
          usecase: [
            "One sensor device installed near the tank. It will have two sensors to check water levels to turn on and turn off the motor.",
            "The reciever device is installed near the Kitchen connected to the 1.5 HP Motor.",
          ],
          trigger: {
            start: "When water level reaches 20%",
            stop: "When water level reaches 90%",
          },
        },
        maintenance: [
          { item: "Pumps & Motors", status: "No Issues", frequency: "Monthly checks", lastChecked: "01.05.2025" },
          { item: "Tank Automation", status: "No Issues", frequency: "Weekly checks", lastChecked: "12.05.2025" },
          { item: "Water Tank Cleaning", status: "No Issues", frequency: "Semi Annually", lastChecked: "01.12.2024" },
        ],
        contacts: [
          { name: "Nagesh", phone: "**********" },
          { name: "GHMC", phone: "040 2343 3933" },
          { name: "Ktronics", phone: "9491388472" },
        ],
      }
    } else if (propertyId === "gandipet-guest-house") {
      return {
        municipalSupply: "There is no Panchayat water connection available at the property.",
        borewell:
          "All water requirements are met through borewells. A total of 8 borewells are located within the premises—4 are operational, while the remaining 4 serve as standby units.",
        contingency: "Each of the 4 active borewells has a discharge capacity of 10,000 litres per hour.",
        distribution: {
          sumps:
            "Water storage infrastructure includes two sumps: An agricultural sump with a capacity of 300,000 litres, equipped with a 7.5 HP pump, and a residential sump with a capacity of 60,000 litres, equipped with a 5 HP pump.",
          tanks:
            "The overhead tank at the Guest House holds 10,000 litres and is sufficient to meet residential demand for up to 2 days.",
          connections: [
            "Water is pumped from the borewells to the sumps",
            "From the residential sump, water is pumped to the overhead tank",
            "The overhead tank supplies water to the entire guest house",
          ],
        },
        automation: {
          provider: "None",
          technology: "Manual operation",
          usecase: [],
          trigger: {
            start: "Manual",
            stop: "Manual",
          },
          hideHeading: true,
        },
        maintenance: [
          { item: "Pumps & Motors", status: "No Issues", frequency: "Monthly checks", lastChecked: "01.05.2025" },
          { item: "Borewell Maintenance", status: "No Issues", frequency: "Quarterly", lastChecked: "01.03.2025" },
          { item: "Water Tank Cleaning", status: "No Issues", frequency: "Quarterly", lastChecked: "01.03.2025" },
        ],
        contacts: [
          { name: "Vishnu", phone: "73867 77677" },
          { name: "GHMC", phone: "040 2343 3933" },
          { name: "Local Plumber", phone: "**********" },
        ],
      }
    }

    // Default fallback
    return {
      municipalSupply: "Information not available",
      borewell: "Information not available",
      contingency: "Information not available",
      distribution: {
        sumps: "Information not available",
        tanks: "Information not available",
        connections: [],
      },
      automation: {
        provider: "Information not available",
        technology: "Information not available",
        usecase: [],
        trigger: {
          start: "Information not available",
          stop: "Information not available",
        },
      },
      maintenance: [],
      contacts: [],
    }
  }

  // Get electricity contacts
  const getElectricityContacts = () => {
    if (propertyId === "jublee-hills") {
      return [
        { name: "Ajay D", phone: "**********" },
        { name: "Pradeep Datla", phone: "**********" },
        { name: "AE, Substation", phone: "**********" },
      ]
    } else if (propertyId === "gandipet-guest-house") {
      return [
        { name: "Vishnu (Caretaker)", phone: "73867 77677" },
        { name: "TSSPDCL", phone: "1912" },
        { name: "Local Electrician", phone: "**********" },
      ]
    }

    return []
  }

  // Get network disruption history data
  const getNetworkDisruptionHistory = () => {
    // Get current date
    const today = new Date()

    // Generate data for the last 7 days
    const data = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)

      // Format date as DD-MM-YYYY
      const formattedDate = `${date.getDate().toString().padStart(2, "0")}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${date.getFullYear()}`

      data.push({
        date: formattedDate,
        uptime: "100%",
        disruptions: "0",
        duration: "0 min",
        cause: "N/A",
      })
    }

    return data
  }

  // Get security details for Gandipet Guest House
  const getGandipetSecurityDetails = () => {
    return {
      overview: `Security Overview
General information about security systems for Gandipet Guest House

Number of Security Guards:
1 guard per shift near the gate (12-hour shifts)

CCTV Cameras:
5 cameras installed at guest house


Entry/Exit Logs:
Maintained by security at the Gate

Records of Workers:
Maintained by Vishnu`,
      guards: [
        { name: "Sunab Sayad", shift: "Day Shift", phone: "7780672687" },
        { name: "Dhananjay", shift: "Night Shift", phone: "8789054994" },
      ],
      cctvDetails: {
        totalCameras: 5,
        retention: "15 days",
        lastMaintenance: "April 25, 2025",
      },
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto p-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/home">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/dashboard/home/<USER>/${serviceId}`}>{serviceName}</BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
          <PageNavigation />
        </div>

        <div className="mb-8 flex items-center gap-3">
          {serviceIcon}
          <h1 className="text-3xl font-bold">{serviceName}</h1>
        </div>

        <Tabs defaultValue={defaultTab} className="w-full">
          <TabsList className="mb-6 grid w-full grid-cols-2 md:grid-cols-4 lg:grid-cols-5">
            {tabs.map((tab) => (
              <TabsTrigger key={tab} value={tab}>
                {getTabName(tab)}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Overview Tab */}
          {tabs.includes("overview") && (
            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="h-5 w-5" />
                    Overview
                  </CardTitle>
                  <CardDescription>
                    General information about {serviceName.toLowerCase()} for {propertyName}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {serviceId === "electricity" ? (
                      <>
                        <div className="space-y-4">
                          {propertyId === "gandipet-guest-house" && getElectricityDetails().customOverview ? (
                            <div className="whitespace-pre-line">{getElectricityDetails().customOverview}</div>
                          ) : (
                            <>
                              <p>
                                <strong>Primary Power Source:</strong>
                              </p>
                              <p>{getElectricityDetails().primarySource}</p>

                              <p className="mt-4">
                                <strong>Distribution:</strong>
                              </p>
                              <ul className="list-inside list-disc space-y-1 ml-4">
                                {getElectricityDetails().distribution.map((item, index) => (
                                  <li key={index}>{item}</li>
                                ))}
                              </ul>

                              <div className="mt-4">
                                <p>
                                  <strong>Generator Backup</strong>
                                </p>
                                <ul className="list-inside list-disc space-y-1 ml-4">
                                  <li>
                                    <strong>Capacity</strong>: {getElectricityDetails().generatorBackup.capacity}
                                  </li>
                                  <li>
                                    <strong>Diesel Storage Tank Capacity</strong>:{" "}
                                    {getElectricityDetails().generatorBackup.dieselStorageCapacity}
                                  </li>
                                  <li>
                                    <strong>Fuel Consumption</strong>:{" "}
                                    {getElectricityDetails().generatorBackup.fuelConsumption}
                                  </li>
                                  <li>
                                    <strong>Stored Diesel</strong>:{" "}
                                    {getElectricityDetails().generatorBackup.storedDiesel}
                                  </li>
                                  <li>
                                    <strong>Fuel Refill Schedule</strong>:{" "}
                                    {getElectricityDetails().generatorBackup.fuelRefillSchedule}
                                  </li>
                                </ul>
                              </div>

                              <div className="mt-4">
                                <p>
                                  <strong>UPS Backup</strong>
                                </p>
                                <ul className="list-inside list-disc space-y-1 ml-4">
                                  {getElectricityDetails().upsBackup.map((item, index) => (
                                    <li key={index}>{item}</li>
                                  ))}
                                </ul>
                              </div>
                            </>
                          )}
                        </div>
                      </>
                    ) : serviceId === "internet" ? (
                      <>
                        <p>
                          <strong>Overview:</strong>
                        </p>
                        <div className="rounded-md bg-slate-100 p-4">
                          <ul className="space-y-2">
                            <li>
                              <strong>Primary Service Provider:</strong> {getInternetDetails().primaryProvider}
                            </li>
                            <li>
                              <strong>Plan Name & Speed:</strong> {getInternetDetails().planSpeed}
                            </li>
                            <li>
                              <strong>Line Connection Type:</strong> {getInternetDetails().connectionType}
                            </li>
                            <li>
                              <strong>Backup Internet Provider:</strong> {getInternetDetails().backupProvider}
                            </li>
                            <li>
                              <strong>Network System:</strong> {getInternetDetails().networkSystem}
                            </li>
                          </ul>
                        </div>

                        <div className="mt-4">
                          <p>
                            <strong>Location of Wifi Routers:</strong>
                          </p>
                          <ul className="list-inside list-disc space-y-1 ml-4">
                            {getInternetDetails().routerLocations.map((location, index) => (
                              <li key={index}>{location}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    ) : serviceId === "water" ? (
                      <>
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-lg font-semibold mb-2">Municipal Water Supply:</h3>
                            <p>{getWaterDetails().municipalSupply}</p>
                          </div>

                          <div>
                            <h3 className="text-lg font-semibold mb-2">Borewell:</h3>
                            <p>{getWaterDetails().borewell}</p>
                          </div>

                          <p>{getWaterDetails().contingency}</p>

                          <div>
                            <h3 className="text-lg font-semibold mb-2">Water Distribution System:</h3>
                            <p>{getWaterDetails().distribution.sumps}</p>
                            <p>{getWaterDetails().distribution.tanks}</p>
                            <ul className="list-inside list-disc space-y-1 ml-4 mt-2">
                              {getWaterDetails().distribution.connections.map((item, index) => (
                                <li key={index}>{item}</li>
                              ))}
                            </ul>
                          </div>

                          {!getWaterDetails().automation?.hideHeading && (
                            <div>
                              <h3 className="text-lg font-semibold mb-2">
                                {getWaterDetails().automation.provider !== "None"
                                  ? "OVERHEAD WATER TANK AUTOMATION SYSTEM INSTALLED"
                                  : "WATER TANK SYSTEM (MANUAL OPERATION)"}
                              </h3>
                              <p>
                                <strong>System Provider:</strong> {getWaterDetails().automation.provider}
                              </p>
                              <p>
                                <strong>Technology:</strong> {getWaterDetails().automation.technology}
                              </p>

                              {getWaterDetails().automation.usecase.length > 0 && (
                                <>
                                  <p className="mt-2">
                                    <strong>Usecase:</strong>
                                  </p>
                                  <ul className="list-inside list-disc space-y-1 ml-4">
                                    {getWaterDetails().automation.usecase.map((item, index) => (
                                      <li key={index}>{item}</li>
                                    ))}
                                  </ul>
                                </>
                              )}

                              <p className="mt-2">
                                <strong>Trigger Mechanism</strong>
                              </p>
                              <p>
                                <strong>Auto Start:</strong> {getWaterDetails().automation.trigger.start}
                              </p>
                              <p>
                                <strong>Auto Stop:</strong> {getWaterDetails().automation.trigger.stop}
                              </p>
                            </div>
                          )}
                        </div>
                      </>
                    ) : serviceId === "security" && propertyId === "gandipet-guest-house" ? (
                      <div className="whitespace-pre-line">{getGandipetSecurityDetails().overview}</div>
                    ) : (
                      <div className="space-y-4">
                        <p>
                          This section provides an overview of the {serviceName.toLowerCase()} system for {propertyName}
                          .
                        </p>
                        <div className="rounded-md bg-slate-100 p-4">
                          <h3 className="mb-2 font-medium">Key Information</h3>
                          <ul className="list-inside list-disc space-y-1">
                            <li>Last maintenance: May 1, 2025</li>
                            <li>Current status: Operational</li>
                            <li>Next scheduled check: June 15, 2025</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Status Tab */}
          {tabs.includes("status") && (
            <TabsContent value="status">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Network Status
                    </CardTitle>
                    <CardDescription>Current status and performance of the internet connection</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Status Overview */}
                      <div className="rounded-md bg-green-50 p-4 text-green-700 flex items-center">
                        <Wifi className="h-6 w-6 mr-2 text-green-500" />
                        <h3 className="font-medium">Network Status: Online</h3>
                        <div className="ml-auto space-x-2">
                          <Button
                            variant="outline"
                            className="bg-white"
                            onClick={() => window.open("https://fast.com/", "_blank")}
                          >
                            Check Speed
                          </Button>
                          <Button
                            variant="outline"
                            className="bg-white"
                            onClick={() => window.open("https://stats.uptimerobot.com/jyL9B1xOqe", "_blank")}
                          >
                            Network Status
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Uptime Reports - Only show for Jublee Hills */}
                {propertyId === "jublee-hills" && <UptimeReports propertyId={propertyId} />}
              </div>
            </TabsContent>
          )}

          {/* Maintenance Tab (Formerly Status Tab) */}
          {tabs.includes("maintenance") && (
            <TabsContent value="maintenance">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wrench className="h-5 w-5" />
                    Maintenance
                  </CardTitle>
                  <CardDescription>Maintenance information and schedule</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {serviceId === "water" ? (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Item</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Frequency</TableHead>
                              <TableHead>Last Checked</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {getWaterDetails().maintenance.map((item, index) => (
                              <TableRow key={index}>
                                <TableCell>{item.item}</TableCell>
                                <TableCell>
                                  <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                                    {item.status}
                                  </span>
                                </TableCell>
                                <TableCell>{item.frequency}</TableCell>
                                <TableCell>{item.lastChecked}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    ) : serviceId === "electricity" ? (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Item</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Frequency</TableHead>
                              <TableHead>Last Checked</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {getElectricityMaintenanceData().map((item, index) => (
                              <TableRow key={index}>
                                <TableCell>{item.item}</TableCell>
                                <TableCell>
                                  <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                                    {item.status}
                                  </span>
                                </TableCell>
                                <TableCell>{item.frequency}</TableCell>
                                <TableCell>{item.lastChecked}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    ) : (
                      <div className="rounded-md bg-green-50 p-4 text-green-700 flex items-center">
                        <Wifi className="h-6 w-6 mr-2 text-green-500" />
                        <h3 className="font-medium">Network Status: Online</h3>
                        <div className="ml-auto space-x-2">
                          <Button
                            variant="outline"
                            className="bg-white"
                            onClick={() => window.open("https://fast.com/", "_blank")}
                          >
                            Check Speed
                          </Button>
                          <Button
                            variant="outline"
                            className="bg-white"
                            onClick={() => window.open("https://stats.uptimerobot.com/jyL9B1xOqe", "_blank")}
                          >
                            Network Status
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Contact Details Tab */}
          {tabs.includes("contact-details") && (
            <TabsContent value="contact-details">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5" />
                    Contact Details
                  </CardTitle>
                  <CardDescription>Contact information for {serviceName.toLowerCase()} services</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="rounded-md border p-4">
                      <h3 className="mb-4 font-medium">Primary Contacts</h3>
                      {serviceId === "water" ? (
                        <div className="space-y-2">
                          {getWaterDetails().contacts.map((contact, index) => (
                            <div key={index} className="flex items-center justify-between border-b pb-2">
                              <span className="font-medium">{contact.name}</span>
                              <span>{contact.phone}</span>
                            </div>
                          ))}
                        </div>
                      ) : serviceId === "internet" ? (
                        <div className="space-y-2">
                          {getInternetDetails().contacts.map((contact, index) => (
                            <div key={index} className="flex items-center justify-between border-b pb-2">
                              <span className="font-medium">{contact.name}</span>
                              <span>{contact.phone}</span>
                            </div>
                          ))}
                        </div>
                      ) : serviceId === "electricity" ? (
                        <div className="space-y-2">
                          {getElectricityContacts().map((contact, index) => (
                            <div key={index} className="flex items-center justify-between border-b pb-2">
                              <span className="font-medium">{contact.name}</span>
                              <span>{contact.phone}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p>Contact information not available</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Generator Status Tab */}
          {tabs.includes("generator-status") && (
            <TabsContent value="generator-status">
              <div className="space-y-6">
                {serviceId === "electricity" && (
                  <>
                    <GeneratorStatusDisplay latestUpdate={latestFuelUpdate} />
                    <GeneratorFuelForm propertyId={propertyId} onSuccess={fetchFuelUpdates} />
                    <MaintenanceUpdates updates={fuelUpdates} onRefresh={fetchFuelUpdates} />
                    <DieselAdditionForm propertyId={propertyId} onSuccess={fetchDieselAdditions} />
                    <DieselAdditionsList additions={dieselAdditions} onRefresh={fetchDieselAdditions} />
                  </>
                )}
              </div>
            </TabsContent>
          )}

          {/* CCTV Tab */}
          {tabs.includes("cctv") && (
            <TabsContent value="cctv">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    CCTV
                  </CardTitle>
                  <CardDescription>CCTV system information and status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="rounded-md bg-green-50 p-4 text-green-700">
                      <h3 className="mb-2 font-medium">System Status: Operational</h3>
                      <p>All cameras are functioning normally.</p>
                    </div>

                    <div className="rounded-md border p-4">
                      <h3 className="mb-2 font-medium">CCTV System Details</h3>
                      {propertyId === "gandipet-guest-house" ? (
                        <>
                          <p>Total cameras: {getGandipetSecurityDetails().cctvDetails.totalCameras}</p>
                          <p>Recording retention: {getGandipetSecurityDetails().cctvDetails.retention}</p>
                          <p>Last maintenance: {getGandipetSecurityDetails().cctvDetails.lastMaintenance}</p>
                        </>
                      ) : (
                        <>
                          <p>Total cameras: 12</p>
                          <p>Recording retention: 30 days</p>
                          <p>Last maintenance: April 25, 2025</p>
                        </>
                      )}
                    </div>

                    {propertyId !== "gandipet-guest-house" && (
                      <>
                        <div className="space-y-2">
                          <h3 className="font-medium">Camera Locations</h3>
                          <div className="grid gap-3 sm:grid-cols-2 md:grid-cols-3">
                            <div className="rounded-md border p-3">
                              <p className="font-medium">Main Gate</p>
                              <p className="text-sm text-slate-500">2 cameras</p>
                            </div>
                            <div className="rounded-md border p-3">
                              <p className="font-medium">Perimeter</p>
                              <p className="text-sm text-slate-500">4 cameras</p>
                            </div>
                            <div className="rounded-md border p-3">
                              <p className="font-medium">Parking Area</p>
                              <p className="text-sm text-slate-500">2 cameras</p>
                            </div>
                            <div className="rounded-md border p-3">
                              <p className="font-medium">Building Entrance</p>
                              <p className="text-sm text-slate-500">1 camera</p>
                            </div>
                            <div className="rounded-md border p-3">
                              <p className="font-medium">Lobby</p>
                              <p className="text-sm text-slate-500">1 camera</p>
                            </div>
                            <div className="rounded-md border p-3">
                              <p className="font-medium">Backyard</p>
                              <p className="text-sm text-slate-500">2 cameras</p>
                            </div>
                          </div>
                        </div>
                      </>
                    )}

                    <div className="rounded-md border p-4">
                      <h3 className="mb-2 font-medium">Access Information</h3>
                      <p>Monitoring station: Security office</p>
                      <p>Remote access: Available for authorized personnel</p>
                      <p>Contact security officer for footage requests</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Security Details Tab */}
          {tabs.includes("security-details") && (
            <TabsContent value="security-details">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Security Details
                  </CardTitle>
                  <CardDescription>Security personnel and system information</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="rounded-md border p-4">
                      <h3 className="mb-2 font-medium">Security Personnel</h3>
                      <div className="space-y-3">
                        <div className="grid gap-2 md:grid-cols-2">
                          {propertyId === "gandipet-guest-house" ? (
                            <>
                              {getGandipetSecurityDetails().guards.map((guard, index) => (
                                <div key={index}>
                                  <p className="font-medium">{guard.shift}</p>
                                  <p>Guard: {guard.name}</p>
                                  <p>Phone: {guard.phone}</p>
                                </div>
                              ))}
                            </>
                          ) : (
                            <>
                              <div>
                                <p className="font-medium">Day Shift</p>
                                <p>Guards: 2</p>
                                <p>Hours: 6:00 AM - 6:00 PM</p>
                                <p>Supervisor: Rajesh Kumar</p>
                              </div>
                              <div>
                                <p className="font-medium">Night Shift</p>
                                <p>Guards: 2</p>
                                <p>Hours: 6:00 PM - 6:00 AM</p>
                                <p>Supervisor: Sunil Verma</p>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="rounded-md border p-4">
                      <h3 className="mb-2 font-medium">Security Systems</h3>
                      <ul className="list-inside list-disc space-y-1">
                        {propertyId === "gandipet-guest-house" ? (
                          <>
                            <li>CCTV surveillance (5 cameras)</li>
                            <li>Entry/Exit logs maintained at gate</li>
                            <li>Worker records maintained by caretaker</li>
                          </>
                        ) : (
                          <>
                            <li>CCTV surveillance (12 cameras)</li>
                            <li>Perimeter alarm system</li>
                            <li>Motion sensors</li>
                            <li>Intercom at main gate</li>
                            <li>Biometric access for main entrance</li>
                          </>
                        )}
                      </ul>
                    </div>

                    <div className="rounded-md border p-4">
                      <h3 className="mb-2 font-medium">Security Protocols</h3>
                      <ul className="list-inside list-disc space-y-1">
                        <li>Visitor registration mandatory</li>
                        <li>Regular perimeter checks every 2 hours</li>
                        <li>Incident reporting procedure in place</li>
                        <li>Emergency response plan available</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

          {/* Submit Issue Tab */}
          {tabs.includes("submit-issue") && (
            <TabsContent value="submit-issue">
              <MaintenanceIssueForm propertyId={propertyId} />
            </TabsContent>
          )}

          {/* Issue Status Tab */}
          {tabs.includes("issue-status") && (
            <TabsContent value="issue-status">
              <MaintenanceIssuesList issues={issues} propertyId={propertyId} onRefresh={fetchIssues} />
            </TabsContent>
          )}

          {/* Ops as Functions Tab */}
          {tabs.includes("ops-as-functions") && (
            <TabsContent value="ops-as-functions">
              <FunctionProcessMatrix />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  )
}
