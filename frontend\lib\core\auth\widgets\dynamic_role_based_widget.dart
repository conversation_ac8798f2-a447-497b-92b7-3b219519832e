import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_role.dart';
import '../../../features/auth/presentation/providers/auth_providers.dart';
import '../providers/permission_providers.dart';

/// A widget that intelligently shows content based on dynamic user roles and permissions
/// This widget pre-checks permissions and only renders authorized content
class DynamicRoleBasedWidget extends ConsumerWidget {
  final List<UserRole>? allowedRoles;
  final List<String>? requiredPermissions;
  final String? requiredResource;
  final String? requiredAction;
  final bool requireAll;
  final Widget child;
  final Widget? fallback;
  final Widget? loadingWidget;
  final bool showLoadingDuringCheck;
  final Duration cacheDuration;

  const DynamicRoleBasedWidget({
    super.key,
    this.allowedRoles,
    this.requiredPermissions,
    this.requiredResource,
    this.requiredAction,
    this.requireAll = false,
    required this.child,
    this.fallback,
    this.loadingWidget,
    this.showLoadingDuringCheck = false,
    this.cacheDuration = const Duration(minutes: 5),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    if (!authState.isAuthenticated || authState.user == null) {
      return fallback ?? const SizedBox.shrink();
    }

    // Use cached permission check for better performance
    final permissionKey = _generatePermissionKey();
    final cachedPermission = ref.watch(cachedPermissionProvider(permissionKey));

    return cachedPermission.when(
      data: (hasAccess) {
        return hasAccess ? child : (fallback ?? const SizedBox.shrink());
      },
      loading: () {
        if (showLoadingDuringCheck) {
          return loadingWidget ?? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          );
        }
        // Don't show anything while checking permissions to avoid flicker
        return const SizedBox.shrink();
      },
      error: (_, __) {
        // On error, default to not showing the widget for security
        return fallback ?? const SizedBox.shrink();
      },
    );
  }

  String _generatePermissionKey() {
    final parts = <String>[];

    if (allowedRoles != null) {
      parts.add('roles:${allowedRoles!.map((r) => r.name).join(',')}');
    }

    if (requiredPermissions != null) {
      parts.add('permissions:${requiredPermissions!.join(',')}');
    }

    if (requiredResource != null && requiredAction != null) {
      parts.add('resource:$requiredResource:$requiredAction');
    }

    parts.add('requireAll:$requireAll');

    return parts.join('|');
  }


}

/// A widget that shows different content based on user permissions
class PermissionBasedBuilder extends ConsumerWidget {
  final List<String> permissions;
  final Widget Function(BuildContext context, List<String> userPermissions) builder;
  final Widget? fallback;

  const PermissionBasedBuilder({
    super.key,
    required this.permissions,
    required this.builder,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    if (!authState.isAuthenticated || authState.user == null) {
      return fallback ?? const SizedBox.shrink();
    }

    return FutureBuilder<List<String>>(
      future: _getUserPermissions(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        }

        final userPermissions = snapshot.data ?? [];
        final relevantPermissions = permissions.where((p) => userPermissions.contains(p)).toList();

        if (relevantPermissions.isEmpty) {
          return fallback ?? const SizedBox.shrink();
        }

        return builder(context, relevantPermissions);
      },
    );
  }

  Future<List<String>> _getUserPermissions(WidgetRef ref) async {
    final userPermissions = await ref.read(userPermissionsProvider.future);

    return permissions.where((permission) =>
      userPermissions.any((p) => p.name == permission)
    ).toList();
  }
}

/// A floating action button that adapts based on dynamic user permissions
class DynamicRoleBasedFAB extends ConsumerWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final List<UserRole>? allowedRoles;
  final List<String>? requiredPermissions;
  final String? requiredResource;
  final String? requiredAction;

  const DynamicRoleBasedFAB({
    super.key,
    this.onPressed,
    this.child,
    this.tooltip,
    this.allowedRoles,
    this.requiredPermissions,
    this.requiredResource,
    this.requiredAction,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DynamicRoleBasedWidget(
      allowedRoles: allowedRoles,
      requiredPermissions: requiredPermissions,
      requiredResource: requiredResource,
      requiredAction: requiredAction,
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        child: child,
      ),
    );
  }
}

/// A button that shows/hides based on dynamic permissions
class PermissionButton extends ConsumerWidget {
  final String permission;
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;

  const PermissionButton({
    super.key,
    required this.permission,
    this.onPressed,
    required this.child,
    this.style,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DynamicRoleBasedWidget(
      requiredPermissions: [permission],
      child: ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: child,
      ),
    );
  }
}

/// A menu item that shows/hides based on dynamic permissions
class PermissionMenuItem extends ConsumerWidget {
  final String permission;
  final VoidCallback? onTap;
  final Widget child;
  final Widget? leading;
  final Widget? trailing;

  const PermissionMenuItem({
    super.key,
    required this.permission,
    this.onTap,
    required this.child,
    this.leading,
    this.trailing,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DynamicRoleBasedWidget(
      requiredPermissions: [permission],
      child: ListTile(
        leading: leading,
        title: child,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }
}

/// A tab that shows/hides based on dynamic permissions
class PermissionTab extends ConsumerWidget {
  final String permission;
  final Widget child;
  final String? text;
  final IconData? icon;

  const PermissionTab({
    super.key,
    required this.permission,
    required this.child,
    this.text,
    this.icon,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DynamicRoleBasedWidget(
      requiredPermissions: [permission],
      child: Tab(
        text: text,
        icon: icon != null ? Icon(icon) : null,
        child: child,
      ),
    );
  }
}

/// A drawer that shows role-appropriate menu items with dynamic permissions
class DynamicRoleBasedDrawer extends ConsumerWidget {
  const DynamicRoleBasedDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    if (!authState.isAuthenticated || authState.user == null) {
      return const Drawer(child: SizedBox.shrink());
    }

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'SRSR Property Management',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  authState.user!.fullName,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white70,
                  ),
                ),
                Text(
                  authState.user!.email,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white60,
                  ),
                ),
              ],
            ),
          ),
          PermissionMenuItem(
            permission: 'properties.read',
            leading: const Icon(Icons.business),
            child: const Text('Properties'),
            onTap: () => Navigator.pushNamed(context, '/properties'),
          ),
          PermissionMenuItem(
            permission: 'maintenance.read',
            leading: const Icon(Icons.build),
            child: const Text('Maintenance'),
            onTap: () => Navigator.pushNamed(context, '/maintenance'),
          ),
          PermissionMenuItem(
            permission: 'attendance.read',
            leading: const Icon(Icons.people),
            child: const Text('Attendance'),
            onTap: () => Navigator.pushNamed(context, '/attendance'),
          ),
          PermissionMenuItem(
            permission: 'fuel.read',
            leading: const Icon(Icons.local_gas_station),
            child: const Text('Fuel Monitoring'),
            onTap: () => Navigator.pushNamed(context, '/fuel'),
          ),
          PermissionMenuItem(
            permission: 'users.read',
            leading: const Icon(Icons.admin_panel_settings),
            child: const Text('Admin'),
            onTap: () => Navigator.pushNamed(context, '/admin'),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: () {
              ref.read(authStateProvider.notifier).logout();
              Navigator.pushReplacementNamed(context, '/login');
            },
          ),
        ],
      ),
    );
  }
}
