import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../../core/constants/app_constants.dart';

/// System health section with circular progress and status breakdown
class SystemHealthSection extends StatelessWidget {
  final double healthScore;
  final dynamic properties;
  final dynamic maintenanceIssues;
  final bool showDetails;

  const SystemHealthSection({
    super.key,
    required this.healthScore,
    required this.properties,
    required this.maintenanceIssues,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHealthOverview(context),
        if (showDetails) ...[
          const SizedBox(height: AppConstants.defaultPadding),
          _buildHealthBreakdown(context),
        ],
      ],
    );
  }

  Widget _buildHealthOverview(BuildContext context) {
    final healthColor = _getHealthColor(healthScore);
    final healthStatus = _getHealthStatus(healthScore);

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            healthColor.withOpacity(0.1),
            healthColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: healthColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Circular health indicator
          SizedBox(
            width: 100,
            height: 100,
            child: Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 100,
                  height: 100,
                  child: CircularProgressIndicator(
                    value: healthScore / 100,
                    strokeWidth: 8,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(healthColor),
                  ),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${healthScore.toInt()}%',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: healthColor,
                      ),
                    ),
                    Text(
                      'Health',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(width: AppConstants.defaultPadding),
          
          // Health details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'System Health',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: healthColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        healthStatus,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: healthColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      _getHealthIcon(healthScore),
                      color: healthColor,
                      size: 16,
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  _getHealthDescription(healthScore),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthBreakdown(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildHealthMetric(
            context,
            'Properties',
            properties.operational,
            properties.total,
            Icons.business,
            Colors.blue,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildHealthMetric(
            context,
            'Maintenance',
            properties.total - maintenanceIssues.total,
            properties.total,
            Icons.build,
            Colors.green,
          ),
        ),
        const SizedBox(width: AppConstants.smallPadding),
        Expanded(
          child: _buildHealthMetric(
            context,
            'Critical Issues',
            maintenanceIssues.critical,
            maintenanceIssues.total,
            Icons.priority_high,
            Colors.red,
            isInverted: true,
          ),
        ),
      ],
    );
  }

  Widget _buildHealthMetric(
    BuildContext context,
    String title,
    int value,
    int total,
    IconData icon,
    Color color, {
    bool isInverted = false,
  }) {
    final percentage = total > 0 ? (value / total * 100) : 0.0;
    final displayPercentage = isInverted ? 100 - percentage : percentage;
    final displayColor = isInverted && percentage > 0 ? Colors.red : color;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: displayColor,
            size: 24,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            '${displayPercentage.toInt()}%',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: displayColor,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          LinearProgressIndicator(
            value: displayPercentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(displayColor),
          ),
        ],
      ),
    );
  }

  Color _getHealthColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 75) return Colors.lightGreen;
    if (score >= 60) return Colors.orange;
    if (score >= 40) return Colors.deepOrange;
    return Colors.red;
  }

  String _getHealthStatus(double score) {
    if (score >= 90) return 'EXCELLENT';
    if (score >= 75) return 'GOOD';
    if (score >= 60) return 'FAIR';
    if (score >= 40) return 'POOR';
    return 'CRITICAL';
  }

  IconData _getHealthIcon(double score) {
    if (score >= 90) return Icons.check_circle;
    if (score >= 75) return Icons.thumb_up;
    if (score >= 60) return Icons.warning;
    if (score >= 40) return Icons.error;
    return Icons.dangerous;
  }

  String _getHealthDescription(double score) {
    if (score >= 90) {
      return 'All systems are operating optimally with minimal issues.';
    } else if (score >= 75) {
      return 'Systems are performing well with minor maintenance needs.';
    } else if (score >= 60) {
      return 'Some systems require attention to maintain optimal performance.';
    } else if (score >= 40) {
      return 'Multiple systems need immediate attention to prevent issues.';
    } else {
      return 'Critical systems failures detected. Immediate action required.';
    }
  }
}

/// Animated health score widget
class AnimatedHealthScore extends StatefulWidget {
  final double targetScore;
  final Color color;
  final Duration duration;

  const AnimatedHealthScore({
    super.key,
    required this.targetScore,
    required this.color,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<AnimatedHealthScore> createState() => _AnimatedHealthScoreState();
}

class _AnimatedHealthScoreState extends State<AnimatedHealthScore>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0,
      end: widget.targetScore,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          width: 120,
          height: 120,
          child: Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 120,
                height: 120,
                child: CircularProgressIndicator(
                  value: _animation.value / 100,
                  strokeWidth: 10,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(widget.color),
                ),
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${_animation.value.toInt()}%',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: widget.color,
                    ),
                  ),
                  Text(
                    'Health Score',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
