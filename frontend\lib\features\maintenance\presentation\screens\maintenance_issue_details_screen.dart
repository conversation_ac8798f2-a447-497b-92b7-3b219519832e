import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/api_constants.dart';
import '../../../../shared/models/maintenance_issue.dart';
import '../providers/maintenance_providers.dart';

class MaintenanceIssueDetailsScreen extends ConsumerWidget {
  final String issueId;
  const MaintenanceIssueDetailsScreen({super.key, required this.issueId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final issueAsync = ref.watch(maintenanceIssueByIdProvider(issueId));
    return Scaffold(
      appBar: AppBar(title: const Text('Maintenance Issue Details')),
      body: issueAsync.when(
        data: (issue) => _buildDetails(context, issue),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, _) => Center(child: Text('Error: $e')),
      ),
    );
  }

  Widget _buildDetails(BuildContext context, MaintenanceIssue issue) {
    debugPrint('Building details for issue: ${issue.id}');
    debugPrint('Property: ${issue.property.name}');
    debugPrint('Reporter: ${issue.reporter.fullName}');
    debugPrint('Assignee: ${issue.assignee?.fullName ?? 'Unassigned'}');

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(issue.title, style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text('Status: ${issue.status}'),
          Text('Priority: ${issue.priority}'),
          const SizedBox(height: 16),
          Text('Property: ${issue.property.name}'),
          Text('Reported by: ${issue.reporter.fullName}'),
          if (issue.assignee != null) Text('Assigned to: ${issue.assignee!.fullName}'),
          const SizedBox(height: 16),
          Text(issue.description),
        ],
      ),
    );
  }
}
