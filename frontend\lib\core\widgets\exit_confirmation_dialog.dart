import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Exit confirmation dialog widget
class ExitConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;

  const ExitConfirmationDialog({
    super.key,
    this.title = 'Exit App',
    this.message = 'Are you sure you want to exit the app?',
    this.confirmText = 'Exit',
    this.cancelText = 'Cancel',
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.exit_to_app,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(cancelText),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.error,
            foregroundColor: Colors.white,
          ),
          child: Text(confirmText),
        ),
      ],
    );
  }

  /// Show exit confirmation dialog
  static Future<bool> show(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const ExitConfirmationDialog(),
    );
    return result ?? false;
  }

  /// Show exit confirmation dialog with custom text
  static Future<bool> showCustom(
    BuildContext context, {
    String? title,
    String? message,
    String? confirmText,
    String? cancelText,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => ExitConfirmationDialog(
        title: title ?? 'Exit App',
        message: message ?? 'Are you sure you want to exit the app?',
        confirmText: confirmText ?? 'Exit',
        cancelText: cancelText ?? 'Cancel',
      ),
    );
    return result ?? false;
  }
}

/// Mixin to handle back button with exit confirmation
mixin ExitConfirmationMixin<T extends StatefulWidget> on State<T> {
  
  /// Handle back button press with confirmation
  Future<bool> onWillPop() async {
    final shouldExit = await ExitConfirmationDialog.show(context);
    if (shouldExit) {
      SystemNavigator.pop();
    }
    return false; // Always return false to prevent default back behavior
  }

  /// Wrap your scaffold with this method
  Widget buildWithExitConfirmation({required Widget child}) {
    return WillPopScope(
      onWillPop: onWillPop,
      child: child,
    );
  }
}

/// Widget wrapper that adds exit confirmation to any screen
class ExitConfirmationWrapper extends StatefulWidget {
  final Widget child;
  final String? title;
  final String? message;
  final String? confirmText;
  final String? cancelText;
  final bool enabled;

  const ExitConfirmationWrapper({
    super.key,
    required this.child,
    this.title,
    this.message,
    this.confirmText,
    this.cancelText,
    this.enabled = true,
  });

  @override
  State<ExitConfirmationWrapper> createState() => _ExitConfirmationWrapperState();
}

class _ExitConfirmationWrapperState extends State<ExitConfirmationWrapper> {
  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return WillPopScope(
      onWillPop: () async {
        final shouldExit = await ExitConfirmationDialog.showCustom(
          context,
          title: widget.title,
          message: widget.message,
          confirmText: widget.confirmText,
          cancelText: widget.cancelText,
        );
        
        if (shouldExit) {
          SystemNavigator.pop();
        }
        
        return false; // Always return false to prevent default back behavior
      },
      child: widget.child,
    );
  }
}
