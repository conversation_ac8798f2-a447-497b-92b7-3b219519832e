# SRSR Property Management Backend API

A comprehensive NextJS + PostgreSQL backend API server for the SRSR Property Management System, built with modern technologies and best practices.

## 🚀 Features

### Core Modules
- **Authentication** - JWT-based authentication with role-based access control
- **Properties** - Complete property management with services tracking
- **Maintenance** - Issue tracking and management system
- **Attendance** - Site and office attendance management
- **Generator Fuel** - Fuel monitoring and logging system
- **Dashboard** - Real-time system status and metrics

### Technical Features
- **NextJS 14** - Modern React framework with App Router
- **PostgreSQL** - Robust relational database
- **Prisma ORM** - Type-safe database access
- **JWT Authentication** - Secure token-based authentication
- **Input Validation** - Comprehensive request validation with Jo<PERSON>
- **Error Handling** - Standardized error responses
- **CORS Support** - Cross-origin resource sharing
- **Pagination** - Efficient data pagination
- **TypeScript** - Full type safety

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL 12+
- npm or yarn

## 🚀 Getting Started

### 1. Clone and Setup
```bash
cd backend
npm install
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb srsr_property_management

# Copy environment variables
cp .env.example .env

# Update DATABASE_URL in .env with your PostgreSQL credentials
# Example: postgresql://username:password@localhost:5432/srsr_property_management
```

### 3. Database Migration and Seeding
```bash
# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Seed database with sample data
npm run db:seed
```

### 4. Start Development Server
```bash
npm run dev
```

The API server will be available at `http://localhost:3000`

## 📊 Database Schema

### Core Tables
- **users** - User accounts with role-based access
- **properties** - Property information and details
- **property_services** - Service status tracking per property
- **maintenance_issues** - Maintenance issue tracking
- **attendance_records** - Employee attendance records
- **generator_fuel_logs** - Generator fuel monitoring
- **security_logs** - Security activity logs
- **sites** - Construction/project sites

## 🔐 Authentication

### Default Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Roles**: admin, property_manager

### JWT Token Usage
```bash
# Login to get token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Use token in subsequent requests
curl -X GET http://localhost:3000/api/properties \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - Register new user (admin only)
- `GET /api/auth/me` - Get current user profile

### Properties
- `GET /api/properties` - Get all properties
- `POST /api/properties` - Create new property
- `GET /api/properties/{id}` - Get property by ID

### Maintenance
- `GET /api/maintenance` - Get maintenance issues (with pagination)
- `POST /api/maintenance` - Create maintenance issue
- `GET /api/maintenance/{id}` - Get specific maintenance issue
- `PUT /api/maintenance/{id}` - Update maintenance issue
- `PATCH /api/maintenance/{id}` - Update issue status only

### Property Management (Consolidated)
- `GET /api/properties?type=office|construction_site|residential` - Get properties by type
- `GET /api/properties/{id}/members` - Get property members
- `POST /api/properties/{id}/members` - Add property member
- `GET /api/properties/{id}/attendance` - Get property attendance
- `POST /api/properties/{id}/attendance` - Submit property attendance

### Generator Fuel
- `GET /api/generator-fuel/{propertyId}` - Get fuel logs
- `POST /api/generator-fuel/{propertyId}` - Add fuel log entry
- `GET /api/generator-fuel/{id}` - Get specific fuel log
- `PUT /api/generator-fuel/{id}` - Update fuel log entry
- `DELETE /api/generator-fuel/{id}` - Delete fuel log entry

### Dashboard
- `GET /api/dashboard/status` - Get system status and metrics

### User Management
- `GET /api/users` - Get all users (with pagination)
- `POST /api/users` - Create new user (admin only)
- `GET /api/users/{id}` - Get specific user
- `PUT /api/users/{id}` - Update user (admin only)
- `DELETE /api/users/{id}` - Delete user (admin only)

## API Documentation (Swagger)

- The OpenAPI/Swagger documentation is available at `/api-docs`.
- To view the docs, run the backend and visit: `http://localhost:3000/api-docs` (or your configured host/port).
- The documentation is served from the `swagger.json` file at the project root.

## Swagger UI

- To view the interactive API documentation, open [http://localhost:3000/swagger.html](http://localhost:3000/swagger.html) in your browser.
- The documentation is powered by the OpenAPI spec at `/swagger.json` (served from the project root or `public/`).
- If you update your API, update `swagger.json` accordingly.

## 🔧 Configuration

### Environment Variables
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/srsr_property_management"

# JWT
JWT_SECRET="your-super-secret-jwt-key"
JWT_EXPIRES_IN="7d"

# App
NODE_ENV="development"
PORT=3000
```

### CORS Configuration
CORS is configured in `next.config.js` to allow cross-origin requests from your Flutter frontend.

## 🧪 Testing

### API Testing with curl
```bash
# Test login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Test properties endpoint
curl -X GET http://localhost:3000/api/properties \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Database Management
```bash
# View database in Prisma Studio
npm run db:studio

# Reset database
npm run db:push --force-reset
npm run db:seed
```

## 📦 Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Setup
1. Set up PostgreSQL database
2. Update environment variables for production
3. Run database migrations
4. Deploy to your preferred platform (Vercel, Railway, etc.)

## 🔒 Security Features

- **JWT Authentication** - Secure token-based authentication
- **Password Hashing** - bcrypt with salt rounds
- **Input Validation** - Joi schema validation
- **Role-based Access** - Admin, property manager, user roles
- **CORS Protection** - Configured cross-origin policies
- **SQL Injection Protection** - Prisma ORM prevents SQL injection

## 📈 Performance

- **Database Indexing** - Optimized database queries
- **Pagination** - Efficient data loading
- **Connection Pooling** - PostgreSQL connection optimization
- **Caching** - Built-in NextJS caching

## 🤝 Integration

This backend is designed to work seamlessly with the Flutter frontend. The API responses match exactly with the swagger.json specification used by the Flutter app.

### Flutter Integration
1. Update API base URL in Flutter app
2. Use the same JWT token for authentication
3. All endpoints match the swagger specification

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:
1. Check the API documentation in swagger.json
2. Review the database schema in prisma/schema.prisma
3. Check logs for detailed error information
