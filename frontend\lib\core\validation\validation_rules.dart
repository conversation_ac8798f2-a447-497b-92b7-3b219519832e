/// Comprehensive validation framework for SRSR Property Management
/// Ported and enhanced from V1's validation patterns
library;

import 'package:reactive_forms/reactive_forms.dart';

class ValidationRules {
  // Email validation
  static List<ValidatorFunction> get emailValidators => [
    Validators.required,
    Validators.email,
  ];

  static Map<String, String> get emailMessages => {
    'required': 'Email is required',
    'email': 'Please enter a valid email address',
  };

  // Password validation
  static List<ValidatorFunction> get passwordValidators => [
    Validators.required,
    Validators.minLength(8),
    _passwordStrength,
  ];

  static Map<String, String> get passwordMessages => {
    'required': 'Password is required',
    'minLength': 'Password must be at least 8 characters',
    'passwordStrength': 'Password must contain uppercase, lowercase, number and special character',
  };

  // Property name validation
  static List<ValidatorFunction> get propertyNameValidators => [
    Validators.required,
    Validators.minLength(2),
    Validators.maxLength(100),
    _noSpecialChars,
  ];

  static Map<String, String> get propertyNameMessages => {
    'required': 'Property name is required',
    'minLength': 'Property name must be at least 2 characters',
    'maxLength': 'Property name cannot exceed 100 characters',
    'noSpecialChars': 'Property name cannot contain special characters',
  };

  // Maintenance issue validation
  static List<ValidatorFunction> get maintenanceTitleValidators => [
    Validators.required,
    Validators.minLength(5),
    Validators.maxLength(200),
  ];

  static Map<String, String> get maintenanceTitleMessages => {
    'required': 'Issue title is required',
    'minLength': 'Title must be at least 5 characters',
    'maxLength': 'Title cannot exceed 200 characters',
  };

  static List<ValidatorFunction> get maintenanceDescriptionValidators => [
    Validators.required,
    Validators.minLength(10),
    Validators.maxLength(1000),
  ];

  static Map<String, String> get maintenanceDescriptionMessages => {
    'required': 'Description is required',
    'minLength': 'Description must be at least 10 characters',
    'maxLength': 'Description cannot exceed 1000 characters',
  };

  // Fuel level validation
  static List<ValidatorFunction> get fuelLevelValidators => [
    Validators.required,
    Validators.min(0),
    Validators.max(1000),
    _positiveNumber,
  ];

  static Map<String, String> get fuelLevelMessages => {
    'required': 'Fuel level is required',
    'min': 'Fuel level cannot be negative',
    'max': 'Fuel level cannot exceed 1000 liters',
    'positiveNumber': 'Fuel level must be a positive number',
  };

  // Attendance validation
  static List<ValidatorFunction> get workerNameValidators => [
    Validators.required,
    Validators.minLength(2),
    Validators.maxLength(50),
    _nameFormat,
  ];

  static Map<String, String> get workerNameMessages => {
    'required': 'Worker name is required',
    'minLength': 'Name must be at least 2 characters',
    'maxLength': 'Name cannot exceed 50 characters',
    'nameFormat': 'Name can only contain letters and spaces',
  };

  static List<ValidatorFunction> get hoursWorkedValidators => [
    Validators.required,
    Validators.min(0),
    Validators.max(24),
    _validHours,
  ];

  static Map<String, String> get hoursWorkedMessages => {
    'required': 'Hours worked is required',
    'min': 'Hours cannot be negative',
    'max': 'Hours cannot exceed 24 per day',
    'validHours': 'Hours must be in 0.5 hour increments',
  };

  // Phone number validation
  static List<ValidatorFunction> get phoneNumberValidators => [
    Validators.required,
    _phoneFormat,
  ];

  static Map<String, String> get phoneNumberMessages => {
    'required': 'Phone number is required',
    'phoneFormat': 'Please enter a valid phone number',
  };

  // Custom validators
  static ValidatorFunction get _passwordStrength => (AbstractControl<dynamic> control) {
    if (control.value == null || control.value.isEmpty) return null;

    final password = control.value as String;
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    if (hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters) {
      return null;
    }

    return {'passwordStrength': true};
  };

  static ValidatorFunction get _noSpecialChars => (AbstractControl<dynamic> control) {
    if (control.value == null || control.value.isEmpty) return null;

    final value = control.value as String;
    if (RegExp(r'^[a-zA-Z0-9\s\-_]+$').hasMatch(value)) {
      return null;
    }

    return {'noSpecialChars': true};
  };

  static ValidatorFunction get _positiveNumber => (AbstractControl<dynamic> control) {
    if (control.value == null) return null;

    final value = double.tryParse(control.value.toString());
    if (value != null && value >= 0) {
      return null;
    }

    return {'positiveNumber': true};
  };

  static ValidatorFunction get _nameFormat => (AbstractControl<dynamic> control) {
    if (control.value == null || control.value.isEmpty) return null;

    final value = control.value as String;
    if (RegExp(r'^[a-zA-Z\s]+$').hasMatch(value)) {
      return null;
    }

    return {'nameFormat': true};
  };

  static ValidatorFunction get _validHours => (AbstractControl<dynamic> control) {
    if (control.value == null) return null;

    final value = double.tryParse(control.value.toString());
    if (value != null && (value * 2) % 1 == 0) {
      return null;
    }

    return {'validHours': true};
  };

  static ValidatorFunction get _phoneFormat => (AbstractControl<dynamic> control) {
    if (control.value == null || control.value.isEmpty) return null;

    final value = control.value as String;
    // Support various phone formats
    final phoneRegex = RegExp(r'^[\+]?[1-9][\d]{0,15}$');
    if (phoneRegex.hasMatch(value.replaceAll(RegExp(r'[\s\-\(\)]'), ''))) {
      return null;
    }

    return {'phoneFormat': true};
  };

  // Date validation helpers
  static ValidatorFunction dateNotInPast() {
    return (AbstractControl<dynamic> control) {
      if (control.value == null) return null;

      final date = control.value as DateTime;
      if (date.isAfter(DateTime.now().subtract(const Duration(days: 1)))) {
        return null;
      }

      return {'dateNotInPast': true};
    };
  }

  static ValidatorFunction dateInRange(DateTime start, DateTime end) {
    return (AbstractControl<dynamic> control) {
      if (control.value == null) return null;

      final date = control.value as DateTime;
      if (date.isAfter(start) && date.isBefore(end)) {
        return null;
      }

      return {'dateInRange': true};
    };
  }

  // Business rule validators
  static ValidatorFunction fuelLevelThreshold(double criticalLevel) {
    return (AbstractControl<dynamic> control) {
      if (control.value == null) return null;

      final level = double.tryParse(control.value.toString());
      if (level != null && level < criticalLevel) {
        return {'belowCriticalLevel': true};
      }

      return null;
    };
  }

  static ValidatorFunction maintenancePriorityRequired() {
    return (AbstractControl<dynamic> control) {
      if (control.value == null || control.value.isEmpty) {
        return {'priorityRequired': true};
      }

      final validPriorities = ['low', 'medium', 'high', 'critical'];
      if (!validPriorities.contains(control.value.toString().toLowerCase())) {
        return {'invalidPriority': true};
      }

      return null;
    };
  }

  // Form group validators
  static Map<String, dynamic> createPropertyForm() {
    return {
      'name': FormControl<String>(validators: [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(100),
      ]),
      'type': FormControl<String>(validators: [
        Validators.required,
      ]),
      'address': FormControl<String>(validators: [
        Validators.maxLength(500),
      ]),
      'description': FormControl<String>(validators: [
        Validators.maxLength(1000),
      ]),
      'capacity': FormControl<int>(),
      'units': FormControl<int>(),
      'parking_spaces': FormControl<int>(),
      'safety_officer': FormControl<String>(),
      'start_date': FormControl<DateTime>(),
      'end_date': FormControl<DateTime>(),
    };
  }



  static Map<String, dynamic> createMaintenanceIssueForm() {
    return {
      'title': FormControl<String>(validators: [
        Validators.required,
        Validators.minLength(5),
        Validators.maxLength(200),
      ]),
      'description': FormControl<String>(validators: [
        Validators.required,
        Validators.minLength(10),
        Validators.maxLength(1000),
      ]),
      'priority': FormControl<String>(validators: [
        Validators.required,
      ]),
      'propertyId': FormControl<String>(validators: [
        Validators.required,
      ]),
      'serviceType': FormControl<String>(validators: [
        Validators.required,
      ]),
      'department': FormControl<String>(), // Optional field
      'dueDate': FormControl<DateTime>(), // Optional field
    };
  }

  static Map<String, dynamic> createAttendanceForm() {
    return {
      'workerName': FormControl<String>(validators: [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
      ]),
      'workerId': FormControl<String>(validators: [
        Validators.required,
      ]),
      'date': FormControl<DateTime>(validators: [
        Validators.required,
      ]),
      'checkInTime': FormControl<String>(),
      'checkOutTime': FormControl<String>(),
      'hoursWorked': FormControl<double>(validators: [
        Validators.min(0),
        Validators.max(24),
      ]),
      'notes': FormControl<String>(validators: [
        Validators.maxLength(500),
      ]),
    };
  }

  static Map<String, dynamic> createGeneratorFuelForm() {
    return {
      'fuelLevel': FormControl<double>(validators: [
        Validators.required,
        Validators.min(0),
        Validators.max(1000),
      ]),
      'consumptionRate': FormControl<double>(validators: [
        Validators.min(0),
        Validators.max(100),
      ]),
      'runtimeHours': FormControl<double>(validators: [
        Validators.min(0),
        Validators.max(24),
      ]),
      'efficiency': FormControl<double>(validators: [
        Validators.min(0),
        Validators.max(100),
      ]),
      'notes': FormControl<String>(validators: [
        Validators.maxLength(500),
      ]),
    };
  }
}

/// Helper class for validation error messages
class ValidationMessages {
  static String getErrorMessage(String key, dynamic value) {
    switch (key) {
      case 'required':
        return 'This field is required';
      case 'email':
        return 'Please enter a valid email address';
      case 'minLength':
        return 'Must be at least ${value['requiredLength']} characters';
      case 'maxLength':
        return 'Cannot exceed ${value['requiredLength']} characters';
      case 'min':
        return 'Value must be at least ${value['min']}';
      case 'max':
        return 'Value cannot exceed ${value['max']}';
      case 'passwordStrength':
        return 'Password must contain uppercase, lowercase, number and special character';
      case 'noSpecialChars':
        return 'Cannot contain special characters';
      case 'positiveNumber':
        return 'Must be a positive number';
      case 'nameFormat':
        return 'Can only contain letters and spaces';
      case 'validHours':
        return 'Hours must be in 0.5 hour increments';
      case 'phoneFormat':
        return 'Please enter a valid phone number';
      case 'dateNotInPast':
        return 'Date cannot be in the past';
      case 'dateInRange':
        return 'Date must be within the specified range';
      case 'belowCriticalLevel':
        return 'Fuel level is below critical threshold';
      case 'priorityRequired':
        return 'Priority is required';
      case 'invalidPriority':
        return 'Invalid priority level';
      default:
        return 'Invalid value';
    }
  }
}
