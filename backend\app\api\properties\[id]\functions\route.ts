import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { createApiResponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';
import { validateRequest } from '@/lib/validation';
import Joi from 'joi';

const updatePropertyFunctionsSchema = Joi.object({
  functions: Joi.array().items(
    Joi.object({
      function_name: Joi.string().required(),
      is_enabled: Joi.boolean().required(),
      configuration: Joi.object().optional(),
      display_order: Joi.number().integer().min(0).optional(),
    })
  ).required(),
});

async function getPropertyFunctionsHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id },
      select: { id: true, name: true, type: true },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Get property functions
    const propertyFunctions = await prisma.propertyFunction.findMany({
      where: { propertyId: id },
      orderBy: [
        { displayOrder: 'asc' },
        { functionName: 'asc' },
      ],
    });

    // Define default functions based on property type
    const defaultFunctions = getDefaultFunctionsForPropertyType(property.type);

    // Merge with existing configuration
    const functionsConfig = defaultFunctions.map(defaultFunc => {
      const existing = propertyFunctions.find(pf => pf.functionName === defaultFunc.function_name);
      return existing ? {
        id: existing.id,
        function_name: existing.functionName,
        is_enabled: existing.isEnabled,
        configuration: existing.configuration,
        display_order: existing.displayOrder,
        created_at: existing.createdAt,
        updated_at: existing.updatedAt,
      } : {
        function_name: defaultFunc.function_name,
        is_enabled: defaultFunc.is_enabled,
        configuration: defaultFunc.configuration,
        display_order: defaultFunc.display_order,
      };
    });

    return Response.json(
      createApiResponse({
        property: {
          id: property.id,
          name: property.name,
          type: property.type,
        },
        functions: functionsConfig,
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to fetch property functions', 'property-functions-get');
  }
}

async function updatePropertyFunctionsHandler(
  request: NextRequest,
  context: { params: { id: string } },
  currentUser: any
) {
  try {
    const { id } = context.params;
    const body = await getRequestBody(request);

    // Validate request body
    const validation = validateRequest(updatePropertyFunctionsSchema, body);
    if (!validation.isValid) {
      return Response.json(
        createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
        { status: 400 }
      );
    }

    const { functions } = validation.data;

    // Verify property exists
    const property = await prisma.property.findUnique({
      where: { id },
      select: { id: true, name: true, type: true },
    });

    if (!property) {
      return Response.json(
        createApiResponse(null, 'Property not found', 'NOT_FOUND'),
        { status: 404 }
      );
    }

    // Update property functions in transaction
    const updatedFunctions = await prisma.$transaction(async (tx) => {
      // Delete existing functions for this property
      await tx.propertyFunction.deleteMany({
        where: { propertyId: id },
      });

      // Create new function configurations
      const createData = functions.map((func: any) => ({
        propertyId: id,
        functionName: func.function_name,
        isEnabled: func.is_enabled,
        configuration: func.configuration || {},
        displayOrder: func.display_order || 0,
      }));

      await tx.propertyFunction.createMany({
        data: createData,
      });

      // Return updated functions
      return await tx.propertyFunction.findMany({
        where: { propertyId: id },
        orderBy: [
          { displayOrder: 'asc' },
          { functionName: 'asc' },
        ],
      });
    });

    return Response.json(
      createApiResponse({
        property: {
          id: property.id,
          name: property.name,
          type: property.type,
        },
        functions: updatedFunctions.map(pf => ({
          id: pf.id,
          function_name: pf.functionName,
          is_enabled: pf.isEnabled,
          configuration: pf.configuration,
          display_order: pf.displayOrder,
          created_at: pf.createdAt,
          updated_at: pf.updatedAt,
        })),
      }),
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Failed to update property functions', 'property-functions-update');
  }
}

function getDefaultFunctionsForPropertyType(propertyType: string) {
  const baseFunctions = [
    { function_name: 'maintenance', is_enabled: true, configuration: { priority_levels: ['low', 'medium', 'high', 'critical'] }, display_order: 1 },
    { function_name: 'security', is_enabled: true, configuration: { monitoring_enabled: true }, display_order: 2 },
    { function_name: 'uptime', is_enabled: true, configuration: { check_interval: 300 }, display_order: 3 },
  ];

  switch (propertyType.toLowerCase()) {
    case 'office':
      return [
        ...baseFunctions,
        { function_name: 'attendance', is_enabled: true, configuration: {
          working_hours: { start: '09:00', end: '18:00' },
          break_duration: 60,
          overtime_threshold: 8
        }, display_order: 4 },
        { function_name: 'meeting_rooms', is_enabled: true, configuration: {
          booking_advance_days: 30,
          max_duration_hours: 8
        }, display_order: 5 },
        { function_name: 'visitor_management', is_enabled: true, configuration: {
          pre_approval_required: true,
          max_visitors_per_day: 50
        }, display_order: 6 },
        { function_name: 'ott', is_enabled: false, configuration: { note: 'Not typically required for office properties' }, display_order: 7 },
        { function_name: 'fuel', is_enabled: false, configuration: { note: 'Not typically required for office properties' }, display_order: 8 },
        { function_name: 'diesel', is_enabled: false, configuration: { note: 'Not typically required for office properties' }, display_order: 9 },
      ];
    case 'construction_site':
      return [
        ...baseFunctions,
        { function_name: 'attendance', is_enabled: true, configuration: {
          working_hours: { start: '07:00', end: '17:00' },
          break_duration: 60,
          overtime_threshold: 8,
          safety_briefing_required: true
        }, display_order: 4 },
        { function_name: 'equipment', is_enabled: true, configuration: {
          maintenance_schedule: 'weekly',
          usage_tracking: true
        }, display_order: 5 },
        { function_name: 'safety', is_enabled: true, configuration: {
          incident_reporting: true,
          safety_checks: 'daily'
        }, display_order: 6 },
        { function_name: 'materials', is_enabled: true, configuration: {
          inventory_tracking: true,
          reorder_threshold: 20
        }, display_order: 7 },
        { function_name: 'progress', is_enabled: true, configuration: {
          milestone_tracking: true,
          photo_documentation: true
        }, display_order: 8 },
        { function_name: 'fuel', is_enabled: true, configuration: {
          generator_monitoring: true,
          fuel_threshold: 25
        }, display_order: 9 },
        { function_name: 'diesel', is_enabled: true, configuration: {
          consumption_tracking: true
        }, display_order: 10 },
        { function_name: 'ott', is_enabled: false, configuration: { note: 'Not typically required for construction sites' }, display_order: 11 },
      ];
    case 'residential':
    default:
      return [
        ...baseFunctions,
        { function_name: 'ott', is_enabled: true, configuration: {
          shared_subscriptions: true,
          cost_splitting: true
        }, display_order: 4 },
        { function_name: 'fuel', is_enabled: true, configuration: {
          generator_monitoring: true,
          fuel_threshold: 30
        }, display_order: 5 },
        { function_name: 'diesel', is_enabled: true, configuration: {
          consumption_tracking: true
        }, display_order: 6 },
        { function_name: 'attendance', is_enabled: false, configuration: {
          note: 'Typically not required for residential properties unless staff is employed'
        }, display_order: 7 },
      ];
  }
}

export const GET = requireAuth(getPropertyFunctionsHandler);
export const PUT = requireAuth(updatePropertyFunctionsHandler);

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
