import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert<PERSON>riangle, Key, Settings, Shield, UserPlus, Users } from "lucide-react"
import Link from "next/link"
import { RoleDebugger } from "@/components/role-debugger"

export default async function AdminPage() {
  // Simple, direct admin check
  const user = await getCurrentUser()

  if (!user || (user.username !== "admin1" && user.role !== "admin")) {
    redirect("/unauthorized")
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600">Manage users, roles, and system settings</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              User Management
            </CardTitle>
            <CardDescription>Manage user accounts and permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin/users">
              <Button className="w-full">Manage Users</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Role Management
            </CardTitle>
            <CardDescription>Configure user roles and access levels</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin/roles">
              <Button className="w-full">Manage Roles</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Key className="mr-2 h-5 w-5" />
              Permissions
            </CardTitle>
            <CardDescription>Set up access permissions for different roles</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin/permissions">
              <Button className="w-full">Manage Permissions</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5" />
              Thresholds
            </CardTitle>
            <CardDescription>Configure system thresholds and alerts</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin/thresholds">
              <Button className="w-full">Manage Thresholds</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserPlus className="mr-2 h-5 w-5" />
              Setup Househelp
            </CardTitle>
            <CardDescription>Configure househelp role and permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/admin/setup-househelp">
              <Button className="w-full">Setup Househelp</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              System Settings
            </CardTitle>
            <CardDescription>Configure system-wide settings</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" disabled>
              Coming Soon
            </Button>
          </CardContent>
        </Card>
      </div>

      <RoleDebugger />
    </div>
  )
}
