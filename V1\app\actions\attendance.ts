"use server"

import { createClient } from "@/lib/supabase/server"
import { revalidatePath } from "next/cache"

export type AttendanceRecord = {
  site_id: string
  worker_id: string
  worker_name: string
  worker_role: string
  date: string
  status: string
  hours_worked: number
  notes?: string
}

export async function submitAttendance(attendance: AttendanceRecord) {
  const supabase = createClient()

  try {
    const { data, error } = await supabase.from("site_attendance").upsert(
      {
        site_id: attendance.site_id,
        worker_id: attendance.worker_id,
        worker_name: attendance.worker_name,
        worker_role: attendance.worker_role,
        date: attendance.date,
        status: attendance.status,
        hours_worked: attendance.hours_worked,
        notes: attendance.notes || null,
      },
      {
        onConflict: "site_id,worker_id,date",
      },
    )

    if (error) throw error

    // Revalidate the sites page to reflect the new data
    revalidatePath("/dashboard/office/sites")

    return { success: true, data }
  } catch (error) {
    console.error("Error submitting attendance:", error)
    return { success: false, error: (error as Error).message }
  }
}

export async function getAttendanceRecords(siteId: string, startDate?: string, endDate?: string) {
  const supabase = createClient()

  try {
    let query = supabase.from("site_attendance").select("*").eq("site_id", siteId).order("date", { ascending: false })

    if (startDate) {
      query = query.gte("date", startDate)
    }

    if (endDate) {
      query = query.lte("date", endDate)
    }

    const { data, error } = await query

    if (error) throw error

    return { success: true, data }
  } catch (error) {
    console.error("Error fetching attendance records:", error)
    return { success: false, error: (error as Error).message, data: [] }
  }
}
