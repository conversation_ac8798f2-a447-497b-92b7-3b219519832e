"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { AlertCircle, Check } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { createOttService, updateOttService, type OttService } from "@/app/actions/ott-services"

interface OttServiceFormProps {
  propertyId: string
  existingService?: OttService
  onSuccess?: () => void
}

export function OttServiceForm({ propertyId, existingService, onSuccess }: OttServiceFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess(false)

    const formData = new FormData(e.currentTarget)

    try {
      if (existingService) {
        // Update existing service
        const result = await updateOttService(existingService.id, {
          platform: formData.get("platform") as string,
          plan_and_duration: formData.get("plan_and_duration") as string,
          username: formData.get("username") as string,
          password: formData.get("password") as string,
          next_recharge_date: formData.get("next_recharge_date") as string,
          current_status: formData.get("current_status") as string,
        })

        if (result.success) {
          setSuccess(true)
          if (onSuccess) {
            setTimeout(onSuccess, 1000)
          }
        } else {
          setError(result.error || "An error occurred")
        }
      } else {
        // Create new service
        const result = await createOttService({
          property_id: propertyId,
          platform: formData.get("platform") as string,
          plan_and_duration: formData.get("plan_and_duration") as string,
          username: formData.get("username") as string,
          password: formData.get("password") as string,
          next_recharge_date: formData.get("next_recharge_date") as string,
          current_status: formData.get("current_status") as string,
        })

        if (result.success) {
          setSuccess(true)
          e.currentTarget.reset()
          if (onSuccess) {
            setTimeout(onSuccess, 1000)
          }
        } else {
          setError(result.error || "An error occurred")
        }
      }
    } catch (err) {
      setError("An unexpected error occurred")
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-0">
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 bg-green-50 text-green-700">
            <Check className="h-4 w-4" />
            <AlertDescription>
              {existingService ? "Service updated successfully!" : "Service added successfully!"}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="platform">Platform</Label>
            <Input
              id="platform"
              name="platform"
              placeholder="e.g., Netflix, Amazon Prime"
              defaultValue={existingService?.platform || ""}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="plan_and_duration">Plan & Duration</Label>
            <Input
              id="plan_and_duration"
              name="plan_and_duration"
              placeholder="e.g., 499 Monthly, 1499 Annual"
              defaultValue={existingService?.plan_and_duration || ""}
              required
            />
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="username">Login Email/Username</Label>
              <Input
                id="username"
                name="username"
                type="text"
                placeholder="Enter login email or username"
                defaultValue={existingService?.username || ""}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="text"
                placeholder="Enter password"
                defaultValue={existingService?.password || ""}
                required
              />
            </div>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="next_recharge_date">Next Payment Date</Label>
              <Input
                id="next_recharge_date"
                name="next_recharge_date"
                type="date"
                defaultValue={
                  existingService?.next_recharge_date && /^\d{4}-\d{2}-\d{2}$/.test(existingService.next_recharge_date)
                    ? existingService.next_recharge_date
                    : new Date().toISOString().split("T")[0]
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="current_status">Status</Label>
              <select
                id="current_status"
                name="current_status"
                className="w-full rounded-md border border-input bg-background px-3 py-2"
                defaultValue={existingService?.current_status || "Active"}
                required
              >
                <option value="Active">Active</option>
                <option value="Expired">Expired</option>
                <option value="Pending">Pending</option>
              </select>
            </div>
          </div>

          <div className="pt-2">
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Submitting..." : existingService ? "Update Service" : "Add Service"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
