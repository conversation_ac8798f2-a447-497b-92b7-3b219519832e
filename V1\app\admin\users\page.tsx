import { getCurrentUser } from "@/lib/auth"
import { redirect } from "next/navigation"
import { UserManagement } from "@/components/user-management"

export default async function UsersPage() {
  // Simple, direct admin check
  const user = await getCurrentUser()

  if (!user || (user.username !== "admin1" && user.role !== "admin")) {
    redirect("/unauthorized")
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
        <p className="text-gray-600">Manage user accounts, roles, and permissions</p>
      </div>
      <UserManagement />
    </div>
  )
}
